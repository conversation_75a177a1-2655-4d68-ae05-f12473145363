# Recipe Finder - React Application   :peach: :ramen: :fork_and_knife: :cherries: :pizza: :green_apple:

### About the Project
A single page web application that allows users to search recipes based on their query.

### Technologies

- React
  - Hooks
  - Components
- CSS
  - CSS Flex
- Javascript
  - Fetch API
  - ES6
- API
  - Edamam API

### Libraries Used

* `styled-components`
* `axios`
* `react-scripts`
* `material-ui`

### API Used
Edamam Recipe Search API

Link : https://www.edamam.com/

### How It Works

- Search a recipe in the search input
- Grabs the recipe information from Edamam API.
- Information is returned has a JSON object
- Using React components to structure data into presentational format
- Using CSS to create visually appealing and user friendly application


### Screenshots

<img  src="https://github.com/khushi-purwar/Web-dev-mini-projects/blob/d-khushi/recipe-finder/screenshot/ss1.png" />

Type your recipe in search bar

<img src="https://github.com/khushi-purwar/Web-dev-mini-projects/blob/d-khushi/recipe-finder/screenshot/ss2.png" />

Click on the Ingredients list, you will see UI like below:

<img src="https://github.com/khushi-purwar/Web-dev-mini-projects/blob/d-khushi/recipe-finder/screenshot/ss3.png" />

### Live Demo

<img src="https://github.com/khushi-purwar/Web-dev-mini-projects/blob/d-khushi/recipe-finder/screenshot/recipe-finder.gif" />
