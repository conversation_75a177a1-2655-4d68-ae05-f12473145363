<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f7f7f7;
        }

        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            text-align: center;
        }

        input {
            padding: 10px;
            width: 100%;
            margin-bottom: 20px;
            border-radius: 5px;
            border: 1px solid #ccc;
            box-sizing: border-box;
        }

        button {
            padding: 10px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }

        img {
            margin-top: 20px;
            max-width: 200px;
            height: auto;
        }
    </style>
</head>
<body>

    <div class="container">
        <h2>QR Code Generator</h2>
        <input type="text" id="text" placeholder="Enter text or URL" />
        <button onclick="generateQRCode()">Generate QR Code</button>

        <div id="qrCode">
            <!-- QR code will appear here -->
        </div>
    </div>

    <script>
        function generateQRCode() {
            // Get the value from the input field
            var text = document.getElementById("text").value;
            
            // API endpoint for QR code generation
            var apiUrl = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" + encodeURIComponent(text);
            
            // Display the QR code in an img tag
            document.getElementById("qrCode").innerHTML = "<img src='" + apiUrl + "' alt='QR Code' />";
        }
    </script>

</body>
</html>
