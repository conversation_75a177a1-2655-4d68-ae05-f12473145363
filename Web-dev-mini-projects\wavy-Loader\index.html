/* Loader Container Styling */
body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: #f0f0f0;
    margin: 0;
}

/* Circle Container Styling */
.circle {
    position: relative;
    width: 150px;
    height: 150px;
}

/* Circle Span Styling */
.circle span {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #4CAF50;
    border-radius: 50%;
    opacity: 0;
    animation: wave 1.5s linear infinite;
}

/* Creating Multiple Circle Animations */
.circle span:nth-child(1) {
    transform: rotate(0deg);
    animation-delay: 0s;
}
.circle span:nth-child(2) {
    transform: rotate(24deg);
    animation-delay: -0.1s;
}
.circle span:nth-child(3) {
    transform: rotate(48deg);
    animation-delay: -0.2s;
}
.circle span:nth-child(4) {
    transform: rotate(72deg);
    animation-delay: -0.3s;
}
.circle span:nth-child(5) {
    transform: rotate(96deg);
    animation-delay: -0.4s;
}
.circle span:nth-child(6) {
    transform: rotate(120deg);
    animation-delay: -0.5s;
}
.circle span:nth-child(7) {
    transform: rotate(144deg);
    animation-delay: -0.6s;
}
.circle span:nth-child(8) {
    transform: rotate(168deg);
    animation-delay: -0.7s;
}
.circle span:nth-child(9) {
    transform: rotate(192deg);
    animation-delay: -0.8s;
}
.circle span:nth-child(10) {
    transform: rotate(216deg);
    animation-delay: -0.9s;
}
.circle span:nth-child(11) {
    transform: rotate(240deg);
    animation-delay: -1s;
}
.circle span:nth-child(12) {
    transform: rotate(264deg);
    animation-delay: -1.1s;
}
.circle span:nth-child(13) {
    transform: rotate(288deg);
    animation-delay: -1.2s;
}
.circle span:nth-child(14) {
    transform: rotate(312deg);
    animation-delay: -1.3s;
}
.circle span:nth-child(15) {
    transform: rotate(336deg);
    animation-delay: -1.4s;
}

/* Wave Animation */
@keyframes wave {
    0% {
        opacity: 1;
        transform: scale(0.2);
    }
    50% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(1.5);
    }
}
