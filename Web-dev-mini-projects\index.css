*{
  font-family: 'Courier New', Courier, monospace;
}
#container{
  width:600px;
  height:700px;
  max-height: max-content;
margin:auto;
border:auto solid black;
text-align: center;
border-radius: 20px;
 }
 input{

  width:450px;
  height:40px;
  border-radius: 10px;
  border:1px solid black;
  font-family: 'Courier New', Courier, monospace;
  font-size: large;
  text-align: center;
 }
 
img{
 
  margin-top:30px;
  border: 3px solid black;
}
button{
  margin:60px 0px 0 0;
  width:200px;
  height:50px;
  border-radius: 10px;
  letter-spacing: 2px;
  
}
.generate{
  color:white;
  background-color: rgba(10, 177, 10, 0.712);
  border:2px solid green
}
.downlode{
  margin-left: 100px;
  color:white;
  background-color: rgb(16, 16, 121);
  border:2px solid rgb(70, 22, 202);

}
input:focus{
  border-color:seagreen;
  background-color: aliceblue;
  color:black

}
p{
  margin-top:70px;
}