@import './variables.scss';
@import './mixins.scss';

.listContainer {
  display: flex;
  flex-direction: column;
  text-transform: capitalize;
  margin-top: -9rem;

  @include for-mobile-only {
    margin-top: 0;
  }

  .category {
    @include fluid-type(0.88rem, 1.6rem);
    margin-left: 3rem;
    padding: 0.5rem 0;
    width: fit-content;
  }

  .cardRow {
    display: flex;
    flex-direction: row;
    padding: 3.2rem 6rem 12.5rem;
    overflow-x: auto;
    margin-top: -3rem;
    margin-left: -3rem;

    &::-webkit-scrollbar {
      display: none;
    }

    @include for-mobile-only {
      padding: 3.2rem 6rem 2.2rem;
    }
  }
}

.card {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  width: 14rem;
  height: 9rem;
  background-color: $card-color;
  border-radius: 0.28rem;
  margin-right: 0.3rem;
  transition: all 300ms ease;
  z-index: 1;
  cursor: pointer;

  &:hover {
    transform: scale(1.45);
    box-shadow: 0 0 1rem rgba(0, 0, 0, 0.6), 0 6px 6px rgba(0, 0, 0, 0.5);
    z-index: 5;

    .cardPoster {
      height: 85%;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }
    .cardInfo {
      box-shadow: 0 0 1rem rgba(0, 0, 0, 0.75), 0 6px 6px rgba(0, 0, 0, 0.5);
      display: flex;
      flex-direction: column;
      height: fit-content;
    }
  }

  .cardPoster {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0.28rem;
  }

  .cardInfo {
    min-height: fit-content;
    background-color: $secondary;
    border-bottom-left-radius: 0.28rem;
    border-bottom-right-radius: 0.28rem;
    display: none;
    padding: 0.4rem;
    font-size: inherit;
  }
}

.modalButton {
  display: flex;
  width: fit-content;
  height: fit-content;
  align-items: center;
}

.dot {
  color: $button-border;
  padding: 0 0.3rem;
}

.longCard {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  width: 14rem;
  height: 24rem;
  background-color: $card-color;
  border-radius: 0.28rem;
  margin-right: 0.3rem;
  transition: all 320ms ease-in-out;
  z-index: 1;
  cursor: pointer;

  &:hover {
    transform: scale(1.15);
    box-shadow: 0 0 1rem rgba(0, 0, 0, 0.6), 0 4px 4px rgba(0, 0, 0, 0.5);
    z-index: 5;

    .cardPoster {
      mask-image: linear-gradient(to bottom, $secondary 70%, transparent 100%);
    }
    .more {
      display: flex;
      flex-direction: column;
    }
  }

  .cardPoster {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0.28rem;
  }
}
.more {
  border-bottom-left-radius: 0.28rem;
  border-bottom-right-radius: 0.28rem;
  display: none;
  width: 100%;
  position: absolute;
  bottom: 0;
  padding: 0.4rem;
  z-index: 8;
}
.actionRow {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
}

.textDetails {
  padding: 0.2rem 0.3rem;
  font-size: 0.75rem;

  .greenText {
    color: $greenText;
    font-weight: bold;
    padding: 0.2rem;
    padding-left: 0;
  }

  .rating {
    border: $button-border solid 1px;
    padding: 0 0.2rem;
    margin: 0 0.2rem;
  }

  .regularText {
    font-weight: 400;
  }
}

.container {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 2.5rem;
  margin-left: 0.5rem;
  overflow: visible;
  width: 11rem;
  margin-top: -3.5rem;
  margin-bottom: -3.5rem;

  .rank {
    font-size: 13rem;
    font-weight: 900;
    -webkit-text-stroke: 5px #7c7c7c;
    -webkit-text-fill-color: $black;
    margin-right: -2rem;
    margin-bottom: 1.4rem;
    padding: 0 !important;
    cursor: pointer;
  }
}

.featureCard {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  width: 7.2rem;
  height: 10rem;
  background-color: $card-color;
  border-top-right-radius: 0.28rem;
  border-bottom-right-radius: 0.28rem;
  margin-right: 0.5rem;
  transition: all 300ms ease;
  z-index: 3;
  cursor: pointer;

  &:hover {
    transform: scale(1.6);
    width: 14rem;
    height: 9rem;
    border-radius: 0.28rem;
    z-index: 5;
    box-shadow: 0 0 1rem rgba(0, 0, 0, 0.6), 0 6px 6px rgba(0, 0, 0, 0.5);

    .poster {
      height: 85%;
      border-top-left-radius: 0.28rem;
      border-bottom-right-radius: 0;
    }

    .info {
      display: flex;
      flex-direction: column;
      box-shadow: 0 0 1rem rgba(0, 0, 0, 0.75), 0 6px 6px rgba(0, 0, 0, 0.5);
      min-height: fit-content;
    }
  }

  .poster {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-top-right-radius: 0.28rem;
    border-bottom-right-radius: 0.28rem;
  }
}

.info {
  min-height: fit-content;
  background-color: $secondary;
  border-bottom-left-radius: 0.28rem;
  border-bottom-right-radius: 0.28rem;
  display: none;
  padding: 0.4rem;
  font-size: inherit;
}
