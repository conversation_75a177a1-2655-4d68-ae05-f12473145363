# Welcome 🖐 to the News React App Website

This is a React app which fetches the recent news from an api and renders it on the DOM without reloading the page.
I've used the concept of *Async functions* and *react hook usestate* also worked with *APIs*.


## 💻Tech Stack
<br>

![HTML](https://img.shields.io/badge/html5%20-%23E34F26.svg?&style=for-the-badge&logo=html5&logoColor=white)
![CSS](https://img.shields.io/badge/css3%20-%231572B6.svg?&style=for-the-badge&logo=css3&logoColor=white)
![JS](https://img.shields.io/badge/javascript%20-%23323330.svg?&style=for-the-badge&logo=javascript&logoColor=%23F7DF1E)
![REACT](https://img.shields.io/badge/react%20-%23628395.svg?&style=for-the-badge&logo=react&logoColor=%2300C1D4)

<br>


### How to get the project on your local machine:


---

- Download or clone the repository

```
git clone https://github.com/Ayushparikh-code/Web-dev-mini-projects.git
```

- Go to the directory
- Open news-app folder
- Open the terminal and run 
```
npm start
```
- The project will be opened in the browser



<br>

## Screenshots

![Demo1](/news-app/public/news.png)

![Demo2](/news-app/public/news1.png)

<br>

## Happy Coding!
