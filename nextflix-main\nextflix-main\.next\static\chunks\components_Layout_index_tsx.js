(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Layout_index_tsx"],{

/***/ "./components/Layout/index.tsx":
/*!*************************************!*\
  !*** ./components/Layout/index.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Layout; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/react/jsx-dev-runtime.js");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ "./node_modules/next/dynamic.js");
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _hooks_useScrollLimit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useScrollLimit */ "./hooks/useScrollLimit.ts");
/* harmony import */ var _styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../styles/Browse.module.scss */ "./styles/Browse.module.scss");
/* harmony import */ var _styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4__);
/* module decorator */ module = __webpack_require__.hmd(module);


var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\Layout\\index.tsx",
    _s = $RefreshSig$();





var Footer = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c = function _c() {
  return __webpack_require__.e(/*! import() */ "components_Footer_index_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ../Footer */ "./components/Footer/index.tsx"));
}, {
  loadableGenerated: {
    webpack: function webpack() {
      return [/*require.resolve*/(/*! ../Footer */ "./components/Footer/index.tsx")];
    },
    modules: ["..\\components\\Layout\\index.tsx -> " + '../Footer']
  }
});
_c2 = Footer;
var Navbar = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(_c3 = function _c3() {
  return __webpack_require__.e(/*! import() */ "components_Navbar_index_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ../Navbar */ "./components/Navbar/index.tsx"));
}, {
  loadableGenerated: {
    webpack: function webpack() {
      return [/*require.resolve*/(/*! ../Navbar */ "./components/Navbar/index.tsx")];
    },
    modules: ["..\\components\\Layout\\index.tsx -> " + '../Navbar']
  }
});
_c4 = Navbar;
var SCROLL_LIMIT = 80;
function Layout(_ref) {
  _s();

  var children = _ref.children;
  var isScrolled = (0,_hooks_useScrollLimit__WEBPACK_IMPORTED_MODULE_3__.default)(SCROLL_LIMIT);
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    className: (_styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4___default().container),
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Navbar, {
      isScrolled: isScrolled
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 20,
      columnNumber: 7
    }, this), children, /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 22,
      columnNumber: 7
    }, this)]
  }, void 0, true, {
    fileName: _jsxFileName,
    lineNumber: 19,
    columnNumber: 5
  }, this);
}

_s(Layout, "uxLPG4az8ZivgfdpjTfexmH/mqw=", false, function () {
  return [_hooks_useScrollLimit__WEBPACK_IMPORTED_MODULE_3__.default];
});

_c5 = Layout;

var _c, _c2, _c3, _c4, _c5;

$RefreshReg$(_c, "Footer$dynamic");
$RefreshReg$(_c2, "Footer");
$RefreshReg$(_c3, "Navbar$dynamic");
$RefreshReg$(_c4, "Navbar");
$RefreshReg$(_c5, "Layout");

;
    var _a, _b;
    // Legacy CSS implementations will `eval` browser code in a Node.js context
    // to extract CSS. For backwards compatibility, we need to check we're in a
    // browser context before continuing.
    if (typeof self !== 'undefined' &&
        // AMP / No-JS mode does not inject these helpers:
        '$RefreshHelpers$' in self) {
        var currentExports = module.__proto__.exports;
        var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
        // This cannot happen in MainTemplate because the exports mismatch between
        // templating and execution.
        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
        // A module can be accepted automatically based on its exports, e.g. when
        // it is a Refresh Boundary.
        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
            // Save the previous exports on update so we can compare the boundary
            // signatures.
            module.hot.dispose(function (data) {
                data.prevExports = currentExports;
            });
            // Unconditionally accept an update to this module, we'll check if it's
            // still a Refresh Boundary later.
            module.hot.accept();
            // This field is set when the previous version of this module was a
            // Refresh Boundary, letting us know we need to check for invalidation or
            // enqueue an update.
            if (prevExports !== null) {
                // A boundary can become ineligible if its exports are incompatible
                // with the previous exports.
                //
                // For example, if you add/remove/change exports, we'll want to
                // re-execute the importing modules, and force those components to
                // re-render. Similarly, if you convert a class component to a
                // function, we want to invalidate the boundary.
                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                    module.hot.invalidate();
                }
                else {
                    self.$RefreshHelpers$.scheduleUpdate();
                }
            }
        }
        else {
            // Since we just executed the code for the module, it's possible that the
            // new exports made it ineligible for being a boundary.
            // We only care about the case when we were _previously_ a boundary,
            // because we already accepted this update (accidental side effect).
            var isNoLongerABoundary = prevExports !== null;
            if (isNoLongerABoundary) {
                module.hot.invalidate();
            }
        }
    }


/***/ }),

/***/ "./hooks/useScrollLimit.ts":
/*!*********************************!*\
  !*** ./hooks/useScrollLimit.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useScrollLimit; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* module decorator */ module = __webpack_require__.hmd(module);
var _s = $RefreshSig$();


function useScrollLimit(limit) {
  _s();

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
      reached = _useState[0],
      setReached = _useState[1];

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    function onScroll() {
      var scrolled = window.scrollY > limit;
      setReached(scrolled);
    }

    window.addEventListener('scroll', onScroll);
    return function () {
      return window.removeEventListener('scroll', onScroll);
    };
  }, [limit]);
  return reached;
}

_s(useScrollLimit, "r2oIAQU8iPDksH/i7EGRUYmRCz8=");

;
    var _a, _b;
    // Legacy CSS implementations will `eval` browser code in a Node.js context
    // to extract CSS. For backwards compatibility, we need to check we're in a
    // browser context before continuing.
    if (typeof self !== 'undefined' &&
        // AMP / No-JS mode does not inject these helpers:
        '$RefreshHelpers$' in self) {
        var currentExports = module.__proto__.exports;
        var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
        // This cannot happen in MainTemplate because the exports mismatch between
        // templating and execution.
        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
        // A module can be accepted automatically based on its exports, e.g. when
        // it is a Refresh Boundary.
        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
            // Save the previous exports on update so we can compare the boundary
            // signatures.
            module.hot.dispose(function (data) {
                data.prevExports = currentExports;
            });
            // Unconditionally accept an update to this module, we'll check if it's
            // still a Refresh Boundary later.
            module.hot.accept();
            // This field is set when the previous version of this module was a
            // Refresh Boundary, letting us know we need to check for invalidation or
            // enqueue an update.
            if (prevExports !== null) {
                // A boundary can become ineligible if its exports are incompatible
                // with the previous exports.
                //
                // For example, if you add/remove/change exports, we'll want to
                // re-execute the importing modules, and force those components to
                // re-render. Similarly, if you convert a class component to a
                // function, we want to invalidate the boundary.
                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                    module.hot.invalidate();
                }
                else {
                    self.$RefreshHelpers$.scheduleUpdate();
                }
            }
        }
        else {
            // Since we just executed the code for the module, it's possible that the
            // new exports made it ineligible for being a boundary.
            // We only care about the case when we were _previously_ a boundary,
            // because we already accepted this update (accidental side effect).
            var isNoLongerABoundary = prevExports !== null;
            if (isNoLongerABoundary) {
                module.hot.invalidate();
            }
        }
    }


/***/ })

}]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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