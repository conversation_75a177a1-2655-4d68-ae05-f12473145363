/* Global Reset */
* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

/* Body Styling */
body {
    background-color: blueviolet;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

/* Circle Container Styling */
.circle {
    width: 300px;
    height: 300px;
    position: relative;
    transform-style: preserve-3d;
}

/* Circle Span Styling */
.circle span {
    border: 5px solid white;
    border-radius: 50%;
    position: absolute;
    display: block;
    animation: load 3s ease-in-out infinite;
    box-shadow: 0 5px 0 rgba(0, 0, 0, 0.2), inset 0 5px 0 rgba(255, 255, 255, 0.5);
}

/* Keyframe Animation for Wavy Effect */
@keyframes load {
    0%, 100% {
        transform: translateY(-100px);
    }
    50% {
        transform: translateY(100px);
    }
}

/* Span Position and Animation Delay */
.circle span:nth-child(1) {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    animation-delay: 1.4s;
}

.circle span:nth-child(2) {
    top: 10px;
    bottom: 10px;
    left: 10px;
    right: 10px;
    animation-delay: 1.3s;
}

.circle span:nth-child(3) {
    top: 20px;
    bottom: 20px;
    left: 20px;
    right: 20px;
    animation-delay: 1.2s;
}

.circle span:nth-child(4) {
    top: 30px;
    bottom: 30px;
    left: 30px;
    right: 30px;
    animation-delay: 1.1s;
}

.circle span:nth-child(5) {
    top: 40px;
    bottom: 40px;
    left: 40px;
    right: 40px;
    animation-delay: 1.0s;
}

.circle span:nth-child(6) {
    top: 50px;
    bottom: 50px;
    left: 50px;
    right: 50px;
    animation-delay: 0.9s;
}

.circle span:nth-child(7) {
    top: 60px;
    bottom: 60px;
    left: 60px;
    right: 60px;
    animation-delay: 0.8s;
}

.circle span:nth-child(8) {
    top: 70px;
    bottom: 70px;
    left: 70px;
    right: 70px;
    anima
