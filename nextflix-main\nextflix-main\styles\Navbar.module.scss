@import './variables.scss';
@import './mixins.scss';

.navBar {
  background-image: linear-gradient(to bottom, $navBar-transparent 10%, $navBar-gradient);
  transition: all 0.2s ease-out;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  position: sticky;
  padding: 0.8rem;
  top: 0;
  overflow: hidden;

  @include for-mobile-only {
    padding: 0.6rem 0rem;
  }

  &__filled {
    background-image: none;
    background-color: $secondary;
    transition: all 0.2s ease-out;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    position: sticky;
    padding: 0.8rem;
    top: 0;

    @include for-mobile-only {
      padding: 0.6rem 0rem;
    }  
  }

  &__left {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 3rem;

    @include for-mobile-only {
      margin-left: 2rem;
    }

    .browse {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-around;
      cursor: pointer;
    }

    .options {
      padding: 0.4rem 0.3rem;
      margin: 0 0.5rem;
      cursor: pointer;
      transition: linear 200ms ease-in;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-around;
      
      @include for-mobile-only {
        margin: 0 0.2rem;
      }

      &:hover {
        color: $white-hover;
      }
    }

    .menu {
      background-color: $secondary;
      opacity: 0.7 !important;
      color: $white;
      position: absolute;
      top: 3rem;
      padding: 0.6rem 0.9rem;
      margin-left: 6rem;
      border: $button-border solid 1.5px;
      cursor: pointer;

      &:hover {
        border-color: $white;
      }
    }
  }

  &__right {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 2rem;
    z-index: 50;

    @include for-mobile-only {
      margin-right: 0.4rem;
    }

    .options {
      padding: 0.3rem;
      margin: 0 0.2rem;
      cursor: pointer;
      transition: linear 200ms ease-in;

      @include for-mobile-only {
        padding: 0.1rem;
        margin: 0;
      }

      &:hover {
        color: $white-hover;
      }
    }

    .icon {
      font-size: 1.4rem;
      margin: 0 0.7rem;
      cursor: pointer;
      transition: linear 250ms ease-in-out;

      @include for-mobile-only {
        margin: 0 0.3rem;
      }

      &:hover {
        transform: translateY(-1px);
      }
    }

    .searchPanel {
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: pointer;
    }
    .searchBar {
      background-color: $secondary;
      border: $white-hover solid 1.2px;
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 0.9rem;

      &__input {
        padding: 0.4rem;
        outline: none;
        border-radius: 0;
        background-color: transparent;
        color: $white;
        border: none;

        @include for-mobile-only {
          width: 90%;
          overflow: hidden;
          padding: 0.3rem;
        }
      }
    }

    .profile {
      padding: 0 0.9rem;
      display: flex;
      align-items: center;
      flex-direction: row;
      cursor: pointer;

      .user {
        height: 2rem;
        border-radius: 0.2rem;
        margin-right: 0.3rem;
        cursor: pointer;

        @include for-mobile-only {
          height: 1.7rem;
        }
      }

      .signout {
        background-color: $secondary;
        opacity: 0.7;
        color: $white;
        position: absolute;
        margin-top: 6rem;
        padding: 0.6rem 0.9rem;
        right: 8;
        border: $button-border solid 1.5px;
        cursor: pointer;
        z-index: 40;

        &:hover {
          border-color: $white;
        }
      }
    }
  }
}
