<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Wonderland Travel Club</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/css/bootstrap.min.css" integrity="sha384-+0n0xVW2eSR5OomGNYDnhzAbDsOXxcvSN1TPprVMTNDbiYZCxYbOOl7+AMvyTG2x" crossorigin="anonymous">
<link rel="stylesheet" href="style.css">
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" fill="currentColor" class="bi bi-diamond" viewBox="0 0 16 16">
            <path d="M6.95.435c.58-.58 1.52-.58 2.1 0l6.515 6.516c.58.58.58 1.519 0 2.098L9.05 15.565c-.58.58-1.519.58-2.098 0L.435 9.05a1.482 1.482 0 0 1 0-2.098L6.95.435zm1.4.7a.495.495 0 0 0-.7 0L1.134 7.65a.495.495 0 0 0 0 .7l6.516 6.516a.495.495 0 0 0 .7 0l6.516-6.516a.495.495 0 0 0 0-.7L8.35 1.134z" />
        </svg>
        <h3 class="navbar-brand">Wonderland Travel Club</h3>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbar">
            <ul class="navbar-nav ml-auto">
                <li class="nav-item">
                    <a class="nav-link" href="#intro">Home Page</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#intro">Introduction</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#pricing">Pricing Plans</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#checkout">Checkout Page</a>
                </li>
            </ul>
        </div>
    </nav>

    <header id="intro">
        <div class="overlay"></div>
        <div class="container h-100">
            <div class="d-flex h-100 align-items-center justify-content-center">
                <div class="w-100 text-white text-center">
                    <p>Welcome to Wonderland Travel Club, where we explore uncharted parts of the country with backpacking hikes and nature trails. </p>
                    <h1 class="display-3">Packages for vacations start at INR 50,000 annually!</h1>
                    <p>Give your family a chance to spend lavish weekends at some of the choicest hotels and travel getaways with attractive prices from our club memberships.</p>
                    <p>
                        <a href="#pricing" class="btn btn-lg btn-secondary my-2 mx-3 py-2">Learn More</a>
                    </p>
                </div>
            </div>
        </div>
    </header>

    <div class="pricing-header text-center py-3" id="pricing">
        <h2 class="display-4 text-white py-3">Pricing Plans and Subscriptions:</h2>
    </div>

    <div class="container my-5">
        <h3 class="text-center">1. Annual Plans:</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-dark text-white">
                        <h3>North-East Trips</h3>
                    </div>
                    <div class="card-body">
                        <h3 class="card-title pricing-card-title">INR 25,000 /- <small class="text-muted">Regular prices: INR 35,000/-</small></h3>
                        <ul class="list-unstyled">States to be covered
                            <li>Assam</li>
                            <li>Nagaland</li>
                            <li>Meghalaya</li>
                            <li>Sikkim</li>
                            <li>Mizoram</li>
                            <li>Manipur</li>
                            <li>Tripura</li>
                        </ul>
                        <button type="button" class="btn btn-lg btn-block btn-outline-primary">Add to Cart</button>
                        <button type="button" class="btn btn-lg btn-block btn-outline-primary">Explore More</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-dark text-white">
                        <h3>Desert Triangle</h3>
                    </div>
                    <div class="card-body">
                        <h3 class="card-title pricing-card-title">INR 25,000 /- <small class="text-muted">Regular prices: INR 35,000/-</small></h3>
                        <ul class="list-unstyled">
                            <li>Jodhpur</li>
                            <li>Jaisalmer</li>
                            <li>Bikaner</li>
                            <li>Jaipur</li>
                        </ul>
                        <button type="button" class="btn btn-lg btn-block btn-outline-primary">Add to Cart</button>
                        <button type="button" class="btn btn-lg btn-block btn-outline-primary">Explore More</button>
                    </div>
                </div>
            </div>
        </div>

        <h3 class="text-center">2. Half-yearly Plans:</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-dark text-white">
                        <h3>Central India: National Parks</h3>
                    </div>
                    <div class="card-body">
                        <h3 class="card-title pricing-card-title">Special pricing plans: INR 17,000/- <small class="text-muted">Regular pricing plans: INR 22,000/-</small></h3>
                        <ul class="list-unstyled">
                            <li>Kanha National Park</li>
                            <li>Bandhavgarh National Park</li>
                            <li>Kanger Ghati National Park</li>
                        </ul>
                        <button type="button" class="btn btn-lg btn-block btn-outline-primary">Add to Cart</button>
                        <button type="button" class="btn btn-lg btn-block btn-outline-secondary">Learn More</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-dark text-white">
                        <h3>Central India: Heritage</h3>
                    </div>
                    <div class="card-body">
                        <h3 class="card-title pricing-card-title">Special pricing plans: INR 17,000/- <small class="text-muted">Regular pricing plans: INR 22,000/-</small></h3>
                        <ul class="list-unstyled">
                            <li>Gwalior</li>
                            <li>Indore</li>
                            <li>Delhi</li>
                            <li>Khajuraho Caves</li>
                        </ul>
                        <button type="button" class="btn btn-lg btn-block btn-outline-primary">Add to Cart</button>
                        <button type="button" class="btn btn-lg btn-block btn-outline-secondary">Learn More</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="checkout" id="checkout">
        <div class="header">
            <h2 class="display-4 py-5 text-white">
                <u>Checkout Page</u>
            </h2>
        </div>
        <div class="row px-3 py-3 mx-4 text-black">
            <div class="col-md-4 order-md-2 mb-4">
                <h4 class="d-flex align-item-center mb-3 justify-content-between">
                    <span class="text-muted">Your Cart</span>
                    <span class="badge badge-primary badge-pill">2</span>
                </h4>
                <ul class="list-group mb-3">
                    <li class="list-group-item d-flex justify-content-between bg-light text-dark">North-East Trips</li>
                    <li class="list-group-item d-flex justify-content-between bg-dark text-light">Central India: National Parks</li>
                    <li class="list-group-item d-flex justify-content-between bg-light text-dark">
                        <div class="text-sucess">
                            <h5>Promo code Alert<br><small>Have any discount coupons and promo codes? Apply below!</small></h5>
                        </div>
                        <div class="text-sucess">-INR</div>
                    </li>
                </ul>
                <div>
                    <li class="list-group-item d-flex justify-content-between bg-dark text-light">
                        Total Amount - <span><strong>INR. 1,00,000 /-</strong></span>
                    </li>
                </div>
                <form class="card p-2">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Promo Code">
                        <div class="input-group-append">
                            <button type="submit" class="btn btn-secondary">Redeem!</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-md-8 order-md-1">
                <h4 class="mb-3 text-center text-white my-3 mx-3 py-3">Billing Address</h4>
                <input required class="form-control" id="BillingAddress" type="text">
                <br>
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <input required class="form-control align-left" type="text" id="FirstName">
                            <label for="FirstName" class="mx-1 my-1 py-2 text-white">First Name:</label>
                        </div>
                        <div class="col-md-6 mb-3">
                            <input required class="form-control" type="text" id="LastName">
                            <label for="LastName" class="my-1 mx-1 py-1 text-white">Last Name:</label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <input class="form-control" required type="email" id="email">
                            <label class="my-1 mx-1 py-1 text-white">Email ID:</label>
                        </div>
                        <div class="row text-white">
                            <div class="col-md-4 text-white align-contents-left">
                                <label for="country">Select Country</label>
                                <select class="custom-select d-block w-100 text-dark" id="country">
                                    <option value="">Choose..</option>
                                    <option>India</option>
                                    <option>Sri Lanka</option>
                                    <option>Singapore</option>
                                    <option>Indonesia</option>
                                    <option>Malaysia</option>
                                    <option>Bhutan</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3 text-white align-center">
                                <label for="state">Select State</label>
                                <select class="custom-select d-block w-100 text-dark" id="state">
                                    <option value="">Choose</option>
                                    <option>Maharashtra</option>
                                    <option>Gujarat</option>
                                    <option>Karnataka</option>
                                    <option>Rajasthan</option>
                                    <option>Gujarat</option>
                                    <option>Kerala</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-1">
                                <label for="pincode">Pin Code</label>
                                <input type="text">
                                <br>
                            </div>
                            <div class="custom-control custom-checkbox" type="text">
                                <input type="checkbox" class="custom-control-input" id="same-as-address">
                                <label class="custom-control-label" for="same-as-address">
                                    Shipping address same as Billing address
                                </label>
                            </div>
                            <div class="custom-control custom-checkbox" type="text">
                                <input type="checkbox" class="custom-control-input">
                                <label class="custom-control-label" type="checkbox" id="save-info">
                                    Save my Information for future reference
                                </label>
                            </div>
                        </div>
                        <br>
                        <div class="" id="payment-methods">
                            <h3 class="my-3 mx-3 py-3 text-white mb-4">Payment Options</h3>
                            <div class="d-block my-3 text-white">
                                <div class="custom-class custom-radio">
                                    <input id="credit-card" name="payment-method" type="radio" class="custom-control-input">
                                    <label for="credit-card" class="custom-control-label">Credit card</label>
                                </div>
                                <div class="custom-radio custom-class">
                                    <input id="debit-card" name="payment-method" type="radio" class="custom-control-input">
                                    <label for="debit-card" class="custom-control-label">Debit Card</label>
                                </div>
                                <div class="custom-radio custom-control">
                                    <input id="upi-pay" name="payment-method" type="radio" class="custom-control-input">
                                    <label for="upi-pay" class="custom-control-label">UPI Payment Methods: <a href="#" class="">Click Here</a> for QR codes <br></label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row text-white">
                        <div class="col-md-6 mb-3">
                            <input type="text" class="form-control" id="card-holder">
                            <label class="" for="cardholder">Name of the Card</label>
                        </div>
                        <div class="col-md-6 mb-3">
                            <input type="number" class="form-control" id="card-number">
                            <label for="card-number">Card Number</label>
                        </div>
                    </div>
                    <div class="row text-white py-3">
                        <div class="col-md-3">
                            <input type="text" id="expiry-date" placeholder="dd-mm-yyyy" class="form-control">
                            <label for="expiry-date" placeholder="dd-mm-yyyy">Expiry date of card:</label>
                        </div>
                        <div class="col-md-3">
                            <input type="text" id="security-code" class="form-control">
                            <label for="security-code">Security Code:</label>
                        </div>
                    </div>
                    <div type="submit" class="btn btn-lg btn-block btn-outline-primary"><b>Submit</b></div>
                    <button class="btn-lg btn btn-outline-secondary">
                        <a href="#">
                            Home
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                class="bi bi-house-door-fill" viewBox="0 0 16 16">
                                <path
                                    d="M6.5 14.5v-3.505c0-.245.25-.495.5-.495h2c.25 0 .5.25.5.5v3.5a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5z" />
                            </svg>
                        </a>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
</div>

<footer class="py-3 bg-dark">
    <div class="container">
        <p class="m-0 text-center text-white">© 2023 Wonderland Travel Club. All rights reserved.</p>
    </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-+D3uK4x6xe4Z/hn7GHv6fQ4ZIH3Bfcpe0HfGfg8Mz5AgADvE6Frfq04bF3DqDG9zp" crossorigin="anonymous"></script>
</body>

</html>
