(function() {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/client/head-manager.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/head-manager.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports) {

"use strict";


exports.__esModule = true;
exports.default = initHeadManager;
exports.DOMAttributeNames = void 0;
const DOMAttributeNames = {
  acceptCharset: 'accept-charset',
  className: 'class',
  htmlFor: 'for',
  httpEquiv: 'http-equiv',
  noModule: 'noModule'
};
exports.DOMAttributeNames = DOMAttributeNames;

function reactElementToDOM({
  type,
  props
}) {
  const el = document.createElement(type);

  for (const p in props) {
    if (!props.hasOwnProperty(p)) continue;
    if (p === 'children' || p === 'dangerouslySetInnerHTML') continue; // we don't render undefined props to the DOM

    if (props[p] === undefined) continue;
    const attr = DOMAttributeNames[p] || p.toLowerCase();

    if (type === 'script' && (attr === 'async' || attr === 'defer' || attr === 'noModule')) {
      ;
      el[attr] = !!props[p];
    } else {
      el.setAttribute(attr, props[p]);
    }
  }

  const {
    children,
    dangerouslySetInnerHTML
  } = props;

  if (dangerouslySetInnerHTML) {
    el.innerHTML = dangerouslySetInnerHTML.__html || '';
  } else if (children) {
    el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';
  }

  return el;
}

function updateElements(type, components) {
  const headEl = document.getElementsByTagName('head')[0];
  const headCountEl = headEl.querySelector('meta[name=next-head-count]');

  if (true) {
    if (!headCountEl) {
      console.error('Warning: next-head-count is missing. https://nextjs.org/docs/messages/next-head-count-missing');
      return;
    }
  }

  const headCount = Number(headCountEl.content);
  const oldTags = [];

  for (let i = 0, j = headCountEl.previousElementSibling; i < headCount; i++, j = j.previousElementSibling) {
    if (j.tagName.toLowerCase() === type) {
      oldTags.push(j);
    }
  }

  const newTags = components.map(reactElementToDOM).filter(newTag => {
    for (let k = 0, len = oldTags.length; k < len; k++) {
      const oldTag = oldTags[k];

      if (oldTag.isEqualNode(newTag)) {
        oldTags.splice(k, 1);
        return false;
      }
    }

    return true;
  });
  oldTags.forEach(t => t.parentNode.removeChild(t));
  newTags.forEach(t => headEl.insertBefore(t, headCountEl));
  headCountEl.content = (headCount - oldTags.length + newTags.length).toString();
}

function initHeadManager() {
  let updatePromise = null;
  return {
    mountedInstances: new Set(),
    updateHead: head => {
      const promise = updatePromise = Promise.resolve().then(() => {
        if (promise !== updatePromise) return;
        updatePromise = null;
        const tags = {};
        head.forEach(h => {
          if ( // If the font tag is loaded only on client navigation
          // it won't be inlined. In this case revert to the original behavior
          h.type === 'link' && h.props['data-optimized-fonts'] && !document.querySelector(`style[data-href="${h.props['data-href']}"]`)) {
            h.props.href = h.props['data-href'];
            h.props['data-href'] = undefined;
          }

          const components = tags[h.type] || [];
          components.push(h);
          tags[h.type] = components;
        });
        const titleComponent = tags.title ? tags.title[0] : null;
        let title = '';

        if (titleComponent) {
          const {
            children
          } = titleComponent.props;
          title = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';
        }

        if (title !== document.title) document.title = title;
        ['meta', 'base', 'link', 'style', 'script'].forEach(type => {
          updateElements(type, tags[type] || []);
        });
      });
    }
  };
}

/***/ }),

/***/ "./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports) {

"use strict";


exports.__esModule = true;
exports.cancelIdleCallback = exports.requestIdleCallback = void 0;

const requestIdleCallback = typeof self !== 'undefined' && self.requestIdleCallback || function (cb) {
  let start = Date.now();
  return setTimeout(function () {
    cb({
      didTimeout: false,
      timeRemaining: function () {
        return Math.max(0, 50 - (Date.now() - start));
      }
    });
  }, 1);
};

exports.requestIdleCallback = requestIdleCallback;

const cancelIdleCallback = typeof self !== 'undefined' && self.cancelIdleCallback || function (id) {
  return clearTimeout(id);
};

exports.cancelIdleCallback = cancelIdleCallback;

/***/ }),

/***/ "./node_modules/next/dist/client/script.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/client/script.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "./node_modules/next/node_modules/@babel/runtime/helpers/interopRequireDefault.js");

exports.__esModule = true;
exports.initScriptLoader = initScriptLoader;
exports.default = void 0;

var _extends2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/extends */ "./node_modules/next/node_modules/@babel/runtime/helpers/extends.js"));

var _objectWithoutPropertiesLoose2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ "./node_modules/next/node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js"));

var _react = __webpack_require__(/*! react */ "react");

var _headManagerContext = __webpack_require__(/*! ../next-server/lib/head-manager-context */ "../next-server/lib/head-manager-context");

var _headManager = __webpack_require__(/*! ./head-manager */ "./node_modules/next/dist/client/head-manager.js");

var _requestIdleCallback = __webpack_require__(/*! ./request-idle-callback */ "./node_modules/next/dist/client/request-idle-callback.js");

const ScriptCache = new Map();
const LoadCache = new Set();
const ignoreProps = ['onLoad', 'dangerouslySetInnerHTML', 'children', 'onError', 'strategy'];

const loadScript = props => {
  const {
    src,
    id,
    onLoad = () => {},
    dangerouslySetInnerHTML,
    children = '',
    onError
  } = props;
  const cacheKey = id || src;

  if (ScriptCache.has(src)) {
    if (!LoadCache.has(cacheKey)) {
      LoadCache.add(cacheKey); // Execute onLoad since the script loading has begun

      ScriptCache.get(src).then(onLoad, onError);
    }

    return;
  }

  const el = document.createElement('script');
  const loadPromise = new Promise((resolve, reject) => {
    el.addEventListener('load', function () {
      resolve();

      if (onLoad) {
        onLoad.call(this);
      }
    });
    el.addEventListener('error', function () {
      reject();

      if (onError) {
        onError();
      }
    });
  });

  if (src) {
    ScriptCache.set(src, loadPromise);
    LoadCache.add(cacheKey);
  }

  if (dangerouslySetInnerHTML) {
    el.innerHTML = dangerouslySetInnerHTML.__html || '';
  } else if (children) {
    el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';
  } else if (src) {
    el.src = src;
  }

  for (const [k, value] of Object.entries(props)) {
    if (value === undefined || ignoreProps.includes(k)) {
      continue;
    }

    const attr = _headManager.DOMAttributeNames[k] || k.toLowerCase();
    el.setAttribute(attr, value);
  }

  document.body.appendChild(el);
};

function handleClientScriptLoad(props) {
  const {
    strategy = 'afterInteractive'
  } = props;

  if (strategy === 'afterInteractive') {
    loadScript(props);
  } else if (strategy === 'lazyOnload') {
    window.addEventListener('load', () => {
      (0, _requestIdleCallback.requestIdleCallback)(() => loadScript(props));
    });
  }
}

function loadLazyScript(props) {
  if (document.readyState === 'complete') {
    (0, _requestIdleCallback.requestIdleCallback)(() => loadScript(props));
  } else {
    window.addEventListener('load', () => {
      (0, _requestIdleCallback.requestIdleCallback)(() => loadScript(props));
    });
  }
}

function initScriptLoader(scriptLoaderItems) {
  scriptLoaderItems.forEach(handleClientScriptLoad);
}

function Script(props) {
  const {
    src = '',
    onLoad = () => {},
    strategy = 'afterInteractive',
    onError
  } = props,
        restProps = (0, _objectWithoutPropertiesLoose2.default)(props, ["src", "onLoad", "dangerouslySetInnerHTML", "strategy", "onError"]); // Context is available only during SSR

  const {
    updateScripts,
    scripts
  } = (0, _react.useContext)(_headManagerContext.HeadManagerContext);
  (0, _react.useEffect)(() => {
    if (strategy === 'afterInteractive') {
      loadScript(props);
    } else if (strategy === 'lazyOnload') {
      loadLazyScript(props);
    }
  }, [props, strategy]);

  if (strategy === 'beforeInteractive') {
    if (updateScripts) {
      scripts.beforeInteractive = (scripts.beforeInteractive || []).concat([(0, _extends2.default)({
        src,
        onLoad,
        onError
      }, restProps)]);
      updateScripts(scripts);
    }
  }

  return null;
}

var _default = Script;
exports.default = _default;

/***/ }),

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

exports.__esModule = true;
exports.Html = Html;
exports.Main = Main;
exports.NextScript = exports.Head = exports.default = void 0;

var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "prop-types"));

var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));

var _server = _interopRequireDefault(__webpack_require__(/*! styled-jsx/server */ "styled-jsx/server"));

var _constants = __webpack_require__(/*! ../next-server/lib/constants */ "../next-server/lib/constants");

var _documentContext = __webpack_require__(/*! ../next-server/lib/document-context */ "../next-server/lib/document-context");

var _utils = __webpack_require__(/*! ../next-server/lib/utils */ "../next-server/lib/utils");

exports.DocumentContext = _utils.DocumentContext;
exports.DocumentInitialProps = _utils.DocumentInitialProps;
exports.DocumentProps = _utils.DocumentProps;

var _getPageFiles = __webpack_require__(/*! ../next-server/server/get-page-files */ "../next-server/server/get-page-files");

var _utils2 = __webpack_require__(/*! ../next-server/server/utils */ "../next-server/server/utils");

var _htmlescape = __webpack_require__(/*! ../server/htmlescape */ "./node_modules/next/dist/server/htmlescape.js");

var _script = _interopRequireDefault(__webpack_require__(/*! ../client/script */ "./node_modules/next/dist/client/script.js"));

function _getRequireWildcardCache() {
  if (typeof WeakMap !== "function") return null;
  var cache = new WeakMap();

  _getRequireWildcardCache = function () {
    return cache;
  };

  return cache;
}

function _interopRequireWildcard(obj) {
  if (obj && obj.__esModule) {
    return obj;
  }

  if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
    return {
      default: obj
    };
  }

  var cache = _getRequireWildcardCache();

  if (cache && cache.has(obj)) {
    return cache.get(obj);
  }

  var newObj = {};
  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;

  for (var key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;

      if (desc && (desc.get || desc.set)) {
        Object.defineProperty(newObj, key, desc);
      } else {
        newObj[key] = obj[key];
      }
    }
  }

  newObj.default = obj;

  if (cache) {
    cache.set(obj, newObj);
  }

  return newObj;
}

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {
    default: obj
  };
}

function getDocumentFiles(buildManifest, pathname, inAmpMode) {
  const sharedFiles = (0, _getPageFiles.getPageFiles)(buildManifest, '/_app');
  const pageFiles = inAmpMode ? [] : (0, _getPageFiles.getPageFiles)(buildManifest, pathname);
  return {
    sharedFiles,
    pageFiles,
    allFiles: [...new Set([...sharedFiles, ...pageFiles])]
  };
}

function getPolyfillScripts(context, props) {
  // polyfills.js has to be rendered as nomodule without async
  // It also has to be the first script to load
  const {
    assetPrefix,
    buildManifest,
    devOnlyCacheBusterQueryString,
    disableOptimizedLoading
  } = context;
  return buildManifest.polyfillFiles.filter(polyfill => polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')).map(polyfill => /*#__PURE__*/_react.default.createElement("script", {
    key: polyfill,
    defer: !disableOptimizedLoading,
    nonce: props.nonce,
    crossOrigin: props.crossOrigin || undefined,
    noModule: true,
    src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`
  }));
}

function getPreNextScripts(context, props) {
  const {
    scriptLoader,
    disableOptimizedLoading
  } = context;
  return (scriptLoader.beforeInteractive || []).map(file => {
    const {
      strategy
    } = file,
          scriptProps = _objectWithoutProperties(file, ["strategy"]);

    return /*#__PURE__*/_react.default.createElement("script", Object.assign({}, scriptProps, {
      defer: !disableOptimizedLoading,
      nonce: props.nonce,
      crossOrigin: props.crossOrigin || undefined
    }));
  });
}

function getDynamicChunks(context, props, files) {
  const {
    dynamicImports,
    assetPrefix,
    isDevelopment,
    devOnlyCacheBusterQueryString,
    disableOptimizedLoading
  } = context;
  return dynamicImports.map(file => {
    if (!file.endsWith('.js') || files.allFiles.includes(file)) return null;
    return /*#__PURE__*/_react.default.createElement("script", {
      async: !isDevelopment && disableOptimizedLoading,
      defer: !disableOptimizedLoading,
      key: file,
      src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,
      nonce: props.nonce,
      crossOrigin: props.crossOrigin || undefined
    });
  });
}

function getScripts(context, props, files) {
  var _buildManifest$lowPri;

  const {
    assetPrefix,
    buildManifest,
    isDevelopment,
    devOnlyCacheBusterQueryString,
    disableOptimizedLoading
  } = context;
  const normalScripts = files.allFiles.filter(file => file.endsWith('.js'));
  const lowPriorityScripts = (_buildManifest$lowPri = buildManifest.lowPriorityFiles) == null ? void 0 : _buildManifest$lowPri.filter(file => file.endsWith('.js'));
  return [...normalScripts, ...lowPriorityScripts].map(file => {
    return /*#__PURE__*/_react.default.createElement("script", {
      key: file,
      src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,
      nonce: props.nonce,
      async: !isDevelopment && disableOptimizedLoading,
      defer: !disableOptimizedLoading,
      crossOrigin: props.crossOrigin || undefined
    });
  });
}
/**
* `Document` component handles the initial `document` markup and renders only on the server side.
* Commonly used for implementing server side rendering for `css-in-js` libraries.
*/


class Document extends _react.Component {
  /**
  * `getInitialProps` hook returns the context object with the addition of `renderPage`.
  * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers
  */
  static async getInitialProps(ctx) {
    const enhanceApp = App => {
      return props => /*#__PURE__*/_react.default.createElement(App, props);
    };

    const {
      html,
      head
    } = await ctx.renderPage({
      enhanceApp
    });
    const styles = [...(0, _server.default)()];
    return {
      html,
      head,
      styles
    };
  }

  static renderDocument(DocumentComponent, props) {
    return /*#__PURE__*/_react.default.createElement(_documentContext.DocumentContext.Provider, {
      value: props
    }, /*#__PURE__*/_react.default.createElement(DocumentComponent, props));
  }

  render() {
    return /*#__PURE__*/_react.default.createElement(Html, null, /*#__PURE__*/_react.default.createElement(Head, null), /*#__PURE__*/_react.default.createElement("body", null, /*#__PURE__*/_react.default.createElement(Main, null), /*#__PURE__*/_react.default.createElement(NextScript, null)));
  }

}

exports.default = Document;

function Html(props) {
  const {
    inAmpMode,
    docComponentsRendered,
    locale
  } = (0, _react.useContext)(_documentContext.DocumentContext);
  docComponentsRendered.Html = true;
  return /*#__PURE__*/_react.default.createElement("html", Object.assign({}, props, {
    lang: props.lang || locale || undefined,
    amp: inAmpMode ? '' : undefined,
    "data-ampdevmode": inAmpMode && true ? '' : undefined
  }));
}

class Head extends _react.Component {
  constructor(...args) {
    super(...args);
    this.context = void 0;
  }

  getCssLinks(files) {
    const {
      assetPrefix,
      devOnlyCacheBusterQueryString,
      dynamicImports
    } = this.context;
    const cssFiles = files.allFiles.filter(f => f.endsWith('.css'));
    const sharedFiles = new Set(files.sharedFiles); // Unmanaged files are CSS files that will be handled directly by the
    // webpack runtime (`mini-css-extract-plugin`).

    let unmangedFiles = new Set([]);
    let dynamicCssFiles = Array.from(new Set(dynamicImports.filter(file => file.endsWith('.css'))));

    if (dynamicCssFiles.length) {
      const existing = new Set(cssFiles);
      dynamicCssFiles = dynamicCssFiles.filter(f => !(existing.has(f) || sharedFiles.has(f)));
      unmangedFiles = new Set(dynamicCssFiles);
      cssFiles.push(...dynamicCssFiles);
    }

    let cssLinkElements = [];
    cssFiles.forEach(file => {
      const isSharedFile = sharedFiles.has(file);

      if (true) {
        cssLinkElements.push( /*#__PURE__*/_react.default.createElement("link", {
          key: `${file}-preload`,
          nonce: this.props.nonce,
          rel: "preload",
          href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,
          as: "style",
          crossOrigin: this.props.crossOrigin || undefined
        }));
      }

      const isUnmanagedFile = unmangedFiles.has(file);
      cssLinkElements.push( /*#__PURE__*/_react.default.createElement("link", {
        key: file,
        nonce: this.props.nonce,
        rel: "stylesheet",
        href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,
        crossOrigin: this.props.crossOrigin || undefined,
        "data-n-g": isUnmanagedFile ? undefined : isSharedFile ? '' : undefined,
        "data-n-p": isUnmanagedFile ? undefined : isSharedFile ? undefined : ''
      }));
    });

    if (false) {}

    return cssLinkElements.length === 0 ? null : cssLinkElements;
  }

  getPreloadDynamicChunks() {
    const {
      dynamicImports,
      assetPrefix,
      devOnlyCacheBusterQueryString
    } = this.context;
    return dynamicImports.map(file => {
      if (!file.endsWith('.js')) {
        return null;
      }

      return /*#__PURE__*/_react.default.createElement("link", {
        rel: "preload",
        key: file,
        href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,
        as: "script",
        nonce: this.props.nonce,
        crossOrigin: this.props.crossOrigin || undefined
      });
    }) // Filter out nulled scripts
    .filter(Boolean);
  }

  getPreloadMainLinks(files) {
    const {
      assetPrefix,
      devOnlyCacheBusterQueryString,
      scriptLoader
    } = this.context;
    const preloadFiles = files.allFiles.filter(file => {
      return file.endsWith('.js');
    });
    return [...(scriptLoader.beforeInteractive || []).map(file => /*#__PURE__*/_react.default.createElement("link", {
      key: file.src,
      nonce: this.props.nonce,
      rel: "preload",
      href: file.src,
      as: "script",
      crossOrigin: this.props.crossOrigin || undefined
    })), ...preloadFiles.map(file => /*#__PURE__*/_react.default.createElement("link", {
      key: file,
      nonce: this.props.nonce,
      rel: "preload",
      href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,
      as: "script",
      crossOrigin: this.props.crossOrigin || undefined
    }))];
  }

  getDynamicChunks(files) {
    return getDynamicChunks(this.context, this.props, files);
  }

  getPreNextScripts() {
    return getPreNextScripts(this.context, this.props);
  }

  getScripts(files) {
    return getScripts(this.context, this.props, files);
  }

  getPolyfillScripts() {
    return getPolyfillScripts(this.context, this.props);
  }

  handleDocumentScriptLoaderItems(children) {
    const {
      scriptLoader
    } = this.context;
    const scriptLoaderItems = [];
    const filteredChildren = [];

    _react.default.Children.forEach(children, child => {
      if (child.type === _script.default) {
        if (child.props.strategy === 'beforeInteractive') {
          scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([_objectSpread({}, child.props)]);
          return;
        } else if (['lazyOnload', 'afterInteractive'].includes(child.props.strategy)) {
          scriptLoaderItems.push(child.props);
          return;
        }
      }

      filteredChildren.push(child);
    });

    this.context.__NEXT_DATA__.scriptLoader = scriptLoaderItems;
    return filteredChildren;
  }

  makeStylesheetInert(node) {
    return _react.default.Children.map(node, c => {
      if (c.type === 'link' && c.props['href'] && _constants.OPTIMIZED_FONT_PROVIDERS.some(({
        url
      }) => c.props['href'].startsWith(url))) {
        const newProps = _objectSpread({}, c.props || {});

        newProps['data-href'] = newProps['href'];
        newProps['href'] = undefined;
        return /*#__PURE__*/_react.default.cloneElement(c, newProps);
      } else if (c.props && c.props['children']) {
        c.props['children'] = this.makeStylesheetInert(c.props['children']);
      }

      return c;
    });
  }

  render() {
    var _this$props$nonce, _this$props$nonce2;

    const {
      styles,
      ampPath,
      inAmpMode,
      hybridAmp,
      canonicalBase,
      __NEXT_DATA__,
      dangerousAsPath,
      headTags,
      unstable_runtimeJS,
      unstable_JsPreload,
      disableOptimizedLoading
    } = this.context;
    const disableRuntimeJS = unstable_runtimeJS === false;
    const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;
    this.context.docComponentsRendered.Head = true;
    let {
      head
    } = this.context;
    let cssPreloads = [];
    let otherHeadElements = [];

    if (head) {
      head.forEach(c => {
        if (c && c.type === 'link' && c.props['rel'] === 'preload' && c.props['as'] === 'style') {
          cssPreloads.push(c);
        } else {
          c && otherHeadElements.push(c);
        }
      });
      head = cssPreloads.concat(otherHeadElements);
    }

    let children = _react.default.Children.toArray(this.props.children).filter(Boolean); // show a warning if Head contains <title> (only in development)


    if (true) {
      children = _react.default.Children.map(children, child => {
        var _child$props;

        const isReactHelmet = child == null ? void 0 : (_child$props = child.props) == null ? void 0 : _child$props['data-react-helmet'];

        if (!isReactHelmet) {
          var _child$props2;

          if ((child == null ? void 0 : child.type) === 'title') {
            console.warn("Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title");
          } else if ((child == null ? void 0 : child.type) === 'meta' && (child == null ? void 0 : (_child$props2 = child.props) == null ? void 0 : _child$props2.name) === 'viewport') {
            console.warn("Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta");
          }
        }

        return child;
      });
      if (this.props.crossOrigin) console.warn('Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');
    }

    if (false) {}

    children = this.handleDocumentScriptLoaderItems(children);
    let hasAmphtmlRel = false;
    let hasCanonicalRel = false; // show warning and remove conflicting amp head tags

    head = _react.default.Children.map(head || [], child => {
      if (!child) return child;
      const {
        type,
        props
      } = child;

      if (inAmpMode) {
        let badProp = '';

        if (type === 'meta' && props.name === 'viewport') {
          badProp = 'name="viewport"';
        } else if (type === 'link' && props.rel === 'canonical') {
          hasCanonicalRel = true;
        } else if (type === 'script') {
          // only block if
          // 1. it has a src and isn't pointing to ampproject's CDN
          // 2. it is using dangerouslySetInnerHTML without a type or
          // a type of text/javascript
          if (props.src && props.src.indexOf('ampproject') < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === 'text/javascript')) {
            badProp = '<script';
            Object.keys(props).forEach(prop => {
              badProp += ` ${prop}="${props[prop]}"`;
            });
            badProp += '/>';
          }
        }

        if (badProp) {
          console.warn(`Found conflicting amp tag "${child.type}" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);
          return null;
        }
      } else {
        // non-amp mode
        if (type === 'link' && props.rel === 'amphtml') {
          hasAmphtmlRel = true;
        }
      }

      return child;
    }); // try to parse styles from fragment for backwards compat

    const curStyles = Array.isArray(styles) ? styles : [];

    if (inAmpMode && styles && // @ts-ignore Property 'props' does not exist on type ReactElement
    styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement
    Array.isArray(styles.props.children)) {
      const hasStyles = el => {
        var _el$props, _el$props$dangerously;

        return el == null ? void 0 : (_el$props = el.props) == null ? void 0 : (_el$props$dangerously = _el$props.dangerouslySetInnerHTML) == null ? void 0 : _el$props$dangerously.__html;
      }; // @ts-ignore Property 'props' does not exist on type ReactElement


      styles.props.children.forEach(child => {
        if (Array.isArray(child)) {
          child.forEach(el => hasStyles(el) && curStyles.push(el));
        } else if (hasStyles(child)) {
          curStyles.push(child);
        }
      });
    }

    const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page, inAmpMode);
    return /*#__PURE__*/_react.default.createElement("head", this.props, this.context.isDevelopment && /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/_react.default.createElement("style", {
      "data-next-hide-fouc": true,
      "data-ampdevmode": inAmpMode ? 'true' : undefined,
      dangerouslySetInnerHTML: {
        __html: `body{display:none}`
      }
    }), /*#__PURE__*/_react.default.createElement("noscript", {
      "data-next-hide-fouc": true,
      "data-ampdevmode": inAmpMode ? 'true' : undefined
    }, /*#__PURE__*/_react.default.createElement("style", {
      dangerouslySetInnerHTML: {
        __html: `body{display:block}`
      }
    }))), children,  false && /*#__PURE__*/0, head, /*#__PURE__*/_react.default.createElement("meta", {
      name: "next-head-count",
      content: _react.default.Children.count(head || []).toString()
    }), inAmpMode && /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/_react.default.createElement("meta", {
      name: "viewport",
      content: "width=device-width,minimum-scale=1,initial-scale=1"
    }), !hasCanonicalRel && /*#__PURE__*/_react.default.createElement("link", {
      rel: "canonical",
      href: canonicalBase + (0, _utils2.cleanAmpPath)(dangerousAsPath)
    }), /*#__PURE__*/_react.default.createElement("link", {
      rel: "preload",
      as: "script",
      href: "https://cdn.ampproject.org/v0.js"
    }), styles && /*#__PURE__*/_react.default.createElement("style", {
      "amp-custom": "",
      dangerouslySetInnerHTML: {
        __html: curStyles.map(style => style.props.dangerouslySetInnerHTML.__html).join('').replace(/\/\*# sourceMappingURL=.*\*\//g, '').replace(/\/\*@ sourceURL=.*?\*\//g, '')
      }
    }), /*#__PURE__*/_react.default.createElement("style", {
      "amp-boilerplate": "",
      dangerouslySetInnerHTML: {
        __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`
      }
    }), /*#__PURE__*/_react.default.createElement("noscript", null, /*#__PURE__*/_react.default.createElement("style", {
      "amp-boilerplate": "",
      dangerouslySetInnerHTML: {
        __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`
      }
    })), /*#__PURE__*/_react.default.createElement("script", {
      async: true,
      src: "https://cdn.ampproject.org/v0.js"
    })), !inAmpMode && /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/_react.default.createElement("link", {
      rel: "amphtml",
      href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)
    }),  true && this.getCssLinks(files),  true && /*#__PURE__*/_react.default.createElement("noscript", {
      "data-n-css": (_this$props$nonce = this.props.nonce) != null ? _this$props$nonce : ''
    }),  false && /*#__PURE__*/0, !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files),  false && 0,  false && /*#__PURE__*/0, this.context.isDevelopment &&
    /*#__PURE__*/
    // this element is used to mount development styles so the
    // ordering matches production
    // (by default, style-loader injects at the bottom of <head />)
    _react.default.createElement("noscript", {
      id: "__next_css__DO_NOT_USE__"
    }), styles || null), /*#__PURE__*/_react.default.createElement(_react.default.Fragment, {}, ...(headTags || [])));
  }

}

exports.Head = Head;
Head.contextType = _documentContext.DocumentContext;
Head.propTypes = {
  nonce: _propTypes.default.string,
  crossOrigin: _propTypes.default.string
};

function Main() {
  const {
    inAmpMode,
    html,
    docComponentsRendered
  } = (0, _react.useContext)(_documentContext.DocumentContext);
  docComponentsRendered.Main = true;
  if (inAmpMode) return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, _constants.AMP_RENDER_TARGET);
  return /*#__PURE__*/_react.default.createElement("div", {
    id: "__next",
    dangerouslySetInnerHTML: {
      __html: html
    }
  });
}

class NextScript extends _react.Component {
  constructor(...args) {
    super(...args);
    this.context = void 0;
  }

  getDynamicChunks(files) {
    return getDynamicChunks(this.context, this.props, files);
  }

  getPreNextScripts() {
    return getPreNextScripts(this.context, this.props);
  }

  getScripts(files) {
    return getScripts(this.context, this.props, files);
  }

  getPolyfillScripts() {
    return getPolyfillScripts(this.context, this.props);
  }

  static getInlineScriptSource(documentProps) {
    const {
      __NEXT_DATA__
    } = documentProps;

    try {
      const data = JSON.stringify(__NEXT_DATA__);
      return (0, _htmlescape.htmlEscapeJsonString)(data);
    } catch (err) {
      if (err.message.indexOf('circular structure')) {
        throw new Error(`Circular structure in "getInitialProps" result of page "${__NEXT_DATA__.page}". https://nextjs.org/docs/messages/circular-structure`);
      }

      throw err;
    }
  }

  render() {
    const {
      assetPrefix,
      inAmpMode,
      buildManifest,
      unstable_runtimeJS,
      docComponentsRendered,
      devOnlyCacheBusterQueryString,
      disableOptimizedLoading
    } = this.context;
    const disableRuntimeJS = unstable_runtimeJS === false;
    docComponentsRendered.NextScript = true;

    if (inAmpMode) {
      if (false) {}

      const ampDevFiles = [...buildManifest.devFiles, ...buildManifest.polyfillFiles, ...buildManifest.ampDevFiles];
      return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/_react.default.createElement("script", {
        id: "__NEXT_DATA__",
        type: "application/json",
        nonce: this.props.nonce,
        crossOrigin: this.props.crossOrigin || undefined,
        dangerouslySetInnerHTML: {
          __html: NextScript.getInlineScriptSource(this.context)
        },
        "data-ampdevmode": true
      }), ampDevFiles.map(file => /*#__PURE__*/_react.default.createElement("script", {
        key: file,
        src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,
        nonce: this.props.nonce,
        crossOrigin: this.props.crossOrigin || undefined,
        "data-ampdevmode": true
      })));
    }

    if (true) {
      if (this.props.crossOrigin) console.warn('Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated');
    }

    const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page, inAmpMode);
    return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map(file => /*#__PURE__*/_react.default.createElement("script", {
      key: file,
      src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,
      nonce: this.props.nonce,
      crossOrigin: this.props.crossOrigin || undefined
    })) : null, disableRuntimeJS ? null : /*#__PURE__*/_react.default.createElement("script", {
      id: "__NEXT_DATA__",
      type: "application/json",
      nonce: this.props.nonce,
      crossOrigin: this.props.crossOrigin || undefined,
      dangerouslySetInnerHTML: {
        __html: NextScript.getInlineScriptSource(this.context)
      }
    }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));
  }

}

exports.NextScript = NextScript;
NextScript.contextType = _documentContext.DocumentContext;
NextScript.propTypes = {
  nonce: _propTypes.default.string,
  crossOrigin: _propTypes.default.string
};
NextScript.safariNomoduleFix = '!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()},!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();';

function getAmpPath(ampPath, asPath) {
  return ampPath || `${asPath}${asPath.includes('?') ? '&' : '?'}amp=1`;
}

/***/ }),

/***/ "./node_modules/next/dist/server/htmlescape.js":
/*!*****************************************************!*\
  !*** ./node_modules/next/dist/server/htmlescape.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports) {

"use strict";
exports.__esModule=true;exports.htmlEscapeJsonString=htmlEscapeJsonString;// This utility is based on https://github.com/zertosh/htmlescape
// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE
const ESCAPE_LOOKUP={'&':'\\u0026','>':'\\u003e','<':'\\u003c','\u2028':'\\u2028','\u2029':'\\u2029'};const ESCAPE_REGEX=/[&><\u2028\u2029]/g;function htmlEscapeJsonString(str){return str.replace(ESCAPE_REGEX,match=>ESCAPE_LOOKUP[match]);}
//# sourceMappingURL=htmlescape.js.map

/***/ }),

/***/ "./node_modules/next/node_modules/@babel/runtime/helpers/extends.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/node_modules/@babel/runtime/helpers/extends.js ***!
  \**************************************************************************/
/***/ (function(module) {

function _extends() {
  module.exports = _extends = Object.assign || function (target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];

      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }

    return target;
  };

  return _extends.apply(this, arguments);
}

module.exports = _extends;

/***/ }),

/***/ "./node_modules/next/node_modules/@babel/runtime/helpers/interopRequireDefault.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/node_modules/@babel/runtime/helpers/interopRequireDefault.js ***!
  \****************************************************************************************/
/***/ (function(module) {

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {
    "default": obj
  };
}

module.exports = _interopRequireDefault;

/***/ }),

/***/ "./node_modules/next/node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next/node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js ***!
  \***********************************************************************************************/
/***/ (function(module) {

function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null) return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;

  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0) continue;
    target[key] = source[key];
  }

  return target;
}

module.exports = _objectWithoutPropertiesLoose;

/***/ }),

/***/ "../next-server/lib/constants":
/*!*********************************************************!*\
  !*** external "next/dist/next-server/lib/constants.js" ***!
  \*********************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/lib/constants.js");;

/***/ }),

/***/ "../next-server/lib/document-context":
/*!****************************************************************!*\
  !*** external "next/dist/next-server/lib/document-context.js" ***!
  \****************************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/lib/document-context.js");;

/***/ }),

/***/ "../next-server/lib/head-manager-context":
/*!********************************************************************!*\
  !*** external "next/dist/next-server/lib/head-manager-context.js" ***!
  \********************************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/lib/head-manager-context.js");;

/***/ }),

/***/ "../next-server/lib/utils":
/*!*****************************************************!*\
  !*** external "next/dist/next-server/lib/utils.js" ***!
  \*****************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/lib/utils.js");;

/***/ }),

/***/ "../next-server/server/get-page-files":
/*!*****************************************************************!*\
  !*** external "next/dist/next-server/server/get-page-files.js" ***!
  \*****************************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/server/get-page-files.js");;

/***/ }),

/***/ "../next-server/server/utils":
/*!********************************************************!*\
  !*** external "next/dist/next-server/server/utils.js" ***!
  \********************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/server/utils.js");;

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ (function(module) {

"use strict";
module.exports = require("prop-types");;

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ (function(module) {

"use strict";
module.exports = require("react");;

/***/ }),

/***/ "styled-jsx/server":
/*!************************************!*\
  !*** external "styled-jsx/server" ***!
  \************************************/
/***/ (function(module) {

"use strict";
module.exports = require("styled-jsx/server");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
var __webpack_exports__ = (__webpack_exec__("./node_modules/next/dist/pages/_document.js"));
module.exports = __webpack_exports__;

})();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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