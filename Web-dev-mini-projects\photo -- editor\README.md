<h1>Photo Editor</h1>

<p>An Awesome Photo editting written in HTML, CSS, JavaScript.</p>

### Use of the Project:

<p>It edits the images that is it it flips images horizontally and vertically resizes it and can also grey scale the image download option is also available here  . </p>

<h3>Used Technologies</h3>
<ul>
  <li>HTML5</li>
  <li>CSS3</li>
  <li>JavaScript</li>
  <li>php</li>

</ul>

#### Steps to Use:

---

- Download or clone the repository

```
git clone https://github.com/soma2000-lang/Web-dev-mini-projects.git
```

- Go to the directory
- Run the index.html file by copying the file path and pasting it in your browser
- Upload The Image
- Download the Modifed Image by clicking On Save Button.
- And Here you have Awesome Editted Image

<h3> ScreenShots </h3> 
<img width="960" alt="Photo-Editor" src="https://github.com/soma2000-lang/Photo-Editor/blob/master/WhatsApp%20Image%202021-07-17%20at%2023.15.55%20(1).jpeg">
<img width="960" alt="Photo-Editor"src="https://github.com/soma2000-lang/Photo-Editor/blob/master/image%20(2)%20(1).png">
<img width="960" alt="Photo-Editor" src="https://github.com/soma2000-lang/Photo-Editor/blob/master/image%20(3).png">

<br>

