exports.id = "components_Layout_index_tsx";
exports.ids = ["components_Layout_index_tsx"];
exports.modules = {

/***/ "./components/Layout/index.tsx":
/*!*************************************!*\
  !*** ./components/Layout/index.tsx ***!
  \*************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Layout; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ "next/dynamic");
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _hooks_useScrollLimit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useScrollLimit */ "./hooks/useScrollLimit.ts");
/* harmony import */ var _styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../styles/Browse.module.scss */ "./styles/Browse.module.scss");
/* harmony import */ var _styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4__);

var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\Layout\\index.tsx";




const Footer = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(() => __webpack_require__.e(/*! import() */ "components_Footer_index_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ../Footer */ "./components/Footer/index.tsx")), {
  loadableGenerated: {
    webpack: () => [/*require.resolve*/(/*! ../Footer */ "./components/Footer/index.tsx")],
    modules: ["..\\components\\Layout\\index.tsx -> " + '../Footer']
  }
});
const Navbar = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(() => __webpack_require__.e(/*! import() */ "components_Navbar_index_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ../Navbar */ "./components/Navbar/index.tsx")), {
  loadableGenerated: {
    webpack: () => [/*require.resolve*/(/*! ../Navbar */ "./components/Navbar/index.tsx")],
    modules: ["..\\components\\Layout\\index.tsx -> " + '../Navbar']
  }
});
const SCROLL_LIMIT = 80;
function Layout({
  children
}) {
  const isScrolled = (0,_hooks_useScrollLimit__WEBPACK_IMPORTED_MODULE_3__.default)(SCROLL_LIMIT);
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    className: (_styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4___default().container),
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Navbar, {
      isScrolled: isScrolled
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 20,
      columnNumber: 7
    }, this), children, /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 22,
      columnNumber: 7
    }, this)]
  }, void 0, true, {
    fileName: _jsxFileName,
    lineNumber: 19,
    columnNumber: 5
  }, this);
}

/***/ }),

/***/ "./hooks/useScrollLimit.ts":
/*!*********************************!*\
  !*** ./hooks/useScrollLimit.ts ***!
  \*********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useScrollLimit; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

function useScrollLimit(limit) {
  const {
    0: reached,
    1: setReached
  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    function onScroll() {
      const scrolled = window.scrollY > limit;
      setReached(scrolled);
    }

    window.addEventListener('scroll', onScroll);
    return () => window.removeEventListener('scroll', onScroll);
  }, [limit]);
  return reached;
}

/***/ })

};
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXRmbGl4LWNsb25lLy4vY29tcG9uZW50cy9MYXlvdXQvaW5kZXgudHN4Iiwid2VicGFjazovL25ldGZsaXgtY2xvbmUvLi9ob29rcy91c2VTY3JvbGxMaW1pdC50cyJdLCJuYW1lcyI6WyJGb290ZXIiLCJkeW5hbWljIiwiTmF2YmFyIiwiU0NST0xMX0xJTUlUIiwiTGF5b3V0IiwiY2hpbGRyZW4iLCJpc1Njcm9sbGVkIiwidXNlU2Nyb2xsTGltaXQiLCJzdHlsZXMiLCJsaW1pdCIsInJlYWNoZWQiLCJzZXRSZWFjaGVkIiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJvblNjcm9sbCIsInNjcm9sbGVkIiwid2luZG93Iiwic2Nyb2xsWSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFFQTtBQUNBO0FBRUEsTUFBTUEsTUFBTSxHQUFHQyxtREFBTyxPQUFDLDBLQUFEO0FBQUE7QUFBQSx3Q0FBUSxnREFBUjtBQUFBLHdEQUFRLFdBQVI7QUFBQTtBQUFBLEVBQXRCO0FBQ0EsTUFBTUMsTUFBTSxHQUFHRCxtREFBTyxPQUFDLDBLQUFEO0FBQUE7QUFBQSx3Q0FBUSxnREFBUjtBQUFBLHdEQUFRLFdBQVI7QUFBQTtBQUFBLEVBQXRCO0FBTUEsTUFBTUUsWUFBb0IsR0FBRyxFQUE3QjtBQUVlLFNBQVNDLE1BQVQsQ0FBZ0I7QUFBRUM7QUFBRixDQUFoQixFQUFzQztBQUNuRCxRQUFNQyxVQUFtQixHQUFHQyw4REFBYyxDQUFDSixZQUFELENBQTFDO0FBQ0Esc0JBQ0U7QUFBSyxhQUFTLEVBQUVLLDZFQUFoQjtBQUFBLDRCQUNFLDhEQUFDLE1BQUQ7QUFBUSxnQkFBVSxFQUFFRjtBQUFwQjtBQUFBO0FBQUE7QUFBQTtBQUFBLFlBREYsRUFFR0QsUUFGSCxlQUdFLDhEQUFDLE1BQUQ7QUFBQTtBQUFBO0FBQUE7QUFBQSxZQUhGO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQSxVQURGO0FBT0QsQzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUN4QkQ7QUFFZSxTQUFTRSxjQUFULENBQXdCRSxLQUF4QixFQUFnRDtBQUM3RCxRQUFNO0FBQUEsT0FBQ0MsT0FBRDtBQUFBLE9BQVVDO0FBQVYsTUFBd0JDLCtDQUFRLENBQVUsS0FBVixDQUF0QztBQUVBQyxrREFBUyxDQUFDLE1BQU07QUFDZCxhQUFTQyxRQUFULEdBQTBCO0FBQ3hCLFlBQU1DLFFBQWlCLEdBQUdDLE1BQU0sQ0FBQ0MsT0FBUCxHQUFpQlIsS0FBM0M7QUFDQUUsZ0JBQVUsQ0FBQ0ksUUFBRCxDQUFWO0FBQ0Q7O0FBRURDLFVBQU0sQ0FBQ0UsZ0JBQVAsQ0FBd0IsUUFBeEIsRUFBa0NKLFFBQWxDO0FBQ0EsV0FBTyxNQUFNRSxNQUFNLENBQUNHLG1CQUFQLENBQTJCLFFBQTNCLEVBQXFDTCxRQUFyQyxDQUFiO0FBQ0QsR0FSUSxFQVFOLENBQUNMLEtBQUQsQ0FSTSxDQUFUO0FBVUEsU0FBT0MsT0FBUDtBQUNELEMiLCJmaWxlIjoiY29tcG9uZW50c19MYXlvdXRfaW5kZXhfdHN4LmpzIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XG5cbmltcG9ydCB1c2VTY3JvbGxMaW1pdCBmcm9tICcuLi8uLi9ob29rcy91c2VTY3JvbGxMaW1pdCc7XG5pbXBvcnQgc3R5bGVzIGZyb20gJy4uLy4uL3N0eWxlcy9Ccm93c2UubW9kdWxlLnNjc3MnO1xuXG5jb25zdCBGb290ZXIgPSBkeW5hbWljKGltcG9ydCgnLi4vRm9vdGVyJykpO1xuY29uc3QgTmF2YmFyID0gZHluYW1pYyhpbXBvcnQoJy4uL05hdmJhcicpKTtcblxuaW50ZXJmYWNlIExheW91dCB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmNvbnN0IFNDUk9MTF9MSU1JVDogbnVtYmVyID0gODA7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExheW91dCh7IGNoaWxkcmVuIH06IExheW91dCkge1xuICBjb25zdCBpc1Njcm9sbGVkOiBib29sZWFuID0gdXNlU2Nyb2xsTGltaXQoU0NST0xMX0xJTUlUKTtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmNvbnRhaW5lcn0+XG4gICAgICA8TmF2YmFyIGlzU2Nyb2xsZWQ9e2lzU2Nyb2xsZWR9IC8+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgICA8Rm9vdGVyIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iLCJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VTY3JvbGxMaW1pdChsaW1pdDogbnVtYmVyKTogYm9vbGVhbiB7XG4gIGNvbnN0IFtyZWFjaGVkLCBzZXRSZWFjaGVkXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZ1bmN0aW9uIG9uU2Nyb2xsKCk6IHZvaWQge1xuICAgICAgY29uc3Qgc2Nyb2xsZWQ6IGJvb2xlYW4gPSB3aW5kb3cuc2Nyb2xsWSA+IGxpbWl0O1xuICAgICAgc2V0UmVhY2hlZChzY3JvbGxlZCk7XG4gICAgfVxuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIG9uU2Nyb2xsKTtcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIG9uU2Nyb2xsKTtcbiAgfSwgW2xpbWl0XSk7XG5cbiAgcmV0dXJuIHJlYWNoZWQ7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9