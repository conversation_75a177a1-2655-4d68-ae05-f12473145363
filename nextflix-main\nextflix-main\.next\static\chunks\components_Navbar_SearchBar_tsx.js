(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Navbar_SearchBar_tsx"],{

/***/ "./components/Navbar/SearchBar.tsx":
/*!*****************************************!*\
  !*** ./components/Navbar/SearchBar.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ SearchBar; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/react/jsx-dev-runtime.js");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ "./node_modules/framer-motion/dist/es/index.js");
/* harmony import */ var _hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useExternalClick */ "./hooks/useExternalClick.ts");
/* harmony import */ var _utils_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/icons */ "./utils/icons.ts");
/* harmony import */ var _hooks_useDimensions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useDimensions */ "./hooks/useDimensions.ts");
/* harmony import */ var _styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../styles/Navbar.module.scss */ "./styles/Navbar.module.scss");
/* harmony import */ var _styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_5__);
/* module decorator */ module = __webpack_require__.hmd(module);


var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\Navbar\\SearchBar.tsx",
    _s = $RefreshSig$();

/* eslint-disable @next/next/no-img-element */






function SearchBar() {
  _s();

  var searchRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),
      isSearch = _useState[0],
      setIsSearch = _useState[1];

  var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''),
      searchInput = _useState2[0],
      setSearchInput = _useState2[1];

  var _useDimensions = (0,_hooks_useDimensions__WEBPACK_IMPORTED_MODULE_4__.default)(),
      isMobile = _useDimensions.isMobile;

  var onSearchActive = function onSearchActive() {
    setIsSearch(true);
  };

  (0,_hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_2__.default)(searchRef, function () {
    setIsSearch(false);
  });

  var onSearchQuery = function onSearchQuery(_ref) {
    var target = _ref.target;
    setSearchInput(target.value);
  };

  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    ref: searchRef,
    className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_5___default().searchPanel),
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {
      className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_5___default().searchBar),
      initial: "hidden",
      animate: isSearch ? 'visible' : 'hidden',
      transition: {
        duration: 0.45
      },
      variants: {
        visible: {
          opacity: 1,
          width: isMobile ? 120 : 250
        },
        hidden: {
          opacity: 0,
          width: 0
        }
      },
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_icons__WEBPACK_IMPORTED_MODULE_3__.Search, {
        className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_5___default().icon)
      }, void 0, false, {
        fileName: _jsxFileName,
        lineNumber: 46,
        columnNumber: 9
      }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("input", {
        type: "text",
        className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_5___default().searchBar__input),
        value: searchInput,
        onChange: onSearchQuery,
        placeholder: "Titles, people, genres"
      }, void 0, false, {
        fileName: _jsxFileName,
        lineNumber: 47,
        columnNumber: 9
      }, this)]
    }, void 0, true, {
      fileName: _jsxFileName,
      lineNumber: 31,
      columnNumber: 7
    }, this), !isSearch && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_icons__WEBPACK_IMPORTED_MODULE_3__.Search, {
      className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_5___default().icon),
      onMouseOver: onSearchActive
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 55,
      columnNumber: 21
    }, this)]
  }, void 0, true, {
    fileName: _jsxFileName,
    lineNumber: 30,
    columnNumber: 5
  }, this);
}

_s(SearchBar, "FTfl8ZXX8wRg3HwlHi3Lb4ZmEXw=", false, function () {
  return [_hooks_useDimensions__WEBPACK_IMPORTED_MODULE_4__.default, _hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_2__.default];
});

_c = SearchBar;

var _c;

$RefreshReg$(_c, "SearchBar");

;
    var _a, _b;
    // Legacy CSS implementations will `eval` browser code in a Node.js context
    // to extract CSS. For backwards compatibility, we need to check we're in a
    // browser context before continuing.
    if (typeof self !== 'undefined' &&
        // AMP / No-JS mode does not inject these helpers:
        '$RefreshHelpers$' in self) {
        var currentExports = module.__proto__.exports;
        var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
        // This cannot happen in MainTemplate because the exports mismatch between
        // templating and execution.
        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
        // A module can be accepted automatically based on its exports, e.g. when
        // it is a Refresh Boundary.
        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
            // Save the previous exports on update so we can compare the boundary
            // signatures.
            module.hot.dispose(function (data) {
                data.prevExports = currentExports;
            });
            // Unconditionally accept an update to this module, we'll check if it's
            // still a Refresh Boundary later.
            module.hot.accept();
            // This field is set when the previous version of this module was a
            // Refresh Boundary, letting us know we need to check for invalidation or
            // enqueue an update.
            if (prevExports !== null) {
                // A boundary can become ineligible if its exports are incompatible
                // with the previous exports.
                //
                // For example, if you add/remove/change exports, we'll want to
                // re-execute the importing modules, and force those components to
                // re-render. Similarly, if you convert a class component to a
                // function, we want to invalidate the boundary.
                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                    module.hot.invalidate();
                }
                else {
                    self.$RefreshHelpers$.scheduleUpdate();
                }
            }
        }
        else {
            // Since we just executed the code for the module, it's possible that the
            // new exports made it ineligible for being a boundary.
            // We only care about the case when we were _previously_ a boundary,
            // because we already accepted this update (accidental side effect).
            var isNoLongerABoundary = prevExports !== null;
            if (isNoLongerABoundary) {
                module.hot.invalidate();
            }
        }
    }


/***/ }),

/***/ "./hooks/useExternalClick.ts":
/*!***********************************!*\
  !*** ./hooks/useExternalClick.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useExternalClick; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* module decorator */ module = __webpack_require__.hmd(module);
var _s = $RefreshSig$();


function useExternalClick(ref, callback) {
  _s();

  var onClick = function onClick(event) {
    var _ref$current;

    if (!(ref !== null && ref !== void 0 && (_ref$current = ref.current) !== null && _ref$current !== void 0 && _ref$current.contains(event.target))) {
      callback();
    }
  };

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    document.addEventListener('click', onClick);
    return function () {
      return document.removeEventListener('click', onClick);
    };
  });
}

_s(useExternalClick, "OD7bBpZva5O2jO+Puf00hKivP7c=");

;
    var _a, _b;
    // Legacy CSS implementations will `eval` browser code in a Node.js context
    // to extract CSS. For backwards compatibility, we need to check we're in a
    // browser context before continuing.
    if (typeof self !== 'undefined' &&
        // AMP / No-JS mode does not inject these helpers:
        '$RefreshHelpers$' in self) {
        var currentExports = module.__proto__.exports;
        var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
        // This cannot happen in MainTemplate because the exports mismatch between
        // templating and execution.
        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
        // A module can be accepted automatically based on its exports, e.g. when
        // it is a Refresh Boundary.
        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
            // Save the previous exports on update so we can compare the boundary
            // signatures.
            module.hot.dispose(function (data) {
                data.prevExports = currentExports;
            });
            // Unconditionally accept an update to this module, we'll check if it's
            // still a Refresh Boundary later.
            module.hot.accept();
            // This field is set when the previous version of this module was a
            // Refresh Boundary, letting us know we need to check for invalidation or
            // enqueue an update.
            if (prevExports !== null) {
                // A boundary can become ineligible if its exports are incompatible
                // with the previous exports.
                //
                // For example, if you add/remove/change exports, we'll want to
                // re-execute the importing modules, and force those components to
                // re-render. Similarly, if you convert a class component to a
                // function, we want to invalidate the boundary.
                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                    module.hot.invalidate();
                }
                else {
                    self.$RefreshHelpers$.scheduleUpdate();
                }
            }
        }
        else {
            // Since we just executed the code for the module, it's possible that the
            // new exports made it ineligible for being a boundary.
            // We only care about the case when we were _previously_ a boundary,
            // because we already accepted this update (accidental side effect).
            var isNoLongerABoundary = prevExports !== null;
            if (isNoLongerABoundary) {
                module.hot.invalidate();
            }
        }
    }


/***/ })

}]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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