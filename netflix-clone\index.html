<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netflix Clone</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Netflix+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-left">
                <div class="logo">
                    <h1>NETFLIX</h1>
                </div>
                <ul class="nav-menu">
                    <li><a href="#" class="active">Home</a></li>
                    <li><a href="#">TV Shows</a></li>
                    <li><a href="#">Movies</a></li>
                    <li><a href="#">New & Popular</a></li>
                    <li><a href="#">My List</a></li>
                    <li><a href="#">Browse by Languages</a></li>
                </ul>
            </div>
            <div class="nav-right">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" placeholder="Search" class="search-input">
                </div>
                <i class="fas fa-bell notification-icon"></i>
                <div class="profile-dropdown">
                    <img src="https://via.placeholder.com/32x32/ff0000/ffffff?text=U" alt="Profile" class="profile-img">
                    <i class="fas fa-caret-down"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1 class="hero-title">Stranger Things</h1>
            <p class="hero-description">
                When a young boy vanishes, a small town uncovers a mystery involving secret experiments, 
                terrifying supernatural forces, and one strange little girl.
            </p>
            <div class="hero-buttons">
                <button class="btn btn-play">
                    <i class="fas fa-play"></i>
                    Play
                </button>
                <button class="btn btn-info">
                    <i class="fas fa-info-circle"></i>
                    More Info
                </button>
            </div>
        </div>
        <div class="hero-fade"></div>
    </section>

    <!-- Content Rows -->
    <main class="main-content">
        <!-- Trending Now -->
        <section class="content-row">
            <h2 class="row-title">Trending Now</h2>
            <div class="movie-slider">
                <div class="movie-list" id="trending-list">
                    <!-- Movies will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Netflix Originals -->
        <section class="content-row">
            <h2 class="row-title">Netflix Originals</h2>
            <div class="movie-slider">
                <div class="movie-list" id="originals-list">
                    <!-- Movies will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Popular Movies -->
        <section class="content-row">
            <h2 class="row-title">Popular Movies</h2>
            <div class="movie-slider">
                <div class="movie-list" id="popular-list">
                    <!-- Movies will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- TV Shows -->
        <section class="content-row">
            <h2 class="row-title">TV Shows</h2>
            <div class="movie-slider">
                <div class="movie-list" id="tv-list">
                    <!-- Movies will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Action Movies -->
        <section class="content-row">
            <h2 class="row-title">Action Movies</h2>
            <div class="movie-slider">
                <div class="movie-list" id="action-list">
                    <!-- Movies will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Comedy Movies -->
        <section class="content-row">
            <h2 class="row-title">Comedy Movies</h2>
            <div class="movie-slider">
                <div class="movie-list" id="comedy-list">
                    <!-- Movies will be populated by JavaScript -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <div class="footer-column">
                    <a href="#">FAQ</a>
                    <a href="#">Investor Relations</a>
                    <a href="#">Privacy</a>
                    <a href="#">Speed Test</a>
                </div>
                <div class="footer-column">
                    <a href="#">Help Center</a>
                    <a href="#">Jobs</a>
                    <a href="#">Cookie Preferences</a>
                    <a href="#">Legal Notices</a>
                </div>
                <div class="footer-column">
                    <a href="#">Account</a>
                    <a href="#">Ways to Watch</a>
                    <a href="#">Corporate Information</a>
                    <a href="#">Only on Netflix</a>
                </div>
                <div class="footer-column">
                    <a href="#">Media Center</a>
                    <a href="#">Terms of Use</a>
                    <a href="#">Contact Us</a>
                </div>
            </div>
            <div class="footer-bottom">
                <button class="service-code-btn">Service Code</button>
                <p class="copyright">© 2024 Netflix Clone. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Movie Modal -->
    <div id="movie-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-header">
                <h2 id="modal-title">Movie Title</h2>
                <div class="modal-buttons">
                    <button class="btn btn-play">
                        <i class="fas fa-play"></i>
                        Play
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-plus"></i>
                        My List
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-thumbs-up"></i>
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <p id="modal-description">Movie description will appear here...</p>
                <div class="modal-details">
                    <span class="genre">Action, Adventure, Sci-Fi</span>
                    <span class="rating">★ 8.5/10</span>
                    <span class="year">2023</span>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
