exports.id = "components_Navbar_Profile_tsx";
exports.ids = ["components_Navbar_Profile_tsx"];
exports.modules = {

/***/ "./components/Navbar/Profile.tsx":
/*!***************************************!*\
  !*** ./components/Navbar/Profile.tsx ***!
  \***************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Profile; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ "framer-motion");
/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(framer_motion__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ "next/dynamic");
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ "next/router");
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _config_route__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../config/route */ "./config/route.ts");
/* harmony import */ var _utils_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/icons */ "./utils/icons.ts");
/* harmony import */ var _styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../styles/Navbar.module.scss */ "./styles/Navbar.module.scss");
/* harmony import */ var _styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_7__);

var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\Navbar\\Profile.tsx";

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

/* eslint-disable @next/next/no-img-element */







const Dialog = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(() => __webpack_require__.e(/*! import() */ "components_Dialog_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ../Dialog */ "./components/Dialog.tsx")), {
  loadableGenerated: {
    webpack: () => [/*require.resolve*/(/*! ../Dialog */ "./components/Dialog.tsx")],
    modules: ["..\\components\\Navbar\\Profile.tsx -> " + '../Dialog']
  }
});
function Profile() {
  const {
    0: visible,
    1: setVisible
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const profileRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);

  const onHover = () => {
    setVisible(true);
  };

  const onClose = () => setVisible(false);

  const onSignout = () => next_router__WEBPACK_IMPORTED_MODULE_4___default().push(_config_route__WEBPACK_IMPORTED_MODULE_5__.ROUTES.HOME);

  const caretAnimation = {
    animate: visible ? 'up' : 'down',
    variants: {
      up: {
        rotate: 180
      },
      down: {
        rotate: 0
      }
    },
    transition: {
      duration: 0.25
    }
  };
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_7___default().profile),
    onMouseOver: onHover,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("img", {
      src: "../../assets/avatar.png",
      alt: "user",
      className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_7___default().user)
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 41,
      columnNumber: 7
    }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, _objectSpread(_objectSpread({}, caretAnimation), {}, {
      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_icons__WEBPACK_IMPORTED_MODULE_6__.CaretDown, {}, void 0, false, {
        fileName: _jsxFileName,
        lineNumber: 43,
        columnNumber: 9
      }, this)
    }), void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 42,
      columnNumber: 7
    }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {
      dialogRef: profileRef,
      onClose: onClose,
      classname: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_7___default().signout),
      visible: visible,
      children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        onClick: onSignout,
        children: "Sign out"
      }, void 0, false, {
        fileName: _jsxFileName,
        lineNumber: 46,
        columnNumber: 9
      }, this)
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 45,
      columnNumber: 7
    }, this)]
  }, void 0, true, {
    fileName: _jsxFileName,
    lineNumber: 40,
    columnNumber: 5
  }, this);
}

/***/ }),

/***/ "./config/route.ts":
/*!*************************!*\
  !*** ./config/route.ts ***!
  \*************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ROUTES": function() { return /* binding */ ROUTES; }
/* harmony export */ });
let ROUTES;

(function (ROUTES) {
  ROUTES["HOME"] = "/";
  ROUTES["BROWSE"] = "/browse";
  ROUTES["LATEST"] = "/latest";
})(ROUTES || (ROUTES = {}));

/***/ })

};
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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