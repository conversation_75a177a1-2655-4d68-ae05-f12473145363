# Welcome to the React Pizza App 🍕

It is a pizza website which is built using React and TailwindCSS.


## 💻Tech Stack
<br>

![HTML](https://img.shields.io/badge/html5%20-%23E34F26.svg?&style=for-the-badge&logo=html5&logoColor=white)
![CSS](https://img.shields.io/badge/css3%20-%231572B6.svg?&style=for-the-badge&logo=css3&logoColor=white)
![JS](https://img.shields.io/badge/javascript%20-%23323330.svg?&style=for-the-badge&logo=javascript&logoColor=%23F7DF1E)
![REACT](https://img.shields.io/badge/react%20-%23323330.svg?&style=for-the-badge&logo=react&logoColor=%23F7DF1E)
![TAILWIND CSS](https://img.shields.io/badge/tailwindcss%20-%23323330.svg?&style=for-the-badge&logo=tailwindcss&logoColor=%23F7DF1E)

<br>


### How to get the form on your local machine:

---

- Download or clone the repository

```
git clone https://github.com/Ayushparikh-code/Web-dev-mini-projects.git
```

- Go to the directory
- Open react-tailwind-pizza-app folder
- Open the terminal and run 
```
npm / yarn start
```
- The website will be opened in your browser.



<br>

## Screenshots

![Demo1](src/images/pizza.png)
<br>

![Demo2](src/images/pizza1.png)
<br>


## Happy Coding!
