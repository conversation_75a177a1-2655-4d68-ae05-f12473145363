exports.id = "components_Button_index_tsx";
exports.ids = ["components_Button_index_tsx"];
exports.modules = {

/***/ "./components/Button/index.tsx":
/*!*************************************!*\
  !*** ./components/Button/index.tsx ***!
  \*************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Button; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../styles/Button.module.scss */ "./styles/Button.module.scss");
/* harmony import */ var _styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1__);

var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\Button\\index.tsx";

function Button(props) {
  const {
    filled,
    label,
    Icon,
    rounded,
    onClick
  } = props;
  const backgroundColor = filled ? 'white' : '#6d6d6db3';
  const fontColor = filled ? 'black' : 'white';
  /* 
  if not rounded === normal long style
  if filled ( and rounded) === round style
  if rounded and not filled === outline style
  */

  const style = !rounded ? (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().button) : filled ? (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().roundButton) : (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().outlineRounded);
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("button", {
    className: style,
    style: {
      backgroundColor: `${backgroundColor}`,
      color: `${fontColor}`
    },
    onClick: onClick,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {
      className: (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().icon)
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 26,
      columnNumber: 7
    }, this), !rounded && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
      className: (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().label),
      children: label
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 27,
      columnNumber: 20
    }, this)]
  }, void 0, true, {
    fileName: _jsxFileName,
    lineNumber: 25,
    columnNumber: 5
  }, this);
}

/***/ }),

/***/ "./styles/Button.module.scss":
/*!***********************************!*\
  !*** ./styles/Button.module.scss ***!
  \***********************************/
/***/ (function(module) {

// Exports
module.exports = {
	"button": "Button_button__1D2JL",
	"label": "Button_label__180cU",
	"icon": "Button_icon__2M4k7",
	"roundButton": "Button_roundButton__mjd6k",
	"outlineRounded": "Button_outlineRounded__adTWY"
};


/***/ })

};
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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