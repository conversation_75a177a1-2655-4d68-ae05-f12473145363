{"..\\components\\Layout\\index.tsx -> ../Footer": {"id": "./components/Footer/index.tsx", "files": ["static/chunks/components_Footer_index_tsx.js"]}, "..\\components\\Layout\\index.tsx -> ../Navbar": {"id": "./components/Navbar/index.tsx", "files": ["static/chunks/components_Navbar_index_tsx.js"]}, "..\\components\\List\\Cards.tsx -> ../Button": {"id": "./components/Button/index.tsx", "files": ["static/chunks/components_Button_index_tsx.js"]}, "..\\components\\List\\FeatureCards.tsx -> ../Button": {"id": "./components/Button/index.tsx", "files": ["static/chunks/components_Button_index_tsx.js"]}, "..\\components\\List\\index.tsx -> ./Cards": {"id": "./components/List/Cards.tsx", "files": ["static/chunks/components_List_Cards_tsx.js"]}, "..\\components\\List\\index.tsx -> ./FeatureCards": {"id": "./components/List/FeatureCards.tsx", "files": ["static/chunks/components_List_FeatureCards_tsx.js"]}, "..\\components\\Navbar\\Menu.tsx -> ../Dialog": {"id": "./components/Dialog.tsx", "files": ["static/chunks/components_Dialog_tsx.js"]}, "..\\components\\Navbar\\Profile.tsx -> ../Dialog": {"id": "./components/Dialog.tsx", "files": ["static/chunks/components_Dialog_tsx.js"]}, "..\\components\\Navbar\\index.tsx -> ./Menu": {"id": "./components/Navbar/Menu.tsx", "files": ["static/chunks/components_Navbar_Menu_tsx.js"]}, "..\\components\\Navbar\\index.tsx -> ./Profile": {"id": "./components/Navbar/Profile.tsx", "files": ["static/chunks/components_Navbar_Profile_tsx.js"]}, "..\\components\\Navbar\\index.tsx -> ./SearchBar": {"id": "./components/Navbar/SearchBar.tsx", "files": ["static/chunks/components_Navbar_SearchBar_tsx.js"]}, "..\\node_modules\\next\\dist\\client\\next-dev.js -> ./dev/noop": {"id": "./node_modules/next/dist/client/dev/noop.js", "files": ["static/chunks/node_modules_next_dist_client_dev_noop_js.js"]}, "browse.tsx -> ../components/Banner": {"id": "./components/Banner/index.tsx", "files": ["static/chunks/components_Banner_index_tsx.js"]}, "browse.tsx -> ../components/Layout": {"id": "./components/Layout/index.tsx", "files": ["static/chunks/components_Layout_index_tsx.js"]}, "browse.tsx -> ../components/List": {"id": "./components/List/index.tsx", "files": ["static/chunks/components_List_index_tsx.js"]}, "browse.tsx -> ../components/Modal": {"id": "./components/Modal/index.tsx", "files": ["static/chunks/components_Modal_index_tsx.js"]}}