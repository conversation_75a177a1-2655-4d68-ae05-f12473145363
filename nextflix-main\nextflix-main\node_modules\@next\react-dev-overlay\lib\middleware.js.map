{"version": 3, "file": "middleware.js", "sourceRoot": "", "sources": ["../src/middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAoD;AACpD,yBAAoD;AAEpD,8CAAuB;AACvB,yCAImB;AAEnB,4CAAqB;AAIrB,sEAAoE;AACpE,gEAA8D;AAgB9D,SAAS,WAAW,CAAC,WAAgB,EAAE,MAAW,EAAE,UAAoB;IACtE,IAAI,UAAU,EAAE;QACd,OAAO,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;KAClD;IAED,OAAO,MAAM,CAAC,EAAE,CAAA;AAClB,CAAC;AAED,SAAS,eAAe,CACtB,WAAgB,EAChB,MAAW,EACX,UAAoB;;IAEpB,IAAI,UAAU,EAAE;QACd,OAAO,CACL,MAAA,CAAC,MAAM;aACL,MAAA,WAAW,CAAC,qBAAqB;iBAC9B,GAAG,CAAC,MAAM,CAAC,0CACV,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA,CAAC,mCAChC,IAAI,CACL,CAAA;KACF;IAED,OAAO,CACL,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,CACZ,WAAW,CAAC,mBAAmB,EAC/B,WAAW,CAAC,eAAe,CAC5B,mCAAI,IAAI,CACV,CAAA;AACH,CAAC;AAED,SAAS,aAAa,CAAC,MAAc;IACnC,uDAAuD;IACvD,IAAI,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;QACpC,OAAO,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;KAC5B;IAED,iDAAiD;IACjD,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE;QACxC,OAAO,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;KAC5B;IAED,IAAI,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;QACnC,OAAO,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;KAC5B;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAe,oCAAoC,CACjD,aAAkB,EAClB,QAAiD;;;;;;wBAEhC,qBAAM,IAAI,8BAAiB,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,EAAA;;oBAA3D,QAAQ,GAAG,SAAgD;oBACjE,IAAI;wBACI,cAAc,GAA2B,QAAQ,CAAC,mBAAmB,CACzE;4BACE,IAAI,EAAE,QAAQ,CAAC,IAAI;4BACnB,MAAM,EAAE,MAAA,QAAQ,CAAC,MAAM,mCAAI,CAAC;yBAC7B,CACF,CAAA;wBAED,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;4BAC1B,sBAAO,IAAI,EAAA;yBACZ;wBAEK,aAAa,GACjB,MAAA,QAAQ,CAAC,gBAAgB,CACvB,cAAc,CAAC,MAAM;wBACrB,yBAAyB,CAAC,IAAI,CAC/B,mCAAI,IAAI,CAAA;wBAEX,sBAAO;gCACL,cAAc,gBAAA;gCACd,aAAa,eAAA;6BACd,EAAA;qBACF;4BAAS;wBACR,QAAQ,CAAC,OAAO,EAAE,CAAA;qBACnB;;;;;CACF;AAED,SAAsB,wBAAwB,CAAC,EAc9C;;QAbC,IAAI,UAAA,EACJ,MAAM,YAAA,EACN,MAAM,YAAA,EACN,UAAU,gBAAA,EACV,aAAa,mBAAA,EACb,KAAK,WAAA;;;;;wBASU,qBAAM,oCAAoC,CAAC,MAAM,EAAE;wBAChE,IAAI,MAAA;wBACJ,MAAM,QAAA;qBACP,CAAC,EAAA;;oBAHI,MAAM,GAAG,SAGb;oBAEF,IAAI,MAAM,KAAK,IAAI,EAAE;wBACnB,sBAAO,IAAI,EAAA;qBACZ;oBAEO,cAAc,GAAoB,MAAM,eAA1B,EAAE,aAAa,GAAK,MAAM,cAAX,CAAW;oBAEhD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;wBAC1B,sBAAO,IAAI,EAAA;qBACZ;oBAEK,QAAQ,GAAG,iBAAI,CAAC,OAAO,CAC3B,aAAa,EACb,UAAU,IAAI,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,CACnD,CAAA;oBAEK,aAAa,GAAe;wBAChC,IAAI,EAAE,aAAa;4BACjB,CAAC,CAAC,iBAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC;4BACxC,CAAC,CAAC,cAAc,CAAC,MAAM;wBACzB,UAAU,EAAE,cAAc,CAAC,IAAI;wBAC/B,MAAM,EAAE,cAAc,CAAC,MAAM;wBAC7B,UAAU,EAAE,KAAK,CAAC,UAAU;wBAC5B,SAAS,EAAE,EAAE;qBACd,CAAA;oBAEK,iBAAiB,GACrB,CAAC,CAAC,MAAA,MAAA,aAAa,CAAC,IAAI,0CAAE,QAAQ,CAAC,cAAc,CAAC,mCAAI,IAAI,CAAC;wBACvD,aAAa;wBACb,cAAc,CAAC,IAAI;wBACjB,CAAC,CAAE,6BAAgB,CACf,aAAa,EACb;4BACE,KAAK,EAAE;gCACL,IAAI,EAAE,cAAc,CAAC,IAAI;gCACzB,MAAM,EAAE,MAAA,cAAc,CAAC,MAAM,mCAAI,CAAC;6BACnC;yBACF,EACD,EAAE,UAAU,EAAE,IAAI,EAAE,CACV;wBACd,CAAC,CAAC,IAAI,CAAA;oBAEV,sBAAO;4BACL,kBAAkB,EAAE,aAAa;4BACjC,iBAAiB,mBAAA;yBAClB,EAAA;;;;CACF;AAjED,4DAiEC;AAED,SAAS,oBAAoB,CAAC,OAAiC;IAC7D,SAAe,aAAa,CAC1B,YAAqB,EACrB,MAAe,EACf,EAAU;;;;;;;6BAEN,MAAM,EAAN,wBAAM;wBAC2B,qBAAM,aAAE;iCACxC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,CACrB,OAAK,CAAA,CAAC,cAAM,OAAA,IAAI,EAAJ,CAAI,CAAC,EAAA;;wBAFd,WAAW,GAAkB,SAEf;wBAEpB,IAAI,WAAW,IAAI,IAAI,EAAE;4BACvB,sBAAO,IAAI,EAAA;yBACZ;wBAEK,QAAM,iCAAe,CAAC,WAAW,CAAC,CAAA;wBACxC,IAAI,KAAG,IAAI,IAAI,EAAE;4BACf,sBAAO,IAAI,EAAA;yBACZ;wBAED,sBAAO;gCACL,GAAG;oCACD,OAAO,KAAG,CAAA;gCACZ,CAAC;6BACF,EAAA;;wBAGH,IAAI;4BACI,gBAAc,YAAY;gCAC9B,CAAC,CAAC,MAAA,OAAO,CAAC,WAAW,EAAE,0CAAE,WAAW;gCACpC,CAAC,CAAC,MAAA,OAAO,CAAC,KAAK,EAAE,0CAAE,WAAW,CAAA;4BAChC,IAAI,aAAW,IAAI,IAAI,EAAE;gCACvB,sBAAO,IAAI,EAAA;6BACZ;4BAEK,WAAS,yBAAI,aAAW,CAAC,OAAO,GAAE,IAAI,CAC1C,UAAC,YAAY;gCACX,OAAA,WAAW,CAAC,aAAW,EAAE,YAAY,EAAE,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE;4BAAjE,CAAiE,CACpE,CAAA;4BACD,sBAAO,eAAe,CAAC,aAAW,EAAE,QAAM,EAAE,OAAO,CAAC,UAAU,CAAC,EAAA;yBAChE;wBAAC,OAAO,GAAG,EAAE;4BACZ,OAAO,CAAC,KAAK,CAAC,sCAAmC,EAAE,SAAK,EAAE,GAAG,CAAC,CAAA;4BAC9D,sBAAO,IAAI,EAAA;yBACZ;;;;;KACF;IAED,OAAO,UACL,GAAoB,EACpB,GAAmB,EACnB,IAAc;;;;;;;wBAER,KAAsB,gBAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAI,EAAE,IAAI,CAAC,EAA7C,QAAQ,cAAA,EAAE,KAAK,WAAA,CAA8B;6BAEjD,CAAA,QAAQ,KAAK,gCAAgC,CAAA,EAA7C,wBAA6C;wBACzC,KAAK,GAAI,KAEd,CAAA;wBACD,IACE,CAAC,CACC,CAAC,CAAA,MAAA,KAAK,CAAC,IAAI,0CAAE,UAAU,CAAC,sBAAsB,CAAC;6BAC7C,MAAA,KAAK,CAAC,IAAI,0CAAE,UAAU,CAAC,SAAS,CAAC,CAAA,CAAC;4BACpC,OAAO,CAAC,QAAQ,CAAC,MAAA,MAAA,KAAK,CAAC,UAAU,0CAAE,QAAQ,EAAE,mCAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAC1D,EACD;4BACA,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;4BACpB,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;4BACxB,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;yBACjB;wBAEK,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,MAAM,CAAA;wBAC5C,QAAQ,GAAW,KAAK,CAAC,IAAI,CAAC,OAAO,CACzC,sCAAsC,EACtC,EAAE,CACH,CAAA;wBAEG,MAAM,SAAQ,CAAA;;;;wBAEP,qBAAM,aAAa,CAC1B,YAAY,EACZ,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAC9B,QAAQ,CACT,EAAA;;wBAJD,MAAM,GAAG,SAIR,CAAA;;;;wBAED,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,KAAG,CAAC,CAAA;wBAC7C,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;wBACpB,GAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAA;wBAClC,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;;wBAGlB,IAAI,MAAM,IAAI,IAAI,EAAE;4BAClB,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;4BACpB,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;4BACvB,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;yBACjB;wBAEK,SAAS,GAAG,QAAQ,CAAC,MAAA,MAAA,KAAK,CAAC,UAAU,0CAAE,QAAQ,EAAE,mCAAI,EAAE,EAAE,EAAE,CAAC,CAAA;wBAC9D,WAAW,GAAkB,QAAQ,CACvC,MAAA,MAAA,KAAK,CAAC,MAAM,0CAAE,QAAQ,EAAE,mCAAI,EAAE,EAC9B,EAAE,CACH,CAAA;wBACD,IAAI,CAAC,WAAW,EAAE;4BAChB,WAAW,GAAG,IAAI,CAAA;yBACnB;;;;wBAGoC,qBAAM,wBAAwB,CAAC;gCAChE,IAAI,EAAE,SAAS;gCACf,MAAM,EAAE,WAAW;gCACnB,MAAM,QAAA;gCACN,KAAK,OAAA;gCACL,UAAU,EAAE,QAAQ;gCACpB,aAAa,EAAE,OAAO,CAAC,aAAa;6BACrC,CAAC,EAAA;;wBAPI,0BAA0B,GAAG,SAOjC;wBAEF,IAAI,0BAA0B,KAAK,IAAI,EAAE;4BACvC,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;4BACpB,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;4BACvB,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;yBACjB;wBAED,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;wBACpB,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAA;wBACjD,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAA;wBAClE,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;;;wBAEhB,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,KAAG,CAAC,CAAA;wBAC/C,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;wBACpB,GAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAA;wBAClC,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;;;6BAET,CAAA,QAAQ,KAAK,yBAAyB,CAAA,EAAtC,yBAAsC;wBACzC,KAAK,GAAI,KAA+B,CAAA;wBAExC,SAAS,GAAG,CAAA,MAAA,KAAK,CAAC,IAAI,0CAAE,QAAQ,EAAE,KAAI,IAAI,CAAA;wBAChD,IAAI,SAAS,IAAI,IAAI,EAAE;4BACrB,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;4BACpB,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;4BACxB,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;yBACjB;wBAEK,QAAQ,GAAG,iBAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;wBAC5C,qBAAM,aAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,cAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CACxD,cAAM,OAAA,IAAI,EAAJ,CAAI,EACV,cAAM,OAAA,KAAK,EAAL,CAAK,CACZ,EAAA;;wBAHK,UAAU,GAAG,SAGlB;wBACD,IAAI,CAAC,UAAU,EAAE;4BACf,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;4BACpB,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;4BACvB,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;yBACjB;wBAEK,SAAS,GAAG,QAAQ,CAAC,MAAA,MAAA,KAAK,CAAC,UAAU,0CAAE,QAAQ,EAAE,mCAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;wBACjE,WAAW,GAAG,QAAQ,CAAC,MAAA,MAAA,KAAK,CAAC,MAAM,0CAAE,QAAQ,EAAE,mCAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;;;;wBAGnE,qBAAM,2BAAY,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,EAAA;;wBAApD,SAAoD,CAAA;;;;wBAEpD,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,KAAG,CAAC,CAAA;wBAC5C,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;wBACpB,GAAG,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAA;wBAClC,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;;wBAGlB,GAAG,CAAC,UAAU,GAAG,GAAG,CAAA;wBACpB,sBAAO,GAAG,CAAC,GAAG,EAAE,EAAA;6BAElB,sBAAO,IAAI,EAAE,EAAA;;;;KACd,CAAA;AACH,CAAC;AAEQ,oDAAoB"}