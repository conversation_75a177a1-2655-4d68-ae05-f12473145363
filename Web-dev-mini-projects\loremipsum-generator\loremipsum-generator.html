
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>💬 Lorem Ipsum</title>

    <style>
        /*
=============== 
Fonts
===============
*/
        @import url("https://fonts.googleapis.com/css?family=Open+Sans|Roboto:400,700&display=swap");

        /*
=============== 
Variables
===============
*/

        :root {
            /* dark shades of primary color*/
            --clr-primary-1: hsl(205, 86%, 17%);
            --clr-primary-2: hsl(205, 77%, 27%);
            --clr-primary-3: hsl(205, 72%, 37%);
            --clr-primary-4: hsl(205, 63%, 48%);
            /* primary/main color */
            --clr-primary-5: #49a6e9;
            /* lighter shades of primary color */
            --clr-primary-6: hsl(205, 89%, 70%);
            --clr-primary-7: hsl(205, 90%, 76%);
            --clr-primary-8: hsl(205, 86%, 81%);
            --clr-primary-9: hsl(205, 90%, 88%);
            --clr-primary-10: hsl(205, 100%, 96%);
            /* darkest grey - used for headings */
            --clr-grey-1: hsl(209, 61%, 16%);
            --clr-grey-2: hsl(211, 39%, 23%);
            --clr-grey-3: hsl(209, 34%, 30%);
            --clr-grey-4: hsl(209, 28%, 39%);
            /* grey used for paragraphs */
            --clr-grey-5: hsl(210, 22%, 49%);
            --clr-grey-6: hsl(209, 23%, 60%);
            --clr-grey-7: hsl(211, 27%, 70%);
            --clr-grey-8: hsl(210, 31%, 80%);
            --clr-grey-9: hsl(212, 33%, 89%);
            --clr-grey-10: hsl(210, 36%, 96%);
            --clr-white: #fff;
            --clr-red-dark: hsl(360, 67%, 44%);
            --clr-red-light: hsl(360, 71%, 66%);
            --clr-green-dark: hsl(125, 67%, 44%);
            --clr-green-light: hsl(125, 71%, 66%);
            --clr-black: #222;
            --ff-primary: "Roboto", sans-serif;
            --ff-secondary: "Open Sans", sans-serif;
            --transition: all 0.3s linear;
            --spacing: 0.25rem;
            --radius: 0.5rem;
            --light-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            --dark-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            --max-width: 1170px;
            --fixed-width: 620px;
        }

        /*
=============== 
Global Styles
===============
*/

        *,
        ::after,
        ::before {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--ff-secondary);
            background: var(--clr-grey-10);
            color: var(--clr-grey-1);
            line-height: 1.5;
            font-size: 0.875rem;
        }

        ul {
            list-style-type: none;
        }

        a {
            text-decoration: none;
        }

        img:not(.logo) {
            width: 100%;
        }

        img {
            display: block;
        }

        h1,
        h2,
        h3,
        h4 {
            letter-spacing: var(--spacing);
            text-transform: capitalize;
            line-height: 1.25;
            margin-bottom: 0.75rem;
            font-family: var(--ff-primary);
        }

        h1 {
            font-size: 3rem;
        }

        h2 {
            font-size: 2rem;
        }

        h3 {
            font-size: 1.25rem;
        }

        h4 {
            font-size: 0.875rem;
        }

        p {
            margin-bottom: 1.25rem;
            color: var(--clr-grey-5);
        }

        @media screen and (min-width: 800px) {
            h1 {
                font-size: 4rem;
            }

            h2 {
                font-size: 2.5rem;
            }

            h3 {
                font-size: 1.75rem;
            }

            h4 {
                font-size: 1rem;
            }

            body {
                font-size: 1rem;
            }

            h1,
            h2,
            h3,
            h4 {
                line-height: 1;
            }
        }

        /*  global classes */

        .btn {
            text-transform: uppercase;
            background: var(--clr-primary-5);
            color: var(--clr-primary-1);
            padding: 0.375rem 0.75rem;
            letter-spacing: 1px;
            display: inline-block;
            transition: var(--transition);
            font-size: 0.875rem;
            border: 2px solid var(--clr-primary-5);
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            border-radius: var(--radius);
        }

        .btn:hover {
            color: var(--clr-primary-5);
            background: var(--clr-primary-8);
            border-color: var(--clr-primary-8);
        }

        /* section */
        .section {
            padding: 5rem 0;
        }

        .section-center {
            width: 90vw;
            margin: 0 auto;
            max-width: 40rem;
            margin-top: 5rem;
            text-align: center;
        }

        @media screen and (min-width: 992px) {
            .section-center {
                width: 95vw;
            }
        }

        main {
            min-height: 100vh;
            display: grid;
            place-items: center;
        }

        /*
=============== 
Lorem Ipsum
===============
*/
        h3 {
            text-transform: uppercase;
            color: var(--clr-primary-1);
        }

        .lorem-form {
            text-transform: capitalize;
            letter-spacing: var(--spacing);
            margin-top: 2rem;
            margin-bottom: 4rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        label {
            font-size: 1.25rem;
            color: var(--clr-primary-1);
        }

        input {
            padding: 0.25rem 0.5rem;

            width: 4rem;
            border-radius: var(--radius);
            border: none;
            margin: 0 0.5rem;
            font-size: 1.25rem;
        }

        button {
            background: var(--clr-primary-10);
        }

        .result {
            margin-bottom: 2rem;
        }
    </style>
</head>

<body>
    <section class="section-center">
        <h3>tired of boring lorem ipsum?</h3>
        <form class="lorem-form">
            <label for="amount">paragraphs:</label>
            <input type="number" name="amount" id="amount" placeholder="5" />
            <button type="submit" class="btn">generate</button>
        </form>
        <article class="lorem-text"></article>
    </section>

    <!-- javascript -->
    <script>
        const text = [
            `Jelly sweet roll jelly beans biscuit pie macaroon chocolate donut. Carrot cake caramels pie sweet apple pie tiramisu carrot cake. Marzipan marshmallow croissant tootsie roll lollipop. Cupcake lemon drops bear claw gummies. Jelly bear claw gummi bears lollipop cotton candy gummi bears chocolate bar cake cookie. Cupcake muffin danish muffin cookie gummies. Jelly beans tiramisu pudding. Toffee soufflé chocolate cake pastry brownie. Oat cake halvah sweet roll cotton candy croissant lollipop. Macaroon tiramisu chocolate bar candy candy carrot cake jelly sweet. Gummies croissant macaroon dessert. Chocolate cake dragée pie.`,
            `Next level tbh everyday carry, blog copper mug forage kitsch roof party pickled hammock kale chips tofu. Etsy shoreditch 8-bit microdosing, XOXO viral butcher banh mi humblebrag listicle woke bicycle rights brunch before they sold out ramps. Twee shabby chic taiyaki flannel, enamel pin venmo vape four loko. Hexagon kale chips typewriter kitsch 8-bit organic plaid small batch keffiyeh ethical banh mi narwhal echo park cronut.`,
            `Zombie ipsum reversus ab viral inferno, nam rick grimes malum cerebro. De carne lumbering animata corpora quaeritis. Summus brains sit​​, morbo vel maleficia? De apocalypsi gorger omero undead survivor dictum mauris. Hi mindless mortuis soulless creaturas, imo evil stalking monstra adventus resi dentevil vultus comedat cerebella viventium. Qui animated corpse, cricket bat max brucks terribilem incessu zomby. The voodoo sacerdos flesh eater, suscitat mortuos comedere carnem virus. Zonbi tattered for solum oculi eorum defunctis go lum cerebro. Nescio brains an Undead zombies. Sicut malus putrid voodoo horror. Nigh tofth eliv ingdead.`,
            `Cat gets stuck in tree firefighters try to get cat down firefighters get stuck in tree cat eats firefighters' slippers kitty power ignore the squirrels, you'll never catch them anyway for what a cat-ass-trophy! or purr as loud as possible, be the most annoying cat that you can, and, knock everything off the table. Pretend you want to go out but then don't bite off human's toes, yet disappear for four days and return home with an expensive injury; bite the vet so catch eat throw up catch eat throw up bad birds. `,
            `This opera's as lousy as it is brilliant! Your lyrics lack subtlety. You can't just have your characters announce how they feel. That makes me feel angry! Anyhoo, your net-suits will allow you to experience Fry's worm infested bowels as if you were actually wriggling through them.
I just told you! You've killed me! Fry! Quit doing the right thing, you jerk! Michelle, I don't regret this, but I both rue and lament it. Morbo can't understand his teleprompter because he forgot how you say that letter that's shaped like a man wearing a hat.`,
            `Airedale hard cheese mozzarella. Pecorino melted cheese port-salut emmental babybel cheese and wine melted cheese manchego. Everyone loves blue castello everyone loves fromage cheese slices airedale cheddar cream cheese. Bavarian bergkase who moved my cheese halloumi port-salut gouda jarlsberg ricotta rubber cheese. Stinking bishop smelly cheese brie.`,
            `Salvia glossier subway tile, leggings mustache YOLO semiotics chia. Pitchfork tbh af blog church-key meggings vaporware PBR&B master cleanse post-ironic man bun pabst mustache letterpress synth. Snackwave raw denim godard, 3 wolf moon shaman offal kitsch unicorn live-edge selvage schlitz fashion axe vaporware drinking vinegar prism. Shabby chic tacos artisan, chambray chicharrones cardigan leggings typewriter af pop-up williamsburg meditation PBR&B viral. You probably haven't heard of them DIY jean shorts subway tile fashion axe bushwick kitsch tumeric cloud bread vaporware freegan franzen pork belly chicharrones banh mi.`,
            `Man braid celiac synth freegan readymade, pitchfork fam salvia waistcoat lomo bitters gentrify four loko. Pitchfork semiotics post-ironic vegan. Tofu meditation microdosing hashtag semiotics venmo. Flexitarian vape tilde taiyaki. Prism poutine farm-to-table, messenger bag vegan taxidermy tattooed sartorial squid jean shorts fixie selvage trust fund vape.`,
            `Rutters Plate Fleet boom chandler Brethren of the Coast handsomely lookout marooned brigantine knave. Buccaneer gangway jack rum loot spyglass line Jack Tar fore gaff. Gaff topmast scuttle ballast swab draught measured fer yer chains dance the hempen jig Chain Shot yardarm.`,
        ];

        const form = document.querySelector(".lorem-form");
        const amount = document.getElementById("amount");
        const result = document.querySelector(".lorem-text");

        form.addEventListener("submit", function (e) {
            // A click on a form submit button – initiates its submission to the server.

            e.preventDefault();

            const value = parseInt(amount.value);
            const random = Math.floor(Math.random() * text.length);

            if (isNaN(value) || value < 0 || value > 9) {
                result.innerHTML = `<p class="result">${text[random]}</p>`;
            } else {
                let tempText = text.slice(0, value);
                tempText = tempText
                    .map(function (item) {
                        return `<p class="result">${item}</p>`;
                    })
                    .join("");
                result.innerHTML = tempText;
            }
        });
    </script>
</body>

</html>
