<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <title>Rock Paper Scissors Game</title>

</head>

<body>
   
    <div class="container">
        <h2>Rock Paper Scissors</h2>
        <p id="startingPoint">Choose any one.</p>
        <div class="flex-box" id="flex-box-rps">
            <img src="images/rock.jpeg" alt="This is a rock image." id="rock" width="150px" height="150px"
                onclick="rpsGame(this)">
            <img src="images/paper.jpeg" alt="This is a paper image." id="paper" width="150px" height="150px"
                onclick="rpsGame(this)">
            <img src="images/scissors.jpeg" alt="This is a scissors image." id="scissors" width="150px" height="150px"
                onclick="rpsGame(this)">
        </div>
    </div>


   <script src="script.js"></script>
    
</body>

</html>