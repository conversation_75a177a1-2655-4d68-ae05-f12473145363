(function() {
var exports = {};
exports.id = "pages/browse";
exports.ids = ["pages/browse"];
exports.modules = {

/***/ "./context/ModalContext.tsx":
/*!**********************************!*\
  !*** ./context/ModalContext.tsx ***!
  \**********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ModalContext": function() { return /* binding */ ModalContext; },
/* harmony export */   "ModalProvider": function() { return /* binding */ ModalProvider; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);

var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\context\\ModalContext.tsx";

const ModalContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});
function ModalProvider({
  children
}) {
  const {
    0: modalData,
    1: setModalData
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});
  const {
    0: isModal,
    1: setIsModal
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModalContext.Provider, {
    value: {
      modalData,
      setModalData,
      isModal,
      setIsModal
    },
    children: children
  }, void 0, false, {
    fileName: _jsxFileName,
    lineNumber: 21,
    columnNumber: 10
  }, this);
}

/***/ }),

/***/ "./pages/browse.tsx":
/*!**************************!*\
  !*** ./pages/browse.tsx ***!
  \**************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Browse; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ "next/dynamic");
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _context_ModalContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/ModalContext */ "./context/ModalContext.tsx");
/* harmony import */ var _styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../styles/Browse.module.scss */ "./styles/Browse.module.scss");
/* harmony import */ var _styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4__);


var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\pages\\browse.tsx";

/* eslint-disable @next/next/no-img-element */




const List = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(() => __webpack_require__.e(/*! import() */ "components_List_index_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ../components/List */ "./components/List/index.tsx")), {
  loadableGenerated: {
    webpack: () => [/*require.resolve*/(/*! ../components/List */ "./components/List/index.tsx")],
    modules: ["browse.tsx -> " + '../components/List']
  }
});
const Modal = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(() => __webpack_require__.e(/*! import() */ "components_Modal_index_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ../components/Modal */ "./components/Modal/index.tsx")), {
  loadableGenerated: {
    webpack: () => [/*require.resolve*/(/*! ../components/Modal */ "./components/Modal/index.tsx")],
    modules: ["browse.tsx -> " + '../components/Modal']
  }
});
const Layout = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(() => __webpack_require__.e(/*! import() */ "components_Layout_index_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ../components/Layout */ "./components/Layout/index.tsx")), {
  loadableGenerated: {
    webpack: () => [/*require.resolve*/(/*! ../components/Layout */ "./components/Layout/index.tsx")],
    modules: ["browse.tsx -> " + '../components/Layout']
  }
});
const Banner = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(() => __webpack_require__.e(/*! import() */ "components_Banner_index_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ../components/Banner */ "./components/Banner/index.tsx")), {
  loadableGenerated: {
    webpack: () => [/*require.resolve*/(/*! ../components/Banner */ "./components/Banner/index.tsx")],
    modules: ["browse.tsx -> " + '../components/Banner']
  }
});
function Browse() {
  const {
    isModal
  } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_context_ModalContext__WEBPACK_IMPORTED_MODULE_3__.ModalContext);
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
    children: [isModal && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {}, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 18,
      columnNumber: 19
    }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Layout, {
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Banner, {}, void 0, false, {
        fileName: _jsxFileName,
        lineNumber: 20,
        columnNumber: 9
      }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
        className: (_styles_Browse_module_scss__WEBPACK_IMPORTED_MODULE_4___default().contentContainer),
        children: sections.map((item, index) => {
          return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(List, {
            heading: item.heading,
            endpoint: item.endpoint,
            defaultCard: item === null || item === void 0 ? void 0 : item.defaultCard,
            topList: item === null || item === void 0 ? void 0 : item.topList
          }, index, false, {
            fileName: _jsxFileName,
            lineNumber: 24,
            columnNumber: 15
          }, this);
        })
      }, void 0, false, {
        fileName: _jsxFileName,
        lineNumber: 21,
        columnNumber: 9
      }, this)]
    }, void 0, true, {
      fileName: _jsxFileName,
      lineNumber: 19,
      columnNumber: 7
    }, this)]
  }, void 0, true);
}
const sections = [{
  heading: 'Popular on Nextflix',
  endpoint: '/api/popular?type=tv'
}, {
  heading: 'Horror Movies',
  endpoint: '/api/discover?type=movie&genre=27'
}, {
  heading: 'Only on Nextflix',
  endpoint: '/api/discover?type=tv',
  defaultCard: false
}, {
  heading: 'Trending Now',
  endpoint: '/api/trending?type=movie&time=week'
}, {
  heading: 'Comedies',
  endpoint: '/api/discover?type=movie&genre=35'
}, {
  heading: 'Top 10 in US Today',
  endpoint: '/api/trending?type=tv&time=day',
  topList: true
}, {
  heading: 'Action',
  endpoint: '/api/discover?type=movie&genre=28'
}, {
  heading: 'TV Sci-Fi and Horror',
  endpoint: '/api/discover?type=tv&genre=10765'
}, {
  heading: 'Mystery Movies',
  endpoint: '/api/discover?type=movie&genre=9648'
}, {
  heading: 'Animation',
  endpoint: '/api/discover?type=tv&genre=16'
}, {
  heading: 'Drama',
  endpoint: '/api/discover?type=movie&genre=18'
}];

/***/ }),

/***/ "./styles/Browse.module.scss":
/*!***********************************!*\
  !*** ./styles/Browse.module.scss ***!
  \***********************************/
/***/ (function(module) {

// Exports
module.exports = {
	"container": "Browse_container__QLH-1",
	"contentContainer": "Browse_contentContainer__1nXEt"
};


/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ (function(module) {

"use strict";
module.exports = require("axios");;

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ (function(module) {

"use strict";
module.exports = require("framer-motion");;

/***/ }),

/***/ "../next-server/lib/head":
/*!****************************************************!*\
  !*** external "next/dist/next-server/lib/head.js" ***!
  \****************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/lib/head.js");;

/***/ }),

/***/ "../next-server/lib/to-base-64":
/*!**********************************************************!*\
  !*** external "next/dist/next-server/lib/to-base-64.js" ***!
  \**********************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/lib/to-base-64.js");;

/***/ }),

/***/ "../next-server/server/image-config":
/*!***************************************************************!*\
  !*** external "next/dist/next-server/server/image-config.js" ***!
  \***************************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/server/image-config.js");;

/***/ }),

/***/ "next/dynamic":
/*!*******************************!*\
  !*** external "next/dynamic" ***!
  \*******************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dynamic");;

/***/ }),

/***/ "next/router":
/*!******************************!*\
  !*** external "next/router" ***!
  \******************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/router");;

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ (function(module) {

"use strict";
module.exports = require("react");;

/***/ }),

/***/ "react-device-detect":
/*!**************************************!*\
  !*** external "react-device-detect" ***!
  \**************************************/
/***/ (function(module) {

"use strict";
module.exports = require("react-device-detect");;

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ (function(module) {

"use strict";
module.exports = require("react/jsx-dev-runtime");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
var __webpack_exports__ = (__webpack_exec__("./pages/browse.tsx"));
module.exports = __webpack_exports__;

})();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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