let questions = [
    {
    numb: 1,
    question: "Regression testing is primarily related to",
    answer: "Functional testing",
    options: [
      "Maintenance testing",
      "Data flow testing",
      "Functional testing",
      "Development testing"
    ]
  },
    {
    numb: 2,
    question: "What is the defect rate for Six sigma",
    answer: "3.4 defects per million lines of code",
    options: [
      "3.0 defects per million lines of code",
      "3.4 defects per million lines of code",
      "1.4 defects per million lines of code",
      "1.0 defect per million lines of code"
    ]
  },
    {
    numb: 3,
    question: "Of the following sort algorithms, which has execution time that is least dependent on initial ordering of the input ?",
    answer: "Merge sort",
    options: [
      "Selection sort",
      "Insertion sort",
      "Quick sort",
      "Merge sort"
    ]
  },
    {
    numb: 4,
    question: "The domain of the function log( log sin(x) ) is",
    answer: "Empty set",
    options: [
      "0 < x < π",
      "Empty set",
      "0 < x < π",
      "2nπ < x < (2n + 1) π , for n in N "
    ]
  },
    
];

