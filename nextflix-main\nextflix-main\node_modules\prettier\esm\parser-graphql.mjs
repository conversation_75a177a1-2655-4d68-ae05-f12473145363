var e=function(e,n){const t=new SyntaxError(e+" ("+n.start.line+":"+n.start.column+")");return t.loc=n,t};var n=function(...e){let n;for(const[t,i]of e.entries())try{return{result:i()}}catch(e){0===t&&(n=e)}return{error:n}};var t={hasPragma:function(e){return/^\s*#[^\S\n]*@(format|prettier)\s*(\n|$)/.test(e)},insertPragma:function(e){return"# @format\n\n"+e}};var i={locStart:function(e){return"number"==typeof e.start?e.start:e.loc&&e.loc.start},locEnd:function(e){return"number"==typeof e.end?e.end:e.loc&&e.loc.end}};function r(e){var n={exports:{}};return e(n,n.exports),n.exports}var o=function(e){return"object"==a(e)&&null!==e};function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var s=Object.defineProperty({default:o},"__esModule",{value:!0}),c=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.SYMBOL_TO_STRING_TAG=n.SYMBOL_ASYNC_ITERATOR=n.SYMBOL_ITERATOR=void 0;var t="function"==typeof Symbol&&null!=Symbol.iterator?Symbol.iterator:"@@iterator";n.SYMBOL_ITERATOR=t;var i="function"==typeof Symbol&&null!=Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator";n.SYMBOL_ASYNC_ITERATOR=i;var r="function"==typeof Symbol&&null!=Symbol.toStringTag?Symbol.toStringTag:"@@toStringTag";n.SYMBOL_TO_STRING_TAG=r})),u=function(e,n){var t,i=/\r\n|[\n\r]/g,r=1,o=n+1;for(;(t=i.exec(e.body))&&t.index<n;)r+=1,o=n+1-(t.index+t[0].length);return{line:r,column:o}};var l=Object.defineProperty({getLocation:u},"__esModule",{value:!0}),p=function(e){return h(e.source,(0,l.getLocation)(e.source,e.start))},d=h;function h(e,n){var t=e.locationOffset.column-1,i=T(t)+e.body,r=n.line-1,o=e.locationOffset.line-1,a=n.line+o,s=1===n.line?t:0,c=n.column+s,u="".concat(e.name,":").concat(a,":").concat(c,"\n"),l=i.split(/\r\n|[\n\r]/g),p=l[r];if(p.length>120){for(var d=Math.floor(c/80),h=c%80,E=[],v=0;v<p.length;v+=80)E.push(p.slice(v,v+80));return u+f([["".concat(a),E[0]]].concat(E.slice(1,d+1).map((function(e){return["",e]})),[[" ",T(h-1)+"^"],["",E[d+1]]]))}return u+f([["".concat(a-1),l[r-1]],["".concat(a),p],["",T(c-1)+"^"],["".concat(a+1),l[r+1]]])}function f(e){var n=e.filter((function(e){return e[0],void 0!==e[1]})),t=Math.max.apply(Math,n.map((function(e){return e[0].length})));return n.map((function(e){var n,i=e[0],r=e[1];return T(t-(n=i).length)+n+(r?" | "+r:" |")})).join("\n")}function T(e){return Array(e+1).join(" ")}var E=Object.defineProperty({printLocation:p,printSourceLocation:d},"__esModule",{value:!0}),v=r((function(e,n){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.printError=k,n.GraphQLError=void 0;var i,r=(i=s)&&i.__esModule?i:{default:i};function o(e,n){for(var t=0;t<n.length;t++){var i=n[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,n){return!n||"object"!==t(n)&&"function"!=typeof n?u(e):n}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e){var n="function"==typeof Map?new Map:void 0;return(p=function(e){if(null===e||(t=e,-1===Function.toString.call(t).indexOf("[native code]")))return e;var t;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(e))return n.get(e);n.set(e,i)}function i(){return d(e,arguments,T(this).constructor)}return i.prototype=Object.create(e.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),f(i,e)})(e)}function d(e,n,t){return(d=h()?Reflect.construct:function(e,n,t){var i=[null];i.push.apply(i,n);var r=new(Function.bind.apply(e,i));return t&&f(r,t.prototype),r}).apply(null,arguments)}function h(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function f(e,n){return(f=Object.setPrototypeOf||function(e,n){return e.__proto__=n,e})(e,n)}function T(e){return(T=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var v=function(e){!function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&f(e,n)}(E,e);var n,t,i,s,p,d=(n=E,t=h(),function(){var e,i=T(n);if(t){var r=T(this).constructor;e=Reflect.construct(i,arguments,r)}else e=i.apply(this,arguments);return a(this,e)});function E(e,n,t,i,o,s,c){var p,h,f,T,v;!function(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}(this,E),v=d.call(this,e);var k,N=Array.isArray(n)?0!==n.length?n:void 0:n?[n]:void 0,y=t;!y&&N&&(y=null===(k=N[0].loc)||void 0===k?void 0:k.source);var _,I=i;!I&&N&&(I=N.reduce((function(e,n){return n.loc&&e.push(n.loc.start),e}),[])),I&&0===I.length&&(I=void 0),i&&t?_=i.map((function(e){return(0,l.getLocation)(t,e)})):N&&(_=N.reduce((function(e,n){return n.loc&&e.push((0,l.getLocation)(n.loc.source,n.loc.start)),e}),[]));var O=c;if(null==O&&null!=s){var m=s.extensions;(0,r.default)(m)&&(O=m)}return Object.defineProperties(u(v),{name:{value:"GraphQLError"},message:{value:e,enumerable:!0,writable:!0},locations:{value:null!==(p=_)&&void 0!==p?p:void 0,enumerable:null!=_},path:{value:null!=o?o:void 0,enumerable:null!=o},nodes:{value:null!=N?N:void 0},source:{value:null!==(h=y)&&void 0!==h?h:void 0},positions:{value:null!==(f=I)&&void 0!==f?f:void 0},originalError:{value:s},extensions:{value:null!==(T=O)&&void 0!==T?T:void 0,enumerable:null!=O}}),null!=s&&s.stack?(Object.defineProperty(u(v),"stack",{value:s.stack,writable:!0,configurable:!0}),a(v)):(Error.captureStackTrace?Error.captureStackTrace(u(v),E):Object.defineProperty(u(v),"stack",{value:Error().stack,writable:!0,configurable:!0}),v)}return i=E,(s=[{key:"toString",value:function(){return k(this)}},{key:c.SYMBOL_TO_STRING_TAG,get:function(){return"Object"}}])&&o(i.prototype,s),p&&o(i,p),E}(p(Error));function k(e){var n=e.message;if(e.nodes)for(var t=0,i=e.nodes;t<i.length;t++){var r=i[t];r.loc&&(n+="\n\n"+(0,E.printLocation)(r.loc))}else if(e.source&&e.locations)for(var o=0,a=e.locations;o<a.length;o++){var s=a[o];n+="\n\n"+(0,E.printSourceLocation)(e.source,s)}return n}n.GraphQLError=v})),k=function(e,n,t){return new v.GraphQLError("Syntax Error: ".concat(t),void 0,e,[n])};var N=Object.defineProperty({syntaxError:k},"__esModule",{value:!0}),y=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.Kind=void 0;var t=Object.freeze({NAME:"Name",DOCUMENT:"Document",OPERATION_DEFINITION:"OperationDefinition",VARIABLE_DEFINITION:"VariableDefinition",SELECTION_SET:"SelectionSet",FIELD:"Field",ARGUMENT:"Argument",FRAGMENT_SPREAD:"FragmentSpread",INLINE_FRAGMENT:"InlineFragment",FRAGMENT_DEFINITION:"FragmentDefinition",VARIABLE:"Variable",INT:"IntValue",FLOAT:"FloatValue",STRING:"StringValue",BOOLEAN:"BooleanValue",NULL:"NullValue",ENUM:"EnumValue",LIST:"ListValue",OBJECT:"ObjectValue",OBJECT_FIELD:"ObjectField",DIRECTIVE:"Directive",NAMED_TYPE:"NamedType",LIST_TYPE:"ListType",NON_NULL_TYPE:"NonNullType",SCHEMA_DEFINITION:"SchemaDefinition",OPERATION_TYPE_DEFINITION:"OperationTypeDefinition",SCALAR_TYPE_DEFINITION:"ScalarTypeDefinition",OBJECT_TYPE_DEFINITION:"ObjectTypeDefinition",FIELD_DEFINITION:"FieldDefinition",INPUT_VALUE_DEFINITION:"InputValueDefinition",INTERFACE_TYPE_DEFINITION:"InterfaceTypeDefinition",UNION_TYPE_DEFINITION:"UnionTypeDefinition",ENUM_TYPE_DEFINITION:"EnumTypeDefinition",ENUM_VALUE_DEFINITION:"EnumValueDefinition",INPUT_OBJECT_TYPE_DEFINITION:"InputObjectTypeDefinition",DIRECTIVE_DEFINITION:"DirectiveDefinition",SCHEMA_EXTENSION:"SchemaExtension",SCALAR_TYPE_EXTENSION:"ScalarTypeExtension",OBJECT_TYPE_EXTENSION:"ObjectTypeExtension",INTERFACE_TYPE_EXTENSION:"InterfaceTypeExtension",UNION_TYPE_EXTENSION:"UnionTypeExtension",ENUM_TYPE_EXTENSION:"EnumTypeExtension",INPUT_OBJECT_TYPE_EXTENSION:"InputObjectTypeExtension"});n.Kind=t})),_=function(e,n){if(!Boolean(e))throw new Error(null!=n?n:"Unexpected invariant triggered.")};var I=Object.defineProperty({default:_},"__esModule",{value:!0}),O=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var t="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):void 0;n.default=t})),m=function(e){var n=e.prototype.toJSON;"function"==typeof n||(0,A.default)(0),e.prototype.inspect=n,x.default&&(e.prototype[x.default]=n)},A=K(I),x=K(O);function K(e){return e&&e.__esModule?e:{default:e}}var D,S=Object.defineProperty({default:m},"__esModule",{value:!0}),b=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.isNode=function(e){return null!=e&&"string"==typeof e.kind},n.Token=n.Location=void 0;var t,i=(t=S)&&t.__esModule?t:{default:t};var r=function(){function e(e,n,t){this.start=e.start,this.end=n.end,this.startToken=e,this.endToken=n,this.source=t}return e.prototype.toJSON=function(){return{start:this.start,end:this.end}},e}();n.Location=r,(0,i.default)(r);var o=function(){function e(e,n,t,i,r,o,a){this.kind=e,this.start=n,this.end=t,this.line=i,this.column=r,this.value=a,this.prev=o,this.next=null}return e.prototype.toJSON=function(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}},e}();n.Token=o,(0,i.default)(o)})),L=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.TokenKind=void 0;var t=Object.freeze({SOF:"<SOF>",EOF:"<EOF>",BANG:"!",DOLLAR:"$",AMP:"&",PAREN_L:"(",PAREN_R:")",SPREAD:"...",COLON:":",EQUALS:"=",AT:"@",BRACKET_L:"[",BRACKET_R:"]",BRACE_L:"{",PIPE:"|",BRACE_R:"}",NAME:"Name",INT:"Int",FLOAT:"Float",STRING:"String",BLOCK_STRING:"BlockString",COMMENT:"Comment"});n.TokenKind=t})),R=function(e){return w(e,[])},g=(D=O)&&D.__esModule?D:{default:D};function C(e){return(C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function w(e,n){switch(C(e)){case"string":return JSON.stringify(e);case"function":return e.name?"[function ".concat(e.name,"]"):"[function]";case"object":return null===e?"null":function(e,n){if(-1!==n.indexOf(e))return"[Circular]";var t=[].concat(n,[e]),i=function(e){var n=e[String(g.default)];if("function"==typeof n)return n;if("function"==typeof e.inspect)return e.inspect}(e);if(void 0!==i){var r=i.call(e);if(r!==e)return"string"==typeof r?r:w(r,t)}else if(Array.isArray(e))return function(e,n){if(0===e.length)return"[]";if(n.length>2)return"[Array]";for(var t=Math.min(10,e.length),i=e.length-t,r=[],o=0;o<t;++o)r.push(w(e[o],n));1===i?r.push("... 1 more item"):i>1&&r.push("... ".concat(i," more items"));return"["+r.join(", ")+"]"}(e,t);return function(e,n){var t=Object.keys(e);if(0===t.length)return"{}";if(n.length>2)return"["+function(e){var n=Object.prototype.toString.call(e).replace(/^\[object /,"").replace(/]$/,"");if("Object"===n&&"function"==typeof e.constructor){var t=e.constructor.name;if("string"==typeof t&&""!==t)return t}return n}(e)+"]";return"{ "+t.map((function(t){return t+": "+w(e[t],n)})).join(", ")+" }"}(e,t)}(e,n);default:return String(e)}}var F=Object.defineProperty({default:R},"__esModule",{value:!0}),P=function(e,n){if(!Boolean(e))throw new Error(n)};var M=Object.defineProperty({default:P},"__esModule",{value:!0}),B=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default=function(e,n){return e instanceof n}})),U=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.isSource=function(e){return(0,r.default)(e,s)},n.Source=void 0;var t=o(F),i=o(M),r=o(B);function o(e){return e&&e.__esModule?e:{default:e}}function a(e,n){for(var t=0;t<n.length;t++){var i=n[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var s=function(){function e(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GraphQL request",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{line:1,column:1};"string"==typeof e||(0,i.default)(0,"Body must be a string. Received: ".concat((0,t.default)(e),".")),this.body=e,this.name=n,this.locationOffset=r,this.locationOffset.line>0||(0,i.default)(0,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||(0,i.default)(0,"column in locationOffset is 1-indexed and must be positive.")}var n,r,o;return n=e,(r=[{key:c.SYMBOL_TO_STRING_TAG,get:function(){return"Source"}}])&&a(n.prototype,r),o&&a(n,o),e}();n.Source=s})),j=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.DirectiveLocation=void 0;var t=Object.freeze({QUERY:"QUERY",MUTATION:"MUTATION",SUBSCRIPTION:"SUBSCRIPTION",FIELD:"FIELD",FRAGMENT_DEFINITION:"FRAGMENT_DEFINITION",FRAGMENT_SPREAD:"FRAGMENT_SPREAD",INLINE_FRAGMENT:"INLINE_FRAGMENT",VARIABLE_DEFINITION:"VARIABLE_DEFINITION",SCHEMA:"SCHEMA",SCALAR:"SCALAR",OBJECT:"OBJECT",FIELD_DEFINITION:"FIELD_DEFINITION",ARGUMENT_DEFINITION:"ARGUMENT_DEFINITION",INTERFACE:"INTERFACE",UNION:"UNION",ENUM:"ENUM",ENUM_VALUE:"ENUM_VALUE",INPUT_OBJECT:"INPUT_OBJECT",INPUT_FIELD_DEFINITION:"INPUT_FIELD_DEFINITION"});n.DirectiveLocation=t})),V=function(e){var n=e.split(/\r\n|[\n\r]/g),t=Q(e);if(0!==t)for(var i=1;i<n.length;i++)n[i]=n[i].slice(t);var r=0;for(;r<n.length&&J(n[r]);)++r;var o=n.length;for(;o>r&&J(n[o-1]);)--o;return n.slice(r,o).join("\n")},G=Q,Y=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=-1===e.indexOf("\n"),r=" "===e[0]||"\t"===e[0],o='"'===e[e.length-1],a="\\"===e[e.length-1],s=!i||o||a||t,c="";!s||i&&r||(c+="\n"+n);c+=n?e.replace(/\n/g,"\n"+n):e,s&&(c+="\n");return'"""'+c.replace(/"""/g,'\\"""')+'"""'};function J(e){for(var n=0;n<e.length;++n)if(" "!==e[n]&&"\t"!==e[n])return!1;return!0}function Q(e){for(var n,t=!0,i=!0,r=0,o=null,a=0;a<e.length;++a)switch(e.charCodeAt(a)){case 13:10===e.charCodeAt(a+1)&&++a;case 10:t=!1,i=!0,r=0;break;case 9:case 32:++r;break;default:i&&!t&&(null===o||r<o)&&(o=r),i=!1}return null!==(n=o)&&void 0!==n?n:0}var X=Object.defineProperty({dedentBlockStringValue:V,getBlockStringIndentation:G,printBlockString:Y},"__esModule",{value:!0}),q=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.isPunctuatorTokenKind=function(e){return e===L.TokenKind.BANG||e===L.TokenKind.DOLLAR||e===L.TokenKind.AMP||e===L.TokenKind.PAREN_L||e===L.TokenKind.PAREN_R||e===L.TokenKind.SPREAD||e===L.TokenKind.COLON||e===L.TokenKind.EQUALS||e===L.TokenKind.AT||e===L.TokenKind.BRACKET_L||e===L.TokenKind.BRACKET_R||e===L.TokenKind.BRACE_L||e===L.TokenKind.PIPE||e===L.TokenKind.BRACE_R},n.Lexer=void 0;var t=function(){function e(e){var n=new b.Token(L.TokenKind.SOF,0,0,0,0,null);this.source=e,this.lastToken=n,this.token=n,this.line=1,this.lineStart=0}var n=e.prototype;return n.advance=function(){return this.lastToken=this.token,this.token=this.lookahead()},n.lookahead=function(){var e=this.token;if(e.kind!==L.TokenKind.EOF)do{var n;e=null!==(n=e.next)&&void 0!==n?n:e.next=r(this,e)}while(e.kind===L.TokenKind.COMMENT);return e},e}();function i(e){return isNaN(e)?L.TokenKind.EOF:e<127?JSON.stringify(String.fromCharCode(e)):'"\\u'.concat(("00"+e.toString(16).toUpperCase()).slice(-4),'"')}function r(e,n){for(var t=e.source,i=t.body,r=i.length,c=n.end;c<r;){var p=i.charCodeAt(c),h=e.line,f=1+c-e.lineStart;switch(p){case 65279:case 9:case 32:case 44:++c;continue;case 10:++c,++e.line,e.lineStart=c;continue;case 13:10===i.charCodeAt(c+1)?c+=2:++c,++e.line,e.lineStart=c;continue;case 33:return new b.Token(L.TokenKind.BANG,c,c+1,h,f,n);case 35:return a(t,c,h,f,n);case 36:return new b.Token(L.TokenKind.DOLLAR,c,c+1,h,f,n);case 38:return new b.Token(L.TokenKind.AMP,c,c+1,h,f,n);case 40:return new b.Token(L.TokenKind.PAREN_L,c,c+1,h,f,n);case 41:return new b.Token(L.TokenKind.PAREN_R,c,c+1,h,f,n);case 46:if(46===i.charCodeAt(c+1)&&46===i.charCodeAt(c+2))return new b.Token(L.TokenKind.SPREAD,c,c+3,h,f,n);break;case 58:return new b.Token(L.TokenKind.COLON,c,c+1,h,f,n);case 61:return new b.Token(L.TokenKind.EQUALS,c,c+1,h,f,n);case 64:return new b.Token(L.TokenKind.AT,c,c+1,h,f,n);case 91:return new b.Token(L.TokenKind.BRACKET_L,c,c+1,h,f,n);case 93:return new b.Token(L.TokenKind.BRACKET_R,c,c+1,h,f,n);case 123:return new b.Token(L.TokenKind.BRACE_L,c,c+1,h,f,n);case 124:return new b.Token(L.TokenKind.PIPE,c,c+1,h,f,n);case 125:return new b.Token(L.TokenKind.BRACE_R,c,c+1,h,f,n);case 34:return 34===i.charCodeAt(c+1)&&34===i.charCodeAt(c+2)?l(t,c,h,f,n,e):u(t,c,h,f,n);case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return s(t,c,p,h,f,n);case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 95:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:return d(t,c,h,f,n)}throw(0,N.syntaxError)(t,c,o(p))}var T=e.line,E=1+c-e.lineStart;return new b.Token(L.TokenKind.EOF,r,r,T,E,n)}function o(e){return e<32&&9!==e&&10!==e&&13!==e?"Cannot contain the invalid character ".concat(i(e),"."):39===e?"Unexpected single quote character ('), did you mean to use a double quote (\")?":"Cannot parse the unexpected character ".concat(i(e),".")}function a(e,n,t,i,r){var o,a=e.body,s=n;do{o=a.charCodeAt(++s)}while(!isNaN(o)&&(o>31||9===o));return new b.Token(L.TokenKind.COMMENT,n,s,t,i,r,a.slice(n+1,s))}function s(e,n,t,r,o,a){var s=e.body,u=t,l=n,p=!1;if(45===u&&(u=s.charCodeAt(++l)),48===u){if((u=s.charCodeAt(++l))>=48&&u<=57)throw(0,N.syntaxError)(e,l,"Invalid number, unexpected digit after 0: ".concat(i(u),"."))}else l=c(e,l,u),u=s.charCodeAt(l);if(46===u&&(p=!0,u=s.charCodeAt(++l),l=c(e,l,u),u=s.charCodeAt(l)),69!==u&&101!==u||(p=!0,43!==(u=s.charCodeAt(++l))&&45!==u||(u=s.charCodeAt(++l)),l=c(e,l,u),u=s.charCodeAt(l)),46===u||function(e){return 95===e||e>=65&&e<=90||e>=97&&e<=122}(u))throw(0,N.syntaxError)(e,l,"Invalid number, expected digit but got: ".concat(i(u),"."));return new b.Token(p?L.TokenKind.FLOAT:L.TokenKind.INT,n,l,r,o,a,s.slice(n,l))}function c(e,n,t){var r=e.body,o=n,a=t;if(a>=48&&a<=57){do{a=r.charCodeAt(++o)}while(a>=48&&a<=57);return o}throw(0,N.syntaxError)(e,o,"Invalid number, expected digit but got: ".concat(i(a),"."))}function u(e,n,t,r,o){for(var a,s,c,u,l=e.body,d=n+1,h=d,f=0,T="";d<l.length&&!isNaN(f=l.charCodeAt(d))&&10!==f&&13!==f;){if(34===f)return T+=l.slice(h,d),new b.Token(L.TokenKind.STRING,n,d+1,t,r,o,T);if(f<32&&9!==f)throw(0,N.syntaxError)(e,d,"Invalid character within String: ".concat(i(f),"."));if(++d,92===f){switch(T+=l.slice(h,d-1),f=l.charCodeAt(d)){case 34:T+='"';break;case 47:T+="/";break;case 92:T+="\\";break;case 98:T+="\b";break;case 102:T+="\f";break;case 110:T+="\n";break;case 114:T+="\r";break;case 116:T+="\t";break;case 117:var E=(a=l.charCodeAt(d+1),s=l.charCodeAt(d+2),c=l.charCodeAt(d+3),u=l.charCodeAt(d+4),p(a)<<12|p(s)<<8|p(c)<<4|p(u));if(E<0){var v=l.slice(d+1,d+5);throw(0,N.syntaxError)(e,d,"Invalid character escape sequence: \\u".concat(v,"."))}T+=String.fromCharCode(E),d+=4;break;default:throw(0,N.syntaxError)(e,d,"Invalid character escape sequence: \\".concat(String.fromCharCode(f),"."))}h=++d}}throw(0,N.syntaxError)(e,d,"Unterminated string.")}function l(e,n,t,r,o,a){for(var s=e.body,c=n+3,u=c,l=0,p="";c<s.length&&!isNaN(l=s.charCodeAt(c));){if(34===l&&34===s.charCodeAt(c+1)&&34===s.charCodeAt(c+2))return p+=s.slice(u,c),new b.Token(L.TokenKind.BLOCK_STRING,n,c+3,t,r,o,(0,X.dedentBlockStringValue)(p));if(l<32&&9!==l&&10!==l&&13!==l)throw(0,N.syntaxError)(e,c,"Invalid character within String: ".concat(i(l),"."));10===l?(++c,++a.line,a.lineStart=c):13===l?(10===s.charCodeAt(c+1)?c+=2:++c,++a.line,a.lineStart=c):92===l&&34===s.charCodeAt(c+1)&&34===s.charCodeAt(c+2)&&34===s.charCodeAt(c+3)?(p+=s.slice(u,c)+'"""',u=c+=4):++c}throw(0,N.syntaxError)(e,c,"Unterminated string.")}function p(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:-1}function d(e,n,t,i,r){for(var o=e.body,a=o.length,s=n+1,c=0;s!==a&&!isNaN(c=o.charCodeAt(s))&&(95===c||c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122);)++s;return new b.Token(L.TokenKind.NAME,n,s,t,i,r,o.slice(n,s))}n.Lexer=t})),H=r((function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.parse=function(e,n){return new t(e,n).parseDocument()},n.parseValue=function(e,n){var i=new t(e,n);i.expectToken(L.TokenKind.SOF);var r=i.parseValueLiteral(!1);return i.expectToken(L.TokenKind.EOF),r},n.parseType=function(e,n){var i=new t(e,n);i.expectToken(L.TokenKind.SOF);var r=i.parseTypeReference();return i.expectToken(L.TokenKind.EOF),r},n.Parser=void 0;var t=function(){function e(e,n){var t=(0,U.isSource)(e)?e:new U.Source(e);this._lexer=new q.Lexer(t),this._options=n}var n=e.prototype;return n.parseName=function(){var e=this.expectToken(L.TokenKind.NAME);return{kind:y.Kind.NAME,value:e.value,loc:this.loc(e)}},n.parseDocument=function(){var e=this._lexer.token;return{kind:y.Kind.DOCUMENT,definitions:this.many(L.TokenKind.SOF,this.parseDefinition,L.TokenKind.EOF),loc:this.loc(e)}},n.parseDefinition=function(){if(this.peek(L.TokenKind.NAME))switch(this._lexer.token.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"schema":case"scalar":case"type":case"interface":case"union":case"enum":case"input":case"directive":return this.parseTypeSystemDefinition();case"extend":return this.parseTypeSystemExtension()}else{if(this.peek(L.TokenKind.BRACE_L))return this.parseOperationDefinition();if(this.peekDescription())return this.parseTypeSystemDefinition()}throw this.unexpected()},n.parseOperationDefinition=function(){var e=this._lexer.token;if(this.peek(L.TokenKind.BRACE_L))return{kind:y.Kind.OPERATION_DEFINITION,operation:"query",name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet(),loc:this.loc(e)};var n,t=this.parseOperationType();return this.peek(L.TokenKind.NAME)&&(n=this.parseName()),{kind:y.Kind.OPERATION_DEFINITION,operation:t,name:n,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},n.parseOperationType=function(){var e=this.expectToken(L.TokenKind.NAME);switch(e.value){case"query":return"query";case"mutation":return"mutation";case"subscription":return"subscription"}throw this.unexpected(e)},n.parseVariableDefinitions=function(){return this.optionalMany(L.TokenKind.PAREN_L,this.parseVariableDefinition,L.TokenKind.PAREN_R)},n.parseVariableDefinition=function(){var e=this._lexer.token;return{kind:y.Kind.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(L.TokenKind.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(L.TokenKind.EQUALS)?this.parseValueLiteral(!0):void 0,directives:this.parseDirectives(!0),loc:this.loc(e)}},n.parseVariable=function(){var e=this._lexer.token;return this.expectToken(L.TokenKind.DOLLAR),{kind:y.Kind.VARIABLE,name:this.parseName(),loc:this.loc(e)}},n.parseSelectionSet=function(){var e=this._lexer.token;return{kind:y.Kind.SELECTION_SET,selections:this.many(L.TokenKind.BRACE_L,this.parseSelection,L.TokenKind.BRACE_R),loc:this.loc(e)}},n.parseSelection=function(){return this.peek(L.TokenKind.SPREAD)?this.parseFragment():this.parseField()},n.parseField=function(){var e,n,t=this._lexer.token,i=this.parseName();return this.expectOptionalToken(L.TokenKind.COLON)?(e=i,n=this.parseName()):n=i,{kind:y.Kind.FIELD,alias:e,name:n,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(L.TokenKind.BRACE_L)?this.parseSelectionSet():void 0,loc:this.loc(t)}},n.parseArguments=function(e){var n=e?this.parseConstArgument:this.parseArgument;return this.optionalMany(L.TokenKind.PAREN_L,n,L.TokenKind.PAREN_R)},n.parseArgument=function(){var e=this._lexer.token,n=this.parseName();return this.expectToken(L.TokenKind.COLON),{kind:y.Kind.ARGUMENT,name:n,value:this.parseValueLiteral(!1),loc:this.loc(e)}},n.parseConstArgument=function(){var e=this._lexer.token;return{kind:y.Kind.ARGUMENT,name:this.parseName(),value:(this.expectToken(L.TokenKind.COLON),this.parseValueLiteral(!0)),loc:this.loc(e)}},n.parseFragment=function(){var e=this._lexer.token;this.expectToken(L.TokenKind.SPREAD);var n=this.expectOptionalKeyword("on");return!n&&this.peek(L.TokenKind.NAME)?{kind:y.Kind.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1),loc:this.loc(e)}:{kind:y.Kind.INLINE_FRAGMENT,typeCondition:n?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(e)}},n.parseFragmentDefinition=function(){var e,n=this._lexer.token;return this.expectKeyword("fragment"),!0===(null===(e=this._options)||void 0===e?void 0:e.experimentalFragmentVariables)?{kind:y.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(n)}:{kind:y.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet(),loc:this.loc(n)}},n.parseFragmentName=function(){if("on"===this._lexer.token.value)throw this.unexpected();return this.parseName()},n.parseValueLiteral=function(e){var n=this._lexer.token;switch(n.kind){case L.TokenKind.BRACKET_L:return this.parseList(e);case L.TokenKind.BRACE_L:return this.parseObject(e);case L.TokenKind.INT:return this._lexer.advance(),{kind:y.Kind.INT,value:n.value,loc:this.loc(n)};case L.TokenKind.FLOAT:return this._lexer.advance(),{kind:y.Kind.FLOAT,value:n.value,loc:this.loc(n)};case L.TokenKind.STRING:case L.TokenKind.BLOCK_STRING:return this.parseStringLiteral();case L.TokenKind.NAME:switch(this._lexer.advance(),n.value){case"true":return{kind:y.Kind.BOOLEAN,value:!0,loc:this.loc(n)};case"false":return{kind:y.Kind.BOOLEAN,value:!1,loc:this.loc(n)};case"null":return{kind:y.Kind.NULL,loc:this.loc(n)};default:return{kind:y.Kind.ENUM,value:n.value,loc:this.loc(n)}}case L.TokenKind.DOLLAR:if(!e)return this.parseVariable()}throw this.unexpected()},n.parseStringLiteral=function(){var e=this._lexer.token;return this._lexer.advance(),{kind:y.Kind.STRING,value:e.value,block:e.kind===L.TokenKind.BLOCK_STRING,loc:this.loc(e)}},n.parseList=function(e){var n=this,t=this._lexer.token;return{kind:y.Kind.LIST,values:this.any(L.TokenKind.BRACKET_L,(function(){return n.parseValueLiteral(e)}),L.TokenKind.BRACKET_R),loc:this.loc(t)}},n.parseObject=function(e){var n=this,t=this._lexer.token;return{kind:y.Kind.OBJECT,fields:this.any(L.TokenKind.BRACE_L,(function(){return n.parseObjectField(e)}),L.TokenKind.BRACE_R),loc:this.loc(t)}},n.parseObjectField=function(e){var n=this._lexer.token,t=this.parseName();return this.expectToken(L.TokenKind.COLON),{kind:y.Kind.OBJECT_FIELD,name:t,value:this.parseValueLiteral(e),loc:this.loc(n)}},n.parseDirectives=function(e){for(var n=[];this.peek(L.TokenKind.AT);)n.push(this.parseDirective(e));return n},n.parseDirective=function(e){var n=this._lexer.token;return this.expectToken(L.TokenKind.AT),{kind:y.Kind.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(e),loc:this.loc(n)}},n.parseTypeReference=function(){var e,n=this._lexer.token;return this.expectOptionalToken(L.TokenKind.BRACKET_L)?(e=this.parseTypeReference(),this.expectToken(L.TokenKind.BRACKET_R),e={kind:y.Kind.LIST_TYPE,type:e,loc:this.loc(n)}):e=this.parseNamedType(),this.expectOptionalToken(L.TokenKind.BANG)?{kind:y.Kind.NON_NULL_TYPE,type:e,loc:this.loc(n)}:e},n.parseNamedType=function(){var e=this._lexer.token;return{kind:y.Kind.NAMED_TYPE,name:this.parseName(),loc:this.loc(e)}},n.parseTypeSystemDefinition=function(){var e=this.peekDescription()?this._lexer.lookahead():this._lexer.token;if(e.kind===L.TokenKind.NAME)switch(e.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}throw this.unexpected(e)},n.peekDescription=function(){return this.peek(L.TokenKind.STRING)||this.peek(L.TokenKind.BLOCK_STRING)},n.parseDescription=function(){if(this.peekDescription())return this.parseStringLiteral()},n.parseSchemaDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("schema");var t=this.parseDirectives(!0),i=this.many(L.TokenKind.BRACE_L,this.parseOperationTypeDefinition,L.TokenKind.BRACE_R);return{kind:y.Kind.SCHEMA_DEFINITION,description:n,directives:t,operationTypes:i,loc:this.loc(e)}},n.parseOperationTypeDefinition=function(){var e=this._lexer.token,n=this.parseOperationType();this.expectToken(L.TokenKind.COLON);var t=this.parseNamedType();return{kind:y.Kind.OPERATION_TYPE_DEFINITION,operation:n,type:t,loc:this.loc(e)}},n.parseScalarTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("scalar");var t=this.parseName(),i=this.parseDirectives(!0);return{kind:y.Kind.SCALAR_TYPE_DEFINITION,description:n,name:t,directives:i,loc:this.loc(e)}},n.parseObjectTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("type");var t=this.parseName(),i=this.parseImplementsInterfaces(),r=this.parseDirectives(!0),o=this.parseFieldsDefinition();return{kind:y.Kind.OBJECT_TYPE_DEFINITION,description:n,name:t,interfaces:i,directives:r,fields:o,loc:this.loc(e)}},n.parseImplementsInterfaces=function(){var e;if(!this.expectOptionalKeyword("implements"))return[];if(!0===(null===(e=this._options)||void 0===e?void 0:e.allowLegacySDLImplementsInterfaces)){var n=[];this.expectOptionalToken(L.TokenKind.AMP);do{n.push(this.parseNamedType())}while(this.expectOptionalToken(L.TokenKind.AMP)||this.peek(L.TokenKind.NAME));return n}return this.delimitedMany(L.TokenKind.AMP,this.parseNamedType)},n.parseFieldsDefinition=function(){var e;return!0===(null===(e=this._options)||void 0===e?void 0:e.allowLegacySDLEmptyFields)&&this.peek(L.TokenKind.BRACE_L)&&this._lexer.lookahead().kind===L.TokenKind.BRACE_R?(this._lexer.advance(),this._lexer.advance(),[]):this.optionalMany(L.TokenKind.BRACE_L,this.parseFieldDefinition,L.TokenKind.BRACE_R)},n.parseFieldDefinition=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName(),i=this.parseArgumentDefs();this.expectToken(L.TokenKind.COLON);var r=this.parseTypeReference(),o=this.parseDirectives(!0);return{kind:y.Kind.FIELD_DEFINITION,description:n,name:t,arguments:i,type:r,directives:o,loc:this.loc(e)}},n.parseArgumentDefs=function(){return this.optionalMany(L.TokenKind.PAREN_L,this.parseInputValueDef,L.TokenKind.PAREN_R)},n.parseInputValueDef=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName();this.expectToken(L.TokenKind.COLON);var i,r=this.parseTypeReference();this.expectOptionalToken(L.TokenKind.EQUALS)&&(i=this.parseValueLiteral(!0));var o=this.parseDirectives(!0);return{kind:y.Kind.INPUT_VALUE_DEFINITION,description:n,name:t,type:r,defaultValue:i,directives:o,loc:this.loc(e)}},n.parseInterfaceTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("interface");var t=this.parseName(),i=this.parseImplementsInterfaces(),r=this.parseDirectives(!0),o=this.parseFieldsDefinition();return{kind:y.Kind.INTERFACE_TYPE_DEFINITION,description:n,name:t,interfaces:i,directives:r,fields:o,loc:this.loc(e)}},n.parseUnionTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("union");var t=this.parseName(),i=this.parseDirectives(!0),r=this.parseUnionMemberTypes();return{kind:y.Kind.UNION_TYPE_DEFINITION,description:n,name:t,directives:i,types:r,loc:this.loc(e)}},n.parseUnionMemberTypes=function(){return this.expectOptionalToken(L.TokenKind.EQUALS)?this.delimitedMany(L.TokenKind.PIPE,this.parseNamedType):[]},n.parseEnumTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("enum");var t=this.parseName(),i=this.parseDirectives(!0),r=this.parseEnumValuesDefinition();return{kind:y.Kind.ENUM_TYPE_DEFINITION,description:n,name:t,directives:i,values:r,loc:this.loc(e)}},n.parseEnumValuesDefinition=function(){return this.optionalMany(L.TokenKind.BRACE_L,this.parseEnumValueDefinition,L.TokenKind.BRACE_R)},n.parseEnumValueDefinition=function(){var e=this._lexer.token,n=this.parseDescription(),t=this.parseName(),i=this.parseDirectives(!0);return{kind:y.Kind.ENUM_VALUE_DEFINITION,description:n,name:t,directives:i,loc:this.loc(e)}},n.parseInputObjectTypeDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("input");var t=this.parseName(),i=this.parseDirectives(!0),r=this.parseInputFieldsDefinition();return{kind:y.Kind.INPUT_OBJECT_TYPE_DEFINITION,description:n,name:t,directives:i,fields:r,loc:this.loc(e)}},n.parseInputFieldsDefinition=function(){return this.optionalMany(L.TokenKind.BRACE_L,this.parseInputValueDef,L.TokenKind.BRACE_R)},n.parseTypeSystemExtension=function(){var e=this._lexer.lookahead();if(e.kind===L.TokenKind.NAME)switch(e.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(e)},n.parseSchemaExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");var n=this.parseDirectives(!0),t=this.optionalMany(L.TokenKind.BRACE_L,this.parseOperationTypeDefinition,L.TokenKind.BRACE_R);if(0===n.length&&0===t.length)throw this.unexpected();return{kind:y.Kind.SCHEMA_EXTENSION,directives:n,operationTypes:t,loc:this.loc(e)}},n.parseScalarTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");var n=this.parseName(),t=this.parseDirectives(!0);if(0===t.length)throw this.unexpected();return{kind:y.Kind.SCALAR_TYPE_EXTENSION,name:n,directives:t,loc:this.loc(e)}},n.parseObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");var n=this.parseName(),t=this.parseImplementsInterfaces(),i=this.parseDirectives(!0),r=this.parseFieldsDefinition();if(0===t.length&&0===i.length&&0===r.length)throw this.unexpected();return{kind:y.Kind.OBJECT_TYPE_EXTENSION,name:n,interfaces:t,directives:i,fields:r,loc:this.loc(e)}},n.parseInterfaceTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");var n=this.parseName(),t=this.parseImplementsInterfaces(),i=this.parseDirectives(!0),r=this.parseFieldsDefinition();if(0===t.length&&0===i.length&&0===r.length)throw this.unexpected();return{kind:y.Kind.INTERFACE_TYPE_EXTENSION,name:n,interfaces:t,directives:i,fields:r,loc:this.loc(e)}},n.parseUnionTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");var n=this.parseName(),t=this.parseDirectives(!0),i=this.parseUnionMemberTypes();if(0===t.length&&0===i.length)throw this.unexpected();return{kind:y.Kind.UNION_TYPE_EXTENSION,name:n,directives:t,types:i,loc:this.loc(e)}},n.parseEnumTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");var n=this.parseName(),t=this.parseDirectives(!0),i=this.parseEnumValuesDefinition();if(0===t.length&&0===i.length)throw this.unexpected();return{kind:y.Kind.ENUM_TYPE_EXTENSION,name:n,directives:t,values:i,loc:this.loc(e)}},n.parseInputObjectTypeExtension=function(){var e=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");var n=this.parseName(),t=this.parseDirectives(!0),i=this.parseInputFieldsDefinition();if(0===t.length&&0===i.length)throw this.unexpected();return{kind:y.Kind.INPUT_OBJECT_TYPE_EXTENSION,name:n,directives:t,fields:i,loc:this.loc(e)}},n.parseDirectiveDefinition=function(){var e=this._lexer.token,n=this.parseDescription();this.expectKeyword("directive"),this.expectToken(L.TokenKind.AT);var t=this.parseName(),i=this.parseArgumentDefs(),r=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");var o=this.parseDirectiveLocations();return{kind:y.Kind.DIRECTIVE_DEFINITION,description:n,name:t,arguments:i,repeatable:r,locations:o,loc:this.loc(e)}},n.parseDirectiveLocations=function(){return this.delimitedMany(L.TokenKind.PIPE,this.parseDirectiveLocation)},n.parseDirectiveLocation=function(){var e=this._lexer.token,n=this.parseName();if(void 0!==j.DirectiveLocation[n.value])return n;throw this.unexpected(e)},n.loc=function(e){var n;if(!0!==(null===(n=this._options)||void 0===n?void 0:n.noLocation))return new b.Location(e,this._lexer.lastToken,this._lexer.source)},n.peek=function(e){return this._lexer.token.kind===e},n.expectToken=function(e){var n=this._lexer.token;if(n.kind===e)return this._lexer.advance(),n;throw(0,N.syntaxError)(this._lexer.source,n.start,"Expected ".concat(r(e),", found ").concat(i(n),"."))},n.expectOptionalToken=function(e){var n=this._lexer.token;if(n.kind===e)return this._lexer.advance(),n},n.expectKeyword=function(e){var n=this._lexer.token;if(n.kind!==L.TokenKind.NAME||n.value!==e)throw(0,N.syntaxError)(this._lexer.source,n.start,'Expected "'.concat(e,'", found ').concat(i(n),"."));this._lexer.advance()},n.expectOptionalKeyword=function(e){var n=this._lexer.token;return n.kind===L.TokenKind.NAME&&n.value===e&&(this._lexer.advance(),!0)},n.unexpected=function(e){var n=null!=e?e:this._lexer.token;return(0,N.syntaxError)(this._lexer.source,n.start,"Unexpected ".concat(i(n),"."))},n.any=function(e,n,t){this.expectToken(e);for(var i=[];!this.expectOptionalToken(t);)i.push(n.call(this));return i},n.optionalMany=function(e,n,t){if(this.expectOptionalToken(e)){var i=[];do{i.push(n.call(this))}while(!this.expectOptionalToken(t));return i}return[]},n.many=function(e,n,t){this.expectToken(e);var i=[];do{i.push(n.call(this))}while(!this.expectOptionalToken(t));return i},n.delimitedMany=function(e,n){this.expectOptionalToken(e);var t=[];do{t.push(n.call(this))}while(this.expectOptionalToken(e));return t},e}();function i(e){var n=e.value;return r(e.kind)+(null!=n?' "'.concat(n,'"'):"")}function r(e){return(0,q.isPunctuatorTokenKind)(e)?'"'.concat(e,'"'):e}n.Parser=t}));const{hasPragma:z}=t,{locStart:$,locEnd:W}=i;function Z(e){if(e&&"object"==typeof e){delete e.startToken,delete e.endToken,delete e.prev,delete e.next;for(const n in e)Z(e[n])}return e}const ee={allowLegacySDLImplementsInterfaces:!1,experimentalFragmentVariables:!0};var ne={parsers:{graphql:{parse:function(t){const{parse:i}=H,{result:r,error:o}=n((()=>i(t,Object.assign({},ee))),(()=>i(t,Object.assign(Object.assign({},ee),{},{allowLegacySDLImplementsInterfaces:!0}))));if(!r)throw function(n){const{GraphQLError:t}=v;if(n instanceof t){const{message:t,locations:[i]}=n;return e(t,{start:i})}return n}(o);return r.comments=function(e){const n=[],{startToken:t}=e.loc;let{next:i}=t;for(;"<EOF>"!==i.kind;)"Comment"===i.kind&&(Object.assign(i,{column:i.column-1}),n.push(i)),i=i.next;return n}(r),Z(r),r},astFormat:"graphql",hasPragma:z,locStart:$,locEnd:W}}};export default ne;
