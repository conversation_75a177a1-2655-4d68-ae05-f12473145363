<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark/Light Theme Toggle</title>
    <style>
        /* Default Light Theme */
        :root {
            --background-color: #ffffff;
            --text-color: #000000;
        }

        /* Dark Theme */
        .dark-mode {
            --background-color: #121212;
            --text-color: #ffffff;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: Arial, sans-serif;
            transition: background-color 0.3s, color 0.3s;
        }

        .theme-toggle-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            cursor: pointer;
            background-color: var(--text-color);
            color: var(--background-color);
            border: none;
            border-radius: 5px;
            transition: background-color 0.3s, color 0.3s;
        }
    </style>
</head>
<body>

    <h1>Dark/Light Theme Toggle</h1>
    <p>This is an example of switching between dark and light modes using a toggle button.</p>
    <button class="theme-toggle-btn" id="theme-toggle">Toggle Theme</button>

    <script>
        const toggleButton = document.getElementById('theme-toggle');
        const body = document.body;

        toggleButton.addEventListener('click', () => {
            body.classList.toggle('dark-mode');
            updateButtonText();
        });

        function updateButtonText() {
            if (body.classList.contains('dark-mode')) {
                toggleButton.textContent = 'Switch to Light Mode';
            } else {
                toggleButton.textContent = 'Switch to Dark Mode';
            }
        }

        // Initialize button text
        updateButtonText();
    </script>

</body>
</html>
