/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Netflix Sans', 'Helvetica Neue', Arial, sans-serif;
    background-color: #141414;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: linear-gradient(180deg, rgba(0,0,0,0.7) 10%, transparent);
    transition: background-color 0.4s ease;
}

.header.scrolled {
    background-color: #141414;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 60px;
    max-width: 1920px;
    margin: 0 auto;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 40px;
}

.logo h1 {
    color: #e50914;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: -1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 25px;
}

.nav-menu a {
    color: #ffffff;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #b3b3b3;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    font-size: 18px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.search-input {
    background: transparent;
    border: 1px solid #ffffff;
    color: #ffffff;
    padding: 8px 12px;
    margin-left: 10px;
    border-radius: 4px;
    font-size: 14px;
    width: 0;
    opacity: 0;
    transition: all 0.3s ease;
}

.search-container:hover .search-input,
.search-input:focus {
    width: 200px;
    opacity: 1;
    outline: none;
}

.notification-icon,
.profile-img {
    font-size: 18px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.profile-dropdown {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.profile-img {
    width: 32px;
    height: 32px;
    border-radius: 4px;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                url('https://via.placeholder.com/1920x1080/8B0000/FFFFFF?text=STRANGER+THINGS');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    padding: 0 60px;
}

.hero-content {
    max-width: 600px;
    z-index: 2;
}

.hero-title {
    font-size: 64px;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.hero-description {
    font-size: 18px;
    line-height: 1.5;
    margin-bottom: 30px;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

.hero-buttons {
    display: flex;
    gap: 15px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-play {
    background-color: #ffffff;
    color: #000000;
}

.btn-play:hover {
    background-color: rgba(255,255,255,0.8);
    transform: scale(1.05);
}

.btn-info {
    background-color: rgba(109,109,110,0.7);
    color: #ffffff;
}

.btn-info:hover {
    background-color: rgba(109,109,110,0.5);
    transform: scale(1.05);
}

.btn-secondary {
    background-color: rgba(42,42,42,0.8);
    color: #ffffff;
    border: 1px solid #ffffff;
}

.btn-secondary:hover {
    background-color: rgba(255,255,255,0.1);
}

.hero-fade {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(transparent, #141414);
}

/* Main Content */
.main-content {
    padding: 0 60px;
    margin-top: -100px;
    position: relative;
    z-index: 3;
}

.content-row {
    margin-bottom: 40px;
}

.row-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #ffffff;
}

.movie-slider {
    position: relative;
    overflow: hidden;
}

.movie-list {
    display: flex;
    gap: 8px;
    transition: transform 0.5s ease;
    padding: 10px 0;
}

.movie-card {
    min-width: 200px;
    height: 300px;
    background-size: cover;
    background-position: center;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.movie-card:hover {
    transform: scale(1.05);
    z-index: 10;
}

.movie-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
}

.movie-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    z-index: 2;
}

.movie-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #ffffff;
}

.movie-rating {
    font-size: 12px;
    color: #46d369;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: #181818;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    position: relative;
    animation: slideIn 0.3s ease;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 10;
    cursor: pointer;
}

.close:hover {
    color: #e50914;
}

.modal-header {
    padding: 40px 40px 20px;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                url('https://via.placeholder.com/800x400/8B0000/FFFFFF?text=MOVIE+PREVIEW');
    background-size: cover;
    background-position: center;
    border-radius: 8px 8px 0 0;
}

.modal-header h2 {
    font-size: 32px;
    margin-bottom: 20px;
}

.modal-buttons {
    display: flex;
    gap: 10px;
}

.modal-body {
    padding: 20px 40px 40px;
}

.modal-details {
    display: flex;
    gap: 20px;
    margin-top: 15px;
    font-size: 14px;
    color: #b3b3b3;
}

.genre {
    color: #ffffff;
}

.rating {
    color: #46d369;
}

/* Footer */
.footer {
    background-color: #000000;
    padding: 60px;
    margin-top: 80px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    margin-bottom: 40px;
}

.footer-column a {
    display: block;
    color: #737373;
    text-decoration: none;
    font-size: 14px;
    margin-bottom: 15px;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: #ffffff;
}

.footer-bottom {
    border-top: 1px solid #333333;
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-code-btn {
    background: transparent;
    border: 1px solid #737373;
    color: #737373;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.copyright {
    color: #737373;
    font-size: 12px;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #141414;
}

::-webkit-scrollbar-thumb {
    background: #e50914;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #f40612;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        padding: 15px 20px;
    }

    .nav-menu {
        display: none;
    }

    .hero {
        padding: 0 20px;
    }

    .hero-title {
        font-size: 36px;
    }

    .main-content {
        padding: 0 20px;
    }

    .movie-card {
        min-width: 150px;
        height: 225px;
    }

    .footer {
        padding: 40px 20px;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
}
