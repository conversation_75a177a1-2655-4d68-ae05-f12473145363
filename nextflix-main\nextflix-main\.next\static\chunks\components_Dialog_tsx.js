(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Dialog_tsx"],{

/***/ "./components/Dialog.tsx":
/*!*******************************!*\
  !*** ./components/Dialog.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Dialog; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/react/jsx-dev-runtime.js");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ "./node_modules/framer-motion/dist/es/index.js");
/* harmony import */ var _hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../hooks/useExternalClick */ "./hooks/useExternalClick.ts");
/* module decorator */ module = __webpack_require__.hmd(module);



var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\Dialog.tsx",
    _s = $RefreshSig$();



function Dialog(props) {
  _s();

  var visible = props.visible,
      classname = props.classname,
      onClose = props.onClose,
      dialogRef = props.dialogRef,
      children = props.children;
  (0,_hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_1__.default)(dialogRef, function () {
    onClose === null || onClose === void 0 ? void 0 : onClose();
  });
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
    children: visible && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {
      className: classname,
      initial: {
        opacity: 0,
        y: 12
      },
      animate: {
        opacity: 1,
        y: 0
      },
      exit: {
        opacity: 0
      },
      children: children
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 23,
      columnNumber: 9
    }, this)
  }, void 0, false);
}

_s(Dialog, "PfoRRE1enXFYdzShsUipsiIJXJk=", false, function () {
  return [_hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_1__.default];
});

_c = Dialog;

var _c;

$RefreshReg$(_c, "Dialog");

;
    var _a, _b;
    // Legacy CSS implementations will `eval` browser code in a Node.js context
    // to extract CSS. For backwards compatibility, we need to check we're in a
    // browser context before continuing.
    if (typeof self !== 'undefined' &&
        // AMP / No-JS mode does not inject these helpers:
        '$RefreshHelpers$' in self) {
        var currentExports = module.__proto__.exports;
        var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
        // This cannot happen in MainTemplate because the exports mismatch between
        // templating and execution.
        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
        // A module can be accepted automatically based on its exports, e.g. when
        // it is a Refresh Boundary.
        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
            // Save the previous exports on update so we can compare the boundary
            // signatures.
            module.hot.dispose(function (data) {
                data.prevExports = currentExports;
            });
            // Unconditionally accept an update to this module, we'll check if it's
            // still a Refresh Boundary later.
            module.hot.accept();
            // This field is set when the previous version of this module was a
            // Refresh Boundary, letting us know we need to check for invalidation or
            // enqueue an update.
            if (prevExports !== null) {
                // A boundary can become ineligible if its exports are incompatible
                // with the previous exports.
                //
                // For example, if you add/remove/change exports, we'll want to
                // re-execute the importing modules, and force those components to
                // re-render. Similarly, if you convert a class component to a
                // function, we want to invalidate the boundary.
                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                    module.hot.invalidate();
                }
                else {
                    self.$RefreshHelpers$.scheduleUpdate();
                }
            }
        }
        else {
            // Since we just executed the code for the module, it's possible that the
            // new exports made it ineligible for being a boundary.
            // We only care about the case when we were _previously_ a boundary,
            // because we already accepted this update (accidental side effect).
            var isNoLongerABoundary = prevExports !== null;
            if (isNoLongerABoundary) {
                module.hot.invalidate();
            }
        }
    }


/***/ }),

/***/ "./hooks/useExternalClick.ts":
/*!***********************************!*\
  !*** ./hooks/useExternalClick.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useExternalClick; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* module decorator */ module = __webpack_require__.hmd(module);
var _s = $RefreshSig$();


function useExternalClick(ref, callback) {
  _s();

  var onClick = function onClick(event) {
    var _ref$current;

    if (!(ref !== null && ref !== void 0 && (_ref$current = ref.current) !== null && _ref$current !== void 0 && _ref$current.contains(event.target))) {
      callback();
    }
  };

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    document.addEventListener('click', onClick);
    return function () {
      return document.removeEventListener('click', onClick);
    };
  });
}

_s(useExternalClick, "OD7bBpZva5O2jO+Puf00hKivP7c=");

;
    var _a, _b;
    // Legacy CSS implementations will `eval` browser code in a Node.js context
    // to extract CSS. For backwards compatibility, we need to check we're in a
    // browser context before continuing.
    if (typeof self !== 'undefined' &&
        // AMP / No-JS mode does not inject these helpers:
        '$RefreshHelpers$' in self) {
        var currentExports = module.__proto__.exports;
        var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
        // This cannot happen in MainTemplate because the exports mismatch between
        // templating and execution.
        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
        // A module can be accepted automatically based on its exports, e.g. when
        // it is a Refresh Boundary.
        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
            // Save the previous exports on update so we can compare the boundary
            // signatures.
            module.hot.dispose(function (data) {
                data.prevExports = currentExports;
            });
            // Unconditionally accept an update to this module, we'll check if it's
            // still a Refresh Boundary later.
            module.hot.accept();
            // This field is set when the previous version of this module was a
            // Refresh Boundary, letting us know we need to check for invalidation or
            // enqueue an update.
            if (prevExports !== null) {
                // A boundary can become ineligible if its exports are incompatible
                // with the previous exports.
                //
                // For example, if you add/remove/change exports, we'll want to
                // re-execute the importing modules, and force those components to
                // re-render. Similarly, if you convert a class component to a
                // function, we want to invalidate the boundary.
                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                    module.hot.invalidate();
                }
                else {
                    self.$RefreshHelpers$.scheduleUpdate();
                }
            }
        }
        else {
            // Since we just executed the code for the module, it's possible that the
            // new exports made it ineligible for being a boundary.
            // We only care about the case when we were _previously_ a boundary,
            // because we already accepted this update (accidental side effect).
            var isNoLongerABoundary = prevExports !== null;
            if (isNoLongerABoundary) {
                module.hot.invalidate();
            }
        }
    }


/***/ })

}]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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