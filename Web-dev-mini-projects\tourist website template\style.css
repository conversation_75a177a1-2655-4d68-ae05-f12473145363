/* General body styles */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

h3, h4, h1, h2 {
    font-weight: bold;
}

.navbar {
    padding: 0.8rem 1.5rem;
    letter-spacing: 1px;
}

.navbar-brand {
    font-size: 1.8rem;
    letter-spacing: 2px;
    margin-left: 0.5rem;
}

.navbar-nav .nav-link {
    font-size: 1.1rem;
    margin-right: 1rem;
    transition: color 0.3s;
}

.navbar-nav .nav-link:hover {
    color: #f8d210;
}

.navbar-dark .navbar-toggler {
    border: none;
}

/* Header/Intro Section */
header {
    position: relative;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-blend-mode: multiply;
}

header .overlay {
    background: rgba(0, 0, 0, 0.6);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

header h1 {
    font-size: 4rem;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.7);
}

header p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.6);
}

header .btn {
    background-color: #f8d210;
    color: #000;
    transition: background-color 0.3s, color 0.3s;
}

header .btn:hover {
    background-color: #fff;
    color: #000;
}

/* Pricing Section */
.pricing-header {
    background-color: #343a40; /* Dark background to contrast with text */
    color: #f8f9fa; /* Light text for contrast */
    padding: 3rem 0; /* Increase padding for better spacing */
    border-radius: 8px; /* Slight rounded corners for a smooth look */
    margin-bottom: 2rem; /* Adds spacing between the section and the next */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Adds a subtle shadow effect */
}

.pricing-header h2 {
    font-size: 3rem; /* Large heading to stand out */
    letter-spacing: 1px; /* Slight letter spacing for better readability */
    font-weight: bold; /* Makes the header more prominent */
}

.text-center {
    text-align: center; /* Ensures text is centered */
}

.py-3 {
    padding-top: 1rem; /* Custom padding top */
    padding-bottom: 1rem; /* Custom padding bottom */
}


.card {
    border-radius: 10px;
    overflow: hidden;
    transition: box-shadow 0.3s ease-in-out;
}

.card:hover {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.card-header {
    background-color: #333;
    color: white;
    text-align: center;
}

.card-body ul {
    font-size: 1.1rem;
}

.card-title {
    color: #f8d210;
}

.card .btn {
    background-color: #f8d210;
    color: #000;
    border: none;
}

.card .btn:hover {
    background-color: #fff;
    color: #000;
}

/* Checkout Section */
.checkout {
    background-color: #2d2d2d;
    padding: 3rem 0;
}

.checkout h2 {
    color: #f8d210;
    text-align: center;
    margin-bottom: 3rem;
}

.checkout .form-control {
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px;
    font-size: 1rem;
}

.checkout .form-control:focus {
    box-shadow: 0 0 5px rgba(248, 210, 16, 0.6);
    border-color: #f8d210;
}

.checkout .custom-select {
    background-color: #f8d210;
    color: #333;
    border: none;
    font-size: 1rem;
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #f8d210;
    border-color: #f8d210;
}

.checkout .btn-outline-primary {
    border-color: #f8d210;
    color: #f8d210;
    transition: background-color 0.3s ease;
}

.checkout .btn-outline-primary:hover {
    background-color: #f8d210;
    color: #000;
}

/* Footer */
footer {
    background-color: #000;
    color: white;
    padding: 1rem 0;
    text-align: center;
}

footer p {
    margin: 0;
    font-size: 1rem;
}
