# Quiz App



This is a simple, interactive quiz application built with React and styled using TailwindCSS. The app allows users to go through a series of questions, provides feedback on their answers, and displays a final score upon completion.

## Screenshots
<table>
  <tr>
    <td>
      <h3>Welcome Screen</h3>
      <img src="https://github.com/Temmarie/Web-dev-mini-projects/blob/quiz-app/quiz-app/src/assets/images/welcome.png" alt="Welcome Screen" width="100%"/>
    </td>
    <td>
      <h3>Instructions Screen</h3>
      <img src="https://github.com/Temmarie/Web-dev-mini-projects/blob/quiz-app/quiz-app/src/assets/images/instructions.png" alt="Instructions Screen" width="100%"/>
    </td>
  </tr>
  <tr>
    <td>
      <h3>Quiz Screen</h3>
      <img src="https://github.com/Temmarie/Web-dev-mini-projects/blob/quiz-app/quiz-app/src/assets/images/quiz.png" alt="Quiz Screen" width="100%"/>
    </td>
    <td>
      <h3>Score Screen</h3>
      <img src="https://github.com/Temmarie/Web-dev-mini-projects/blob/quiz-app/quiz-app/src/assets/images/score.png" alt="Completion Screen" width="100%"/>
    </td>
  </tr>
</table>

## Table of Contents

- [Features](#features)
- [Technologies Used](#technologies-used)
- [Getting Started](#getting-started)


## Features

- **Welcome Screen**: Users are greeted with a welcome screen where they can choose to start the quiz or view instructions.
- **Instructions**: An optional instructions screen explaining how to navigate and answer the quiz.
- **Question Display**: Each question is displayed one at a time with multiple answer choices.
- **Feedback**: Users receive instant feedback after answering each question.
- **Progress Indicator**: The app tracks the user's progress, indicating how many questions they have answered and how many are left.
- **Score Tracking**: The app calculates the final score and displays it upon quiz completion.
- **Responsive Design**: Fully responsive and mobile-friendly UI using TailwindCSS.

## Built With

- **React**
- **TailwindCSS**
- **Vite**

## Getting Started

To get a local copy up and running follow these simple example steps.

### Prerequisites

- **Node.js** (v14 or above)
- **npm** or **yarn**

### Setup

1. Clone the repository:

   `
   git clone https://github.com/temmarie/quiz-app.git
   cd quiz-app
   ```

2. Install dependencies:

   ```
   npm install
   ```

3. Start the development server:

   ```
   npm run dev
   ```

4. Open your browser and navigate to:

   ```
   http://localhost:5173
   ```