module.exports={A:{A:{"2":"J D E F hB","33":"A B"},B:{"33":"C K L G M N O","132":"R S T U V W X Y","260":"Z P a H"},C:{"1":"0 1 2 3 4 5 6 7 8 9 AB BB CB DB EB FB ZB GB aB Q HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB bB R S T jB U V W X Y Z P a H cB","2":"iB YB I b kB lB","33":"J D E F A B C K L G M N O c d e f g h i j k l m n o p q r s t u v w x y z"},D:{"1":"Z P a H cB mB nB oB","2":"0 1 2 3 4 5 6 7 8 9 I b J D E F A B C K L G M N O c d e f g h i j k l m n o p q r s t u v w x y z AB BB","132":"CB DB EB FB ZB GB aB Q HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB bB R S T U V W X Y"},E:{"2":"I b pB dB","33":"J D E F A B C K L G qB rB sB tB eB WB XB uB vB wB"},F:{"2":"F B C G M N O c d e f g h i j k l m n o p q r s t u v w x y xB yB zB 0B WB fB 1B XB","132":"0 1 2 3 4 5 6 7 8 9 z AB BB CB DB EB FB GB Q HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB"},G:{"2":"dB 2B","33":"E gB 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC KC"},H:{"2":"LC"},I:{"1":"H","2":"YB I MC NC OC PC gB QC RC"},J:{"2":"D A"},K:{"1":"Q","2":"A B C WB fB XB"},L:{"1":"H"},M:{"1":"P"},N:{"2":"A B"},O:{"4":"SC"},P:{"1":"UC VC WC XC eB YC ZC aC bC","2":"I","132":"TC"},Q:{"2":"cC"},R:{"132":"dC"},S:{"1":"eC"}},B:5,C:"CSS Hyphenation"};
