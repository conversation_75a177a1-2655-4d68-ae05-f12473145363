body {
    margin: 0;
    font-family: Arial, sans-serif;
  }
  
  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgb(184, 181, 181);
    transition: background-color 0.5s ease;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    
  }
  
  .navbar-logo {
    padding: 10px;
    color: white;

}
  
  .navbar-links {
    display: flex;
    flex-direction: row;
  }
  
  .navbar-links a {
    display: block;
    color: rgb(255, 255, 255);
    text-decoration: none;
    padding: 10px 20px;
    transition: background-color 0.3s ease;
  }
  
  .navbar-links a:hover {
    background-color: #ddd;
    color: black;
  }
  
  .navbar-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 10px;
  }
  
  .bar {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
  }
  
  .content {
    padding: 20px;
    margin-top: 70px; 
  }
  
  .scrolled {
    background-color: rgb(0, 0, 0) !important;
    
  }

  @media screen and (max-width: 768px) {
    .navbar-links {
      display: none;
      flex-direction: column;
      width: 100%;
      background-color: black;
      position: absolute;
      top: 45px;
      left: -100%; 
      z-index: 999;
      transition: left 0.5s ease; 
    }
  
    .navbar-links.show {
      left: 0; 
      display: flex;
    }
  
    .navbar-links a {
      padding: 10px;
      text-align: center;
      border-bottom: 1px solid #ccc;
    }
  
    .navbar-toggle {
      display: flex;
    }
    .navbar
    {
      display: flex;
      justify-content: space-around;
      gap: 54%;
    }
  }
  