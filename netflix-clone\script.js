// Movie Data
const movieData = {
    trending: [
        { title: "Stranger Things", rating: "98% Match", image: "https://image.tmdb.org/t/p/w300/49WJfeN0moxb9IPfGn8AIqMGskD.jpg" },
        { title: "Wednesday", rating: "92% Match", image: "https://image.tmdb.org/t/p/w300/9PFonBhy4cQy7Jz20NpMygczOkv.jpg" },
        { title: "The Crown", rating: "94% Match", image: "https://image.tmdb.org/t/p/w300/1M876KPjulVwppEpldhdc8V4o68.jpg" },
        { title: "Bridgerton", rating: "91% Match", image: "https://image.tmdb.org/t/p/w300/luoKpgVwi1E5nQsi7W0UuKHu2Rq.jpg" }
    ],
    originals: [
        { title: "House of Cards", rating: "87% Match", image: "https://image.tmdb.org/t/p/w300/hKWxWjFwnMvkWQawbhvC0Y9voM.jpg" },
        { title: "Narcos", rating: "93% Match", image: "https://image.tmdb.org/t/p/w300/rTmal9fDbwh5F0waol2hq35U4ah.jpg" },
        { title: "Black Mirror", rating: "96% Match", image: "https://image.tmdb.org/t/p/w300/5UaYsGZOFhjFDwQh6GuLjjA5WfA.jpg" },
        { title: "Dark", rating: "97% Match", image: "https://image.tmdb.org/t/p/w300/5csl9cBXEK4StdEHbYxqo2xYpfH.jpg" }
    ],
    popular: [
        { title: "The Avengers", rating: "94% Match", image: "https://image.tmdb.org/t/p/w300/RYMX2wcKCBAr24UyPD7xwmjaTn.jpg" },
        { title: "Inception", rating: "96% Match", image: "https://image.tmdb.org/t/p/w300/9gk7adHYeDvHkCSEqAvQNLV5Uge.jpg" },
        { title: "The Dark Knight", rating: "98% Match", image: "https://image.tmdb.org/t/p/w300/qJ2tW6WMUDux911r6m7haRef0WH.jpg" },
        { title: "Interstellar", rating: "95% Match", image: "https://image.tmdb.org/t/p/w300/gEU2QniE6E77NI6lCU6MxlNBvIx.jpg" }
    ],
    tv: [
        { title: "Breaking Bad", rating: "99% Match", image: "https://image.tmdb.org/t/p/w300/3xnWaLQjelJDDF7LT1WBo6f4BRe.jpg" },
        { title: "Game of Thrones", rating: "91% Match", image: "https://image.tmdb.org/t/p/w300/1XS1oqL89opfnbLl8WnZY1O1uJx.jpg" },
        { title: "Friends", rating: "89% Match", image: "https://image.tmdb.org/t/p/w300/f496cm9enuEsZkSPzCwnTESEK5s.jpg" },
        { title: "The Mandalorian", rating: "96% Match", image: "https://image.tmdb.org/t/p/w300/sWgBv7LV2PRoQgkxwlibdGXKz1S.jpg" }
    ],
    action: [
        { title: "John Wick", rating: "95% Match", image: "https://image.tmdb.org/t/p/w300/fZPSd91yGE9fCcCe6OoQr6E3Bev.jpg" },
        { title: "Mad Max: Fury Road", rating: "93% Match", image: "https://image.tmdb.org/t/p/w300/hA2ple9q4qnwxp3hKVNhroipsir.jpg" },
        { title: "Mission Impossible", rating: "91% Match", image: "https://image.tmdb.org/t/p/w300/AkJQpZp9WoNdj7pLYSj1L0RcMMN.jpg" },
        { title: "Terminator", rating: "89% Match", image: "https://image.tmdb.org/t/p/w300/qvktm0BHcnmDpul4Hz01GIazWPr.jpg" }
    ],
    comedy: [
        { title: "The Hangover", rating: "84% Match", image: "https://image.tmdb.org/t/p/w300/uluhlXqQpOohcXJ8YOjsyBMIRaB.jpg" },
        { title: "Superbad", rating: "82% Match", image: "https://image.tmdb.org/t/p/w300/ek8e8txUyUwd2BNqj6lFEerJfbq.jpg" },
        { title: "Step Brothers", rating: "81% Match", image: "https://image.tmdb.org/t/p/w300/wRR62VjgaECtiZlpb7xqYGqbszN.jpg" },
        { title: "Zoolander", rating: "75% Match", image: "https://image.tmdb.org/t/p/w300/qdrbSneHZjJG2Dj0hhBxzzAo4HB.jpg" }
    ]
};

// DOM Elements
const header = document.querySelector('.header');
const modal = document.getElementById('movie-modal');
const modalTitle = document.getElementById('modal-title');
const modalDescription = document.getElementById('modal-description');
const closeModal = document.querySelector('.close');

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    populateMovieRows();
    setupEventListeners();
    setupScrollEffect();
});

// Populate movie rows
function populateMovieRows() {
    const categories = ['trending', 'originals', 'popular', 'tv', 'action', 'comedy'];
    
    categories.forEach(category => {
        const container = document.getElementById(`${category}-list`);
        const movies = movieData[category];
        
        movies.forEach(movie => {
            const movieCard = createMovieCard(movie);
            container.appendChild(movieCard);
        });
    });
}

// Create movie card element
function createMovieCard(movie) {
    const card = document.createElement('div');
    card.className = 'movie-card';
    card.style.backgroundImage = `url(${movie.image})`;
    card.style.backgroundSize = 'cover';
    card.style.backgroundPosition = 'center';

    card.innerHTML = `
        <div class="movie-info">
            <div class="movie-title">${movie.title}</div>
            <div class="movie-rating">${movie.rating}</div>
        </div>
    `;

    card.addEventListener('click', () => openModal(movie));

    return card;
}

// Setup event listeners
function setupEventListeners() {
    // Modal close functionality
    closeModal.addEventListener('click', closeModalHandler);
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModalHandler();
        }
    });
    
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    searchInput.addEventListener('input', handleSearch);
    
    // Hero buttons
    const playBtn = document.querySelector('.hero-buttons .btn-play');
    const infoBtn = document.querySelector('.hero-buttons .btn-info');
    
    playBtn.addEventListener('click', () => {
        alert('Play functionality would be implemented here!');
    });
    
    infoBtn.addEventListener('click', () => {
        openModal({
            title: 'Stranger Things',
            rating: '98% Match',
            description: 'When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces, and one strange little girl.',
            image: 'https://images.unsplash.com/photo-1489599735734-79b4169c4388?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        });
    });
}

// Setup scroll effect for header
function setupScrollEffect() {
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Open modal
function openModal(movie) {
    modalTitle.textContent = movie.title;
    modalDescription.textContent = movie.description || `${movie.title} is an amazing ${getRandomGenre()} that you'll love watching. Experience the thrill and excitement with stunning visuals and compelling storytelling.`;
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close modal
function closeModalHandler() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Handle search
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    if (searchTerm.length > 2) {
        // Simple search simulation
        console.log(`Searching for: ${searchTerm}`);
        // In a real app, this would filter the movie cards
    }
}

// Get random genre for description
function getRandomGenre() {
    const genres = ['thriller', 'drama', 'action movie', 'comedy', 'sci-fi series', 'mystery'];
    return genres[Math.floor(Math.random() * genres.length)];
}

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add keyboard navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && modal.style.display === 'block') {
        closeModalHandler();
    }
});

// Add loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Simulate dynamic content loading
setTimeout(() => {
    console.log('Netflix Clone loaded successfully! 🎬');
}, 1000);
