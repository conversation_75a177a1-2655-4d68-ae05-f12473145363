// Movie Data
const movieData = {
    trending: [
        { title: "Stranger Things", rating: "98% Match", image: "https://via.placeholder.com/300x450/8B0000/FFFFFF?text=STRANGER+THINGS" },
        { title: "The Witcher", rating: "95% Match", image: "https://via.placeholder.com/300x450/4B0082/FFFFFF?text=THE+WITCHER" },
        { title: "Money Heist", rating: "92% Match", image: "https://via.placeholder.com/300x450/DC143C/FFFFFF?text=MONEY+HEIST" },
        { title: "Ozark", rating: "89% Match", image: "https://via.placeholder.com/300x450/2F4F4F/FFFFFF?text=OZARK" },
        { title: "The Crown", rating: "94% Match", image: "https://via.placeholder.com/300x450/FFD700/000000?text=THE+CROWN" },
        { title: "Bridgerton", rating: "91% Match", image: "https://via.placeholder.com/300x450/FF69B4/FFFFFF?text=BRIDGERTON" }
    ],
    originals: [
        { title: "House of Cards", rating: "87% Match", image: "https://via.placeholder.com/300x450/000080/FFFFFF?text=HOUSE+OF+CARDS" },
        { title: "Orange is the New Black", rating: "85% Match", image: "https://via.placeholder.com/300x450/FF8C00/FFFFFF?text=ORANGE+IS+THE+NEW+BLACK" },
        { title: "Narcos", rating: "93% Match", image: "https://via.placeholder.com/300x450/8B4513/FFFFFF?text=NARCOS" },
        { title: "Black Mirror", rating: "96% Match", image: "https://via.placeholder.com/300x450/000000/FFFFFF?text=BLACK+MIRROR" },
        { title: "The Umbrella Academy", rating: "88% Match", image: "https://via.placeholder.com/300x450/FF0000/FFFFFF?text=UMBRELLA+ACADEMY" },
        { title: "Dark", rating: "97% Match", image: "https://via.placeholder.com/300x450/1C1C1C/FFFFFF?text=DARK" }
    ],
    popular: [
        { title: "The Avengers", rating: "94% Match", image: "https://via.placeholder.com/300x450/FF4500/FFFFFF?text=THE+AVENGERS" },
        { title: "Inception", rating: "96% Match", image: "https://via.placeholder.com/300x450/4682B4/FFFFFF?text=INCEPTION" },
        { title: "The Dark Knight", rating: "98% Match", image: "https://via.placeholder.com/300x450/2F2F2F/FFFFFF?text=THE+DARK+KNIGHT" },
        { title: "Interstellar", rating: "95% Match", image: "https://via.placeholder.com/300x450/191970/FFFFFF?text=INTERSTELLAR" },
        { title: "Pulp Fiction", rating: "92% Match", image: "https://via.placeholder.com/300x450/8B0000/FFFFFF?text=PULP+FICTION" },
        { title: "The Matrix", rating: "93% Match", image: "https://via.placeholder.com/300x450/006400/FFFFFF?text=THE+MATRIX" }
    ],
    tv: [
        { title: "Breaking Bad", rating: "99% Match", image: "https://via.placeholder.com/300x450/228B22/FFFFFF?text=BREAKING+BAD" },
        { title: "Game of Thrones", rating: "91% Match", image: "https://via.placeholder.com/300x450/8B4513/FFFFFF?text=GAME+OF+THRONES" },
        { title: "The Office", rating: "87% Match", image: "https://via.placeholder.com/300x450/4169E1/FFFFFF?text=THE+OFFICE" },
        { title: "Friends", rating: "89% Match", image: "https://via.placeholder.com/300x450/FF69B4/FFFFFF?text=FRIENDS" },
        { title: "Sherlock", rating: "94% Match", image: "https://via.placeholder.com/300x450/800080/FFFFFF?text=SHERLOCK" },
        { title: "The Mandalorian", rating: "96% Match", image: "https://via.placeholder.com/300x450/2F4F4F/FFFFFF?text=THE+MANDALORIAN" }
    ],
    action: [
        { title: "John Wick", rating: "95% Match", image: "https://via.placeholder.com/300x450/000000/FFFFFF?text=JOHN+WICK" },
        { title: "Mad Max: Fury Road", rating: "93% Match", image: "https://via.placeholder.com/300x450/FF8C00/FFFFFF?text=MAD+MAX" },
        { title: "Die Hard", rating: "88% Match", image: "https://via.placeholder.com/300x450/B22222/FFFFFF?text=DIE+HARD" },
        { title: "Mission Impossible", rating: "91% Match", image: "https://via.placeholder.com/300x450/2F4F4F/FFFFFF?text=MISSION+IMPOSSIBLE" },
        { title: "Fast & Furious", rating: "86% Match", image: "https://via.placeholder.com/300x450/FF4500/FFFFFF?text=FAST+FURIOUS" },
        { title: "Terminator", rating: "89% Match", image: "https://via.placeholder.com/300x450/708090/FFFFFF?text=TERMINATOR" }
    ],
    comedy: [
        { title: "The Hangover", rating: "84% Match", image: "https://via.placeholder.com/300x450/FFD700/000000?text=THE+HANGOVER" },
        { title: "Superbad", rating: "82% Match", image: "https://via.placeholder.com/300x450/32CD32/FFFFFF?text=SUPERBAD" },
        { title: "Anchorman", rating: "79% Match", image: "https://via.placeholder.com/300x450/FF6347/FFFFFF?text=ANCHORMAN" },
        { title: "Step Brothers", rating: "81% Match", image: "https://via.placeholder.com/300x450/20B2AA/FFFFFF?text=STEP+BROTHERS" },
        { title: "Dumb and Dumber", rating: "77% Match", image: "https://via.placeholder.com/300x450/9370DB/FFFFFF?text=DUMB+DUMBER" },
        { title: "Zoolander", rating: "75% Match", image: "https://via.placeholder.com/300x450/FF1493/FFFFFF?text=ZOOLANDER" }
    ]
};

// DOM Elements
const header = document.querySelector('.header');
const modal = document.getElementById('movie-modal');
const modalTitle = document.getElementById('modal-title');
const modalDescription = document.getElementById('modal-description');
const closeModal = document.querySelector('.close');

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    populateMovieRows();
    setupEventListeners();
    setupScrollEffect();
});

// Populate movie rows
function populateMovieRows() {
    const categories = ['trending', 'originals', 'popular', 'tv', 'action', 'comedy'];
    
    categories.forEach(category => {
        const container = document.getElementById(`${category}-list`);
        const movies = movieData[category];
        
        movies.forEach(movie => {
            const movieCard = createMovieCard(movie);
            container.appendChild(movieCard);
        });
    });
}

// Create movie card element
function createMovieCard(movie) {
    const card = document.createElement('div');
    card.className = 'movie-card';
    card.style.backgroundImage = `url(${movie.image})`;
    
    card.innerHTML = `
        <div class="movie-info">
            <div class="movie-title">${movie.title}</div>
            <div class="movie-rating">${movie.rating}</div>
        </div>
    `;
    
    card.addEventListener('click', () => openModal(movie));
    
    return card;
}

// Setup event listeners
function setupEventListeners() {
    // Modal close functionality
    closeModal.addEventListener('click', closeModalHandler);
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModalHandler();
        }
    });
    
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    searchInput.addEventListener('input', handleSearch);
    
    // Hero buttons
    const playBtn = document.querySelector('.hero-buttons .btn-play');
    const infoBtn = document.querySelector('.hero-buttons .btn-info');
    
    playBtn.addEventListener('click', () => {
        alert('Play functionality would be implemented here!');
    });
    
    infoBtn.addEventListener('click', () => {
        openModal({
            title: 'Stranger Things',
            rating: '98% Match',
            description: 'When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces, and one strange little girl.',
            image: 'https://images.unsplash.com/photo-1489599735734-79b4169c4388?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        });
    });
}

// Setup scroll effect for header
function setupScrollEffect() {
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Open modal
function openModal(movie) {
    modalTitle.textContent = movie.title;
    modalDescription.textContent = movie.description || `${movie.title} is an amazing ${getRandomGenre()} that you'll love watching. Experience the thrill and excitement with stunning visuals and compelling storytelling.`;
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close modal
function closeModalHandler() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Handle search
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    if (searchTerm.length > 2) {
        // Simple search simulation
        console.log(`Searching for: ${searchTerm}`);
        // In a real app, this would filter the movie cards
    }
}

// Get random genre for description
function getRandomGenre() {
    const genres = ['thriller', 'drama', 'action movie', 'comedy', 'sci-fi series', 'mystery'];
    return genres[Math.floor(Math.random() * genres.length)];
}

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add keyboard navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && modal.style.display === 'block') {
        closeModalHandler();
    }
});

// Add loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Simulate dynamic content loading
setTimeout(() => {
    console.log('Netflix Clone loaded successfully! 🎬');
}, 1000);
