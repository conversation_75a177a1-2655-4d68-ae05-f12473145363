// Movie Data
const movieData = {
    trending: [
        { title: "Breaking Bad", rating: "98% Match", image: "https://upload.wikimedia.org/wikipedia/en/0/03/<PERSON>_<PERSON>_S5B.png" },
        { title: "Game of Thrones", rating: "92% Match", image: "https://upload.wikimedia.org/wikipedia/en/d/d8/Game_of_Thrones_title_card.jpg" },
        { title: "The Office", rating: "94% Match", image: "https://upload.wikimedia.org/wikipedia/en/3/37/Theofficeuslogo.png" },
        { title: "Friends", rating: "91% Match", image: "https://upload.wikimedia.org/wikipedia/en/d/da/Friends_logo.svg" },
        { title: "Stranger Things", rating: "96% Match", image: "https://upload.wikimedia.org/wikipedia/commons/3/38/Stranger_Things_logo.png" },
        { title: "The Witcher", rating: "89% Match", image: "https://upload.wikimedia.org/wikipedia/en/1/1e/The_Witcher_Netflix_logo.png" }
    ],
    originals: [
        { title: "House of Cards", rating: "87% Match", image: "https://upload.wikimedia.org/wikipedia/en/8/80/House_of_Cards_season_1.png" },
        { title: "Orange is the New Black", rating: "93% Match", image: "https://upload.wikimedia.org/wikipedia/en/7/74/Orange_Is_the_New_Black_poster.jpg" },
        { title: "Narcos", rating: "96% Match", image: "https://upload.wikimedia.org/wikipedia/en/9/9a/Narcos_Title_Card.png" },
        { title: "Black Mirror", rating: "97% Match", image: "https://upload.wikimedia.org/wikipedia/en/5/50/Black_Mirror_titlecard.jpg" },
        { title: "Mindhunter", rating: "88% Match", image: "https://upload.wikimedia.org/wikipedia/en/4/4b/Mindhunter_%28TV_series%29.png" },
        { title: "Dark", rating: "85% Match", image: "https://upload.wikimedia.org/wikipedia/en/2/26/Dark_Netflix.jpg" }
    ],
    popular: [
        { title: "Titanic", rating: "94% Match", image: "https://upload.wikimedia.org/wikipedia/en/1/18/Titanic_%281997_film%29_poster.png" },
        { title: "Avatar", rating: "96% Match", image: "https://upload.wikimedia.org/wikipedia/en/d/d6/Avatar_%282009_film%29_poster.jpg" },
        { title: "Avengers Endgame", rating: "98% Match", image: "https://upload.wikimedia.org/wikipedia/en/0/0d/Avengers_Endgame_poster.jpg" },
        { title: "Spider-Man", rating: "95% Match", image: "https://upload.wikimedia.org/wikipedia/en/f/f3/Spider-Man2002Poster.jpg" },
        { title: "The Lion King", rating: "92% Match", image: "https://upload.wikimedia.org/wikipedia/en/3/3d/The_Lion_King_poster.jpg" },
        { title: "Frozen", rating: "93% Match", image: "https://upload.wikimedia.org/wikipedia/en/0/05/Frozen_%282013_film%29_poster.jpg" }
    ],
    tv: [
        { title: "The Simpsons", rating: "99% Match", image: "https://upload.wikimedia.org/wikipedia/en/0/0d/Simpsons_FamilyPicture.png" },
        { title: "South Park", rating: "91% Match", image: "https://upload.wikimedia.org/wikipedia/en/7/77/South_Park_title.png" },
        { title: "Family Guy", rating: "89% Match", image: "https://upload.wikimedia.org/wikipedia/en/0/0c/Family_Guy_Logo.svg" },
        { title: "Rick and Morty", rating: "96% Match", image: "https://upload.wikimedia.org/wikipedia/en/d/d4/Rick_and_Morty_season_1_poster.jpg" },
        { title: "The Big Bang Theory", rating: "94% Match", image: "https://upload.wikimedia.org/wikipedia/en/7/7b/The_Big_Bang_Theory_logo.png" },
        { title: "How I Met Your Mother", rating: "87% Match", image: "https://upload.wikimedia.org/wikipedia/en/d/db/How_I_Met_Your_Mother_Logo.svg" }
    ],
    action: [
        { title: "Top Gun", rating: "95% Match", image: "https://upload.wikimedia.org/wikipedia/en/4/45/Top_Gun_Movie.jpg" },
        { title: "Rambo", rating: "93% Match", image: "https://upload.wikimedia.org/wikipedia/en/8/8a/Rambo_First_Blood_poster.jpg" },
        { title: "Rocky", rating: "91% Match", image: "https://upload.wikimedia.org/wikipedia/en/1/18/Rocky_poster.jpg" },
        { title: "Gladiator", rating: "89% Match", image: "https://upload.wikimedia.org/wikipedia/en/f/fb/Gladiator_%282000_film_poster%29.png" },
        { title: "300", rating: "88% Match", image: "https://upload.wikimedia.org/wikipedia/en/5/5c/300poster.jpg" },
        { title: "Speed", rating: "86% Match", image: "https://upload.wikimedia.org/wikipedia/en/3/39/Speed_movie_poster.jpg" }
    ],
    comedy: [
        { title: "Home Alone", rating: "84% Match", image: "https://upload.wikimedia.org/wikipedia/en/7/76/Home_alone_poster.jpg" },
        { title: "Ghostbusters", rating: "82% Match", image: "https://upload.wikimedia.org/wikipedia/en/2/2f/Ghostbusters_%281984%29_theatrical_poster.png" },
        { title: "Mrs. Doubtfire", rating: "81% Match", image: "https://upload.wikimedia.org/wikipedia/en/1/1e/Mrs._Doubtfire.jpg" },
        { title: "The Mask", rating: "75% Match", image: "https://upload.wikimedia.org/wikipedia/en/4/40/The_Mask_Poster.jpg" },
        { title: "Ace Ventura", rating: "79% Match", image: "https://upload.wikimedia.org/wikipedia/en/e/e6/Ace_Ventura_Pet_Detective.jpg" },
        { title: "Dumb and Dumber", rating: "77% Match", image: "https://upload.wikimedia.org/wikipedia/en/6/64/Dumbanddumber.jpg" }
    ]
};

// DOM Elements
const header = document.querySelector('.header');
const modal = document.getElementById('movie-modal');
const modalTitle = document.getElementById('modal-title');
const modalDescription = document.getElementById('modal-description');
const closeModal = document.querySelector('.close');

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    populateMovieRows();
    setupEventListeners();
    setupScrollEffect();
});

// Populate movie rows
function populateMovieRows() {
    const categories = ['trending', 'originals', 'popular', 'tv', 'action', 'comedy'];
    
    categories.forEach(category => {
        const container = document.getElementById(`${category}-list`);
        const movies = movieData[category];
        
        movies.forEach(movie => {
            const movieCard = createMovieCard(movie);
            container.appendChild(movieCard);
        });
    });
}

// Create movie card element
function createMovieCard(movie) {
    const card = document.createElement('div');
    card.className = 'movie-card';
    card.style.backgroundImage = `url(${movie.image})`;
    card.style.backgroundSize = 'cover';
    card.style.backgroundPosition = 'center';

    card.innerHTML = `
        <div class="movie-info">
            <div class="movie-title">${movie.title}</div>
            <div class="movie-rating">${movie.rating}</div>
        </div>
    `;

    card.addEventListener('click', () => openModal(movie));

    return card;
}

// Setup event listeners
function setupEventListeners() {
    // Modal close functionality
    closeModal.addEventListener('click', closeModalHandler);
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModalHandler();
        }
    });
    
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    searchInput.addEventListener('input', handleSearch);
    
    // Hero buttons
    const playBtn = document.querySelector('.hero-buttons .btn-play');
    const infoBtn = document.querySelector('.hero-buttons .btn-info');
    
    playBtn.addEventListener('click', () => {
        alert('Play functionality would be implemented here!');
    });
    
    infoBtn.addEventListener('click', () => {
        openModal({
            title: 'Stranger Things',
            rating: '98% Match',
            description: 'When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces, and one strange little girl.',
            image: 'https://images.unsplash.com/photo-1489599735734-79b4169c4388?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        });
    });
}

// Setup scroll effect for header
function setupScrollEffect() {
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Open modal
function openModal(movie) {
    modalTitle.textContent = movie.title;
    modalDescription.textContent = movie.description || `${movie.title} is an amazing ${getRandomGenre()} that you'll love watching. Experience the thrill and excitement with stunning visuals and compelling storytelling.`;
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close modal
function closeModalHandler() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Handle search
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    if (searchTerm.length > 2) {
        // Simple search simulation
        console.log(`Searching for: ${searchTerm}`);
        // In a real app, this would filter the movie cards
    }
}

// Get random genre for description
function getRandomGenre() {
    const genres = ['thriller', 'drama', 'action movie', 'comedy', 'sci-fi series', 'mystery'];
    return genres[Math.floor(Math.random() * genres.length)];
}

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add keyboard navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && modal.style.display === 'block') {
        closeModalHandler();
    }
});

// Add loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Simulate dynamic content loading
setTimeout(() => {
    console.log('Netflix Clone loaded successfully! 🎬');
}, 1000);
