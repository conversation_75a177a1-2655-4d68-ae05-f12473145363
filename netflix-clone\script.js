// Movie Data
const movieData = {
    trending: [
        { title: "Stranger Things", rating: "98% Match", image: "https://m.media-amazon.com/images/M/MV5BMDZkYmVhNjMtNWU4MC00MDQxLWE3MjYtZGMzZWI1ZjhlOWJmXkEyXkFqcGdeQXVyMTkxNjUyNQ@@._V1_FMjpg_UX1000_.jpg" },
        { title: "Wednesday", rating: "92% Match", image: "https://m.media-amazon.com/images/M/MV5BM2ZmMjEyZmYtOGM4YS00YTNhLWE3ZDMtNzQxM2RhNjBlODIyXkEyXkFqcGdeQXVyMTUzMTg2ODkz._V1_FMjpg_UX1000_.jpg" },
        { title: "The Crown", rating: "94% Match", image: "https://m.media-amazon.com/images/M/MV5BZmY0MzBlNjctZWVmMS00ODJhLTg0NmQtZmJkMzk2MDEyNzFkXkEyXkFqcGdeQXVyMTUzMTg2ODkz._V1_FMjpg_UX1000_.jpg" },
        { title: "Bridgerton", rating: "91% Match", image: "https://m.media-amazon.com/images/M/MV5BNTQwMjlhZTMtMmU5MS00ZDE4LWJmOGYtMjk1NjM5YWM3ZjdkXkEyXkFqcGdeQXVyMTkxNjUyNQ@@._V1_FMjpg_UX1000_.jpg" },
        { title: "Squid Game", rating: "96% Match", image: "https://m.media-amazon.com/images/M/MV5BYWE3MDVkN2EtNjQ5MS00ZDQ4LTliNzYtMjc2YWMzMDEwMTA3XkEyXkFqcGdeQXVyMTEzMTI1Mjk3._V1_FMjpg_UX1000_.jpg" },
        { title: "Money Heist", rating: "89% Match", image: "https://m.media-amazon.com/images/M/MV5BODI0ZTljYTMtODQ1NC00NmI0LTk1YWUtN2FlNDM1MDExMDlhXkEyXkFqcGdeQXVyMTMxODk2OTU@._V1_FMjpg_UX1000_.jpg" }
    ],
    originals: [
        { title: "House of Cards", rating: "87% Match", image: "https://m.media-amazon.com/images/M/MV5BODM1MDU2NjY5NF5BMl5BanBnXkFtZTgwMDkxNTcwNjE@._V1_FMjpg_UX1000_.jpg" },
        { title: "Narcos", rating: "93% Match", image: "https://m.media-amazon.com/images/M/MV5BNmVjNzM3NzAtNjg0YS00NGUwLWFmMGYtMjE4ZmNhYjVlZWUwXkEyXkFqcGdeQXVyMTMxODk2OTU@._V1_FMjpg_UX1000_.jpg" },
        { title: "Black Mirror", rating: "96% Match", image: "https://m.media-amazon.com/images/M/MV5BYTM3YWVhMDMtNjczMy00NGEyLWJhZDctYjNhMTRkNDE0ZTI1XkEyXkFqcGdeQXVyMTkxNjUyNQ@@._V1_FMjpg_UX1000_.jpg" },
        { title: "Dark", rating: "97% Match", image: "https://m.media-amazon.com/images/M/MV5BOTk2NzUyOTctZDVkMS00ZGE2LWE3YzMtZGRhYjlmNTBmZTE4XkEyXkFqcGdeQXVyMjg1NDcxNDE@._V1_FMjpg_UX1000_.jpg" },
        { title: "The Umbrella Academy", rating: "88% Match", image: "https://m.media-amazon.com/images/M/MV5BYTU5ZThjNzEtMjQ1Zi00ZWUxLWI4NzEtYWI5MDBiYjVlOGY4XkEyXkFqcGdeQXVyNTgyNTA4MjM@._V1_FMjpg_UX1000_.jpg" },
        { title: "Ozark", rating: "85% Match", image: "https://m.media-amazon.com/images/M/MV5BNDJkMzBkZGUtMzc1MS00YjU5LWI0ZDMtNzE3ZTA4NzJlNzY5XkEyXkFqcGdeQXVyMTkxNjUyNQ@@._V1_FMjpg_UX1000_.jpg" }
    ],
    popular: [
        { title: "The Avengers", rating: "94% Match", image: "https://m.media-amazon.com/images/M/MV5BNDYxNjQyMjAtNTdiOS00NGYwLWFmNTAtNThmYjU5ZGI2YTI1XkEyXkFqcGdeQXVyMTMxODk2OTU@._V1_FMjpg_UX1000_.jpg" },
        { title: "Inception", rating: "96% Match", image: "https://m.media-amazon.com/images/M/MV5BMjAxMzY3NjcxNF5BMl5BanBnXkFtZTcwNTI5OTM0Mw@@._V1_FMjpg_UX1000_.jpg" },
        { title: "The Dark Knight", rating: "98% Match", image: "https://m.media-amazon.com/images/M/MV5BMTMxNTMwODM0NF5BMl5BanBnXkFtZTcwODAyMTk2Mw@@._V1_FMjpg_UX1000_.jpg" },
        { title: "Interstellar", rating: "95% Match", image: "https://m.media-amazon.com/images/M/MV5BZjdkOTU3MDktN2IxOS00OGEyLWFmMjktY2FiMmZkNWIyODZiXkEyXkFqcGdeQXVyMTMxODk2OTU@._V1_FMjpg_UX1000_.jpg" },
        { title: "Pulp Fiction", rating: "92% Match", image: "https://m.media-amazon.com/images/M/MV5BNGNhMDIzZTUtNTBlZi00MTRlLWFjM2ItYzViMjE3YzI5MjljXkEyXkFqcGdeQXVyNzkwMjQ5NzM@._V1_FMjpg_UX1000_.jpg" },
        { title: "The Matrix", rating: "93% Match", image: "https://m.media-amazon.com/images/M/MV5BNzQzOTk3OTAtNDQ0Zi00ZTVkLWI0MTEtMDllZjNkYzNjNTc4L2ltYWdlXkEyXkFqcGdeQXVyNjU0OTQ0OTY@._V1_FMjpg_UX1000_.jpg" }
    ],
    tv: [
        { title: "Breaking Bad", rating: "99% Match", image: "https://image.tmdb.org/t/p/w300/3xnWaLQjelJDDF7LT1WBo6f4BRe.jpg" },
        { title: "Game of Thrones", rating: "91% Match", image: "https://image.tmdb.org/t/p/w300/1XS1oqL89opfnbLl8WnZY1O1uJx.jpg" },
        { title: "Friends", rating: "89% Match", image: "https://image.tmdb.org/t/p/w300/f496cm9enuEsZkSPzCwnTESEK5s.jpg" },
        { title: "The Mandalorian", rating: "96% Match", image: "https://image.tmdb.org/t/p/w300/sWgBv7LV2PRoQgkxwlibdGXKz1S.jpg" },
        { title: "Sherlock", rating: "94% Match", image: "https://image.tmdb.org/t/p/w300/7WTsnHkbA0FaG6R9twfFde0I9hl.jpg" },
        { title: "The Office", rating: "87% Match", image: "https://image.tmdb.org/t/p/w300/7DJKHzAi83BmQrWLrYYOqcoKfhR.jpg" }
    ],
    action: [
        { title: "John Wick", rating: "95% Match", image: "https://image.tmdb.org/t/p/w300/fZPSd91yGE9fCcCe6OoQr6E3Bev.jpg" },
        { title: "Mad Max: Fury Road", rating: "93% Match", image: "https://image.tmdb.org/t/p/w300/hA2ple9q4qnwxp3hKVNhroipsir.jpg" },
        { title: "Mission Impossible", rating: "91% Match", image: "https://image.tmdb.org/t/p/w300/AkJQpZp9WoNdj7pLYSj1L0RcMMN.jpg" },
        { title: "Terminator", rating: "89% Match", image: "https://image.tmdb.org/t/p/w300/qvktm0BHcnmDpul4Hz01GIazWPr.jpg" },
        { title: "Die Hard", rating: "88% Match", image: "https://image.tmdb.org/t/p/w300/yFihWxQcmqcaBR31QM6Y8gT6aYV.jpg" },
        { title: "Fast & Furious", rating: "86% Match", image: "https://image.tmdb.org/t/p/w300/mc5Oq15ZhbXdZpDUT5lOjKBpcBi.jpg" }
    ],
    comedy: [
        { title: "The Hangover", rating: "84% Match", image: "https://image.tmdb.org/t/p/w300/uluhlXqQpOohcXJ8YOjsyBMIRaB.jpg" },
        { title: "Superbad", rating: "82% Match", image: "https://image.tmdb.org/t/p/w300/ek8e8txUyUwd2BNqj6lFEerJfbq.jpg" },
        { title: "Step Brothers", rating: "81% Match", image: "https://image.tmdb.org/t/p/w300/wRR62VjgaECtiZlpb7xqYGqbszN.jpg" },
        { title: "Zoolander", rating: "75% Match", image: "https://image.tmdb.org/t/p/w300/qdrbSneHZjJG2Dj0hhBxzzAo4HB.jpg" },
        { title: "Anchorman", rating: "79% Match", image: "https://image.tmdb.org/t/p/w300/7f3bqOHdZbmd8tFbHopex1BQZH.jpg" },
        { title: "Dumb and Dumber", rating: "77% Match", image: "https://image.tmdb.org/t/p/w300/4LdpBXiCyGKkR8FGHgjKlphrfUc.jpg" }
    ]
};

// DOM Elements
const header = document.querySelector('.header');
const modal = document.getElementById('movie-modal');
const modalTitle = document.getElementById('modal-title');
const modalDescription = document.getElementById('modal-description');
const closeModal = document.querySelector('.close');

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    populateMovieRows();
    setupEventListeners();
    setupScrollEffect();
});

// Populate movie rows
function populateMovieRows() {
    const categories = ['trending', 'originals', 'popular', 'tv', 'action', 'comedy'];
    
    categories.forEach(category => {
        const container = document.getElementById(`${category}-list`);
        const movies = movieData[category];
        
        movies.forEach(movie => {
            const movieCard = createMovieCard(movie);
            container.appendChild(movieCard);
        });
    });
}

// Create movie card element
function createMovieCard(movie) {
    const card = document.createElement('div');
    card.className = 'movie-card';
    card.style.backgroundImage = `url(${movie.image})`;
    card.style.backgroundSize = 'cover';
    card.style.backgroundPosition = 'center';

    card.innerHTML = `
        <div class="movie-info">
            <div class="movie-title">${movie.title}</div>
            <div class="movie-rating">${movie.rating}</div>
        </div>
    `;

    card.addEventListener('click', () => openModal(movie));

    return card;
}

// Setup event listeners
function setupEventListeners() {
    // Modal close functionality
    closeModal.addEventListener('click', closeModalHandler);
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModalHandler();
        }
    });
    
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    searchInput.addEventListener('input', handleSearch);
    
    // Hero buttons
    const playBtn = document.querySelector('.hero-buttons .btn-play');
    const infoBtn = document.querySelector('.hero-buttons .btn-info');
    
    playBtn.addEventListener('click', () => {
        alert('Play functionality would be implemented here!');
    });
    
    infoBtn.addEventListener('click', () => {
        openModal({
            title: 'Stranger Things',
            rating: '98% Match',
            description: 'When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces, and one strange little girl.',
            image: 'https://images.unsplash.com/photo-1489599735734-79b4169c4388?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        });
    });
}

// Setup scroll effect for header
function setupScrollEffect() {
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Open modal
function openModal(movie) {
    modalTitle.textContent = movie.title;
    modalDescription.textContent = movie.description || `${movie.title} is an amazing ${getRandomGenre()} that you'll love watching. Experience the thrill and excitement with stunning visuals and compelling storytelling.`;
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close modal
function closeModalHandler() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Handle search
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    if (searchTerm.length > 2) {
        // Simple search simulation
        console.log(`Searching for: ${searchTerm}`);
        // In a real app, this would filter the movie cards
    }
}

// Get random genre for description
function getRandomGenre() {
    const genres = ['thriller', 'drama', 'action movie', 'comedy', 'sci-fi series', 'mystery'];
    return genres[Math.floor(Math.random() * genres.length)];
}

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add keyboard navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && modal.style.display === 'block') {
        closeModalHandler();
    }
});

// Add loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Simulate dynamic content loading
setTimeout(() => {
    console.log('Netflix Clone loaded successfully! 🎬');
}, 1000);
