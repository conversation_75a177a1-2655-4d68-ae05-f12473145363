// Movie Data
const movieData = {
    trending: [
        { title: "Stranger Things", rating: "98% Match", color: "linear-gradient(135deg, #8B0000, #DC143C)", textColor: "#FFFFFF" },
        { title: "The Witcher", rating: "95% Match", color: "linear-gradient(135deg, #4B0082, #8A2BE2)", textColor: "#FFFFFF" },
        { title: "Money Heist", rating: "92% Match", color: "linear-gradient(135deg, #DC143C, #FF6347)", textColor: "#FFFFFF" },
        { title: "Ozark", rating: "89% Match", color: "linear-gradient(135deg, #2F4F4F, #708090)", textColor: "#FFFFFF" },
        { title: "The Crown", rating: "94% Match", color: "linear-gradient(135deg, #FFD700, #FFA500)", textColor: "#000000" },
        { title: "Bridgerton", rating: "91% Match", color: "linear-gradient(135deg, #FF69B4, #FF1493)", textColor: "#FFFFFF" }
    ],
    originals: [
        { title: "House of Cards", rating: "87% Match", color: "linear-gradient(135deg, #000080, #4169E1)", textColor: "#FFFFFF" },
        { title: "Orange is the New Black", rating: "85% Match", color: "linear-gradient(135deg, #FF8C00, #FF4500)", textColor: "#FFFFFF" },
        { title: "Narcos", rating: "93% Match", color: "linear-gradient(135deg, #8B4513, #D2691E)", textColor: "#FFFFFF" },
        { title: "Black Mirror", rating: "96% Match", color: "linear-gradient(135deg, #000000, #2F2F2F)", textColor: "#FFFFFF" },
        { title: "The Umbrella Academy", rating: "88% Match", color: "linear-gradient(135deg, #FF0000, #FF6347)", textColor: "#FFFFFF" },
        { title: "Dark", rating: "97% Match", color: "linear-gradient(135deg, #1C1C1C, #696969)", textColor: "#FFFFFF" }
    ],
    popular: [
        { title: "The Avengers", rating: "94% Match", color: "linear-gradient(135deg, #FF4500, #FF6347)", textColor: "#FFFFFF" },
        { title: "Inception", rating: "96% Match", color: "linear-gradient(135deg, #4682B4, #87CEEB)", textColor: "#FFFFFF" },
        { title: "The Dark Knight", rating: "98% Match", color: "linear-gradient(135deg, #2F2F2F, #696969)", textColor: "#FFFFFF" },
        { title: "Interstellar", rating: "95% Match", color: "linear-gradient(135deg, #191970, #4169E1)", textColor: "#FFFFFF" },
        { title: "Pulp Fiction", rating: "92% Match", color: "linear-gradient(135deg, #8B0000, #DC143C)", textColor: "#FFFFFF" },
        { title: "The Matrix", rating: "93% Match", color: "linear-gradient(135deg, #006400, #32CD32)", textColor: "#FFFFFF" }
    ],
    tv: [
        { title: "Breaking Bad", rating: "99% Match", color: "linear-gradient(135deg, #228B22, #32CD32)", textColor: "#FFFFFF" },
        { title: "Game of Thrones", rating: "91% Match", color: "linear-gradient(135deg, #8B4513, #D2691E)", textColor: "#FFFFFF" },
        { title: "The Office", rating: "87% Match", color: "linear-gradient(135deg, #4169E1, #87CEEB)", textColor: "#FFFFFF" },
        { title: "Friends", rating: "89% Match", color: "linear-gradient(135deg, #FF69B4, #FF1493)", textColor: "#FFFFFF" },
        { title: "Sherlock", rating: "94% Match", color: "linear-gradient(135deg, #800080, #9370DB)", textColor: "#FFFFFF" },
        { title: "The Mandalorian", rating: "96% Match", color: "linear-gradient(135deg, #2F4F4F, #708090)", textColor: "#FFFFFF" }
    ],
    action: [
        { title: "John Wick", rating: "95% Match", color: "linear-gradient(135deg, #000000, #2F2F2F)", textColor: "#FFFFFF" },
        { title: "Mad Max: Fury Road", rating: "93% Match", color: "linear-gradient(135deg, #FF8C00, #FF4500)", textColor: "#FFFFFF" },
        { title: "Die Hard", rating: "88% Match", color: "linear-gradient(135deg, #B22222, #DC143C)", textColor: "#FFFFFF" },
        { title: "Mission Impossible", rating: "91% Match", color: "linear-gradient(135deg, #2F4F4F, #708090)", textColor: "#FFFFFF" },
        { title: "Fast & Furious", rating: "86% Match", color: "linear-gradient(135deg, #FF4500, #FF6347)", textColor: "#FFFFFF" },
        { title: "Terminator", rating: "89% Match", color: "linear-gradient(135deg, #708090, #2F4F4F)", textColor: "#FFFFFF" }
    ],
    comedy: [
        { title: "The Hangover", rating: "84% Match", color: "linear-gradient(135deg, #FFD700, #FFA500)", textColor: "#000000" },
        { title: "Superbad", rating: "82% Match", color: "linear-gradient(135deg, #32CD32, #00FF00)", textColor: "#FFFFFF" },
        { title: "Anchorman", rating: "79% Match", color: "linear-gradient(135deg, #FF6347, #FF4500)", textColor: "#FFFFFF" },
        { title: "Step Brothers", rating: "81% Match", color: "linear-gradient(135deg, #20B2AA, #00CED1)", textColor: "#FFFFFF" },
        { title: "Dumb and Dumber", rating: "77% Match", color: "linear-gradient(135deg, #9370DB, #8A2BE2)", textColor: "#FFFFFF" },
        { title: "Zoolander", rating: "75% Match", color: "linear-gradient(135deg, #FF1493, #FF69B4)", textColor: "#FFFFFF" }
    ]
};

// DOM Elements
const header = document.querySelector('.header');
const modal = document.getElementById('movie-modal');
const modalTitle = document.getElementById('modal-title');
const modalDescription = document.getElementById('modal-description');
const closeModal = document.querySelector('.close');

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    populateMovieRows();
    setupEventListeners();
    setupScrollEffect();
});

// Populate movie rows
function populateMovieRows() {
    const categories = ['trending', 'originals', 'popular', 'tv', 'action', 'comedy'];
    
    categories.forEach(category => {
        const container = document.getElementById(`${category}-list`);
        const movies = movieData[category];
        
        movies.forEach(movie => {
            const movieCard = createMovieCard(movie);
            container.appendChild(movieCard);
        });
    });
}

// Create movie card element
function createMovieCard(movie) {
    const card = document.createElement('div');
    card.className = 'movie-card';
    card.style.background = movie.color;
    card.style.display = 'flex';
    card.style.alignItems = 'center';
    card.style.justifyContent = 'center';
    card.style.textAlign = 'center';
    card.style.position = 'relative';

    card.innerHTML = `
        <div class="movie-poster-content">
            <div class="movie-title-large" style="color: ${movie.textColor}; font-size: 18px; font-weight: bold; margin-bottom: 10px;">${movie.title}</div>
            <div class="movie-rating" style="color: ${movie.textColor}; font-size: 14px;">${movie.rating}</div>
        </div>
        <div class="movie-info">
            <div class="movie-title">${movie.title}</div>
            <div class="movie-rating">${movie.rating}</div>
        </div>
    `;

    card.addEventListener('click', () => openModal(movie));

    return card;
}

// Setup event listeners
function setupEventListeners() {
    // Modal close functionality
    closeModal.addEventListener('click', closeModalHandler);
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModalHandler();
        }
    });
    
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    searchInput.addEventListener('input', handleSearch);
    
    // Hero buttons
    const playBtn = document.querySelector('.hero-buttons .btn-play');
    const infoBtn = document.querySelector('.hero-buttons .btn-info');
    
    playBtn.addEventListener('click', () => {
        alert('Play functionality would be implemented here!');
    });
    
    infoBtn.addEventListener('click', () => {
        openModal({
            title: 'Stranger Things',
            rating: '98% Match',
            description: 'When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces, and one strange little girl.',
            image: 'https://images.unsplash.com/photo-1489599735734-79b4169c4388?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
        });
    });
}

// Setup scroll effect for header
function setupScrollEffect() {
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Open modal
function openModal(movie) {
    modalTitle.textContent = movie.title;
    modalDescription.textContent = movie.description || `${movie.title} is an amazing ${getRandomGenre()} that you'll love watching. Experience the thrill and excitement with stunning visuals and compelling storytelling.`;
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close modal
function closeModalHandler() {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Handle search
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    if (searchTerm.length > 2) {
        // Simple search simulation
        console.log(`Searching for: ${searchTerm}`);
        // In a real app, this would filter the movie cards
    }
}

// Get random genre for description
function getRandomGenre() {
    const genres = ['thriller', 'drama', 'action movie', 'comedy', 'sci-fi series', 'mystery'];
    return genres[Math.floor(Math.random() * genres.length)];
}

// Add smooth scrolling for better UX
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add keyboard navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && modal.style.display === 'block') {
        closeModalHandler();
    }
});

// Add loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Simulate dynamic content loading
setTimeout(() => {
    console.log('Netflix Clone loaded successfully! 🎬');
}, 1000);
