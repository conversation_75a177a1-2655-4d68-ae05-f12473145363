exports.id = "components_List_index_tsx";
exports.ids = ["components_List_index_tsx"];
exports.modules = {

/***/ "./components/List/index.tsx":
/*!***********************************!*\
  !*** ./components/List/index.tsx ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ List; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ "next/dynamic");
/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ "axios");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(axios__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _styles_Cards_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../styles/Cards.module.scss */ "./styles/Cards.module.scss");
/* harmony import */ var _styles_Cards_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_Cards_module_scss__WEBPACK_IMPORTED_MODULE_4__);

var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\List\\index.tsx";




const Cards = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(() => __webpack_require__.e(/*! import() */ "components_List_Cards_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ./Cards */ "./components/List/Cards.tsx")), {
  loadableGenerated: {
    webpack: () => [/*require.resolve*/(/*! ./Cards */ "./components/List/Cards.tsx")],
    modules: ["..\\components\\List\\index.tsx -> " + './Cards']
  }
});
const FeatureCard = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(() => __webpack_require__.e(/*! import() */ "components_List_FeatureCards_tsx").then(__webpack_require__.bind(__webpack_require__, /*! ./FeatureCards */ "./components/List/FeatureCards.tsx")), {
  loadableGenerated: {
    webpack: () => [/*require.resolve*/(/*! ./FeatureCards */ "./components/List/FeatureCards.tsx")],
    modules: ["..\\components\\List\\index.tsx -> " + './FeatureCards']
  }
});
function List({
  defaultCard = true,
  heading,
  topList = false,
  endpoint
}) {
  const {
    0: media,
    1: setMedia
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);

  async function getEndpoint() {
    try {
      const result = await axios__WEBPACK_IMPORTED_MODULE_3___default().get(endpoint);
      setMedia(result.data.data);
    } catch (error) {}
  }

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    getEndpoint();
  }, []);
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    className: (_styles_Cards_module_scss__WEBPACK_IMPORTED_MODULE_4___default().listContainer),
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("strong", {
      className: (_styles_Cards_module_scss__WEBPACK_IMPORTED_MODULE_4___default().category),
      children: heading
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 39,
      columnNumber: 7
    }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
      className: (_styles_Cards_module_scss__WEBPACK_IMPORTED_MODULE_4___default().cardRow),
      children: media === null || media === void 0 ? void 0 : media.map((item, index) => {
        if (topList) {
          if (index < 10) {
            return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeatureCard, {
              index: index + 1,
              item: item
            }, index, false, {
              fileName: _jsxFileName,
              lineNumber: 44,
              columnNumber: 22
            }, this);
          }
        } else {
          return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Cards, {
            defaultCard: defaultCard,
            item: item
          }, index, false, {
            fileName: _jsxFileName,
            lineNumber: 48,
            columnNumber: 15
          }, this);
        }
      })
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 40,
      columnNumber: 7
    }, this)]
  }, void 0, true, {
    fileName: _jsxFileName,
    lineNumber: 38,
    columnNumber: 5
  }, this);
}

/***/ }),

/***/ "./styles/Cards.module.scss":
/*!**********************************!*\
  !*** ./styles/Cards.module.scss ***!
  \**********************************/
/***/ (function(module) {

// Exports
module.exports = {
	"listContainer": "Cards_listContainer__30SLO",
	"category": "Cards_category__1LVSN",
	"cardRow": "Cards_cardRow__c5hKo",
	"card": "Cards_card__3bNVM",
	"cardPoster": "Cards_cardPoster__2kqBQ",
	"cardInfo": "Cards_cardInfo__1dmoo",
	"modalButton": "Cards_modalButton__2E9yU",
	"dot": "Cards_dot__1YVzI",
	"longCard": "Cards_longCard__3-dkk",
	"more": "Cards_more__3NGxW",
	"actionRow": "Cards_actionRow__D-VbC",
	"row": "Cards_row__1aNq9",
	"textDetails": "Cards_textDetails__3b7mT",
	"greenText": "Cards_greenText__3cdPR",
	"rating": "Cards_rating__RY5RD",
	"regularText": "Cards_regularText__cXNoB",
	"container": "Cards_container__2LFU_",
	"rank": "Cards_rank__3xDBG",
	"featureCard": "Cards_featureCard__3UyRI",
	"poster": "Cards_poster__R1DQx",
	"info": "Cards_info__3VSUh"
};


/***/ })

};
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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