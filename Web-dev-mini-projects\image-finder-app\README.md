# Image Finder - React Application   

## About the Project
A single page web application that uses the PixaBay API to display the images according to user search.

## Technologies Used

- React
  - Hooks
  - Components
- CSS
- Javascript
  - Axios
  - ES6
- API
- MaterialUI


## API Used
PixaBay API

Link : https://pixabay.com/api/

## How to use the Project

1. Download or clone the repository `git clone https://github.com/Ayushparikh-code/Web-dev-mini-projects.git`
2. Go to the directory
3. Open the terminal, and type `npm start`
4. After application opens, there are two search input, one is asking for image title and second one is for number of images.
5. If number of images less than 3 or more than 200 , it will show a message to the user.


## Screenshots

Initial UI looks like:

<img src="https://github.com/ayushseth07/Web-dev-mini-projects/blob/patch/image-finder-app/Screenshots/ss1.png" />

After user search for a image:

<img src="https://github.com/ayushseth07/Web-dev-mini-projects/blob/patch/image-finder-app/Screenshots/ss2.png" />

A message will be shown if user search for a image and number of images are less than 3 or more than 200:

<img src="https://github.com/ayushseth07/Web-dev-mini-projects/blob/patch/image-finder-app/Screenshots/ss3.png" />
