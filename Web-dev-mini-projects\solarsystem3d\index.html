<!DOCTYPE html>
<!--coding with lucifer the devil !! -->
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>3D solar System By team Elite</title>
  <link rel="stylesheet" href="style.css" />
</head>

<body class="opening hide-UI view-2D zoom-large data-close controls-close">
  <div id="navbar">
    <a id="toggle-data" href="#data"><i class="icon-data"></i>Data</a>
    <h1>3D Solar System by Team Elite<br /></h1>
    <a id="toggle-controls" href="#controls"><i class="icon-controls"></i>Controls</a>
  </div>

  <button class="menu-button">SHOW MORE</button>
    <div class="sidebar">
        <button class="close-button">X</button>
        <ul>
            <li>
                <a href="https://en.wikipedia.org/wiki/99942_Apophis">
                    <span class="object-name">99942 Apophis</span>
                    <span class="object-description">Description:  Discovered in 2004, Apophis is a near-Earth asteroid approximately 370 meters in diameter. Initially predicted to have a chance of impacting Earth in 2029, further observations have ruled out that possibility.</span>
                </a>
            </li>
            <li>
                <a href="https://en.wikipedia.org/wiki/101955_Bennu">
                    <span class="object-name">2004 MN4 (Apophis)</span>
                    <span class="object-description">Description:Apophis is a large asteroid with a diameter of about 370 meters, known for its significant close approaches to Earth. When it was first discovered, it garnered attention due to its potential impact threat in 2029.</span>
                </a>
            </li>
            <li>
                <a href="https://en.wikipedia.org/wiki/2006_QV89.html">
                    <span class="object-name">O2006 QV89</span>
                    <span class="object-description">Description:  2006 QV89 is a near-Earth asteroid with a diameter of approximately 40 meters. It gained notoriety in 2019 when it was identified as a potential impactor, with an uncertain trajectory that sparked concern.</span>
                </a>
            </li>
            <li>
                <a href="https://en.wikipedia.org/wiki/2001_FO32.html">
                    <span class="object-name">101955 Bennu</span>
                    <span class="object-description">Description: Bennu is a carbon-rich asteroid approximately 492 meters in diameter. It is of significant interest due to its potential to impact Earth in the late 22nd century. NASA's OSIRIS-REx mission successfully collected samples from Bennu's surface in 2020, and the samples are expected to return to Earth in 2023.</span>
                </a>
            </li>
            <li>
                <a href="https://en.wikipedia.org/wiki/2004_MN4">
                    <span class="object-name">2001 FO32</span>
                    <span class="object-description">Description:  2001 FO32 is an Apollo-type asteroid with an estimated diameter of 900 meters. It is known for its close encounters with Earth, particularly in March 2021 when it passed within 2 million kilometers. </span>
                </a>
            </li>
        </ul>
    </div>



  <div id="data">
    <a class="sun" title="sun" href="#sunspeed">Sun</a>
    <a class="mercury" title="mercury" href="#mercuryspeed">Mercury</a>
    <a class="venus" title="venus" href="#venusspeed">Venus</a>
    <a class="earth active" title="earth" href="#earthspeed">Earth</a>
    <a class="mars" title="mars" href="#marsspeed">Mars</a>
    <a class="jupiter" title="jupiter" href="#jupiterspeed">Jupiter</a>
    <a class="saturn" title="saturn" href="#saturnspeed">Saturn</a>
    <a class="uranus" title="uranus" href="#uranusspeed">Uranus</a>
    <a class="neptune" title="neptune" href="#neptunespeed">Neptune</a>
  </div>
  <div id="controls">
    <label class="set-view">
      <input type="checkbox" />
    </label>
    <label class="set-zoom">
      <input type="checkbox" />
    </label>
    <label>
      <input type="radio" class="set-speed" name="scale" checked />
      <span>Speed</span>
    </label>
    <label>
      <input type="radio" class="set-size" name="scale" />
      <span>Size</span>
    </label>
    <label>
      <input type="radio" class="set-distance" name="scale" />
      <span>Distance</span>
    </label>
  </div>
  <div id="universe" class="scale-stretched">
    <div id="galaxy">
      <div id="solar-system" class="earth">
        <div id="mercury" class="orbit">
          <div class="pos">
            <div class="planet launch-button" id="launchButton" data-key="mercury">
              <dl class="infos">
                <dt>Mercury</dt>
                <dd><span></span></dd>
              </dl>
            </div>
          </div>
        </div>
        <div id="venus" class="orbit">
          <div class="pos">
            <div class="planet launch-button" id="launchButton" data-key="venus">
              <dl class="infos">
                <dt>Venus</dt>
                <dd><span></span></dd>
              </dl>
            </div>
          </div>
        </div>
        <div id="earth" class="orbit">
          <div class="pos">
            <div class="orbit">
              <div class="pos">
                <div class="moon"></div>
              </div>
            </div>
            <div class="planet launch-button" id="launchButton" data-key="earth">
              <dl class="infos">
                <dt>Earth</dt>
                <dd><span></span></dd>
              </dl>
            </div>
          </div>
        </div>
        <div id="mars" class="orbit">
          <div class="pos">
            <div class="planet launch-button" id="launchButton" data-key="mars">
              <dl class="infos">
                <dt>Mars</dt>
                <dd><span></span></dd>
              </dl>
            </div>
          </div>
        </div>
        <div id="jupiter" class="orbit">
          <div class="pos">
            <div class="planet launch-button" id="launchButton" data-key="jupiter">
              <dl class="infos">
                <dt>Jupiter</dt>
                <dd><span></span></dd>
              </dl>
            </div>
          </div>
        </div>
        <div id="saturn" class="orbit">
          <div class="pos">
            <div class="planet launch-button" id="launchButton" data-key="saturn">
              <div class="ring"></div>
              <dl class="infos">
                <dt>Saturn</dt>
                <dd><span></span></dd>
              </dl>
            </div>
          </div>
        </div>
        <div id="uranus" class="orbit">
          <div class="pos">
            <div class="planet launch-button" id="launchButton" data-key="uranus">
              <dl class="infos">
                <dt>Uranus</dt>
                <dd><span></span></dd>
              </dl>
            </div>
          </div>
        </div>
        <div id="neptune" class="orbit">
          <div class="pos">
            <div class="planet launch-button" id="launchButton" data-key="neptune">
              <dl class="infos">
                <dt>Neptune</dt>
                <dd><span></span></dd>
              </dl>
            </div>
          </div>
        </div>
        <div id="sun">
          <dl class="infos">
            <dt>Sun</dt>
            <dd><span></span></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
  <a class="advanced-button" href="https://satyam32-sat.github.io/Solar2dPrototype/">ADV 2D</a>

  <button class="spaceship-btn" onclick="window.location.href='NASA_Game_Dashboard/index.html'">
    🚀
  </button>
  <script src="//ajax.googleapis.com/ajax/libs/jquery/1.8.1/jquery.min.js"></script>
  <script type="text/javascript">


document.addEventListener("DOMContentLoaded", function() {
    const menuButton = document.querySelector('.menu-button');
    const sidebar = document.querySelector('.sidebar');
    const closeButton = document.querySelector('.close-button');

    menuButton.addEventListener('click', function() {
        // Toggle the sidebar's position
        if (sidebar.style.left === '0%') {
            sidebar.style.left = '-50%'; // Hide the sidebar
        } else {
            sidebar.style.left = '0%'; // Show the sidebar
        }
    });

    closeButton.addEventListener('click', function() {
        sidebar.style.left = '-50%'; // Hide the sidebar
    });
});


    // Data object with detailed facts about each planet
    const data = {
      earth: {
        title: "Earth",
        facts: `Earth is the third planet from the Sun and the only one known to support life. It has a diverse range of ecosystems and climates, from tropical rainforests to polar ice caps. Earth's surface is 71% water, mostly in the form of oceans, which play a crucial role in regulating the planet's climate and weather patterns. The remaining 29% is land, comprising continents and islands with various terrains. Earth's atmosphere, rich in nitrogen and oxygen, shields the planet from harmful solar radiation and helps sustain life. It rotates on its axis every 24 hours, creating day and night, and orbits the Sun every 365.25 days, leading to the changing seasons. The planet's magnetic field, generated by its molten iron core, protects it from solar winds and cosmic radiation. Human activities, such as deforestation and pollution, are affecting Earth's ecosystems and climate, highlighting the need for sustainable practices and environmental conservation.`,
      },
      mars: {
        title: "Mars",
        facts: `Mars, the fourth planet from the Sun, is known for its reddish appearance due to iron oxide, or rust, on its surface. It is a terrestrial planet with a thin atmosphere, composed mostly of carbon dioxide, with traces of nitrogen and argon. Mars has the largest volcano in the solar system, Olympus Mons, and a canyon system, Valles Marineris, that stretches over 4,000 kilometers. The planet's surface features include polar ice caps, seasonal dust storms, and ancient riverbeds, suggesting it once had liquid water. Mars experiences extreme temperature variations, with averages around -80 degrees Fahrenheit. Despite its harsh conditions, Mars has been a focal point for exploration, with rovers like Curiosity and Perseverance providing insights into its geology and potential for past life. Future missions aim to investigate its potential for human colonization.`,
      },
      jupiter: {
        title: "Jupiter",
        facts: `Jupiter is the largest planet in our solar system, with a diameter of about 86,881 miles. It is a gas giant with a composition primarily of hydrogen and helium. Jupiter's most notable feature is the Great Red Spot, a massive storm that has been raging for at least 400 years. The planet has a strong magnetic field and numerous moons, including the four largest, known as the Galilean moons: Io, Europa, Ganymede, and Callisto. Jupiter's atmosphere is characterized by its banded appearance, with alternating zones of high and low pressure creating colorful cloud bands. The planet has a very fast rotation, completing one spin in just under 10 hours. Jupiter's intense gravity influences many objects in the solar system, and its massive size makes it a key player in the dynamics of planetary science.`,
      },
      saturn: {
        title: "Saturn",
        facts: `Saturn is the sixth planet from the Sun and is famous for its prominent ring system. The planet is a gas giant, composed mainly of hydrogen and helium. Saturn's rings are made up of countless ice and rock particles, varying in size from tiny grains to large chunks. The planet has a diameter of about 72,367 miles and is known for its low density, as it is less dense than water. Saturn has a strong magnetic field and dozens of moons, with Titan being the largest and having a thick atmosphere of nitrogen and methane. The planet's atmosphere features bands of clouds and storms, including the mysterious hexagonal storm system at its north pole. Saturn's complex ring system and its diverse collection of moons make it a fascinating subject for study.`,
      },
      uranus: {
        title: "Uranus",
        facts: `Uranus, the seventh planet from the Sun, is unique for its extreme tilt, with an axial tilt of about 98 degrees. This tilt causes its poles to face the Sun directly, leading to extreme seasonal variations. The planet is an ice giant with a composition of hydrogen, helium, and water, ammonia, and methane ices. Uranus has a faint ring system and 27 known moons, with Titania, Oberon, Ariel, and Umbriel being the largest. The planet's blue-green color is due to the presence of methane in its atmosphere, which absorbs red light and reflects blue. Uranus has a relatively low internal heat, and its magnetic field is tilted compared to its rotational axis. The planet was discovered in 1781 by William Herschel and has been studied by the Voyager 2 spacecraft.`,
      },
      neptune: {
        title: "Neptune",
        facts: `Neptune is the eighth and farthest planet from the Sun. It is a gas giant with a composition similar to Uranus, primarily composed of hydrogen, helium, and methane. Neptune's striking blue color is due to the absorption of red light by methane in its atmosphere. The planet has the strongest winds in the solar system, reaching speeds of up to 1,200 miles per hour. Neptune has a faint ring system and 14 known moons, with Triton being the largest. Triton is unique for its retrograde orbit and geysers that spew nitrogen gas. Neptune's atmosphere is characterized by its dynamic weather patterns, including large storms and high-altitude clouds. The planet was discovered in 1846 through mathematical predictions and has been studied by the Voyager 2 spacecraft, which provided valuable data on its atmospheric conditions and ring system.`,
      },
      mercury: {
        title: "Mercury",
        facts: `Mercury is the closest planet to the Sun and has the shortest orbital period of any planet in the solar system, completing one orbit in about 88 Earth days. It is a rocky planet with a diameter of about 3,032 miles. Mercury's surface is heavily cratered, similar to the Moon, due to impacts from space debris. The planet has a very thin atmosphere, composed mostly of oxygen, sodium, hydrogen, helium, and potassium. Mercury experiences extreme temperature fluctuations, ranging from about 800 degrees Fahrenheit during the day to -330 degrees Fahrenheit at night. The planet has a weak magnetic field and no known moons. Mercury's surface features include vast plains, steep cliffs, and large impact basins, making it an interesting subject for geological studies.`,
      },
      venus: {
        title: "Venus",
        facts: `Venus, the second planet from the Sun, is similar in size and composition to Earth but has a very different atmosphere and surface conditions. The planet is covered by thick clouds of sulfuric acid and has a surface pressure about 92 times that of Earth. Venus's atmosphere is primarily composed of carbon dioxide, with trace amounts of nitrogen and other gases. This dense atmosphere creates a strong greenhouse effect, leading to surface temperatures that can reach up to 900 degrees Fahrenheit, hotter than any other planet in the solar system. Venus rotates very slowly on its axis, taking about 243 Earth days to complete one rotation, and has a rotation direction opposite to most planets. The planet has no moons and features a rocky surface with mountains, valleys, and volcanic plains.`,
      },
    };

    function openMenu() {
      document.getElementById("sideMenu").style.width = "250px";
    }

    function closeMenu() {
      document.getElementById("sideMenu").style.width = "0";
    }

    // const launchButton = document.getElementById("launchButton");
    const launchButtons = document.querySelectorAll(".launch-button");
    const popupCard = document.getElementById("popupCard");
    const closePopup = document.getElementById("closePopup");
    const popupTitle = document.getElementById("popupTitle");
    const popupFacts = document.getElementById("popupFacts");

    launchButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const key = button.getAttribute("data-key");
        const info = data[key];

        // Update popup content
        popupTitle.textContent = info.title;
        popupFacts.textContent = info.facts;

        // Show popup with animation
        popupCard.classList.add("show");
      });
    });

    // Show the popup card with animation
    // launchButton.addEventListener("click", () => {
    //   popupCard.classList.add("show");
    // });

    // Close the popup when the close button is clicked
    closePopup.addEventListener("click", () => {
      popupCard.classList.remove("show");
    });

    // Close the popup when clicking outside of it
    window.addEventListener("click", (e) => {
      if (e.target === popupCard) {
        popupCard.classList.remove("show");
      }
    });

    if (typeof jQuery == "undefined") {
      document.write(
        unescape(
          "%3Cscript src='js/jquery.min.js' type='text/javascript'%3E%3C/script%3E"
        )
      );
    }
  </script>
  <script src="js/prefixfree.min.js"></script>
  <script src="js/scripts.min.js"></script>
  <script src="script.js"></script>
</body>

</html>