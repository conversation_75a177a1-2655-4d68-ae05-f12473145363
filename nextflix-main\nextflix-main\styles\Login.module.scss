@import './variables.scss';

.container {
  min-height: 100vh;
  padding: 0 0.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.main {
  padding: 5rem 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &__bgImage {
    z-index: 0;
    background-color: #0e0e0e99;
  }

  &__card {
    margin: 1rem;
    padding: 1.5rem;
    text-align: left;
    color: inherit;
    background-color:$background-overlay;
    color: $white;
    text-decoration: none;
    z-index: 100;
    border-radius: 0.5rem;

    .button {
      background-color: $primary-button;
      padding: 0.7rem;
      text-align: center;
      border-radius: 0.2rem;
      min-width: 18rem ;
      transition: linear 200ms ease-in-out;

      &:hover {
        background-color: $primary-buttonHover;
        cursor: pointer;
        transform: scale(1.02);
      }
    }
  }


}
