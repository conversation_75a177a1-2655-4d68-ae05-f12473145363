*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.container {
  width: 100%;
}

@media (min-width: 480px) {
  .container {
    max-width: 480px;
  }
}

@media (min-width: 635px) {
  .container {
    max-width: 635px;
  }
}

@media (min-width: 1000px) {
  .container {
    max-width: 1000px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

@media (min-width: 1600px) {
  .container {
    max-width: 1600px;
  }
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.-left-2 {
  left: -0.5rem;
}

.left-2\/4 {
  left: 50%;
}

.top-2\/4 {
  top: 50%;
}

.top-96 {
  top: 24rem;
}

.z-10 {
  z-index: 10;
}

.z-\[1\] {
  z-index: 1;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-auto {
  margin-bottom: auto;
}

.ms-auto {
  margin-inline-start: auto;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-52 {
  margin-top: 13rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-auto {
  margin-top: auto;
}

.block {
  display: block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.h-10 {
  height: 2.5rem;
}

.h-20 {
  height: 5rem;
}

.h-5 {
  height: 1.25rem;
}

.h-64 {
  height: 16rem;
}

.h-7 {
  height: 1.75rem;
}

.h-80 {
  height: 20rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[1px\] {
  height: 1px;
}

.h-\[450px\] {
  height: 450px;
}

.max-h-\[80px\] {
  max-height: 80px;
}

.min-h-\[560px\] {
  min-height: 560px;
}

.w-10 {
  width: 2.5rem;
}

.w-20 {
  width: 5rem;
}

.w-32 {
  width: 8rem;
}

.w-5 {
  width: 1.25rem;
}

.w-64 {
  width: 16rem;
}

.w-7 {
  width: 1.75rem;
}

.w-9 {
  width: 2.25rem;
}

.w-\[155px\] {
  width: 155px;
}

.w-full {
  width: 100%;
}

.min-w-\[450px\] {
  min-width: 450px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-56 {
  max-width: 14rem;
}

.max-w-64 {
  max-width: 16rem;
}

.max-w-\[550px\] {
  max-width: 550px;
}

.max-w-max {
  max-width: -moz-max-content;
  max-width: max-content;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-xl {
  max-width: 36rem;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.grow {
  flex-grow: 1;
}

.-translate-x-2\/4 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bouncing {
  0%, 100% {
    transform: translateY(-25%);
  }

  50% {
    transform: translateY(0);
  }
}

.animate-bouncing {
  animation: bouncing 12s linear infinite;
}

.cursor-pointer {
  cursor: pointer;
}

.snap-x {
  scroll-snap-type: x var(--tw-scroll-snap-strictness);
}

.snap-mandatory {
  --tw-scroll-snap-strictness: mandatory;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.flex-wrap {
  flex-wrap: wrap;
}

.place-items-center {
  place-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}

.gap-x-20 {
  -moz-column-gap: 5rem;
       column-gap: 5rem;
}

.gap-y-1 {
  row-gap: 0.25rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.border {
  border-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-solid {
  border-style: solid;
}

.border-\[\#1111111a\] {
  border-color: #1111111a;
}

.border-butterscotch {
  --tw-border-opacity: 1;
  border-color: rgb(216 146 69 / var(--tw-border-opacity, 1));
}

.border-t-caf_noir {
  border-top-color: #4f3120ff;
}

.bg-\[\#4f3120db\] {
  background-color: #4f3120db;
}

.bg-\[\#d89245c7\] {
  background-color: #d89245c7;
}

.bg-butterscotch {
  --tw-bg-opacity: 1;
  background-color: rgb(216 146 69 / var(--tw-bg-opacity, 1));
}

.bg-butterscotch_light {
  background-color: #dd9d4cff;
}

.bg-caf_noir {
  background-color: #4f3120ff;
}

.bg-papaya_whip {
  background-color: #fff3dcff;
}

.bg-cover {
  background-size: cover;
}

.bg-center {
  background-position: center;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.p-3 {
  padding: 0.75rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.text-center {
  text-align: center;
}

.font-primary {
  font-family: "Cormorant Garamond", serif;
}

.font-secondary {
  font-family: "Montserrat", sans-serif;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-\[13px\] {
  font-size: 13px;
}

.text-\[22px\] {
  font-size: 22px;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.italic {
  font-style: italic;
}

.leading-\[0\] {
  line-height: 0;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.text-butterscotch {
  --tw-text-opacity: 1;
  color: rgb(216 146 69 / var(--tw-text-opacity, 1));
}

.text-caf_noir {
  color: #4f3120ff;
}

.text-jet {
  color: #333333ff;
}

.text-licorice {
  color: #281811ff;
}

.text-papaya_whip {
  color: #fff3dcff;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.opacity-0 {
  opacity: 0;
}

.opacity-5 {
  opacity: 0.05;
}

.blur-lg {
  --tw-blur: blur(16px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.brightness-\[0\.3\] {
  --tw-brightness: brightness(0.3);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-75 {
  transition-duration: 75ms;
}

.duration-\[0\.5s\] {
  transition-duration: 0.5s;
}

.ease-linear {
  transition-timing-function: linear;
}

/*-------------------------*\
  #RESET
\*-------------------------*/

a,
img,
span,
button {
  display: block;
}

html {
  scroll-behavior: smooth;
}

/*-------------------------*\
  #REUSED STYLE
\*-------------------------*/

.material-symbols-rounded {
  font-variation-settings: "FILL" 0, "wght" 400, "GRAD" 0, "opsz" 24;
}

ion-icon {
  --ionicon-stroke-width: 50px;
}

.img-holder {
  overflow: hidden;
  aspect-ratio: var(--width) / var(--height);
}

.img-cover {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.grid-list > li:nth-child(even):not(.off-card) {
  margin-inline-start: auto;
}

@media (min-width: 635px) {
  .grid-list > li:nth-child(even):not(.off-card) {
    margin-inline-start: 0px;
  }
}

/*-------------------------*\
  #COMPONENTS
\*-------------------------*/

/**
 * Dropdown
 */

.dropdown {
  position: relative;
}

.dropdown-content {
  visibility: hidden;
  height: 0px;
}

.dropdown-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.dropdown-content {
  border-bottom-width: 1px;
  border-style: solid;
  border-color: #fef3c717;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #f2efe3ff;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.22, 0.37, 1, 1);
}

@media (min-width: 1000px) {
  .dropdown-content {
    position: absolute;
    left: -2rem;
    top: 2.5rem;
    min-width: 125px;
  }

  .dropdown-content > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
  }

  .dropdown-content {
    border-radius: 0.375rem;
    background-color: #fff3dcff;
    color: #333333ff;
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}

@media (min-width: 1600px) {
  .dropdown-content {
    min-width: 10rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.dropdown-content {
  clip-path: inset(0 0 100% 0);
}

.dropdown.active .dropdown-content {
  visibility: visible;
  height: 184px;
}

@media (min-width: 1600px) {
  .dropdown.active .dropdown-content {
    height: 15rem;
  }
}

.dropdown.active .dropdown-content {
  clip-path: inset(0 0 0 0);
}

.navbar-link:has(~ .dropdown.active) ion-icon {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dropdown-content a {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.dropdown-content a:hover {
  --tw-text-opacity: 1;
  color: rgb(216 146 69 / var(--tw-text-opacity, 1));
}

/**
 * Button
 */

.btn {
  max-width: -moz-max-content;
  max-width: max-content;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  transition-duration: 200ms;
}

.btn:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-text {
  color: #4f3120ff;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(216 146 69 / var(--tw-ring-opacity, 1));
}

.btn-text:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/**
 * Card
 */

.card {
  min-height: 219px;
  border-radius: 0.375rem;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

@media (min-width: 1000px) {
  .card {
    min-height: 360px;
  }
}

@media (min-width: 1200px) {
  .card {
    min-height: 219px;
  }
}

/**
 * Slider
 */

.slider {
  position: relative;
  overflow: hidden;
}

.slider-item {
  position: absolute;
  top: 0px;
  left: 0px;
  overflow: hidden;
  background-color: #fff3dcff;
  transition: left 0.5s cubic-bezier(0.645, 0.045, 0.354, 1);
}

.slider-item:nth-child(1) {
  left: calc(-260px + -20px);
}

.slider-item:nth-child(2) {
  left: 0px;
}

.slider-item:nth-child(3) {
  left: calc(260px + 20px);
}

.slider-item:nth-child(4) {
  left: calc((260px + 20px) * 2);
}

.slider-item:nth-child(5) {
  left: calc((260px + 20px) * 3);
}

.slider-item:nth-child(6) {
  left: calc((260px + 20px) * 4);
}

.slider-item:nth-child(7) {
  left: calc((260px + 20px) * 5);
}

.slider-item:nth-child(8) {
  left: calc((260px + 20px) * 6);
}

.slider-item:nth-child(9) {
  left: calc((260px + 20px) * 7);
}

.slider-item:nth-child(10) {
  left: calc((260px + 20px) * 8);
}

/**
 * Arrow button
 */

.arrow-button {
  display: grid;
  height: 2.5rem;
  width: 2.5rem;
  place-items: center;
  border-radius: 0.125rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(216 146 69 / var(--tw-border-opacity, 1));
}

/**
 * Avatar
 */

.avatar {
  height: 3rem;
  width: 3rem;
  flex-shrink: 0;
  border-radius: 9999px;
}

/*-------------------------*\
  #HEADER
\*-------------------------*/

.header {
  position: fixed;
  left: 0px;
  top: 0px;
  z-index: 30;
  height: 5rem;
  width: 100%;
  background-color: #4f3120ff;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.22, 0.37, 1, 1);
}

@media (min-width: 1200px) {
  .header {
    background-color: transparent;
  }
}

@keyframes fadeInDown {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0);
  }
}

.header.active {
  animation: fadeInDown 500ms ease-in-out 0s 1 running;
  --tw-shadow: 0 0 35px 0 #281811ff;
  --tw-shadow-colored: 0 0 35px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (min-width: 1200px) {
  .header.active {
    background-color: #4f3120ff;
  }
}

.header .container {
  /* responsive for lg:1200px screen */
}

@media (min-width: 1200px) {
  .header .container {
    margin-left: 0px;
    margin-right: 0px;
    width: 50%;
    max-width: calc(100% - 600px);
    padding-top: 1.75rem;
    padding-bottom: 1.75rem;
    padding-left: 1.75rem;
    padding-right: 1.75rem;
  }
}

@media (min-width: 1400px) {
  .header .container {
    width: 100%;
    max-width: calc(100% - 666px);
  }
}

@media (min-width: 1600px) {
  .header .container {
    margin-left: auto;
    margin-right: auto;
    display: grid;
    max-width: 1650px;
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.navbar {
  visibility: hidden;
  position: fixed;
  right: 0px;
  top: 0px;
  z-index: 50;
  height: 100vh;
  width: 20rem;
  --tw-translate-x: 20rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  background-color: #281811ff;
  padding-top: 5rem;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.22, 0.37, 1, 1);
}

@media (min-width: 1000px) {
  .navbar {
    all: unset;
  }
}

.navbar.active {
  visibility: visible;
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.nav-close-btn {
  position: absolute;
  top: 1.25rem;
  right: 0.75rem;
  display: grid;
  height: 2.5rem;
  width: 2.5rem;
  place-items: center;
  font-size: 28px;
  color: #f2efe3ff;
}

@media (min-width: 1000px) {
  .nav-close-btn {
    display: none;
  }
}

.navbar-link {
  cursor: pointer;
  border-bottom-width: 1px;
  border-style: solid;
  border-color: #fef3c717;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  font-weight: 700;
  text-transform: uppercase;
  color: #f2efe3ff;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.navbar-link:hover {
  --tw-text-opacity: 1;
  color: rgb(216 146 69 / var(--tw-text-opacity, 1));
}

@media (min-width: 1000px) {
  .navbar-link {
    border-bottom-width: 0px;
    padding: 0px;
  }
}

@media (min-width: 1600px) {
  .navbar-link {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

/**
 * cart
 */

.cart-toggler {
  position: relative;
  display: grid;
  height: 2.5rem;
  width: 2.5rem;
  place-items: center;
  border-radius: 9999px;
}

.cart-toggler::before {
  position: absolute;
  right: 0px;
  top: 0px;
  z-index: 10;
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 27 27 / var(--tw-bg-opacity, 1));
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  color: #fff3dcff;
  --tw-content: '2';
  content: var(--tw-content);
}

@media (min-width: 1200px) {
  .cart-toggler {
    background-color: #4f3120ff;
  }
}

.cart-modal {
  visibility: hidden;
  position: fixed;
  top: 6rem;
  right: 0.5rem;
  z-index: 20;
  max-width: 18rem;
  transform-origin: top;
  border-radius: 0.5rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  opacity: 0;
  --tw-shadow: 0 -4px 0 #dd9d4cff;
  --tw-shadow-colored: 0 -4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-duration: 75ms;
  transition-timing-function: cubic-bezier(0.22, 0.37, 1, 1);
}

@media (min-width: 635px) {
  .cart-modal {
    right: 3.5rem;
  }
}

.cart-modal {
  background: linear-gradient(359deg, #fde6b9, #fff3dcff);
}

.cart-modal.active {
  visibility: visible;
  --tw-translate-y: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1;
}

.cart-modal .material-symbols-rounded {
  font-variation-settings: "opsz" 20;
}

/**
 * Overlay
 */

.overlay {
  visibility: hidden;
  position: fixed;
  inset: 0px;
  z-index: 40;
  opacity: 0;
  transition-duration: 75ms;
  transition-timing-function: cubic-bezier(0.22, 0.37, 1, 1);
}

.overlay.active {
  visibility: visible;
  opacity: 1;
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

/*-------------------------*\
  #HERO
\*-------------------------*/

.hero {
  position: relative;
  background-color: #4f3120ff;
  padding-top: 5rem;
}

@media (min-width: 1200px) {
  .hero::before {
    all: unset;
  }
}

.hero .container {
  /* response for lg:1200px screen */
}

@media (min-width: 1200px) {
  .hero .container {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.hero-banner {
  position: absolute;
  inset: 0px;
}

@media (min-width: 1200px) {
  .hero-banner {
    left: auto;
    max-width: 50%;
  }
}

.brand-list::-webkit-scrollbar {
  display: none;
}

.brand-list-item {
  display: grid;
  height: 3.5rem;
  min-width: 8rem;
  cursor: pointer;
  place-content: center;
  border-radius: 0.375rem;
  padding: 0.5rem;
}

/*-------------------------*\
  #TESTIMONIALS
\*-------------------------*/

.testimonial-card:nth-child(1) {
  left: calc(-450px + -20px);
}

@media (min-width: 635px) {
  .testimonial-card:nth-child(1) {
    left: calc(-580px + -30px);
  }
}

@media (min-width: 1000px) {
  .testimonial-card:nth-child(1) {
    left: calc(-756px + -30px);
  }
}

.testimonial-card:nth-child(2) {
  left: 0px;
}

.testimonial-card:nth-child(3) {
  left: calc(450px + 20px);
}

@media (min-width: 635px) {
  .testimonial-card:nth-child(3) {
    left: calc(580px + 30px);
  }
}

@media (min-width: 1000px) {
  .testimonial-card:nth-child(3) {
    left: calc(756px + 30px);
  }
}

.testimonial-card:nth-child(4) {
  left: calc((450px + 20px) * 2);
}

@media (min-width: 635px) {
  .testimonial-card:nth-child(4) {
    left: calc((580px + 30px) * 2);
  }
}

@media (min-width: 1000px) {
  .testimonial-card:nth-child(4) {
    left: calc((756px + 30px) * 2);
  }
}

/*-------------------------*\
  #FOOTER
\*-------------------------*/

.parallax {
  min-height: 300px;
  background-size: cover;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
}

@media (min-width: 1400px) {
  .parallax {
    min-height: 24rem;
  }
}

.group:hover .group-hover\:-translate-y-2\/4 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-\[1\.01\] {
  --tw-scale-x: 1.01;
  --tw-scale-y: 1.01;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

@media (min-width: 480px) {
  .xs\:order-1 {
    order: 1;
  }

  .xs\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .xs\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .xs\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 635px) {
  .sm\:-left-16 {
    left: -4rem;
  }

  .sm\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .sm\:ms-5 {
    margin-inline-start: 1.25rem;
  }

  .sm\:grid {
    display: grid;
  }

  .sm\:h-full {
    height: 100%;
  }

  .sm\:min-h-\[300px\] {
    min-height: 300px;
  }

  .sm\:min-w-\[580px\] {
    min-width: 580px;
  }

  .sm\:max-w-\[540px\] {
    max-width: 540px;
  }

  .sm\:max-w-\[580px\] {
    max-width: 580px;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-\[0\.4fr_1fr\] {
    grid-template-columns: 0.4fr 1fr;
  }

  .sm\:grid-rows-2 {
    grid-template-rows: repeat(2, minmax(0, 1fr));
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:gap-5 {
    gap: 1.25rem;
  }

  .sm\:px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:before\:absolute::before {
    content: var(--tw-content);
    position: absolute;
  }

  .sm\:before\:bottom-0::before {
    content: var(--tw-content);
    bottom: 0px;
  }

  .sm\:before\:left-2\/4::before {
    content: var(--tw-content);
    left: 50%;
  }

  .sm\:before\:-z-10::before {
    content: var(--tw-content);
    z-index: -10;
  }

  .sm\:before\:h-\[100\%\]::before {
    content: var(--tw-content);
    height: 100%;
  }

  .sm\:before\:w-\[92\%\]::before {
    content: var(--tw-content);
    width: 92%;
  }

  .sm\:before\:-translate-x-2\/4::before {
    content: var(--tw-content);
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:before\:rounded-full::before {
    content: var(--tw-content);
    border-radius: 9999px;
  }

  .sm\:before\:shadow-\[0_0_50px_25px_\#00000038\]::before {
    content: var(--tw-content);
    --tw-shadow: 0 0 50px 25px #00000038;
    --tw-shadow-colored: 0 0 50px 25px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}

@media (min-width: 1000px) {
  .md\:-order-1 {
    order: -1;
  }

  .md\:order-1 {
    order: 1;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .md\:-mt-10 {
    margin-top: -2.5rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-16 {
    margin-bottom: 4rem;
  }

  .md\:mb-9 {
    margin-bottom: 2.25rem;
  }

  .md\:ms-0 {
    margin-inline-start: 0px;
  }

  .md\:ms-auto {
    margin-inline-start: auto;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-3 {
    margin-top: 0.75rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:min-h-\[305px\] {
    min-height: 305px;
  }

  .md\:min-h-\[712px\] {
    min-height: 712px;
  }

  .md\:min-w-36 {
    min-width: 9rem;
  }

  .md\:min-w-\[756px\] {
    min-width: 756px;
  }

  .md\:max-w-\[1000px\] {
    max-width: 1000px;
  }

  .md\:max-w-\[800px\] {
    max-width: 800px;
  }

  .md\:max-w-full {
    max-width: 100%;
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-\[0\.8fr_1fr\] {
    grid-template-columns: 0.8fr 1fr;
  }

  .md\:content-between {
    align-content: space-between;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:items-stretch {
    align-items: stretch;
  }

  .md\:gap-0 {
    gap: 0px;
  }

  .md\:gap-3 {
    gap: 0.75rem;
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:gap-x-10 {
    -moz-column-gap: 2.5rem;
         column-gap: 2.5rem;
  }

  .md\:space-y-24 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(6rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(6rem * var(--tw-space-y-reverse));
  }

  .md\:p-5 {
    padding: 1.25rem;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:text-start {
    text-align: start;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-8xl {
    font-size: 6rem;
    line-height: 1;
  }

  .md\:text-\[64px\] {
    font-size: 64px;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1200px) {
  .lg\:fixed {
    position: fixed;
  }

  .lg\:right-5 {
    right: 1.25rem;
  }

  .lg\:top-5 {
    top: 1.25rem;
  }

  .lg\:order-none {
    order: 0;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:-ms-3 {
    margin-inline-start: -0.75rem;
  }

  .lg\:mr-10 {
    margin-right: 2.5rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:min-h-\[600px\] {
    min-height: 600px;
  }

  .lg\:max-w-\[1200px\] {
    max-width: 1200px;
  }

  .lg\:max-w-\[1370px\] {
    max-width: 1370px;
  }

  .lg\:max-w-full {
    max-width: 100%;
  }

  .lg\:max-w-md {
    max-width: 28rem;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-\[0\.8fr_1fr\] {
    grid-template-columns: 0.8fr 1fr;
  }

  .lg\:gap-10 {
    gap: 2.5rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .lg\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .lg\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .lg\:pb-24 {
    padding-bottom: 6rem;
  }

  .lg\:text-start {
    text-align: start;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }

  .lg\:text-\[85px\] {
    font-size: 85px;
  }

  .lg\:blur-none {
    --tw-blur:  ;
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }

  .lg\:brightness-100 {
    --tw-brightness: brightness(1);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }
}

@media (min-width: 1400px) {
  .xl\:max-w-\[1400px\] {
    max-width: 1400px;
  }

  .xl\:max-w-lg {
    max-width: 32rem;
  }

  .xl\:grid-cols-\[0\.5fr_1fr\] {
    grid-template-columns: 0.5fr 1fr;
  }

  .xl\:gap-5 {
    gap: 1.25rem;
  }

  .xl\:gap-7 {
    gap: 1.75rem;
  }

  .xl\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .xl\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .xl\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .xl\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .xl\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }
}

@media (min-width: 1600px) {
  .xxl\:min-h-\[600px\] {
    min-height: 600px;
  }

  .xxl\:w-40 {
    width: 10rem;
  }

  .xxl\:max-w-\[1600px\] {
    max-width: 1600px;
  }

  .xxl\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .xxl\:text-8xl {
    font-size: 6rem;
    line-height: 1;
  }

  .xxl\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}
