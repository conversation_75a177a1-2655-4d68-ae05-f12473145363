<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Create Account</title>

    <!-- Font Awesome Icons -->
    <link
      rel="stylesheet"
      href="https://use.fontawesome.com/releases/v5.15.3/css/all.css"
      integrity="sha384-SZXxX4whJ79/gErwcOYf+zWLeJdY/qpuqC4cAa9rOGUstPomtqpuNWT9wdPEn2fk"
      crossorigin="anonymous"
    />

    <!-- External CSS -->
    <link rel="stylesheet" type="text/css" href="style.css" />
  </head>

  <body>
    <div class="container">
      <header class="header">
        <h2>Create Account</h2>
      </header>

      <form class="form" id="form" novalidate>
        <!-- Username Input -->
        <div class="form-control">
          <label for="username">Username</label>
          <input 
            type="text" 
            id="username" 
            name="username" 
            placeholder="beautyndbeast" 
            required 
            aria-describedby="usernameError" 
          />
          <i class="fas fa-check-circle"></i>
          <i class="fas fa-exclamation-circle"></i>
          <small id="usernameError">Error Message</small>
        </div>

        <!-- Email Input -->
        <div class="form-control">
          <label for="email">Email</label>
          <input 
            type="email" 
            id="email" 
            name="email" 
            placeholder="<EMAIL>" 
            required 
            aria-describedby="emailError" 
          />
          <i class="fas fa-check-circle"></i>
          <i class="fas fa-exclamation-circle"></i>
          <small id="emailError">Error Message</small>
        </div>

        <!-- Password Input -->
        <div class="form-control">
          <label for="password">Password</label>
          <input 
            type="password" 
            id="password" 
            name="password" 
            placeholder="password nahi batate" 
            required 
            aria-describedby="passwordError" 
          />
          <i class="fas fa-check-circle"></i>
          <i class="fas fa-exclamation-circle"></i>
          <small id="passwordError">Error Message</small>
        </div>

        <!-- Confirm Password Input -->
        <div class="form-control">
          <label for="confirm">Confirm Password</label>
          <input 
            type="password" 
            id="confirm" 
            name="confirm" 
            placeholder="yeh bhi nahi batate" 
            required 
            aria-describedby="confirmError" 
          />
          <i class="fas fa-check-circle"></i>
          <i class="fas fa-exclamation-circle"></i>
          <small id="confirmError">Error Message</small>
        </div>

        <!-- Submit Button -->
        <button type="submit">Submit</button>
      </form>
    </div>

    <script type="text/javascript" src="script.js"></script>
  </body>
</html>
