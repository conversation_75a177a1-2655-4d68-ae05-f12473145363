@import './_mixins.scss';
@import './_variables.scss';

.container {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 500;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
}

.overlay {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 550;
}

.modal {
  background-color: $secondary;
  color: $white;
  z-index: 600;
  min-width: 55%;
  max-width: 60%;
  position: absolute;
  align-self: center;
  justify-self: center;
  border-radius: 0.7rem;
  overflow: hidden;
}

.cross {
  position: absolute;
  top: 3%;
  right: 2%;
  cursor: pointer;
  background-color: $navBar-transparent;
  padding: 0.2rem 0.5rem;
  border-radius: 50%;
}

.greenText {
  color: $greenText;
  font-weight: bold;
  padding: 0.35rem;
  @include fluid-type(0.8rem, 1.05rem);
}

.bottomContainer {
  display: flex;
  flex-direction: row;
  flex-grow: 1;

  .column {
    margin-top: -11rem;
    margin-left: 3.5rem;
    @include fluid-type(0.7rem, 1.05rem);
    max-width: 50%;
    margin-bottom: 2rem;

    .genre {
      display: flex;
      flex-direction: row;
      color: $button-border;
    }

    .row {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap;
      color: white;
    }
  }
}

.spotlight {
  display: flex;
  align-items: flex-end;
  width: 100%;
  height: 84vh;
  background-color: $secondary;

  @include for-mobile-only {
    height: 64vh;
  }

  &__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 70%;
    z-index: 0;
    object-fit: fill;
    mask-image: linear-gradient(to bottom, $secondary 36%, transparent 100%);

    @include for-mobile-only {
      height: 68vh;
    }
  }

  .details {
    z-index: 10;
    width: 55%;
    margin-left: 3rem;
    margin-top: 4rem;
    display: flex;
    align-self: center;
    justify-self: center;
    flex-direction: column;
    justify-content: flex-end;
    text-shadow: 2px 2px 4px rgb(0 0 0 / 45%);

    @include for-mobile-only {
      width: 75%;
      justify-content: center;
      text-align: center;
      align-self: flex-end;
      margin-bottom: 0.7rem;
      margin-left: 2.9rem;
    }

    .title {
      font-weight: 600;
      padding: 0.4rem;

      @include fluid-type(2rem, 2.3rem);
    }
  }

  .buttonRow {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}

@media only screen and (max-width: 1000px) {
  .modal {
    max-width: 70%;
  }
}

@media only screen and (max-width: 500px) {
  .modal {
    max-width: 90%;
  }
}
