\documentclass[10pt, letterpaper]{article}

% Enhanced Packages:
\usepackage[
    ignoreheadfoot,
    top=1.5 cm,
    bottom=1.5 cm,
    left=1.8 cm,
    right=1.8 cm,
    footskip=1.0 cm,
]{geometry}
\usepackage[explicit]{titlesec}
\usepackage{tabularx}
\usepackage{array}
\usepackage[dvipsnames]{xcolor}
\definecolor{primaryColor}{RGB}{0, 102, 204}
\definecolor{accentColor}{RGB}{51, 51, 51}
\definecolor{lightGray}{RGB}{128, 128, 128}
\definecolor{darkGray}{RGB}{64, 64, 64}
\usepackage{enumitem}
\usepackage{fontawesome5}
\usepackage{amsmath}
\usepackage{tikz}
\usepackage{progressbar}
\usepackage[
    pdftitle={Shadab Sa<PERSON>war's CV},
    pdfauthor={Shadab Sarwar},
    pdfcreator={LaTeX with RenderCV},
    colorlinks=true,
    urlcolor=primaryColor
]{hyperref}
\usepackage[pscoord]{eso-pic}
\usepackage{calc}
\usepackage{bookmark}
\usepackage{lastpage}
\usepackage{changepage}
\usepackage{paracol}
\usepackage{ifthen}
\usepackage{needspace}
\usepackage{iftex}
\usepackage{multicol}

% Ensure machine-readable PDF
\ifPDFTeX
    \input{glyphtounicode}
    \pdfgentounicode=1
    \usepackage[T1]{fontenc}
    \usepackage[utf8]{inputenc}
    \usepackage{lmodern}
\fi

\usepackage[default, type1]{sourcesanspro} 

% Enhanced Settings
\AtBeginEnvironment{adjustwidth}{\partopsep0pt}
\pagestyle{empty}
\setcounter{secnumdepth}{0}
\setlength{\parindent}{0pt}
\setlength{\topskip}{0pt}
\setlength{\columnsep}{0.15cm}

% Custom footer
\makeatletter
\let\ps@customFooterStyle\ps@plain
\patchcmd{\ps@customFooterStyle}{\thepage}{
    \color{lightGray}\textit{\small Shadab Sarwar - Page \thepage{} of \pageref*{LastPage}}
}{}{}
\makeatother
\pagestyle{customFooterStyle}

% Enhanced section formatting with icons
\titleformat{\section}{
    \needspace{4\baselineskip}
    \Large\color{primaryColor}\bfseries
}{}{\MakeUppercase{#1}\hspace{0.15cm}\titlerule[1.2pt]\hspace{-0.1cm}}[]

\titlespacing{\section}{-1pt}{0.4 cm}{0.25 cm}

% Enhanced environments
\newenvironment{highlights}{
    \begin{itemize}[
        topsep=0.10 cm,
        parsep=0.10 cm,
        partopsep=0pt,
        itemsep=0pt,
        leftmargin=0.4 cm + 10pt,
        label={\color{primaryColor}\textbullet}
    ]
}{\end{itemize}}

\newenvironment{skillslist}{
    \begin{itemize}[
        topsep=0.05 cm,
        parsep=0.05 cm,
        partopsep=0pt,
        itemsep=0pt,
        leftmargin=0.4 cm + 10pt,
        label={\color{primaryColor}\faCode}
    ]
}{\end{itemize}}

\newenvironment{onecolentry}{
    \begin{adjustwidth}{0.2 cm + 0.00001 cm}{0.2 cm + 0.00001 cm}
}{\end{adjustwidth}}

\newenvironment{twocolentry}[2][]{
    \onecolentry
    \def\secondColumn{#2}
    \setcolumnwidth{\fill, 4.8 cm}
    \begin{paracol}{2}
}{
    \switchcolumn \raggedleft \secondColumn
    \end{paracol}
    \endonecolentry
}

\newenvironment{threecolentry}[3][]{
    \onecolentry
    \def\thirdColumn{#3}
    \setcolumnwidth{1 cm, \fill, 4.8 cm}
    \begin{paracol}{3}
    {\raggedright #2} \switchcolumn
}{
    \switchcolumn \raggedleft \thirdColumn
    \end{paracol}
    \endonecolentry
}

% Enhanced header
\newenvironment{header}{
    \setlength{\topsep}{0pt}\par\kern\topsep\centering\color{primaryColor}\linespread{1.5}
}{\par\kern\topsep}

% Skill bar command
\newcommand{\skillbar}[2]{
    \textbf{#1} \hfill
    \begin{tikzpicture}[baseline=-0.6ex]
        \draw[fill=lightGray!30] (0,0) rectangle (3,0.15);
        \draw[fill=primaryColor] (0,0) rectangle (#2*3/5,0.15);
    \end{tikzpicture}
}

% Last updated text
\newcommand{\placelastupdatedtext}{%
  \AddToShipoutPictureFG*{
    \put(
        \LenToUnit{\paperwidth-1.8 cm-0.2 cm+0.05cm},
        \LenToUnit{\paperheight-1.0 cm}
    ){\vtop{{\null}\makebox[0pt][c]{
        \small\color{lightGray}\textit{Last updated in January 2025}\hspace{\widthof{Last updated in January 2025}}
    }}}%
  }%
}

\let\hrefWithoutArrow\href
\renewcommand{\href}[2]{\hrefWithoutArrow{#1}{\ifthenelse{\equal{#2}{}}{ }{#2 }\raisebox{.15ex}{\footnotesize \faExternalLink*}}}

\begin{document}

\placelastupdatedtext
\begin{header}
    \fontsize{32 pt}{32 pt}
    \textbf{Shadab Sarwar}
    
    \vspace{0.2 cm}
    \fontsize{14 pt}{14 pt}
    \color{accentColor}
    \textbf{Full-Stack Web Developer}

    \vspace{0.4 cm}

    \normalsize
    \color{darkGray}
    \mbox{{\footnotesize\faMapMarker*}\hspace*{0.13cm}Ranchi, Jharkhand, India}%
    \kern 0.25 cm%
    \AND%
    \kern 0.25 cm%
    \mbox{\hrefWithoutArrow{mailto:<EMAIL>}{{\footnotesize\faEnvelope[regular]}\hspace*{0.13cm}<EMAIL>}}%
    \kern 0.25 cm%
    \AND%
    \kern 0.25 cm%
    \mbox{\hrefWithoutArrow{tel:+91-8340204570}{{\footnotesize\faPhone*}\hspace*{0.13cm}+91 8340204570}}%
    
    \vspace{0.2 cm}
    
    \mbox{\hrefWithoutArrow{https://linkedin.com/in/shadab-sarwar-581919258}{{\footnotesize\faLinkedinIn}\hspace*{0.13cm}linkedin.com/in/shadab-sarwar}}%
    \kern 0.25 cm%
    \AND%
    \kern 0.25 cm%
    \mbox{\hrefWithoutArrow{https://github.com/shadarsarwar}{{\footnotesize\faGithub}\hspace*{0.13cm}github.com/shadarsarwar}}%
    \kern 0.25 cm%
    \AND%
    \kern 0.25 cm%
    \mbox{\hrefWithoutArrow{https://shadarsarwar.github.io/portfolio}{{\footnotesize\faGlobe}\hspace*{0.13cm}Portfolio Website}}%
\end{header}

\vspace{0.4 cm}

\section{Professional Summary}
\begin{onecolentry}
    \textcolor{accentColor}{Passionate Full-Stack Web Developer with expertise in modern frontend technologies and growing backend proficiency. Currently pursuing BTech in Computer Science with hands-on experience in building responsive, user-centric web applications. Demonstrated ability to create seamless e-commerce solutions with focus on exceptional user experience and clean, maintainable code architecture.}
\end{onecolentry}

\section{Education}
\begin{threecolentry}{\textbf{Bachelor of Technology (BTech)}}{2025 -- 2029}
    \textbf{Computer Science and Engineering} \\
    \textit{Birla Institute of Technology, Mesra} \\
    \textcolor{lightGray}{Currently pursuing with focus on Software Engineering}
\end{threecolentry}

\vspace{0.2 cm}

\begin{threecolentry}{\textbf{Diploma in Engineering}}{2019 -- 2022}
    \textbf{Computer Engineering} \\
    \textit{Birla Institute of Technology, Mesra} \\
    \textcolor{lightGray}{Strong foundation in programming and computer systems}
\end{threecolentry}

\section{Featured Projects}

\begin{twocolentry}{
    \href{https://github.com/shadarsarwar/cozynest-e-com}{\faGithub\hspace{0.1cm}GitHub} | \href{https://cozyneste-com.netlify.app/}{\faGlobe\hspace{0.1cm}Live Demo}
}
    \textbf{CozyNest -- Modern E-commerce Platform}
    \begin{highlights}
        \item \textbf{Full-Stack E-commerce Solution:} Developed a comprehensive online shopping platform with modern UI/UX design using React.js and Vite for optimal performance
        \item \textbf{Advanced Frontend Features:} Implemented dynamic product catalog, intelligent search functionality, category-based filtering, shopping cart management, and responsive checkout workflow
        \item \textbf{State Management \& Persistence:} Utilized React hooks and local storage for seamless cart persistence across browser sessions and efficient state management
        \item \textbf{Responsive Design:} Created mobile-first responsive design ensuring optimal user experience across all devices and screen sizes
        \item \textbf{Performance Optimization:} Leveraged Vite's fast build system and implemented code splitting for enhanced loading performance
    \end{highlights}
\end{twocolentry}

\vspace{0.3 cm}

\begin{twocolentry}{
    \href{https://github.com/shadarsarwar/netflix-clone}{\faGithub\hspace{0.1cm}GitHub} | \href{https://shadarsarwar-netflix.netlify.app/}{\faGlobe\hspace{0.1cm}Live Demo}
}
    \textbf{Netflix Clone -- Streaming Platform UI}
    \begin{highlights}
        \item \textbf{Pixel-Perfect Recreation:} Built a high-fidelity Netflix interface clone with authentic styling and smooth animations using vanilla JavaScript and CSS3
        \item \textbf{Interactive Components:} Developed dynamic movie carousels, hover effects, modal popups, and responsive navigation menu
        \item \textbf{Content Management:} Implemented categorized movie sections with working poster images and rating systems
        \item \textbf{Cross-Browser Compatibility:} Ensured consistent performance across modern browsers with progressive enhancement techniques
    \end{highlights}
\end{twocolentry}

\vspace{0.3 cm}

\begin{twocolentry}{
    \href{https://github.com/shadarsarwar/portfolio}{\faGithub\hspace{0.1cm}GitHub} | \href{https://shadarsarwar.github.io/portfolio}{\faGlobe\hspace{0.1cm}Live Demo}
}
    \textbf{Personal Portfolio Website}
    \begin{highlights}
        \item \textbf{Professional Showcase:} Designed and developed a modern portfolio website highlighting projects, skills, and professional experience
        \item \textbf{Interactive Design:} Implemented smooth scrolling, animated sections, and engaging micro-interactions using CSS3 and JavaScript
        \item \textbf{SEO Optimized:} Applied best practices for search engine optimization and web accessibility standards
        \item \textbf{Contact Integration:} Built functional contact form with email integration and social media links
    \end{highlights}
\end{twocolentry}

\section{Technical Skills}

\begin{onecolentry}
    \begin{multicols}{2}
    \textbf{\color{primaryColor}Frontend Development}
    \begin{skillslist}
        \item \skillbar{React.js}{5}
        \item \skillbar{JavaScript (ES6+)}{4}
        \item \skillbar{HTML5 \& CSS3}{5}
        \item \skillbar{Responsive Design}{5}
        \item \skillbar{Tailwind CSS}{4}
        \item \skillbar{Vite \& Webpack}{3}
    \end{skillslist}

    \columnbreak

    \textbf{\color{primaryColor}Programming \& Tools}
    \begin{skillslist}
        \item \skillbar{C++ \& Data Structures}{4}
        \item \skillbar{Java}{3}
        \item \skillbar{Git \& GitHub}{4}
        \item \skillbar{VS Code \& IDEs}{5}
        \item \skillbar{npm \& Package Management}{4}
        \item \skillbar{Debugging \& Testing}{3}
    \end{skillslist}
    \end{multicols}
\end{onecolentry}

\section{Core Competencies}

\begin{onecolentry}
    \begin{multicols}{2}
    \textbf{\color{primaryColor}Technical Expertise}
    \begin{highlights}
        \item Component-Based Architecture
        \item RESTful API Integration
        \item Cross-Browser Compatibility
        \item Performance Optimization
        \item Mobile-First Development
        \item Version Control (Git)
    \end{highlights}

    \columnbreak

    \textbf{\color{primaryColor}Soft Skills}
    \begin{highlights}
        \item Problem-Solving \& Analytical Thinking
        \item Project Management
        \item Continuous Learning
        \item Team Collaboration
        \item Attention to Detail
        \item Time Management
    \end{highlights}
    \end{multicols}
\end{onecolentry}

\section{Achievements \& Certifications}

\begin{onecolentry}
    \begin{highlights}
        \item \textbf{Academic Excellence:} Consistent high performance in Computer Science coursework with focus on software engineering principles
        \item \textbf{Self-Directed Learning:} Completed multiple online courses in modern web development technologies and frameworks
        \item \textbf{Open Source Contribution:} Active GitHub contributor with multiple public repositories showcasing diverse programming skills
        \item \textbf{Project Portfolio:} Successfully deployed multiple web applications demonstrating full development lifecycle expertise
    \end{highlights}
\end{onecolentry}

\end{document}
