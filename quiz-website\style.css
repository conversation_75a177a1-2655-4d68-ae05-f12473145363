/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

/* Particles Background */
#particles-js {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* Container */
.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.logo i {
    font-size: 2.5rem;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.tagline {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 300;
}

/* Screen Management */
.screen {
    display: none;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: slideIn 0.5s ease-out;
}

.screen.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Welcome Screen */
.welcome-content {
    text-align: center;
}

.welcome-icon i {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.welcome-content h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.welcome-content p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

.quiz-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 15px 20px;
    border-radius: 15px;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.info-item i {
    font-size: 1.2rem;
}

.user-input {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.user-input input {
    padding: 15px 25px;
    font-size: 1.1rem;
    border: 2px solid #e0e0e0;
    border-radius: 15px;
    width: 300px;
    max-width: 100%;
    text-align: center;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
}

.user-input input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

/* Buttons */
.btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Poppins', sans-serif;
    text-decoration: none;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
    background: linear-gradient(135deg, #ffeaa7, #fab1a0);
    color: #333;
    box-shadow: 0 5px 15px rgba(255, 234, 167, 0.4);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 234, 167, 0.6);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Quiz Screen */
.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.progress-container {
    flex: 1;
    min-width: 200px;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    transition: width 0.5s ease;
    width: 0%;
}

.question-counter {
    font-weight: 600;
    color: #667eea;
}

.score-display {
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    padding: 10px 20px;
    border-radius: 15px;
    font-weight: 600;
    color: #333;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.question-container {
    margin-bottom: 30px;
}

.question-text {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    line-height: 1.5;
}

.question-category {
    display: inline-block;
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    color: #333;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.options-container {
    display: grid;
    gap: 15px;
    margin-bottom: 30px;
}

.option {
    padding: 20px;
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    position: relative;
    overflow: hidden;
}

.option:hover {
    border-color: #667eea;
    background: #f0f4ff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.option.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.option.correct {
    border-color: #27ae60;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.option.incorrect {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.quiz-controls {
    text-align: center;
}

/* Results Screen */
.results-content {
    text-align: center;
}

.results-icon i {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.results-content h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.results-message {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

.score-breakdown {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
}

.score-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.score-inner {
    text-align: center;
    color: white;
}

.score-inner #final-score {
    font-size: 3rem;
    font-weight: 700;
    display: block;
}

.score-total {
    font-size: 1.2rem;
    opacity: 0.8;
}

.percentage-score {
    font-size: 1.5rem;
    font-weight: 600;
    color: #667eea;
}

.category-breakdown {
    margin-bottom: 30px;
}

.category-breakdown h4 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 20px;
}

.category-stats {
    display: grid;
    gap: 15px;
    max-width: 400px;
    margin: 0 auto;
}

.category-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.results-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 15px;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.social-links a {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    color: #ffd700;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        padding: 20px;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .screen {
        padding: 25px;
    }
    
    .welcome-content h2 {
        font-size: 2rem;
    }
    
    .quiz-info {
        flex-direction: column;
        align-items: center;
    }
    
    .user-input input {
        width: 100%;
    }
    
    .quiz-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .question-text {
        font-size: 1.2rem;
    }
    
    .results-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .logo {
        flex-direction: column;
        gap: 10px;
    }
    
    .logo h1 {
        font-size: 1.8rem;
    }
    
    .welcome-content h2 {
        font-size: 1.8rem;
    }
    
    .results-content h2 {
        font-size: 2rem;
    }
    
    .score-circle {
        width: 120px;
        height: 120px;
    }
    
    .score-inner #final-score {
        font-size: 2.5rem;
    }
}
