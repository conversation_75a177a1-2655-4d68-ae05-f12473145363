var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e){var t={exports:{}};return e(t,t.exports),t.exports}var r=t((function(e,t){function r(e){return t.$0<=e&&e<=t.$9}
/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
Object.defineProperty(t,"__esModule",{value:!0}),t.$EOF=0,t.$BSPACE=8,t.$TAB=9,t.$LF=10,t.$VTAB=11,t.$FF=12,t.$CR=13,t.$SPACE=32,t.$BANG=33,t.$DQ=34,t.$HASH=35,t.$$=36,t.$PERCENT=37,t.$AMPERSAND=38,t.$SQ=39,t.$LPAREN=40,t.$RPAREN=41,t.$STAR=42,t.$PLUS=43,t.$COMMA=44,t.$MINUS=45,t.$PERIOD=46,t.$SLASH=47,t.$COLON=58,t.$SEMICOLON=59,t.$LT=60,t.$EQ=61,t.$GT=62,t.$QUESTION=63,t.$0=48,t.$7=55,t.$9=57,t.$A=65,t.$E=69,t.$F=70,t.$X=88,t.$Z=90,t.$LBRACKET=91,t.$BACKSLASH=92,t.$RBRACKET=93,t.$CARET=94,t.$_=95,t.$a=97,t.$b=98,t.$e=101,t.$f=102,t.$n=110,t.$r=114,t.$t=116,t.$u=117,t.$v=118,t.$x=120,t.$z=122,t.$LBRACE=123,t.$BAR=124,t.$RBRACE=125,t.$NBSP=160,t.$PIPE=124,t.$TILDA=126,t.$AT=64,t.$BT=96,t.isWhitespace=function(e){return e>=t.$TAB&&e<=t.$SPACE||e==t.$NBSP},t.isDigit=r,t.isAsciiLetter=function(e){return e>=t.$a&&e<=t.$z||e>=t.$A&&e<=t.$Z},t.isAsciiHexDigit=function(e){return e>=t.$a&&e<=t.$f||e>=t.$A&&e<=t.$F||r(e)},t.isNewLine=function(e){return e===t.$LF||e===t.$CR},t.isOctalDigit=function(e){return t.$0<=e&&e<=t.$7}}));
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */class n{constructor(e,t,r){this.filePath=e,this.name=t,this.members=r}assertNoMembers(){if(this.members.length)throw new Error(`Illegal state: symbol without members expected, but got ${JSON.stringify(this)}.`)}}var i=n;var s=class{constructor(){this.cache=new Map}get(e,t,r){const i=`"${e}".${t}${(r=r||[]).length?`.${r.join(".")}`:""}`;let s=this.cache.get(i);return s||(s=new n(e,t,r),this.cache.set(i,s)),s}},o=Object.defineProperty({StaticSymbol:i,StaticSymbolCache:s},"__esModule",{value:!0});
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
const a=/-+([a-z0-9])/g;var u=function(e){return e.replace(a,((...e)=>e[1].toUpperCase()))};var c=function(e,t){return p(e,":",t)};var l=function(e,t){return p(e,".",t)};function p(e,t,r){const n=e.indexOf(t);return-1==n?r:[e.slice(0,n).trim(),e.slice(n+1).trim()]}function D(e,t,r){return Array.isArray(e)?t.visitArray(e,r):"object"==typeof(n=e)&&null!==n&&Object.getPrototypeOf(n)===b?t.visitStringMap(e,r):null==e||"string"==typeof e||"number"==typeof e||"boolean"==typeof e?t.visitPrimitive(e,r):t.visitOther(e,r);var n}var h=D;var d=function(e){return null!=e};var f=function(e){return void 0===e?null:e};var m=class{visitArray(e,t){return e.map((e=>D(e,this,t)))}visitStringMap(e,t){const r={};return Object.keys(e).forEach((n=>{r[n]=D(e[n],this,t)})),r}visitPrimitive(e,t){return e}visitOther(e,t){return e}},g={assertSync:e=>{if(N(e))throw new Error("Illegal state: value cannot be a promise");return e},then:(e,t)=>N(e)?e.then(t):t(e),all:e=>e.some(N)?Promise.all(e):e};var E=function(e){throw new Error(`Internal Error: ${e}`)};var C=function(e,t){const r=Error(e);return r[T]=!0,t&&(r[y]=t),r};const T="ngSyntaxError",y="ngParseErrors";var S=function(e){return e[T]};var _=function(e){return e[y]||[]};var v=function(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")};const b=Object.getPrototypeOf({});var F=function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);if(n>=55296&&n<=56319&&e.length>r+1){const t=e.charCodeAt(r+1);t>=56320&&t<=57343&&(r++,n=(n-55296<<10)+t-56320+65536)}n<=127?t+=String.fromCharCode(n):n<=2047?t+=String.fromCharCode(n>>6&31|192,63&n|128):n<=65535?t+=String.fromCharCode(n>>12|224,n>>6&63|128,63&n|128):n<=2097151&&(t+=String.fromCharCode(n>>18&7|240,n>>12&63|128,n>>6&63|128,63&n|128))}return t};var A=function e(t){if("string"==typeof t)return t;if(t instanceof Array)return"["+t.map(e).join(", ")+"]";if(null==t)return""+t;if(t.overriddenName)return`${t.overriddenName}`;if(t.name)return`${t.name}`;if(!t.toString)return"object";const r=t.toString();if(null==r)return""+r;const n=r.indexOf("\n");return-1===n?r:r.substring(0,n)};var w=function(e){return"function"==typeof e&&e.hasOwnProperty("__forward_ref__")?e():e};function N(e){return!!e&&"function"==typeof e.then}var k=N;var O=class{constructor(e){this.full=e;const t=e.split(".");this.major=t[0],this.minor=t[1],this.patch=t.slice(2).join(".")}};const x="undefined"!=typeof window&&window,I="undefined"!=typeof self&&"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&self;var P=void 0!==e&&e||x||I,R=Object.defineProperty({dashCaseToCamelCase:u,splitAtColon:c,splitAtPeriod:l,visitValue:h,isDefined:d,noUndefined:f,ValueTransformer:m,SyncAsync:g,error:E,syntaxError:C,isSyntaxError:S,getParseErrors:_,escapeRegExp:v,utf8Encode:F,stringify:A,resolveForwardRef:w,isPromise:k,Version:O,global:P},"__esModule",{value:!0}),L=t((function(e,t){
/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
Object.defineProperty(t,"__esModule",{value:!0});const r=/^(?:(?:\[([^\]]+)\])|(?:\(([^\)]+)\)))|(\@[-\w]+)$/;function n(e){return e.replace(/\W/g,"_")}t.sanitizeIdentifier=n;let i=0;function s(e){if(!e||!e.reference)return null;const t=e.reference;if(t instanceof o.StaticSymbol)return t.name;if(t.__anonymousType)return t.__anonymousType;let r=R.stringify(t);return r.indexOf("(")>=0?(r="anonymous_"+i++,t.__anonymousType=r):r=n(r),r}var a;t.identifierName=s,t.identifierModuleUrl=function(e){const t=e.reference;return t instanceof o.StaticSymbol?t.filePath:`./${R.stringify(t)}`},t.viewClassName=function(e,t){return`View_${s({reference:e})}_${t}`},t.rendererTypeName=function(e){return`RenderType_${s({reference:e})}`},t.hostViewClassName=function(e){return`HostView_${s({reference:e})}`},t.componentFactoryName=function(e){return`${s({reference:e})}NgFactory`},function(e){e[e.Pipe=0]="Pipe",e[e.Directive=1]="Directive",e[e.NgModule=2]="NgModule",e[e.Injectable=3]="Injectable"}(a=t.CompileSummaryKind||(t.CompileSummaryKind={})),t.tokenName=function(e){return null!=e.value?n(e.value):s(e.identifier)},t.tokenReference=function(e){return null!=e.identifier?e.identifier.reference:e.value};t.CompileStylesheetMetadata=class{constructor({moduleUrl:e,styles:t,styleUrls:r}={}){this.moduleUrl=e||null,this.styles=c(t),this.styleUrls=c(r)}};t.CompileTemplateMetadata=class{constructor({encapsulation:e,template:t,templateUrl:r,htmlAst:n,styles:i,styleUrls:s,externalStylesheets:o,animations:a,ngContentSelectors:u,interpolation:p,isInline:D,preserveWhitespaces:h}){if(this.encapsulation=e,this.template=t,this.templateUrl=r,this.htmlAst=n,this.styles=c(i),this.styleUrls=c(s),this.externalStylesheets=c(o),this.animations=a?l(a):[],this.ngContentSelectors=u||[],p&&2!=p.length)throw new Error("'interpolation' should have a start and an end symbol.");this.interpolation=p,this.isInline=D,this.preserveWhitespaces=h}toSummary(){return{ngContentSelectors:this.ngContentSelectors,encapsulation:this.encapsulation,styles:this.styles,animations:this.animations}}};class u{static create({isHost:e,type:t,isComponent:n,selector:i,exportAs:s,changeDetection:o,inputs:a,outputs:c,host:l,providers:p,viewProviders:D,queries:h,guards:d,viewQueries:f,entryComponents:m,template:g,componentViewType:E,rendererType:C,componentFactory:T}){const y={},S={},_={};null!=l&&Object.keys(l).forEach((e=>{const t=l[e],n=e.match(r);null===n?_[e]=t:null!=n[1]?S[n[1]]=t:null!=n[2]&&(y[n[2]]=t)}));const v={};null!=a&&a.forEach((e=>{const t=R.splitAtColon(e,[e,e]);v[t[0]]=t[1]}));const b={};return null!=c&&c.forEach((e=>{const t=R.splitAtColon(e,[e,e]);b[t[0]]=t[1]})),new u({isHost:e,type:t,isComponent:!!n,selector:i,exportAs:s,changeDetection:o,inputs:v,outputs:b,hostListeners:y,hostProperties:S,hostAttributes:_,providers:p,viewProviders:D,queries:h,guards:d,viewQueries:f,entryComponents:m,template:g,componentViewType:E,rendererType:C,componentFactory:T})}constructor({isHost:e,type:t,isComponent:r,selector:n,exportAs:i,changeDetection:s,inputs:o,outputs:a,hostListeners:u,hostProperties:l,hostAttributes:p,providers:D,viewProviders:h,queries:d,guards:f,viewQueries:m,entryComponents:g,template:E,componentViewType:C,rendererType:T,componentFactory:y}){this.isHost=!!e,this.type=t,this.isComponent=r,this.selector=n,this.exportAs=i,this.changeDetection=s,this.inputs=o,this.outputs=a,this.hostListeners=u,this.hostProperties=l,this.hostAttributes=p,this.providers=c(D),this.viewProviders=c(h),this.queries=c(d),this.guards=f,this.viewQueries=c(m),this.entryComponents=c(g),this.template=E,this.componentViewType=C,this.rendererType=T,this.componentFactory=y}toSummary(){return{summaryKind:a.Directive,type:this.type,isComponent:this.isComponent,selector:this.selector,exportAs:this.exportAs,inputs:this.inputs,outputs:this.outputs,hostListeners:this.hostListeners,hostProperties:this.hostProperties,hostAttributes:this.hostAttributes,providers:this.providers,viewProviders:this.viewProviders,queries:this.queries,guards:this.guards,viewQueries:this.viewQueries,entryComponents:this.entryComponents,changeDetection:this.changeDetection,template:this.template&&this.template.toSummary(),componentViewType:this.componentViewType,rendererType:this.rendererType,componentFactory:this.componentFactory}}}t.CompileDirectiveMetadata=u;t.CompilePipeMetadata=class{constructor({type:e,name:t,pure:r}){this.type=e,this.name=t,this.pure=!!r}toSummary(){return{summaryKind:a.Pipe,type:this.type,name:this.name,pure:this.pure}}};t.CompileShallowModuleMetadata=class{};t.CompileNgModuleMetadata=class{constructor({type:e,providers:t,declaredDirectives:r,exportedDirectives:n,declaredPipes:i,exportedPipes:s,entryComponents:o,bootstrapComponents:a,importedModules:u,exportedModules:l,schemas:p,transitiveModule:D,id:h}){this.type=e||null,this.declaredDirectives=c(r),this.exportedDirectives=c(n),this.declaredPipes=c(i),this.exportedPipes=c(s),this.providers=c(t),this.entryComponents=c(o),this.bootstrapComponents=c(a),this.importedModules=c(u),this.exportedModules=c(l),this.schemas=c(p),this.id=h||null,this.transitiveModule=D||null}toSummary(){const e=this.transitiveModule;return{summaryKind:a.NgModule,type:this.type,entryComponents:e.entryComponents,providers:e.providers,modules:e.modules,exportedDirectives:e.exportedDirectives,exportedPipes:e.exportedPipes}}};function c(e){return e||[]}t.TransitiveCompileNgModuleMetadata=class{constructor(){this.directivesSet=new Set,this.directives=[],this.exportedDirectivesSet=new Set,this.exportedDirectives=[],this.pipesSet=new Set,this.pipes=[],this.exportedPipesSet=new Set,this.exportedPipes=[],this.modulesSet=new Set,this.modules=[],this.entryComponentsSet=new Set,this.entryComponents=[],this.providers=[]}addProvider(e,t){this.providers.push({provider:e,module:t})}addDirective(e){this.directivesSet.has(e.reference)||(this.directivesSet.add(e.reference),this.directives.push(e))}addExportedDirective(e){this.exportedDirectivesSet.has(e.reference)||(this.exportedDirectivesSet.add(e.reference),this.exportedDirectives.push(e))}addPipe(e){this.pipesSet.has(e.reference)||(this.pipesSet.add(e.reference),this.pipes.push(e))}addExportedPipe(e){this.exportedPipesSet.has(e.reference)||(this.exportedPipesSet.add(e.reference),this.exportedPipes.push(e))}addModule(e){this.modulesSet.has(e.reference)||(this.modulesSet.add(e.reference),this.modules.push(e))}addEntryComponent(e){this.entryComponentsSet.has(e.componentType)||(this.entryComponentsSet.add(e.componentType),this.entryComponents.push(e))}};function l(e){return e.reduce(((e,t)=>{const r=Array.isArray(t)?l(t):t;return e.concat(r)}),[])}function p(e){return e.replace(/(\w+:\/\/[\w:-]+)?(\/+)?/,"ng:///")}t.ProviderMeta=class{constructor(e,{useClass:t,useValue:r,useExisting:n,useFactory:i,deps:s,multi:o}){this.token=e,this.useClass=t||null,this.useValue=r,this.useExisting=n,this.useFactory=i||null,this.dependencies=s||null,this.multi=!!o}},t.flatten=l,t.templateSourceUrl=function(e,t,r){let n;return n=r.isInline?t.type.reference instanceof o.StaticSymbol?`${t.type.reference.filePath}.${t.type.reference.name}.html`:`${s(e)}/${s(t.type)}.html`:r.templateUrl,t.type.reference instanceof o.StaticSymbol?n:p(n)},t.sharedStylesheetJitUrl=function(e,t){const r=e.moduleUrl.split(/\/\\/g);return p(`css/${t}${r[r.length-1]}.ngstyle.js`)},t.ngModuleJitUrl=function(e){return p(`${s(e.type)}/module.ngfactory.js`)},t.templateJitUrl=function(e,t){return p(`${s(e)}/${s(t.type)}.ngfactory.js`)}})),B=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});
/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
class n{constructor(e,t,r,n){this.file=e,this.offset=t,this.line=r,this.col=n}toString(){return null!=this.offset?`${this.file.url}@${this.line}:${this.col}`:this.file.url}moveBy(e){const t=this.file.content,i=t.length;let s=this.offset,o=this.line,a=this.col;for(;s>0&&e<0;){s--,e++;if(t.charCodeAt(s)==r.$LF){o--;const e=t.substr(0,s-1).lastIndexOf(String.fromCharCode(r.$LF));a=e>0?s-e:s}else a--}for(;s<i&&e>0;){const n=t.charCodeAt(s);s++,e--,n==r.$LF?(o++,a=0):a++}return new n(this.file,s,o,a)}getContext(e,t){const r=this.file.content;let n=this.offset;if(null!=n){n>r.length-1&&(n=r.length-1);let i=n,s=0,o=0;for(;s<e&&n>0&&(n--,s++,"\n"!=r[n]||++o!=t););for(s=0,o=0;s<e&&i<r.length-1&&(i++,s++,"\n"!=r[i]||++o!=t););return{before:r.substring(n,this.offset),after:r.substring(this.offset,i+1)}}return null}}t.ParseLocation=n;class i{constructor(e,t){this.content=e,this.url=t}}t.ParseSourceFile=i;class s{constructor(e,t,r=null){this.start=e,this.end=t,this.details=r}toString(){return this.start.file.content.substring(this.start.offset,this.end.offset)}}var o;t.ParseSourceSpan=s,t.EMPTY_PARSE_LOCATION=new n(new i("",""),0,0,0),t.EMPTY_SOURCE_SPAN=new s(t.EMPTY_PARSE_LOCATION,t.EMPTY_PARSE_LOCATION),function(e){e[e.WARNING=0]="WARNING",e[e.ERROR=1]="ERROR"}(o=t.ParseErrorLevel||(t.ParseErrorLevel={}));t.ParseError=class{constructor(e,t,r=o.ERROR){this.span=e,this.msg=t,this.level=r}contextualMessage(){const e=this.span.start.getContext(100,3);return e?`${this.msg} ("${e.before}[${o[this.level]} ->]${e.after}")`:this.msg}toString(){const e=this.span.details?`, ${this.span.details}`:"";return`${this.contextualMessage()}: ${this.span.start}${e}`}},t.typeSourceSpan=function(e,t){const r=L.identifierModuleUrl(t),o=null!=r?`in ${e} ${L.identifierName(t)} in ${r}`:`in ${e} ${L.identifierName(t)}`,a=new i("",o);return new s(new n(a,-1,-1,-1),new n(a,-1,-1,-1))},t.r3JitTypeSourceSpan=function(e,t,r){const o=new i("",`in ${e} ${t} in ${r}`);return new s(new n(o,-1,-1,-1),new n(o,-1,-1,-1))}}));const $=new RegExp("^(?<startDelimiter>-{3}|\\+{3})(?<language>[^\\n]*)\\n(?:|(?<value>.*?)\\n)(?<endDelimiter>\\k<startDelimiter>|\\.{3})[^\\S\\n]*(?:\\n|$)","s");var q=function(e){const t=e.match($);if(!t)return{content:e};const{startDelimiter:r,language:n,value:i="",endDelimiter:s}=t.groups;let o=n.trim()||"yaml";if("+++"===r&&(o="toml"),"yaml"!==o&&r!==s)return{content:e};const[a]=t;return{frontMatter:{type:"front-matter",lang:o,value:i,startDelimiter:r,endDelimiter:s,raw:a.replace(/\n$/,"")},content:a.replace(/[^\n]/g," ")+e.slice(a.length)}};var M=e=>e[e.length-1];var U=function(e,t){const r=new SyntaxError(e+" ("+t.start.line+":"+t.start.column+")");return r.loc=t,r},G=e=>"string"==typeof e?e.replace((({onlyFirst:e=!1}={})=>{const t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")})(),""):e;const j=e=>!Number.isNaN(e)&&(e>=4352&&(e<=4447||9001===e||9002===e||11904<=e&&e<=12871&&12351!==e||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141));var V=j,H=j;V.default=H;const X=e=>{if("string"!=typeof e||0===e.length)return 0;if(0===(e=G(e)).length)return 0;e=e.replace(/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g,"  ");let t=0;for(let r=0;r<e.length;r++){const n=e.codePointAt(r);n<=31||n>=127&&n<=159||(n>=768&&n<=879||(n>65535&&r++,t+=V(n)?2:1))}return t};var z=X,W=X;z.default=W;var Y=e=>{if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")};function Q(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var J=function(e){return e&&e.Math==Math&&e},K=J("object"==typeof globalThis&&globalThis)||J("object"==typeof window&&window)||J("object"==typeof self&&self)||J("object"==typeof e&&e)||function(){return this}()||Function("return this")(),Z=function(e){try{return!!e()}catch(e){return!0}},ee=!Z((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),te={}.propertyIsEnumerable,re=Object.getOwnPropertyDescriptor,ne={f:re&&!te.call({1:2},1)?function(e){var t=re(this,e);return!!t&&t.enumerable}:te},ie=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},se={}.toString,oe=function(e){return se.call(e).slice(8,-1)},ae="".split,ue=Z((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==oe(e)?ae.call(e,""):Object(e)}:Object,ce=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},le=function(e){return ue(ce(e))},pe=function(e){return"object"==typeof e?null!==e:"function"==typeof e},De=function(e,t){if(!pe(e))return e;var r,n;if(t&&"function"==typeof(r=e.toString)&&!pe(n=r.call(e)))return n;if("function"==typeof(r=e.valueOf)&&!pe(n=r.call(e)))return n;if(!t&&"function"==typeof(r=e.toString)&&!pe(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")},he=function(e){return Object(ce(e))},de={}.hasOwnProperty,fe=Object.hasOwn||function(e,t){return de.call(he(e),t)},me=K.document,ge=pe(me)&&pe(me.createElement),Ee=!ee&&!Z((function(){return 7!=Object.defineProperty((e="div",ge?me.createElement(e):{}),"a",{get:function(){return 7}}).a;var e})),Ce=Object.getOwnPropertyDescriptor,Te={f:ee?Ce:function(e,t){if(e=le(e),t=De(t,!0),Ee)try{return Ce(e,t)}catch(e){}if(fe(e,t))return ie(!ne.f.call(e,t),e[t])}},ye=function(e){if(!pe(e))throw TypeError(String(e)+" is not an object");return e},Se=Object.defineProperty,_e={f:ee?Se:function(e,t,r){if(ye(e),t=De(t,!0),ye(r),Ee)try{return Se(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},ve=ee?function(e,t,r){return _e.f(e,t,ie(1,r))}:function(e,t,r){return e[t]=r,e},be=function(e,t){try{ve(K,e,t)}catch(r){K[e]=t}return t},Fe=K["__core-js_shared__"]||be("__core-js_shared__",{}),Ae=Function.toString;"function"!=typeof Fe.inspectSource&&(Fe.inspectSource=function(e){return Ae.call(e)});var we,Ne,ke,Oe,xe=Fe.inspectSource,Ie=K.WeakMap,Pe="function"==typeof Ie&&/native code/.test(xe(Ie)),Re=t((function(e){(e.exports=function(e,t){return Fe[e]||(Fe[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.14.0",mode:"global",copyright:"\xa9 2021 Denis Pushkarev (zloirock.ru)"})})),Le=0,Be=Math.random(),$e=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++Le+Be).toString(36)},qe=Re("keys"),Me={},Ue=K.WeakMap;if(Pe||Fe.state){var Ge=Fe.state||(Fe.state=new Ue),je=Ge.get,Ve=Ge.has,He=Ge.set;we=function(e,t){if(Ve.call(Ge,e))throw new TypeError("Object already initialized");return t.facade=e,He.call(Ge,e,t),t},Ne=function(e){return je.call(Ge,e)||{}},ke=function(e){return Ve.call(Ge,e)}}else{var Xe=qe[Oe="state"]||(qe[Oe]=$e(Oe));Me[Xe]=!0,we=function(e,t){if(fe(e,Xe))throw new TypeError("Object already initialized");return t.facade=e,ve(e,Xe,t),t},Ne=function(e){return fe(e,Xe)?e[Xe]:{}},ke=function(e){return fe(e,Xe)}}var ze,We,Ye={set:we,get:Ne,has:ke,enforce:function(e){return ke(e)?Ne(e):we(e,{})},getterFor:function(e){return function(t){var r;if(!pe(t)||(r=Ne(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return r}}},Qe=t((function(e){var t=Ye.get,r=Ye.enforce,n=String(String).split("String");(e.exports=function(e,t,i,s){var o,a=!!s&&!!s.unsafe,u=!!s&&!!s.enumerable,c=!!s&&!!s.noTargetGet;"function"==typeof i&&("string"!=typeof t||fe(i,"name")||ve(i,"name",t),(o=r(i)).source||(o.source=n.join("string"==typeof t?t:""))),e!==K?(a?!c&&e[t]&&(u=!0):delete e[t],u?e[t]=i:ve(e,t,i)):u?e[t]=i:be(t,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||xe(this)}))})),Je=K,Ke=function(e){return"function"==typeof e?e:void 0},Ze=function(e,t){return arguments.length<2?Ke(Je[e])||Ke(K[e]):Je[e]&&Je[e][t]||K[e]&&K[e][t]},et=Math.ceil,tt=Math.floor,rt=function(e){return isNaN(e=+e)?0:(e>0?tt:et)(e)},nt=Math.min,it=function(e){return e>0?nt(rt(e),9007199254740991):0},st=Math.max,ot=Math.min,at=function(e){return function(t,r,n){var i,s=le(t),o=it(s.length),a=function(e,t){var r=rt(e);return r<0?st(r+t,0):ot(r,t)}(n,o);if(e&&r!=r){for(;o>a;)if((i=s[a++])!=i)return!0}else for(;o>a;a++)if((e||a in s)&&s[a]===r)return e||a||0;return!e&&-1}},ut={includes:at(!0),indexOf:at(!1)}.indexOf,ct=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),lt={f:Object.getOwnPropertyNames||function(e){return function(e,t){var r,n=le(e),i=0,s=[];for(r in n)!fe(Me,r)&&fe(n,r)&&s.push(r);for(;t.length>i;)fe(n,r=t[i++])&&(~ut(s,r)||s.push(r));return s}(e,ct)}},pt={f:Object.getOwnPropertySymbols},Dt=Ze("Reflect","ownKeys")||function(e){var t=lt.f(ye(e)),r=pt.f;return r?t.concat(r(e)):t},ht=function(e,t){for(var r=Dt(t),n=_e.f,i=Te.f,s=0;s<r.length;s++){var o=r[s];fe(e,o)||n(e,o,i(t,o))}},dt=/#|\.prototype\./,ft=function(e,t){var r=gt[mt(e)];return r==Ct||r!=Et&&("function"==typeof t?Z(t):!!t)},mt=ft.normalize=function(e){return String(e).replace(dt,".").toLowerCase()},gt=ft.data={},Et=ft.NATIVE="N",Ct=ft.POLYFILL="P",Tt=ft,yt=Te.f,St=function(e,t){var r,n,i,s,o,a=e.target,u=e.global,c=e.stat;if(r=u?K:c?K[a]||be(a,{}):(K[a]||{}).prototype)for(n in t){if(s=t[n],i=e.noTargetGet?(o=yt(r,n))&&o.value:r[n],!Tt(u?n:a+(c?".":"#")+n,e.forced)&&void 0!==i){if(typeof s==typeof i)continue;ht(s,i)}(e.sham||i&&i.sham)&&ve(s,"sham",!0),Qe(r,n,s,e)}},_t=Array.isArray||function(e){return"Array"==oe(e)},vt=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},bt=function(e,t,r){if(vt(e),void 0===t)return e;switch(r){case 0:return function(){return e.call(t)};case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,i){return e.call(t,r,n,i)}}return function(){return e.apply(t,arguments)}},Ft=function(e,t,r,n,i,s,o,a){for(var u,c=i,l=0,p=!!o&&bt(o,a,3);l<n;){if(l in r){if(u=p?p(r[l],l,t):r[l],s>0&&_t(u))c=Ft(e,t,u,it(u.length),c,s-1)-1;else{if(c>=9007199254740991)throw TypeError("Exceed the acceptable array length");e[c]=u}c++}l++}return c},At=Ft,wt=Ze("navigator","userAgent")||"",Nt=K.process,kt=Nt&&Nt.versions,Ot=kt&&kt.v8;Ot?We=(ze=Ot.split("."))[0]<4?1:ze[0]+ze[1]:wt&&(!(ze=wt.match(/Edge\/(\d+)/))||ze[1]>=74)&&(ze=wt.match(/Chrome\/(\d+)/))&&(We=ze[1]);var xt=We&&+We,It=!!Object.getOwnPropertySymbols&&!Z((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&xt&&xt<41})),Pt=It&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Rt=Re("wks"),Lt=K.Symbol,Bt=Pt?Lt:Lt&&Lt.withoutSetter||$e,$t=function(e){return fe(Rt,e)&&(It||"string"==typeof Rt[e])||(It&&fe(Lt,e)?Rt[e]=Lt[e]:Rt[e]=Bt("Symbol."+e)),Rt[e]},qt=$t("species"),Mt=function(e,t){var r;return _t(e)&&("function"!=typeof(r=e.constructor)||r!==Array&&!_t(r.prototype)?pe(r)&&null===(r=r[qt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===t?0:t)};St({target:"Array",proto:!0},{flatMap:function(e){var t,r=he(this),n=it(r.length);return vt(e),(t=Mt(r,0)).length=At(t,r,r,n,0,1,e,arguments.length>1?arguments[1]:void 0),t}});var Ut,Gt,jt=Math.floor,Vt=function(e,t){var r=e.length,n=jt(r/2);return r<8?Ht(e,t):Xt(Vt(e.slice(0,n),t),Vt(e.slice(n),t),t)},Ht=function(e,t){for(var r,n,i=e.length,s=1;s<i;){for(n=s,r=e[s];n&&t(e[n-1],r)>0;)e[n]=e[--n];n!==s++&&(e[n]=r)}return e},Xt=function(e,t,r){for(var n=e.length,i=t.length,s=0,o=0,a=[];s<n||o<i;)s<n&&o<i?a.push(r(e[s],t[o])<=0?e[s++]:t[o++]):a.push(s<n?e[s++]:t[o++]);return a},zt=Vt,Wt=wt.match(/firefox\/(\d+)/i),Yt=!!Wt&&+Wt[1],Qt=/MSIE|Trident/.test(wt),Jt=wt.match(/AppleWebKit\/(\d+)\./),Kt=!!Jt&&+Jt[1],Zt=[],er=Zt.sort,tr=Z((function(){Zt.sort(void 0)})),rr=Z((function(){Zt.sort(null)})),nr=!!(Gt=[]["sort"])&&Z((function(){Gt.call(null,Ut||function(){throw 1},1)})),ir=!Z((function(){if(xt)return xt<70;if(!(Yt&&Yt>3)){if(Qt)return!0;if(Kt)return Kt<603;var e,t,r,n,i="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)Zt.push({k:t+n,v:r})}for(Zt.sort((function(e,t){return t.v-e.v})),n=0;n<Zt.length;n++)t=Zt[n].k.charAt(0),i.charAt(i.length-1)!==t&&(i+=t);return"DGBEFHACIJK"!==i}}));St({target:"Array",proto:!0,forced:tr||!rr||!nr||!ir},{sort:function(e){void 0!==e&&vt(e);var t=he(this);if(ir)return void 0===e?er.call(t):er.call(t,e);var r,n,i=[],s=it(t.length);for(n=0;n<s;n++)n in t&&i.push(t[n]);for(r=(i=zt(i,function(e){return function(t,r){return void 0===r?-1:void 0===t?1:void 0!==e?+e(t,r)||0:String(t)>String(r)?1:-1}}(e))).length,n=0;n<r;)t[n]=i[n++];for(;n<s;)delete t[n++];return t}});var sr={},or=$t("iterator"),ar=Array.prototype,ur={};ur[$t("toStringTag")]="z";var cr="[object z]"===String(ur),lr=$t("toStringTag"),pr="Arguments"==oe(function(){return arguments}()),Dr=cr?oe:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),lr))?r:pr?oe(t):"Object"==(n=oe(t))&&"function"==typeof t.callee?"Arguments":n},hr=$t("iterator"),dr=function(e){var t=e.return;if(void 0!==t)return ye(t.call(e)).value},fr=function(e,t){this.stopped=e,this.result=t},mr=function(e,t,r){var n,i,s,o,a,u,c,l,p=r&&r.that,D=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_ITERATOR),d=!(!r||!r.INTERRUPTED),f=bt(t,p,1+D+d),m=function(e){return n&&dr(n),new fr(!0,e)},g=function(e){return D?(ye(e),d?f(e[0],e[1],m):f(e[0],e[1])):d?f(e,m):f(e)};if(h)n=e;else{if("function"!=typeof(i=function(e){if(null!=e)return e[hr]||e["@@iterator"]||sr[Dr(e)]}(e)))throw TypeError("Target is not iterable");if(void 0!==(l=i)&&(sr.Array===l||ar[or]===l)){for(s=0,o=it(e.length);o>s;s++)if((a=g(e[s]))&&a instanceof fr)return a;return new fr(!1)}n=i.call(e)}for(u=n.next;!(c=u.call(n)).done;){try{a=g(c.value)}catch(e){throw dr(n),e}if("object"==typeof a&&a&&a instanceof fr)return a}return new fr(!1)};St({target:"Object",stat:!0},{fromEntries:function(e){var t={};return mr(e,(function(e,r){!function(e,t,r){var n=De(t);n in e?_e.f(e,n,ie(0,r)):e[n]=r}(t,e,r)}),{AS_ENTRIES:!0}),t}});var gr=void 0!==gr?gr:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function Er(){throw new Error("setTimeout has not been defined")}function Cr(){throw new Error("clearTimeout has not been defined")}var Tr=Er,yr=Cr;function Sr(e){if(Tr===setTimeout)return setTimeout(e,0);if((Tr===Er||!Tr)&&setTimeout)return Tr=setTimeout,setTimeout(e,0);try{return Tr(e,0)}catch(t){try{return Tr.call(null,e,0)}catch(t){return Tr.call(this,e,0)}}}"function"==typeof gr.setTimeout&&(Tr=setTimeout),"function"==typeof gr.clearTimeout&&(yr=clearTimeout);var _r,vr=[],br=!1,Fr=-1;function Ar(){br&&_r&&(br=!1,_r.length?vr=_r.concat(vr):Fr=-1,vr.length&&wr())}function wr(){if(!br){var e=Sr(Ar);br=!0;for(var t=vr.length;t;){for(_r=vr,vr=[];++Fr<t;)_r&&_r[Fr].run();Fr=-1,t=vr.length}_r=null,br=!1,function(e){if(yr===clearTimeout)return clearTimeout(e);if((yr===Cr||!yr)&&clearTimeout)return yr=clearTimeout,clearTimeout(e);try{yr(e)}catch(t){try{return yr.call(null,e)}catch(t){return yr.call(this,e)}}}(e)}}function Nr(e,t){this.fun=e,this.array=t}Nr.prototype.run=function(){this.fun.apply(null,this.array)};function kr(){}var Or=kr,xr=kr,Ir=kr,Pr=kr,Rr=kr,Lr=kr,Br=kr;var $r=gr.performance||{},qr=$r.now||$r.mozNow||$r.msNow||$r.oNow||$r.webkitNow||function(){return(new Date).getTime()};var Mr=new Date;var Ur={nextTick:function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];vr.push(new Nr(e,t)),1!==vr.length||br||Sr(wr)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:Or,addListener:xr,once:Ir,off:Pr,removeListener:Rr,removeAllListeners:Lr,emit:Br,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*qr.call($r),r=Math.floor(t),n=Math.floor(t%1*1e9);return e&&(r-=e[0],(n-=e[1])<0&&(r--,n+=1e9)),[r,n]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-Mr)/1e3}};var Gr="object"==typeof Ur&&Ur.env&&Ur.env.NODE_DEBUG&&/\bsemver\b/i.test(Ur.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var jr={SEMVER_SPEC_VERSION:"2.0.0",MAX_LENGTH:256,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||9007199254740991,MAX_SAFE_COMPONENT_LENGTH:16},Vr=t((function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:r}=jr,n=(t=e.exports={}).re=[],i=t.src=[],s=t.t={};let o=0;const a=(e,t,r)=>{const a=o++;Gr(a,t),s[e]=a,i[a]=t,n[a]=new RegExp(t,r?"g":void 0)};a("NUMERICIDENTIFIER","0|[1-9]\\d*"),a("NUMERICIDENTIFIERLOOSE","[0-9]+"),a("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),a("MAINVERSION",`(${i[s.NUMERICIDENTIFIER]})\\.(${i[s.NUMERICIDENTIFIER]})\\.(${i[s.NUMERICIDENTIFIER]})`),a("MAINVERSIONLOOSE",`(${i[s.NUMERICIDENTIFIERLOOSE]})\\.(${i[s.NUMERICIDENTIFIERLOOSE]})\\.(${i[s.NUMERICIDENTIFIERLOOSE]})`),a("PRERELEASEIDENTIFIER",`(?:${i[s.NUMERICIDENTIFIER]}|${i[s.NONNUMERICIDENTIFIER]})`),a("PRERELEASEIDENTIFIERLOOSE",`(?:${i[s.NUMERICIDENTIFIERLOOSE]}|${i[s.NONNUMERICIDENTIFIER]})`),a("PRERELEASE",`(?:-(${i[s.PRERELEASEIDENTIFIER]}(?:\\.${i[s.PRERELEASEIDENTIFIER]})*))`),a("PRERELEASELOOSE",`(?:-?(${i[s.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${i[s.PRERELEASEIDENTIFIERLOOSE]})*))`),a("BUILDIDENTIFIER","[0-9A-Za-z-]+"),a("BUILD",`(?:\\+(${i[s.BUILDIDENTIFIER]}(?:\\.${i[s.BUILDIDENTIFIER]})*))`),a("FULLPLAIN",`v?${i[s.MAINVERSION]}${i[s.PRERELEASE]}?${i[s.BUILD]}?`),a("FULL",`^${i[s.FULLPLAIN]}$`),a("LOOSEPLAIN",`[v=\\s]*${i[s.MAINVERSIONLOOSE]}${i[s.PRERELEASELOOSE]}?${i[s.BUILD]}?`),a("LOOSE",`^${i[s.LOOSEPLAIN]}$`),a("GTLT","((?:<|>)?=?)"),a("XRANGEIDENTIFIERLOOSE",`${i[s.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),a("XRANGEIDENTIFIER",`${i[s.NUMERICIDENTIFIER]}|x|X|\\*`),a("XRANGEPLAIN",`[v=\\s]*(${i[s.XRANGEIDENTIFIER]})(?:\\.(${i[s.XRANGEIDENTIFIER]})(?:\\.(${i[s.XRANGEIDENTIFIER]})(?:${i[s.PRERELEASE]})?${i[s.BUILD]}?)?)?`),a("XRANGEPLAINLOOSE",`[v=\\s]*(${i[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${i[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${i[s.XRANGEIDENTIFIERLOOSE]})(?:${i[s.PRERELEASELOOSE]})?${i[s.BUILD]}?)?)?`),a("XRANGE",`^${i[s.GTLT]}\\s*${i[s.XRANGEPLAIN]}$`),a("XRANGELOOSE",`^${i[s.GTLT]}\\s*${i[s.XRANGEPLAINLOOSE]}$`),a("COERCE",`(^|[^\\d])(\\d{1,${r}})(?:\\.(\\d{1,${r}}))?(?:\\.(\\d{1,${r}}))?(?:$|[^\\d])`),a("COERCERTL",i[s.COERCE],!0),a("LONETILDE","(?:~>?)"),a("TILDETRIM",`(\\s*)${i[s.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",a("TILDE",`^${i[s.LONETILDE]}${i[s.XRANGEPLAIN]}$`),a("TILDELOOSE",`^${i[s.LONETILDE]}${i[s.XRANGEPLAINLOOSE]}$`),a("LONECARET","(?:\\^)"),a("CARETTRIM",`(\\s*)${i[s.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",a("CARET",`^${i[s.LONECARET]}${i[s.XRANGEPLAIN]}$`),a("CARETLOOSE",`^${i[s.LONECARET]}${i[s.XRANGEPLAINLOOSE]}$`),a("COMPARATORLOOSE",`^${i[s.GTLT]}\\s*(${i[s.LOOSEPLAIN]})$|^$`),a("COMPARATOR",`^${i[s.GTLT]}\\s*(${i[s.FULLPLAIN]})$|^$`),a("COMPARATORTRIM",`(\\s*)${i[s.GTLT]}\\s*(${i[s.LOOSEPLAIN]}|${i[s.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",a("HYPHENRANGE",`^\\s*(${i[s.XRANGEPLAIN]})\\s+-\\s+(${i[s.XRANGEPLAIN]})\\s*$`),a("HYPHENRANGELOOSE",`^\\s*(${i[s.XRANGEPLAINLOOSE]})\\s+-\\s+(${i[s.XRANGEPLAINLOOSE]})\\s*$`),a("STAR","(<|>)?=?\\s*\\*"),a("GTE0","^\\s*>=\\s*0.0.0\\s*$"),a("GTE0PRE","^\\s*>=\\s*0.0.0-0\\s*$")}));const Hr=["includePrerelease","loose","rtl"];var Xr=e=>e?"object"!=typeof e?{loose:!0}:Hr.filter((t=>e[t])).reduce(((e,t)=>(e[t]=!0,e)),{}):{};const zr=/^[0-9]+$/,Wr=(e,t)=>{const r=zr.test(e),n=zr.test(t);return r&&n&&(e=+e,t=+t),e===t?0:r&&!n?-1:n&&!r?1:e<t?-1:1};var Yr={compareIdentifiers:Wr,rcompareIdentifiers:(e,t)=>Wr(t,e)};const{MAX_LENGTH:Qr,MAX_SAFE_INTEGER:Jr}=jr,{re:Kr,t:Zr}=Vr,{compareIdentifiers:en}=Yr;class tn{constructor(e,t){if(t=Xr(t),e instanceof tn){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid Version: ${e}`);if(e.length>Qr)throw new TypeError(`version is longer than ${Qr} characters`);Gr("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?Kr[Zr.LOOSE]:Kr[Zr.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>Jr||this.major<0)throw new TypeError("Invalid major version");if(this.minor>Jr||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>Jr||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<Jr)return t}return e})):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(Gr("SemVer.compare",this.version,this.options,e),!(e instanceof tn)){if("string"==typeof e&&e===this.version)return 0;e=new tn(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof tn||(e=new tn(e,this.options)),en(this.major,e.major)||en(this.minor,e.minor)||en(this.patch,e.patch)}comparePre(e){if(e instanceof tn||(e=new tn(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const r=this.prerelease[t],n=e.prerelease[t];if(Gr("prerelease compare",t,r,n),void 0===r&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===r)return-1;if(r!==n)return en(r,n)}while(++t)}compareBuild(e){e instanceof tn||(e=new tn(e,this.options));let t=0;do{const r=this.build[t],n=e.build[t];if(Gr("prerelease compare",t,r,n),void 0===r&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===r)return-1;if(r!==n)return en(r,n)}while(++t)}inc(e,t){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t),this.inc("pre",t);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t),this.inc("pre",t);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{let e=this.prerelease.length;for(;--e>=0;)"number"==typeof this.prerelease[e]&&(this.prerelease[e]++,e=-2);-1===e&&this.prerelease.push(0)}t&&(this.prerelease[0]===t?isNaN(this.prerelease[1])&&(this.prerelease=[t,0]):this.prerelease=[t,0]);break;default:throw new Error(`invalid increment argument: ${e}`)}return this.format(),this.raw=this.version,this}}var rn=tn;var nn=(e,t,r)=>new rn(e,r).compare(new rn(t,r));var sn=(e,t,r)=>nn(e,t,r)<0;var on=(e,t,r)=>nn(e,t,r)>=0,an="2.3.2",un=t((function(e,t){function r(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]}function n(){return"undefined"!=typeof WeakMap?new WeakMap:{add:r,delete:r,get:r,set:r,has:function(e){return!1}}}Object.defineProperty(t,"__esModule",{value:!0}),t.outdent=void 0;var i=Object.prototype.hasOwnProperty,s=function(e,t){return i.call(e,t)};function o(e,t){for(var r in t)s(t,r)&&(e[r]=t[r]);return e}var a=/^[ \t]*(?:\r\n|\r|\n)/,u=/(?:\r\n|\r|\n)[ \t]*$/,c=/^(?:[\r\n]|$)/,l=/(?:\r\n|\r|\n)([ \t]*)(?:[^ \t\r\n]|$)/,p=/^[ \t]*[\r\n][ \t\r\n]*$/;function D(e,t,r){var n=0,i=e[0].match(l);i&&(n=i[1].length);var s=new RegExp("(\\r\\n|\\r|\\n).{0,"+n+"}","g");t&&(e=e.slice(1));var o=r.newline,c=r.trimLeadingNewline,p=r.trimTrailingNewline,D="string"==typeof o,h=e.length;return e.map((function(e,t){return e=e.replace(s,"$1"),0===t&&c&&(e=e.replace(a,"")),t===h-1&&p&&(e=e.replace(u,"")),D&&(e=e.replace(/\r\n|\n|\r/g,(function(e){return o}))),e}))}function h(e,t){for(var r="",n=0,i=e.length;n<i;n++)r+=e[n],n<i-1&&(r+=t[n]);return r}function d(e){return s(e,"raw")&&s(e,"length")}var f=function e(t){var r=n(),i=n();return o((function n(s){for(var a=[],u=1;u<arguments.length;u++)a[u-1]=arguments[u];if(d(s)){var l=s,m=(a[0]===n||a[0]===f)&&p.test(l[0])&&c.test(l[1]),g=m?i:r,E=g.get(l);if(E||(E=D(l,m,t),g.set(l,E)),0===a.length)return E[0];var C=h(E,m?a.slice(1):a);return C}return e(o(o({},t),s||{}))}),{string:function(e){return D([e],!1,t)[0]}})}({trimLeadingNewline:!0,trimTrailingNewline:!0});t.outdent=f,t.default=f;try{e.exports=f,Object.defineProperty(f,"__esModule",{value:!0}),f.default=f,f.outdent=f}catch(e){}}));const{outdent:cn}=un,ln={cursorOffset:{since:"1.4.0",category:"Special",type:"int",default:-1,range:{start:-1,end:Number.POSITIVE_INFINITY,step:1},description:cn`
      Print (to stderr) where a cursor at the given position would move to after formatting.
      This option cannot be used with --range-start and --range-end.
    `,cliCategory:"Editor"},endOfLine:{since:"1.15.0",category:"Global",type:"choice",default:[{since:"1.15.0",value:"auto"},{since:"2.0.0",value:"lf"}],description:"Which end of line characters to apply.",choices:[{value:"lf",description:"Line Feed only (\\n), common on Linux and macOS as well as inside git repos"},{value:"crlf",description:"Carriage Return + Line Feed characters (\\r\\n), common on Windows"},{value:"cr",description:"Carriage Return character only (\\r), used very rarely"},{value:"auto",description:cn`
          Maintain existing
          (mixed values within one file are normalised by looking at what's used after the first line)
        `}]},filepath:{since:"1.4.0",category:"Special",type:"path",description:"Specify the input filepath. This will be used to do parser inference.",cliName:"stdin-filepath",cliCategory:"Other",cliDescription:"Path to the file to pretend that stdin comes from."},insertPragma:{since:"1.8.0",category:"Special",type:"boolean",default:!1,description:"Insert @format pragma into file's first docblock comment.",cliCategory:"Other"},parser:{since:"0.0.10",category:"Global",type:"choice",default:[{since:"0.0.10",value:"babylon"},{since:"1.13.0",value:void 0}],description:"Which parser to use.",exception:e=>"string"==typeof e||"function"==typeof e,choices:[{value:"flow",description:"Flow"},{value:"babel",since:"1.16.0",description:"JavaScript"},{value:"babel-flow",since:"1.16.0",description:"Flow"},{value:"babel-ts",since:"2.0.0",description:"TypeScript"},{value:"typescript",since:"1.4.0",description:"TypeScript"},{value:"espree",since:"2.2.0",description:"JavaScript"},{value:"meriyah",since:"2.2.0",description:"JavaScript"},{value:"css",since:"1.7.1",description:"CSS"},{value:"less",since:"1.7.1",description:"Less"},{value:"scss",since:"1.7.1",description:"SCSS"},{value:"json",since:"1.5.0",description:"JSON"},{value:"json5",since:"1.13.0",description:"JSON5"},{value:"json-stringify",since:"1.13.0",description:"JSON.stringify"},{value:"graphql",since:"1.5.0",description:"GraphQL"},{value:"markdown",since:"1.8.0",description:"Markdown"},{value:"mdx",since:"1.15.0",description:"MDX"},{value:"vue",since:"1.10.0",description:"Vue"},{value:"yaml",since:"1.14.0",description:"YAML"},{value:"glimmer",since:"2.3.0",description:"Ember / Handlebars"},{value:"html",since:"1.15.0",description:"HTML"},{value:"angular",since:"1.15.0",description:"Angular"},{value:"lwc",since:"1.17.0",description:"Lightning Web Components"}]},plugins:{since:"1.10.0",type:"path",array:!0,default:[{value:[]}],category:"Global",description:"Add a plugin. Multiple plugins can be passed as separate `--plugin`s.",exception:e=>"string"==typeof e||"object"==typeof e,cliName:"plugin",cliCategory:"Config"},pluginSearchDirs:{since:"1.13.0",type:"path",array:!0,default:[{value:[]}],category:"Global",description:cn`
      Custom directory that contains prettier plugins in node_modules subdirectory.
      Overrides default behavior when plugins are searched relatively to the location of Prettier.
      Multiple values are accepted.
    `,exception:e=>"string"==typeof e||"object"==typeof e,cliName:"plugin-search-dir",cliCategory:"Config"},printWidth:{since:"0.0.0",category:"Global",type:"int",default:80,description:"The line length where Prettier will try wrap.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},rangeEnd:{since:"1.4.0",category:"Special",type:"int",default:Number.POSITIVE_INFINITY,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:cn`
      Format code ending at a given character offset (exclusive).
      The range will extend forwards to the end of the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:"Editor"},rangeStart:{since:"1.4.0",category:"Special",type:"int",default:0,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:cn`
      Format code starting at a given character offset.
      The range will extend backwards to the start of the first line containing the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:"Editor"},requirePragma:{since:"1.7.0",category:"Special",type:"boolean",default:!1,description:cn`
      Require either '@prettier' or '@format' to be present in the file's first docblock comment
      in order for it to be formatted.
    `,cliCategory:"Other"},tabWidth:{type:"int",category:"Global",default:2,description:"Number of spaces per indentation level.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},useTabs:{since:"1.0.0",category:"Global",type:"boolean",default:!1,description:"Indent with tabs instead of spaces."},embeddedLanguageFormatting:{since:"2.1.0",category:"Global",type:"choice",default:[{since:"2.1.0",value:"auto"}],description:"Control how Prettier formats quoted code embedded in the file.",choices:[{value:"auto",description:"Format embedded code if Prettier can automatically identify it."},{value:"off",description:"Never automatically format embedded code."}]}};const pn=["cliName","cliCategory","cliDescription"],Dn={compare:nn,lt:sn,gte:on},hn=an,dn={CATEGORY_CONFIG:"Config",CATEGORY_EDITOR:"Editor",CATEGORY_FORMAT:"Format",CATEGORY_OTHER:"Other",CATEGORY_OUTPUT:"Output",CATEGORY_GLOBAL:"Global",CATEGORY_SPECIAL:"Special",options:ln}.options;var fn={getSupportInfo:function({plugins:e=[],showUnreleased:t=!1,showDeprecated:r=!1,showInternal:n=!1}={}){const i=hn.split("-",1)[0],s=e.flatMap((e=>e.languages||[])).filter(a),o=((e,t)=>Object.entries(e).map((([e,r])=>Object.assign({[t]:e},r))))(Object.assign({},...e.map((({options:e})=>e)),dn),"name").filter((e=>a(e)&&u(e))).sort(((e,t)=>e.name===t.name?0:e.name<t.name?-1:1)).map((function(e){if(n)return e;return Q(e,pn)})).map((t=>{t=Object.assign({},t),Array.isArray(t.default)&&(t.default=1===t.default.length?t.default[0].value:t.default.filter(a).sort(((e,t)=>Dn.compare(t.since,e.since)))[0].value),Array.isArray(t.choices)&&(t.choices=t.choices.filter((e=>a(e)&&u(e))),"parser"===t.name&&function(e,t,r){const n=new Set(e.choices.map((e=>e.value)));for(const i of t)if(i.parsers)for(const t of i.parsers)if(!n.has(t)){n.add(t);const s=r.find((e=>e.parsers&&e.parsers[t]));let o=i.name;s&&s.name&&(o+=` (plugin: ${s.name})`),e.choices.push({value:t,description:o})}}(t,s,e));const r=Object.fromEntries(e.filter((e=>e.defaultOptions&&void 0!==e.defaultOptions[t.name])).map((e=>[e.name,e.defaultOptions[t.name]])));return Object.assign(Object.assign({},t),{},{pluginDefaults:r})}));return{languages:s,options:o};function a(e){return t||!("since"in e)||e.since&&Dn.gte(i,e.since)}function u(e){return r||!("deprecated"in e)||e.deprecated&&Dn.lt(i,e.deprecated)}}};const{getSupportInfo:mn}=fn,gn=/[^\x20-\x7F]/;function En(e){return(t,r,n)=>{const i=n&&n.backwards;if(!1===r)return!1;const{length:s}=t;let o=r;for(;o>=0&&o<s;){const r=t.charAt(o);if(e instanceof RegExp){if(!e.test(r))return o}else if(!e.includes(r))return o;i?o--:o++}return(-1===o||o===s)&&o}}const Cn=En(/\s/),Tn=En(" \t"),yn=En(",; \t"),Sn=En(/[^\n\r]/);function _n(e,t){if(!1===t)return!1;if("/"===e.charAt(t)&&"*"===e.charAt(t+1))for(let r=t+2;r<e.length;++r)if("*"===e.charAt(r)&&"/"===e.charAt(r+1))return r+2;return t}function vn(e,t){return!1!==t&&("/"===e.charAt(t)&&"/"===e.charAt(t+1)?Sn(e,t):t)}function bn(e,t,r){const n=r&&r.backwards;if(!1===t)return!1;const i=e.charAt(t);if(n){if("\r"===e.charAt(t-1)&&"\n"===i)return t-2;if("\n"===i||"\r"===i||"\u2028"===i||"\u2029"===i)return t-1}else{if("\r"===i&&"\n"===e.charAt(t+1))return t+2;if("\n"===i||"\r"===i||"\u2028"===i||"\u2029"===i)return t+1}return t}function Fn(e,t,r={}){const n=Tn(e,r.backwards?t-1:t,r);return n!==bn(e,n,r)}function An(e,t){let r=null,n=t;for(;n!==r;)r=n,n=yn(e,n),n=_n(e,n),n=Tn(e,n);return n=vn(e,n),n=bn(e,n),!1!==n&&Fn(e,n)}function wn(e,t){let r=null,n=t;for(;n!==r;)r=n,n=Tn(e,n),n=_n(e,n),n=vn(e,n),n=bn(e,n);return n}function Nn(e,t,r){return wn(e,r(t))}function kn(e,t,r=0){let n=0;for(let i=r;i<e.length;++i)"\t"===e[i]?n=n+t-n%t:n++;return n}function On(e,t){const r=e.slice(1,-1),n={quote:'"',regex:/"/g},i={quote:"'",regex:/'/g},s="'"===t?i:n,o=s===i?n:i;let a=s.quote;if(r.includes(s.quote)||r.includes(o.quote)){a=(r.match(s.regex)||[]).length>(r.match(o.regex)||[]).length?o.quote:s.quote}return a}function xn(e,t,r){const n='"'===t?"'":'"',i=e.replace(/\\(.)|(["'])/gs,((e,i,s)=>i===n?i:s===t?"\\"+s:s||(r&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/.test(i)?i:"\\"+i)));return t+i+t}function In(e,t){(e.comments||(e.comments=[])).push(t),t.printed=!1,t.nodeDescription=function(e){const t=e.type||e.kind||"(unknown type)";let r=String(e.name||e.id&&("object"==typeof e.id?e.id.name:e.id)||e.key&&("object"==typeof e.key?e.key.name:e.key)||e.value&&("object"==typeof e.value?"":String(e.value))||e.operator||"");r.length>20&&(r=r.slice(0,19)+"\u2026");return t+(r?" "+r:"")}(e)}var Pn={inferParserByLanguage:function(e,t){const{languages:r}=mn({plugins:t.plugins}),n=r.find((({name:t})=>t.toLowerCase()===e))||r.find((({aliases:t})=>Array.isArray(t)&&t.includes(e)))||r.find((({extensions:t})=>Array.isArray(t)&&t.includes(`.${e}`)));return n&&n.parsers[0]},getStringWidth:function(e){return e?gn.test(e)?z(e):e.length:0},getMaxContinuousCount:function(e,t){const r=e.match(new RegExp(`(${Y(t)})+`,"g"));return null===r?0:r.reduce(((e,r)=>Math.max(e,r.length/t.length)),0)},getMinNotPresentContinuousCount:function(e,t){const r=e.match(new RegExp(`(${Y(t)})+`,"g"));if(null===r)return 0;const n=new Map;let i=0;for(const e of r){const r=e.length/t.length;n.set(r,!0),r>i&&(i=r)}for(let e=1;e<i;e++)if(!n.get(e))return e;return i+1},getPenultimate:e=>e[e.length-2],getLast:M,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:wn,getNextNonSpaceNonCommentCharacterIndex:Nn,getNextNonSpaceNonCommentCharacter:function(e,t,r){return e.charAt(Nn(e,t,r))},skip:En,skipWhitespace:Cn,skipSpaces:Tn,skipToLineEnd:yn,skipEverythingButNewLine:Sn,skipInlineComment:_n,skipTrailingComment:vn,skipNewline:bn,isNextLineEmptyAfterIndex:An,isNextLineEmpty:function(e,t,r){return An(e,r(t))},isPreviousLineEmpty:function(e,t,r){let n=r(t)-1;return n=Tn(e,n,{backwards:!0}),n=bn(e,n,{backwards:!0}),n=Tn(e,n,{backwards:!0}),n!==bn(e,n,{backwards:!0})},hasNewline:Fn,hasNewlineInRange:function(e,t,r){for(let n=t;n<r;++n)if("\n"===e.charAt(n))return!0;return!1},hasSpaces:function(e,t,r={}){return Tn(e,r.backwards?t-1:t,r)!==t},getAlignmentSize:kn,getIndentSize:function(e,t){const r=e.lastIndexOf("\n");return-1===r?0:kn(e.slice(r+1).match(/^[\t ]*/)[0],t)},getPreferredQuote:On,printString:function(e,t){return xn(e.slice(1,-1),"json"===t.parser||"json5"===t.parser&&"preserve"===t.quoteProps&&!t.singleQuote?'"':t.__isInHtmlAttribute?"'":On(e,t.singleQuote?"'":'"'),!("css"===t.parser||"less"===t.parser||"scss"===t.parser||t.__embeddedInHtml))},printNumber:function(e){return e.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(\d)/,"$1$2$3").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")},makeString:xn,addLeadingComment:function(e,t){t.leading=!0,t.trailing=!1,In(e,t)},addDanglingComment:function(e,t,r){t.leading=!1,t.trailing=!1,r&&(t.marker=r),In(e,t)},addTrailingComment:function(e,t){t.leading=!1,t.trailing=!0,In(e,t)},isFrontMatterNode:function(e){return e&&"front-matter"===e.type},getShebang:function(e){if(!e.startsWith("#!"))return"";const t=e.indexOf("\n");return-1===t?e:e.slice(0,t)},isNonEmptyArray:function(e){return Array.isArray(e)&&e.length>0},createGroupIdMapper:function(e){const t=new WeakMap;return function(r){return t.has(r)||t.set(r,Symbol(e)),t.get(r)}}},Rn={"*":["accesskey","autocapitalize","autofocus","class","contenteditable","dir","draggable","enterkeyhint","hidden","id","inputmode","is","itemid","itemprop","itemref","itemscope","itemtype","lang","nonce","slot","spellcheck","style","tabindex","title","translate"],a:["accesskey","charset","coords","download","href","hreflang","name","ping","referrerpolicy","rel","rev","shape","tabindex","target","type"],abbr:["title"],applet:["align","alt","archive","code","codebase","height","hspace","name","object","vspace","width"],area:["accesskey","alt","coords","download","href","hreflang","nohref","ping","referrerpolicy","rel","shape","tabindex","target","type"],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],base:["href","target"],basefont:["color","face","size"],bdo:["dir"],blockquote:["cite"],body:["alink","background","bgcolor","link","text","vlink"],br:["clear"],button:["accesskey","autofocus","disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","name","tabindex","type","value"],canvas:["height","width"],caption:["align"],col:["align","char","charoff","span","valign","width"],colgroup:["align","char","charoff","span","valign","width"],data:["value"],del:["cite","datetime"],details:["open"],dfn:["title"],dialog:["open"],dir:["compact"],div:["align"],dl:["compact"],embed:["height","src","type","width"],fieldset:["disabled","form","name"],font:["color","face","size"],form:["accept","accept-charset","action","autocomplete","enctype","method","name","novalidate","target"],frame:["frameborder","longdesc","marginheight","marginwidth","name","noresize","scrolling","src"],frameset:["cols","rows"],h1:["align"],h2:["align"],h3:["align"],h4:["align"],h5:["align"],h6:["align"],head:["profile"],hr:["align","noshade","size","width"],html:["manifest","version"],iframe:["align","allow","allowfullscreen","allowpaymentrequest","allowusermedia","frameborder","height","loading","longdesc","marginheight","marginwidth","name","referrerpolicy","sandbox","scrolling","src","srcdoc","width"],img:["align","alt","border","crossorigin","decoding","height","hspace","ismap","loading","longdesc","name","referrerpolicy","sizes","src","srcset","usemap","vspace","width"],input:["accept","accesskey","align","alt","autocomplete","autofocus","checked","dirname","disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","height","ismap","list","max","maxlength","min","minlength","multiple","name","pattern","placeholder","readonly","required","size","src","step","tabindex","title","type","usemap","value","width"],ins:["cite","datetime"],isindex:["prompt"],label:["accesskey","for","form"],legend:["accesskey","align"],li:["type","value"],link:["as","charset","color","crossorigin","disabled","href","hreflang","imagesizes","imagesrcset","integrity","media","nonce","referrerpolicy","rel","rev","sizes","target","title","type"],map:["name"],menu:["compact"],meta:["charset","content","http-equiv","name","scheme"],meter:["high","low","max","min","optimum","value"],object:["align","archive","border","classid","codebase","codetype","data","declare","form","height","hspace","name","standby","tabindex","type","typemustmatch","usemap","vspace","width"],ol:["compact","reversed","start","type"],optgroup:["disabled","label"],option:["disabled","label","selected","value"],output:["for","form","name"],p:["align"],param:["name","type","value","valuetype"],pre:["width"],progress:["max","value"],q:["cite"],script:["async","charset","crossorigin","defer","integrity","language","nomodule","nonce","referrerpolicy","src","type"],select:["autocomplete","autofocus","disabled","form","multiple","name","required","size","tabindex"],slot:["name"],source:["media","sizes","src","srcset","type"],style:["media","nonce","title","type"],table:["align","bgcolor","border","cellpadding","cellspacing","frame","rules","summary","width"],tbody:["align","char","charoff","valign"],td:["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"],textarea:["accesskey","autocomplete","autofocus","cols","dirname","disabled","form","maxlength","minlength","name","placeholder","readonly","required","rows","tabindex","wrap"],tfoot:["align","char","charoff","valign"],th:["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"],thead:["align","char","charoff","valign"],time:["datetime"],tr:["align","bgcolor","char","charoff","valign"],track:["default","kind","label","src","srclang"],ul:["compact","type"],video:["autoplay","controls","crossorigin","height","loop","muted","playsinline","poster","preload","src","width"]};const{inferParserByLanguage:Ln,isFrontMatterNode:Bn}=Pn,{CSS_DISPLAY_TAGS:$n,CSS_DISPLAY_DEFAULT:qn,CSS_WHITE_SPACE_TAGS:Mn,CSS_WHITE_SPACE_DEFAULT:Un}={CSS_DISPLAY_TAGS:{area:"none",base:"none",basefont:"none",datalist:"none",head:"none",link:"none",meta:"none",noembed:"none",noframes:"none",param:"block",rp:"none",script:"block",source:"block",style:"none",template:"inline",track:"block",title:"none",html:"block",body:"block",address:"block",blockquote:"block",center:"block",div:"block",figure:"block",figcaption:"block",footer:"block",form:"block",header:"block",hr:"block",legend:"block",listing:"block",main:"block",p:"block",plaintext:"block",pre:"block",xmp:"block",slot:"contents",ruby:"ruby",rt:"ruby-text",article:"block",aside:"block",h1:"block",h2:"block",h3:"block",h4:"block",h5:"block",h6:"block",hgroup:"block",nav:"block",section:"block",dir:"block",dd:"block",dl:"block",dt:"block",ol:"block",ul:"block",li:"list-item",table:"table",caption:"table-caption",colgroup:"table-column-group",col:"table-column",thead:"table-header-group",tbody:"table-row-group",tfoot:"table-footer-group",tr:"table-row",td:"table-cell",th:"table-cell",fieldset:"block",button:"inline-block",details:"block",summary:"block",dialog:"block",meter:"inline-block",progress:"inline-block",object:"inline-block",video:"inline-block",audio:"inline-block",select:"inline-block",option:"block",optgroup:"block"},CSS_DISPLAY_DEFAULT:"inline",CSS_WHITE_SPACE_TAGS:{listing:"pre",plaintext:"pre",pre:"pre",xmp:"pre",nobr:"nowrap",table:"initial",textarea:"pre-wrap"},CSS_WHITE_SPACE_DEFAULT:"normal"},Gn=Xn(["a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","bgsound","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","command","content","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","element","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","image","img","input","ins","isindex","kbd","keygen","label","legend","li","link","listing","main","map","mark","marquee","math","menu","menuitem","meta","meter","multicol","nav","nextid","nobr","noembed","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","plaintext","pre","progress","q","rb","rbc","rp","rt","rtc","ruby","s","samp","script","section","select","shadow","slot","small","source","spacer","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr","xmp"]),jn=function(e,t){const r=Object.create(null);for(const[n,i]of Object.entries(e))r[n]=t(i,n);return r}(Rn,Xn),Vn=new Set(["\t","\n","\f","\r"," "]),Hn=e=>e.replace(/[\t\n\f\r ]+$/,"");function Xn(e){const t=Object.create(null);for(const r of e)t[r]=!0;return t}function zn(e,t){return!("ieConditionalComment"!==e.type||!e.lastChild||e.lastChild.isSelfClosing||e.lastChild.endSourceSpan)||("ieConditionalComment"===e.type&&!e.complete||(!(!si(e)||!e.children.some((e=>"text"!==e.type&&"interpolation"!==e.type)))||!(!pi(e,t)||Yn(e)||"interpolation"===e.type)))}function Wn(e){if("attribute"===e.type)return!1;if(!e.parent)return!1;if("number"!=typeof e.index||0===e.index)return!1;return function(e){return"comment"===e.type&&"prettier-ignore"===e.value.trim()}(e.parent.children[e.index-1])}function Yn(e){return"element"===e.type&&("script"===e.fullName||"style"===e.fullName||"svg:style"===e.fullName||oi(e)&&("script"===e.name||"style"===e.name))}function Qn(e){return ai(e).startsWith("pre")}function Jn(e){return"element"===e.type&&e.children.length>0&&(["html","head","ul","ol","select"].includes(e.name)||e.cssDisplay.startsWith("table")&&"table-cell"!==e.cssDisplay)}function Kn(e){return ri(e)||"element"===e.type&&"br"===e.fullName||Zn(e)}function Zn(e){return ei(e)&&ti(e)}function ei(e){return e.hasLeadingSpaces&&(e.prev?e.prev.sourceSpan.end.line<e.sourceSpan.start.line:"root"===e.parent.type||e.parent.startSourceSpan.end.line<e.sourceSpan.start.line)}function ti(e){return e.hasTrailingSpaces&&(e.next?e.next.sourceSpan.start.line>e.sourceSpan.end.line:"root"===e.parent.type||e.parent.endSourceSpan&&e.parent.endSourceSpan.start.line>e.sourceSpan.end.line)}function ri(e){switch(e.type){case"ieConditionalComment":case"comment":case"directive":return!0;case"element":return["script","select"].includes(e.name)}return!1}function ni(e){const{type:t,lang:r}=e.attrMap;return"module"===t||"text/javascript"===t||"text/babel"===t||"application/javascript"===t||"jsx"===r?"babel":"application/x-typescript"===t||"ts"===r||"tsx"===r?"typescript":"text/markdown"===t?"markdown":"text/html"===t?"html":t&&(t.endsWith("json")||t.endsWith("importmap"))?"json":"text/x-handlebars-template"===t?"glimmer":void 0}function ii(e){return"block"===e||"list-item"===e||e.startsWith("table")}function si(e){return ai(e).startsWith("pre")}function oi(e){return"element"===e.type&&!e.hasExplicitNamespace&&!["html","svg"].includes(e.namespace)}function ai(e){return"element"===e.type&&(!e.namespace||oi(e))&&Mn[e.name]||Un}const ui=new Set(["template","style","script"]);function ci(e,t){return li(e,t)&&!ui.has(e.fullName)}function li(e,t){return"vue"===t.parser&&"element"===e.type&&"root"===e.parent.type&&"html"!==e.fullName.toLowerCase()}function pi(e,t){return li(e,t)&&(ci(e,t)||e.attrMap.lang&&"html"!==e.attrMap.lang)}var Di={HTML_ELEMENT_ATTRIBUTES:jn,HTML_TAGS:Gn,htmlTrim:e=>(e=>e.replace(/^[\t\n\f\r ]+/,""))(Hn(e)),htmlTrimPreserveIndentation:e=>(e=>e.replace(/^[\t\f\r ]*?\n/g,""))(Hn(e)),splitByHtmlWhitespace:e=>e.split(/[\t\n\f\r ]+/),hasHtmlWhitespace:e=>/[\t\n\f\r ]/.test(e),getLeadingAndTrailingHtmlWhitespace:e=>{const[,t,r,n]=e.match(/^([\t\n\f\r ]*)(.*?)([\t\n\f\r ]*)$/s);return{leadingWhitespace:t,trailingWhitespace:n,text:r}},canHaveInterpolation:function(e){return e.children&&!Yn(e)},countChars:function(e,t){let r=0;for(let n=0;n<e.length;n++)e[n]===t&&r++;return r},countParents:function(e,t){let r=0;for(let n=e.stack.length-1;n>=0;n--){const i=e.stack[n];i&&"object"==typeof i&&!Array.isArray(i)&&t(i)&&r++}return r},dedentString:function(e,t=function(e){let t=Number.POSITIVE_INFINITY;for(const n of e.split("\n")){if(0===n.length)continue;if(!Vn.has(n[0]))return 0;const e=(r=n,r.match(/^[\t\n\f\r ]*/)[0]).length;n.length!==e&&e<t&&(t=e)}var r;return t===Number.POSITIVE_INFINITY?0:t}(e)){return 0===t?e:e.split("\n").map((e=>e.slice(t))).join("\n")},forceBreakChildren:Jn,forceBreakContent:function(e){return Jn(e)||"element"===e.type&&e.children.length>0&&(["body","script","style"].includes(e.name)||e.children.some((e=>function(e){return e.children&&e.children.some((e=>"text"!==e.type))}(e))))||e.firstChild&&e.firstChild===e.lastChild&&"text"!==e.firstChild.type&&ei(e.firstChild)&&(!e.lastChild.isTrailingSpaceSensitive||ti(e.lastChild))},forceNextEmptyLine:function(e){return Bn(e)||e.next&&e.sourceSpan.end&&e.sourceSpan.end.line+1<e.next.sourceSpan.start.line},getLastDescendant:function e(t){return t.lastChild?e(t.lastChild):t},getNodeCssStyleDisplay:function(e,t){if(e.prev&&"comment"===e.prev.type){const t=e.prev.value.match(/^\s*display:\s*([a-z]+)\s*$/);if(t)return t[1]}let r=!1;if("element"===e.type&&"svg"===e.namespace){if(!function(e,t){let r=e;for(;r;){if(t(r))return!0;r=r.parent}return!1}(e,(e=>"svg:foreignObject"===e.fullName)))return"svg"===e.name?"inline-block":"block";r=!0}switch(t.htmlWhitespaceSensitivity){case"strict":return"inline";case"ignore":return"block";default:return"vue"===t.parser&&e.parent&&"root"===e.parent.type?"block":"element"===e.type&&(!e.namespace||r||oi(e))&&$n[e.name]||qn}},getNodeCssStyleWhiteSpace:ai,getPrettierIgnoreAttributeCommentData:function(e){const t=e.trim().match(/^prettier-ignore-attribute(?:\s+(.+))?$/s);return!!t&&(!t[1]||t[1].split(/\s+/))},hasPrettierIgnore:Wn,inferScriptParser:function(e,t){return"script"!==e.name||e.attrMap.src?"style"===e.name?function(e){const{lang:t}=e.attrMap;return t&&"postcss"!==t&&"css"!==t?"scss"===t?"scss":"less"===t?"less":void 0:"css"}(e):t&&pi(e,t)?ni(e)||!("src"in e.attrMap)&&Ln(e.attrMap.lang,t):void 0:e.attrMap.lang||e.attrMap.type?ni(e):"babel"},isVueCustomBlock:ci,isVueNonHtmlBlock:pi,isVueSlotAttribute:function(e){const t=e.fullName;return"#"===t.charAt(0)||"slot-scope"===t||"v-slot"===t||t.startsWith("v-slot:")},isVueSfcBindingsAttribute:function(e,t){const r=e.parent;if(!li(r,t))return!1;const n=r.fullName,i=e.fullName;return"script"===n&&"setup"===i||"style"===n&&"vars"===i},isDanglingSpaceSensitiveNode:function(e){return!(t=e.cssDisplay,ii(t)||"inline-block"===t||Yn(e));var t},isIndentationSensitiveNode:Qn,isLeadingSpaceSensitiveNode:function(e,t){const r=function(){if(Bn(e))return!1;if(("text"===e.type||"interpolation"===e.type)&&e.prev&&("text"===e.prev.type||"interpolation"===e.prev.type))return!0;if(!e.parent||"none"===e.parent.cssDisplay)return!1;if(si(e.parent))return!0;if(!e.prev&&("root"===e.parent.type||si(e)&&e.parent||Yn(e.parent)||ci(e.parent,t)||(r=e.parent.cssDisplay,ii(r)||"inline-block"===r)))return!1;var r;if(e.prev&&!function(e){return!ii(e)}(e.prev.cssDisplay))return!1;return!0}();return r&&!e.prev&&e.parent&&e.parent.tagDefinition&&e.parent.tagDefinition.ignoreFirstLf?"interpolation"===e.type:r},isPreLikeNode:si,isScriptLikeTag:Yn,isTextLikeNode:function(e){return"text"===e.type||"comment"===e.type},isTrailingSpaceSensitiveNode:function(e,t){return!Bn(e)&&(!("text"!==e.type&&"interpolation"!==e.type||!e.next||"text"!==e.next.type&&"interpolation"!==e.next.type)||!(!e.parent||"none"===e.parent.cssDisplay)&&(!!si(e.parent)||!(!e.next&&("root"===e.parent.type||si(e)&&e.parent||Yn(e.parent)||ci(e.parent,t)||(r=e.parent.cssDisplay,ii(r)||"inline-block"===r)))&&!(e.next&&!function(e){return!ii(e)}(e.next.cssDisplay))));var r},isWhitespaceSensitiveNode:function(e){return Yn(e)||"interpolation"===e.type||Qn(e)},isUnknownNamespace:oi,preferHardlineAsLeadingSpaces:function(e){return ri(e)||e.prev&&Kn(e.prev)||Zn(e)},preferHardlineAsTrailingSpaces:Kn,shouldNotPrintClosingTag:function(e,t){return!e.isSelfClosing&&!e.endSourceSpan&&(Wn(e)||zn(e.parent,t))},shouldPreserveContent:zn,unescapeQuoteEntities:function(e){return e.replace(/&apos;/g,"'").replace(/&quot;/g,'"')}};var hi={hasPragma:function(e){return/^\s*<!--\s*@(format|prettier)\s*-->/.test(e)},insertPragma:function(e){return"\x3c!-- @format --\x3e\n\n"+e.replace(/^\s*\n/,"")}};const{isNonEmptyArray:di}=Pn,fi={attrs:!0,children:!0};class mi{constructor(e={}){for(const[t,r]of Object.entries(e))t in fi?this._setNodes(t,r):this[t]=r}_setNodes(e,t){t!==this[e]&&(this[e]=function(e,t){const r=e.map((e=>e instanceof mi?e.clone():new mi(e)));let n=null,i=r[0],s=r[1]||null;for(let e=0;e<r.length;e++)Ei(i,{index:e,siblings:r,prev:n,next:s,parent:t}),n=i,i=s,s=r[e+2]||null;return r}(t,this),"attrs"===e&&Ei(this,{attrMap:Object.fromEntries(this[e].map((e=>[e.fullName,e.value])))}))}map(e){let t=null;for(const r in fi){const n=this[r];if(n){const i=gi(n,(t=>t.map(e)));t!==n&&(t||(t=new mi),t._setNodes(r,i))}}if(t){for(const e in this)e in fi||(t[e]=this[e]);const{index:e,siblings:r,prev:n,next:i,parent:s}=this;Ei(t,{index:e,siblings:r,prev:n,next:i,parent:s})}return e(t||this)}clone(e){return new mi(e?Object.assign(Object.assign({},this),e):this)}get firstChild(){return di(this.children)?this.children[0]:null}get lastChild(){return di(this.children)?M(this.children):null}get rawName(){return this.hasExplicitNamespace?this.fullName:this.name}get fullName(){return this.namespace?this.namespace+":"+this.name:this.name}}function gi(e,t){const r=e.map(t);return r.some(((t,r)=>t!==e[r]))?r:e}function Ei(e,t){const r=Object.fromEntries(Object.entries(t).map((([e,t])=>[e,{value:t,enumerable:!1}])));Object.defineProperties(e,r)}var Ci={Node:mi};const{ParseSourceSpan:Ti}=B,yi=[{regex:/^(\[if([^\]]*?)]>)(.*?)<!\s*\[endif]$/s,parse:function(e,t,r){const[,n,i,s]=r,o="\x3c!--".length+n.length,a=e.sourceSpan.start.moveBy(o),u=a.moveBy(s.length),[c,l]=(()=>{try{return[!0,t(s,a).children]}catch{return[!1,[{type:"text",value:s,sourceSpan:new Ti(a,u)}]]}})();return{type:"ieConditionalComment",complete:c,children:l,condition:i.trim().replace(/\s+/g," "),sourceSpan:e.sourceSpan,startSourceSpan:new Ti(e.sourceSpan.start,a),endSourceSpan:new Ti(u,e.sourceSpan.end)}}},{regex:/^\[if([^\]]*?)]><!$/,parse:function(e,t,r){const[,n]=r;return{type:"ieConditionalStartComment",condition:n.trim().replace(/\s+/g," "),sourceSpan:e.sourceSpan}}},{regex:/^<!\s*\[endif]$/,parse:function(e){return{type:"ieConditionalEndComment",sourceSpan:e.sourceSpan}}}];var Si={parseIeConditionalComment:function(e,t){if(e.value)for(const{regex:r,parse:n}of yi){const i=e.value.match(r);if(i)return n(e,t,i)}return null}};var _i={locStart:function(e){return e.sourceSpan.start.offset},locEnd:function(e){return e.sourceSpan.end.offset}},vi=t((function(e,t){function r(e){if(":"!=e[0])return[null,e];const t=e.indexOf(":",1);if(-1==t)throw new Error(`Unsupported format "${e}" expecting ":namespace:name"`);return[e.slice(1,t),e.slice(t+1)]}
/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.RAW_TEXT=0]="RAW_TEXT",e[e.ESCAPABLE_RAW_TEXT=1]="ESCAPABLE_RAW_TEXT",e[e.PARSABLE_DATA=2]="PARSABLE_DATA"}(t.TagContentType||(t.TagContentType={})),t.splitNsName=r,t.isNgContainer=function(e){return"ng-container"===r(e)[1]},t.isNgContent=function(e){return"ng-content"===r(e)[1]},t.isNgTemplate=function(e){return"ng-template"===r(e)[1]},t.getNsPrefix=function(e){return null===e?null:r(e)[0]},t.mergeNsAndName=function(e,t){return e?`:${e}:${t}`:t},t.NAMED_ENTITIES={Aacute:"\xc1",aacute:"\xe1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223e",acd:"\u223f",acE:"\u223e\u0333",Acirc:"\xc2",acirc:"\xe2",acute:"\xb4",Acy:"\u0410",acy:"\u0430",AElig:"\xc6",aelig:"\xe6",af:"\u2061",Afr:"\ud835\udd04",afr:"\ud835\udd1e",Agrave:"\xc0",agrave:"\xe0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03b1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2a3f",AMP:"&",amp:"&",And:"\u2a53",and:"\u2227",andand:"\u2a55",andd:"\u2a5c",andslope:"\u2a58",andv:"\u2a5a",ang:"\u2220",ange:"\u29a4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29a8",angmsdab:"\u29a9",angmsdac:"\u29aa",angmsdad:"\u29ab",angmsdae:"\u29ac",angmsdaf:"\u29ad",angmsdag:"\u29ae",angmsdah:"\u29af",angrt:"\u221f",angrtvb:"\u22be",angrtvbd:"\u299d",angsph:"\u2222",angst:"\xc5",angzarr:"\u237c",Aogon:"\u0104",aogon:"\u0105",Aopf:"\ud835\udd38",aopf:"\ud835\udd52",ap:"\u2248",apacir:"\u2a6f",apE:"\u2a70",ape:"\u224a",apid:"\u224b",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224a",Aring:"\xc5",aring:"\xe5",Ascr:"\ud835\udc9c",ascr:"\ud835\udcb6",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224d",Atilde:"\xc3",atilde:"\xe3",Auml:"\xc4",auml:"\xe4",awconint:"\u2233",awint:"\u2a11",backcong:"\u224c",backepsilon:"\u03f6",backprime:"\u2035",backsim:"\u223d",backsimeq:"\u22cd",Backslash:"\u2216",Barv:"\u2ae7",barvee:"\u22bd",Barwed:"\u2306",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23b5",bbrktbrk:"\u23b6",bcong:"\u224c",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201e",becaus:"\u2235",Because:"\u2235",because:"\u2235",bemptyv:"\u29b0",bepsi:"\u03f6",bernou:"\u212c",Bernoullis:"\u212c",Beta:"\u0392",beta:"\u03b2",beth:"\u2136",between:"\u226c",Bfr:"\ud835\udd05",bfr:"\ud835\udd1f",bigcap:"\u22c2",bigcirc:"\u25ef",bigcup:"\u22c3",bigodot:"\u2a00",bigoplus:"\u2a01",bigotimes:"\u2a02",bigsqcup:"\u2a06",bigstar:"\u2605",bigtriangledown:"\u25bd",bigtriangleup:"\u25b3",biguplus:"\u2a04",bigvee:"\u22c1",bigwedge:"\u22c0",bkarow:"\u290d",blacklozenge:"\u29eb",blacksquare:"\u25aa",blacktriangle:"\u25b4",blacktriangledown:"\u25be",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20e5",bnequiv:"\u2261\u20e5",bNot:"\u2aed",bnot:"\u2310",Bopf:"\ud835\udd39",bopf:"\ud835\udd53",bot:"\u22a5",bottom:"\u22a5",bowtie:"\u22c8",boxbox:"\u29c9",boxDL:"\u2557",boxDl:"\u2556",boxdL:"\u2555",boxdl:"\u2510",boxDR:"\u2554",boxDr:"\u2553",boxdR:"\u2552",boxdr:"\u250c",boxH:"\u2550",boxh:"\u2500",boxHD:"\u2566",boxHd:"\u2564",boxhD:"\u2565",boxhd:"\u252c",boxHU:"\u2569",boxHu:"\u2567",boxhU:"\u2568",boxhu:"\u2534",boxminus:"\u229f",boxplus:"\u229e",boxtimes:"\u22a0",boxUL:"\u255d",boxUl:"\u255c",boxuL:"\u255b",boxul:"\u2518",boxUR:"\u255a",boxUr:"\u2559",boxuR:"\u2558",boxur:"\u2514",boxV:"\u2551",boxv:"\u2502",boxVH:"\u256c",boxVh:"\u256b",boxvH:"\u256a",boxvh:"\u253c",boxVL:"\u2563",boxVl:"\u2562",boxvL:"\u2561",boxvl:"\u2524",boxVR:"\u2560",boxVr:"\u255f",boxvR:"\u255e",boxvr:"\u251c",bprime:"\u2035",Breve:"\u02d8",breve:"\u02d8",brvbar:"\xa6",Bscr:"\u212c",bscr:"\ud835\udcb7",bsemi:"\u204f",bsim:"\u223d",bsime:"\u22cd",bsol:"\\",bsolb:"\u29c5",bsolhsub:"\u27c8",bull:"\u2022",bullet:"\u2022",bump:"\u224e",bumpE:"\u2aae",bumpe:"\u224f",Bumpeq:"\u224e",bumpeq:"\u224f",Cacute:"\u0106",cacute:"\u0107",Cap:"\u22d2",cap:"\u2229",capand:"\u2a44",capbrcup:"\u2a49",capcap:"\u2a4b",capcup:"\u2a47",capdot:"\u2a40",CapitalDifferentialD:"\u2145",caps:"\u2229\ufe00",caret:"\u2041",caron:"\u02c7",Cayleys:"\u212d",ccaps:"\u2a4d",Ccaron:"\u010c",ccaron:"\u010d",Ccedil:"\xc7",ccedil:"\xe7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2a4c",ccupssm:"\u2a50",Cdot:"\u010a",cdot:"\u010b",cedil:"\xb8",Cedilla:"\xb8",cemptyv:"\u29b2",cent:"\xa2",CenterDot:"\xb7",centerdot:"\xb7",Cfr:"\u212d",cfr:"\ud835\udd20",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03a7",chi:"\u03c7",cir:"\u25cb",circ:"\u02c6",circeq:"\u2257",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",circledast:"\u229b",circledcirc:"\u229a",circleddash:"\u229d",CircleDot:"\u2299",circledR:"\xae",circledS:"\u24c8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cirE:"\u29c3",cire:"\u2257",cirfnint:"\u2a10",cirmid:"\u2aef",cirscir:"\u29c2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201d",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",Colon:"\u2237",colon:":",Colone:"\u2a74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2a6d",Congruent:"\u2261",Conint:"\u222f",conint:"\u222e",ContourIntegral:"\u222e",Copf:"\u2102",copf:"\ud835\udd54",coprod:"\u2210",Coproduct:"\u2210",COPY:"\xa9",copy:"\xa9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21b5",Cross:"\u2a2f",cross:"\u2717",Cscr:"\ud835\udc9e",cscr:"\ud835\udcb8",csub:"\u2acf",csube:"\u2ad1",csup:"\u2ad0",csupe:"\u2ad2",ctdot:"\u22ef",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22de",cuesc:"\u22df",cularr:"\u21b6",cularrp:"\u293d",Cup:"\u22d3",cup:"\u222a",cupbrcap:"\u2a48",CupCap:"\u224d",cupcap:"\u2a46",cupcup:"\u2a4a",cupdot:"\u228d",cupor:"\u2a45",cups:"\u222a\ufe00",curarr:"\u21b7",curarrm:"\u293c",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",curlyvee:"\u22ce",curlywedge:"\u22cf",curren:"\xa4",curvearrowleft:"\u21b6",curvearrowright:"\u21b7",cuvee:"\u22ce",cuwed:"\u22cf",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232d",Dagger:"\u2021",dagger:"\u2020",daleth:"\u2138",Darr:"\u21a1",dArr:"\u21d3",darr:"\u2193",dash:"\u2010",Dashv:"\u2ae4",dashv:"\u22a3",dbkarow:"\u290f",dblac:"\u02dd",Dcaron:"\u010e",dcaron:"\u010f",Dcy:"\u0414",dcy:"\u0434",DD:"\u2145",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21ca",DDotrahd:"\u2911",ddotseq:"\u2a77",deg:"\xb0",Del:"\u2207",Delta:"\u0394",delta:"\u03b4",demptyv:"\u29b1",dfisht:"\u297f",Dfr:"\ud835\udd07",dfr:"\ud835\udd21",dHar:"\u2965",dharl:"\u21c3",dharr:"\u21c2",DiacriticalAcute:"\xb4",DiacriticalDot:"\u02d9",DiacriticalDoubleAcute:"\u02dd",DiacriticalGrave:"`",DiacriticalTilde:"\u02dc",diam:"\u22c4",Diamond:"\u22c4",diamond:"\u22c4",diamondsuit:"\u2666",diams:"\u2666",die:"\xa8",DifferentialD:"\u2146",digamma:"\u03dd",disin:"\u22f2",div:"\xf7",divide:"\xf7",divideontimes:"\u22c7",divonx:"\u22c7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231e",dlcrop:"\u230d",dollar:"$",Dopf:"\ud835\udd3b",dopf:"\ud835\udd55",Dot:"\xa8",dot:"\u02d9",DotDot:"\u20dc",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22a1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222f",DoubleDot:"\xa8",DoubleDownArrow:"\u21d3",DoubleLeftArrow:"\u21d0",DoubleLeftRightArrow:"\u21d4",DoubleLeftTee:"\u2ae4",DoubleLongLeftArrow:"\u27f8",DoubleLongLeftRightArrow:"\u27fa",DoubleLongRightArrow:"\u27f9",DoubleRightArrow:"\u21d2",DoubleRightTee:"\u22a8",DoubleUpArrow:"\u21d1",DoubleUpDownArrow:"\u21d5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",Downarrow:"\u21d3",downarrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21f5",DownBreve:"\u0311",downdownarrows:"\u21ca",downharpoonleft:"\u21c3",downharpoonright:"\u21c2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295e",DownLeftVector:"\u21bd",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295f",DownRightVector:"\u21c1",DownRightVectorBar:"\u2957",DownTee:"\u22a4",DownTeeArrow:"\u21a7",drbkarow:"\u2910",drcorn:"\u231f",drcrop:"\u230c",Dscr:"\ud835\udc9f",dscr:"\ud835\udcb9",DScy:"\u0405",dscy:"\u0455",dsol:"\u29f6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22f1",dtri:"\u25bf",dtrif:"\u25be",duarr:"\u21f5",duhar:"\u296f",dwangle:"\u29a6",DZcy:"\u040f",dzcy:"\u045f",dzigrarr:"\u27ff",Eacute:"\xc9",eacute:"\xe9",easter:"\u2a6e",Ecaron:"\u011a",ecaron:"\u011b",ecir:"\u2256",Ecirc:"\xca",ecirc:"\xea",ecolon:"\u2255",Ecy:"\u042d",ecy:"\u044d",eDDot:"\u2a77",Edot:"\u0116",eDot:"\u2251",edot:"\u0117",ee:"\u2147",efDot:"\u2252",Efr:"\ud835\udd08",efr:"\ud835\udd22",eg:"\u2a9a",Egrave:"\xc8",egrave:"\xe8",egs:"\u2a96",egsdot:"\u2a98",el:"\u2a99",Element:"\u2208",elinters:"\u23e7",ell:"\u2113",els:"\u2a95",elsdot:"\u2a97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25fb",emptyv:"\u2205",EmptyVerySmallSquare:"\u25ab",emsp:"\u2003",emsp13:"\u2004",emsp14:"\u2005",ENG:"\u014a",eng:"\u014b",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\ud835\udd3c",eopf:"\ud835\udd56",epar:"\u22d5",eparsl:"\u29e3",eplus:"\u2a71",epsi:"\u03b5",Epsilon:"\u0395",epsilon:"\u03b5",epsiv:"\u03f5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2a96",eqslantless:"\u2a95",Equal:"\u2a75",equals:"=",EqualTilde:"\u2242",equest:"\u225f",Equilibrium:"\u21cc",equiv:"\u2261",equivDD:"\u2a78",eqvparsl:"\u29e5",erarr:"\u2971",erDot:"\u2253",Escr:"\u2130",escr:"\u212f",esdot:"\u2250",Esim:"\u2a73",esim:"\u2242",Eta:"\u0397",eta:"\u03b7",ETH:"\xd0",eth:"\xf0",Euml:"\xcb",euml:"\xeb",euro:"\u20ac",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",ExponentialE:"\u2147",exponentiale:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\ufb03",fflig:"\ufb00",ffllig:"\ufb04",Ffr:"\ud835\udd09",ffr:"\ud835\udd23",filig:"\ufb01",FilledSmallSquare:"\u25fc",FilledVerySmallSquare:"\u25aa",fjlig:"fj",flat:"\u266d",fllig:"\ufb02",fltns:"\u25b1",fnof:"\u0192",Fopf:"\ud835\udd3d",fopf:"\ud835\udd57",ForAll:"\u2200",forall:"\u2200",fork:"\u22d4",forkv:"\u2ad9",Fouriertrf:"\u2131",fpartint:"\u2a0d",frac12:"\xbd",frac13:"\u2153",frac14:"\xbc",frac15:"\u2155",frac16:"\u2159",frac18:"\u215b",frac23:"\u2154",frac25:"\u2156",frac34:"\xbe",frac35:"\u2157",frac38:"\u215c",frac45:"\u2158",frac56:"\u215a",frac58:"\u215d",frac78:"\u215e",frasl:"\u2044",frown:"\u2322",Fscr:"\u2131",fscr:"\ud835\udcbb",gacute:"\u01f5",Gamma:"\u0393",gamma:"\u03b3",Gammad:"\u03dc",gammad:"\u03dd",gap:"\u2a86",Gbreve:"\u011e",gbreve:"\u011f",Gcedil:"\u0122",Gcirc:"\u011c",gcirc:"\u011d",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",gE:"\u2267",ge:"\u2265",gEl:"\u2a8c",gel:"\u22db",geq:"\u2265",geqq:"\u2267",geqslant:"\u2a7e",ges:"\u2a7e",gescc:"\u2aa9",gesdot:"\u2a80",gesdoto:"\u2a82",gesdotol:"\u2a84",gesl:"\u22db\ufe00",gesles:"\u2a94",Gfr:"\ud835\udd0a",gfr:"\ud835\udd24",Gg:"\u22d9",gg:"\u226b",ggg:"\u22d9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gl:"\u2277",gla:"\u2aa5",glE:"\u2a92",glj:"\u2aa4",gnap:"\u2a8a",gnapprox:"\u2a8a",gnE:"\u2269",gne:"\u2a88",gneq:"\u2a88",gneqq:"\u2269",gnsim:"\u22e7",Gopf:"\ud835\udd3e",gopf:"\ud835\udd58",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22db",GreaterFullEqual:"\u2267",GreaterGreater:"\u2aa2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2a7e",GreaterTilde:"\u2273",Gscr:"\ud835\udca2",gscr:"\u210a",gsim:"\u2273",gsime:"\u2a8e",gsiml:"\u2a90",GT:">",Gt:"\u226b",gt:">",gtcc:"\u2aa7",gtcir:"\u2a7a",gtdot:"\u22d7",gtlPar:"\u2995",gtquest:"\u2a7c",gtrapprox:"\u2a86",gtrarr:"\u2978",gtrdot:"\u22d7",gtreqless:"\u22db",gtreqqless:"\u2a8c",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\ufe00",gvnE:"\u2269\ufe00",Hacek:"\u02c7",hairsp:"\u200a",half:"\xbd",hamilt:"\u210b",HARDcy:"\u042a",hardcy:"\u044a",hArr:"\u21d4",harr:"\u2194",harrcir:"\u2948",harrw:"\u21ad",Hat:"^",hbar:"\u210f",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22b9",Hfr:"\u210c",hfr:"\ud835\udd25",HilbertSpace:"\u210b",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21ff",homtht:"\u223b",hookleftarrow:"\u21a9",hookrightarrow:"\u21aa",Hopf:"\u210d",hopf:"\ud835\udd59",horbar:"\u2015",HorizontalLine:"\u2500",Hscr:"\u210b",hscr:"\ud835\udcbd",hslash:"\u210f",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224e",HumpEqual:"\u224f",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xcd",iacute:"\xed",ic:"\u2063",Icirc:"\xce",icirc:"\xee",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xa1",iff:"\u21d4",Ifr:"\u2111",ifr:"\ud835\udd26",Igrave:"\xcc",igrave:"\xec",ii:"\u2148",iiiint:"\u2a0c",iiint:"\u222d",iinfin:"\u29dc",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Im:"\u2111",Imacr:"\u012a",imacr:"\u012b",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22b7",imped:"\u01b5",Implies:"\u21d2",in:"\u2208",incare:"\u2105",infin:"\u221e",infintie:"\u29dd",inodot:"\u0131",Int:"\u222c",int:"\u222b",intcal:"\u22ba",integers:"\u2124",Integral:"\u222b",intercal:"\u22ba",Intersection:"\u22c2",intlarhk:"\u2a17",intprod:"\u2a3c",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012e",iogon:"\u012f",Iopf:"\ud835\udd40",iopf:"\ud835\udd5a",Iota:"\u0399",iota:"\u03b9",iprod:"\u2a3c",iquest:"\xbf",Iscr:"\u2110",iscr:"\ud835\udcbe",isin:"\u2208",isindot:"\u22f5",isinE:"\u22f9",isins:"\u22f4",isinsv:"\u22f3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xcf",iuml:"\xef",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\ud835\udd0d",jfr:"\ud835\udd27",jmath:"\u0237",Jopf:"\ud835\udd41",jopf:"\ud835\udd5b",Jscr:"\ud835\udca5",jscr:"\ud835\udcbf",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039a",kappa:"\u03ba",kappav:"\u03f0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041a",kcy:"\u043a",Kfr:"\ud835\udd0e",kfr:"\ud835\udd28",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040c",kjcy:"\u045c",Kopf:"\ud835\udd42",kopf:"\ud835\udd5c",Kscr:"\ud835\udca6",kscr:"\ud835\udcc0",lAarr:"\u21da",Lacute:"\u0139",lacute:"\u013a",laemptyv:"\u29b4",lagran:"\u2112",Lambda:"\u039b",lambda:"\u03bb",Lang:"\u27ea",lang:"\u27e8",langd:"\u2991",langle:"\u27e8",lap:"\u2a85",Laplacetrf:"\u2112",laquo:"\xab",Larr:"\u219e",lArr:"\u21d0",larr:"\u2190",larrb:"\u21e4",larrbfs:"\u291f",larrfs:"\u291d",larrhk:"\u21a9",larrlp:"\u21ab",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21a2",lat:"\u2aab",lAtail:"\u291b",latail:"\u2919",late:"\u2aad",lates:"\u2aad\ufe00",lBarr:"\u290e",lbarr:"\u290c",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298b",lbrksld:"\u298f",lbrkslu:"\u298d",Lcaron:"\u013d",lcaron:"\u013e",Lcedil:"\u013b",lcedil:"\u013c",lceil:"\u2308",lcub:"{",Lcy:"\u041b",lcy:"\u043b",ldca:"\u2936",ldquo:"\u201c",ldquor:"\u201e",ldrdhar:"\u2967",ldrushar:"\u294b",ldsh:"\u21b2",lE:"\u2266",le:"\u2264",LeftAngleBracket:"\u27e8",LeftArrow:"\u2190",Leftarrow:"\u21d0",leftarrow:"\u2190",LeftArrowBar:"\u21e4",LeftArrowRightArrow:"\u21c6",leftarrowtail:"\u21a2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27e6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21c3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230a",leftharpoondown:"\u21bd",leftharpoonup:"\u21bc",leftleftarrows:"\u21c7",LeftRightArrow:"\u2194",Leftrightarrow:"\u21d4",leftrightarrow:"\u2194",leftrightarrows:"\u21c6",leftrightharpoons:"\u21cb",leftrightsquigarrow:"\u21ad",LeftRightVector:"\u294e",LeftTee:"\u22a3",LeftTeeArrow:"\u21a4",LeftTeeVector:"\u295a",leftthreetimes:"\u22cb",LeftTriangle:"\u22b2",LeftTriangleBar:"\u29cf",LeftTriangleEqual:"\u22b4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21bf",LeftUpVectorBar:"\u2958",LeftVector:"\u21bc",LeftVectorBar:"\u2952",lEg:"\u2a8b",leg:"\u22da",leq:"\u2264",leqq:"\u2266",leqslant:"\u2a7d",les:"\u2a7d",lescc:"\u2aa8",lesdot:"\u2a7f",lesdoto:"\u2a81",lesdotor:"\u2a83",lesg:"\u22da\ufe00",lesges:"\u2a93",lessapprox:"\u2a85",lessdot:"\u22d6",lesseqgtr:"\u22da",lesseqqgtr:"\u2a8b",LessEqualGreater:"\u22da",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2aa1",lesssim:"\u2272",LessSlantEqual:"\u2a7d",LessTilde:"\u2272",lfisht:"\u297c",lfloor:"\u230a",Lfr:"\ud835\udd0f",lfr:"\ud835\udd29",lg:"\u2276",lgE:"\u2a91",lHar:"\u2962",lhard:"\u21bd",lharu:"\u21bc",lharul:"\u296a",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",Ll:"\u22d8",ll:"\u226a",llarr:"\u21c7",llcorner:"\u231e",Lleftarrow:"\u21da",llhard:"\u296b",lltri:"\u25fa",Lmidot:"\u013f",lmidot:"\u0140",lmoust:"\u23b0",lmoustache:"\u23b0",lnap:"\u2a89",lnapprox:"\u2a89",lnE:"\u2268",lne:"\u2a87",lneq:"\u2a87",lneqq:"\u2268",lnsim:"\u22e6",loang:"\u27ec",loarr:"\u21fd",lobrk:"\u27e6",LongLeftArrow:"\u27f5",Longleftarrow:"\u27f8",longleftarrow:"\u27f5",LongLeftRightArrow:"\u27f7",Longleftrightarrow:"\u27fa",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",LongRightArrow:"\u27f6",Longrightarrow:"\u27f9",longrightarrow:"\u27f6",looparrowleft:"\u21ab",looparrowright:"\u21ac",lopar:"\u2985",Lopf:"\ud835\udd43",lopf:"\ud835\udd5d",loplus:"\u2a2d",lotimes:"\u2a34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25ca",lozenge:"\u25ca",lozf:"\u29eb",lpar:"(",lparlt:"\u2993",lrarr:"\u21c6",lrcorner:"\u231f",lrhar:"\u21cb",lrhard:"\u296d",lrm:"\u200e",lrtri:"\u22bf",lsaquo:"\u2039",Lscr:"\u2112",lscr:"\ud835\udcc1",Lsh:"\u21b0",lsh:"\u21b0",lsim:"\u2272",lsime:"\u2a8d",lsimg:"\u2a8f",lsqb:"[",lsquo:"\u2018",lsquor:"\u201a",Lstrok:"\u0141",lstrok:"\u0142",LT:"<",Lt:"\u226a",lt:"<",ltcc:"\u2aa6",ltcir:"\u2a79",ltdot:"\u22d6",lthree:"\u22cb",ltimes:"\u22c9",ltlarr:"\u2976",ltquest:"\u2a7b",ltri:"\u25c3",ltrie:"\u22b4",ltrif:"\u25c2",ltrPar:"\u2996",lurdshar:"\u294a",luruhar:"\u2966",lvertneqq:"\u2268\ufe00",lvnE:"\u2268\ufe00",macr:"\xaf",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21a6",mapsto:"\u21a6",mapstodown:"\u21a7",mapstoleft:"\u21a4",mapstoup:"\u21a5",marker:"\u25ae",mcomma:"\u2a29",Mcy:"\u041c",mcy:"\u043c",mdash:"\u2014",mDDot:"\u223a",measuredangle:"\u2221",MediumSpace:"\u205f",Mellintrf:"\u2133",Mfr:"\ud835\udd10",mfr:"\ud835\udd2a",mho:"\u2127",micro:"\xb5",mid:"\u2223",midast:"*",midcir:"\u2af0",middot:"\xb7",minus:"\u2212",minusb:"\u229f",minusd:"\u2238",minusdu:"\u2a2a",MinusPlus:"\u2213",mlcp:"\u2adb",mldr:"\u2026",mnplus:"\u2213",models:"\u22a7",Mopf:"\ud835\udd44",mopf:"\ud835\udd5e",mp:"\u2213",Mscr:"\u2133",mscr:"\ud835\udcc2",mstpos:"\u223e",Mu:"\u039c",mu:"\u03bc",multimap:"\u22b8",mumap:"\u22b8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20d2",nap:"\u2249",napE:"\u2a70\u0338",napid:"\u224b\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266e",natural:"\u266e",naturals:"\u2115",nbsp:"\xa0",nbump:"\u224e\u0338",nbumpe:"\u224f\u0338",ncap:"\u2a43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2a6d\u0338",ncup:"\u2a42",Ncy:"\u041d",ncy:"\u043d",ndash:"\u2013",ne:"\u2260",nearhk:"\u2924",neArr:"\u21d7",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200b",NegativeThickSpace:"\u200b",NegativeThinSpace:"\u200b",NegativeVeryThinSpace:"\u200b",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226b",NestedLessLess:"\u226a",NewLine:"\n",nexist:"\u2204",nexists:"\u2204",Nfr:"\ud835\udd11",nfr:"\ud835\udd2b",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2a7e\u0338",nges:"\u2a7e\u0338",nGg:"\u22d9\u0338",ngsim:"\u2275",nGt:"\u226b\u20d2",ngt:"\u226f",ngtr:"\u226f",nGtv:"\u226b\u0338",nhArr:"\u21ce",nharr:"\u21ae",nhpar:"\u2af2",ni:"\u220b",nis:"\u22fc",nisd:"\u22fa",niv:"\u220b",NJcy:"\u040a",njcy:"\u045a",nlArr:"\u21cd",nlarr:"\u219a",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nLeftarrow:"\u21cd",nleftarrow:"\u219a",nLeftrightarrow:"\u21ce",nleftrightarrow:"\u21ae",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2a7d\u0338",nles:"\u2a7d\u0338",nless:"\u226e",nLl:"\u22d8\u0338",nlsim:"\u2274",nLt:"\u226a\u20d2",nlt:"\u226e",nltri:"\u22ea",nltrie:"\u22ec",nLtv:"\u226a\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xa0",Nopf:"\u2115",nopf:"\ud835\udd5f",Not:"\u2aec",not:"\xac",NotCongruent:"\u2262",NotCupCap:"\u226d",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226f",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226b\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2a7e\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224e\u0338",NotHumpEqual:"\u224f\u0338",notin:"\u2209",notindot:"\u22f5\u0338",notinE:"\u22f9\u0338",notinva:"\u2209",notinvb:"\u22f7",notinvc:"\u22f6",NotLeftTriangle:"\u22ea",NotLeftTriangleBar:"\u29cf\u0338",NotLeftTriangleEqual:"\u22ec",NotLess:"\u226e",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226a\u0338",NotLessSlantEqual:"\u2a7d\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2aa2\u0338",NotNestedLessLess:"\u2aa1\u0338",notni:"\u220c",notniva:"\u220c",notnivb:"\u22fe",notnivc:"\u22fd",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2aaf\u0338",NotPrecedesSlantEqual:"\u22e0",NotReverseElement:"\u220c",NotRightTriangle:"\u22eb",NotRightTriangleBar:"\u29d0\u0338",NotRightTriangleEqual:"\u22ed",NotSquareSubset:"\u228f\u0338",NotSquareSubsetEqual:"\u22e2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22e3",NotSubset:"\u2282\u20d2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2ab0\u0338",NotSucceedsSlantEqual:"\u22e1",NotSucceedsTilde:"\u227f\u0338",NotSuperset:"\u2283\u20d2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2afd\u20e5",npart:"\u2202\u0338",npolint:"\u2a14",npr:"\u2280",nprcue:"\u22e0",npre:"\u2aaf\u0338",nprec:"\u2280",npreceq:"\u2aaf\u0338",nrArr:"\u21cf",nrarr:"\u219b",nrarrc:"\u2933\u0338",nrarrw:"\u219d\u0338",nRightarrow:"\u21cf",nrightarrow:"\u219b",nrtri:"\u22eb",nrtrie:"\u22ed",nsc:"\u2281",nsccue:"\u22e1",nsce:"\u2ab0\u0338",Nscr:"\ud835\udca9",nscr:"\ud835\udcc3",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22e2",nsqsupe:"\u22e3",nsub:"\u2284",nsubE:"\u2ac5\u0338",nsube:"\u2288",nsubset:"\u2282\u20d2",nsubseteq:"\u2288",nsubseteqq:"\u2ac5\u0338",nsucc:"\u2281",nsucceq:"\u2ab0\u0338",nsup:"\u2285",nsupE:"\u2ac6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20d2",nsupseteq:"\u2289",nsupseteqq:"\u2ac6\u0338",ntgl:"\u2279",Ntilde:"\xd1",ntilde:"\xf1",ntlg:"\u2278",ntriangleleft:"\u22ea",ntrianglelefteq:"\u22ec",ntriangleright:"\u22eb",ntrianglerighteq:"\u22ed",Nu:"\u039d",nu:"\u03bd",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224d\u20d2",nVDash:"\u22af",nVdash:"\u22ae",nvDash:"\u22ad",nvdash:"\u22ac",nvge:"\u2265\u20d2",nvgt:">\u20d2",nvHarr:"\u2904",nvinfin:"\u29de",nvlArr:"\u2902",nvle:"\u2264\u20d2",nvlt:"<\u20d2",nvltrie:"\u22b4\u20d2",nvrArr:"\u2903",nvrtrie:"\u22b5\u20d2",nvsim:"\u223c\u20d2",nwarhk:"\u2923",nwArr:"\u21d6",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xd3",oacute:"\xf3",oast:"\u229b",ocir:"\u229a",Ocirc:"\xd4",ocirc:"\xf4",Ocy:"\u041e",ocy:"\u043e",odash:"\u229d",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2a38",odot:"\u2299",odsold:"\u29bc",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29bf",Ofr:"\ud835\udd12",ofr:"\ud835\udd2c",ogon:"\u02db",Ograve:"\xd2",ograve:"\xf2",ogt:"\u29c1",ohbar:"\u29b5",ohm:"\u03a9",oint:"\u222e",olarr:"\u21ba",olcir:"\u29be",olcross:"\u29bb",oline:"\u203e",olt:"\u29c0",Omacr:"\u014c",omacr:"\u014d",Omega:"\u03a9",omega:"\u03c9",Omicron:"\u039f",omicron:"\u03bf",omid:"\u29b6",ominus:"\u2296",Oopf:"\ud835\udd46",oopf:"\ud835\udd60",opar:"\u29b7",OpenCurlyDoubleQuote:"\u201c",OpenCurlyQuote:"\u2018",operp:"\u29b9",oplus:"\u2295",Or:"\u2a54",or:"\u2228",orarr:"\u21bb",ord:"\u2a5d",order:"\u2134",orderof:"\u2134",ordf:"\xaa",ordm:"\xba",origof:"\u22b6",oror:"\u2a56",orslope:"\u2a57",orv:"\u2a5b",oS:"\u24c8",Oscr:"\ud835\udcaa",oscr:"\u2134",Oslash:"\xd8",oslash:"\xf8",osol:"\u2298",Otilde:"\xd5",otilde:"\xf5",Otimes:"\u2a37",otimes:"\u2297",otimesas:"\u2a36",Ouml:"\xd6",ouml:"\xf6",ovbar:"\u233d",OverBar:"\u203e",OverBrace:"\u23de",OverBracket:"\u23b4",OverParenthesis:"\u23dc",par:"\u2225",para:"\xb6",parallel:"\u2225",parsim:"\u2af3",parsl:"\u2afd",part:"\u2202",PartialD:"\u2202",Pcy:"\u041f",pcy:"\u043f",percnt:"%",period:".",permil:"\u2030",perp:"\u22a5",pertenk:"\u2031",Pfr:"\ud835\udd13",pfr:"\ud835\udd2d",Phi:"\u03a6",phi:"\u03c6",phiv:"\u03d5",phmmat:"\u2133",phone:"\u260e",Pi:"\u03a0",pi:"\u03c0",pitchfork:"\u22d4",piv:"\u03d6",planck:"\u210f",planckh:"\u210e",plankv:"\u210f",plus:"+",plusacir:"\u2a23",plusb:"\u229e",pluscir:"\u2a22",plusdo:"\u2214",plusdu:"\u2a25",pluse:"\u2a72",PlusMinus:"\xb1",plusmn:"\xb1",plussim:"\u2a26",plustwo:"\u2a27",pm:"\xb1",Poincareplane:"\u210c",pointint:"\u2a15",Popf:"\u2119",popf:"\ud835\udd61",pound:"\xa3",Pr:"\u2abb",pr:"\u227a",prap:"\u2ab7",prcue:"\u227c",prE:"\u2ab3",pre:"\u2aaf",prec:"\u227a",precapprox:"\u2ab7",preccurlyeq:"\u227c",Precedes:"\u227a",PrecedesEqual:"\u2aaf",PrecedesSlantEqual:"\u227c",PrecedesTilde:"\u227e",preceq:"\u2aaf",precnapprox:"\u2ab9",precneqq:"\u2ab5",precnsim:"\u22e8",precsim:"\u227e",Prime:"\u2033",prime:"\u2032",primes:"\u2119",prnap:"\u2ab9",prnE:"\u2ab5",prnsim:"\u22e8",prod:"\u220f",Product:"\u220f",profalar:"\u232e",profline:"\u2312",profsurf:"\u2313",prop:"\u221d",Proportion:"\u2237",Proportional:"\u221d",propto:"\u221d",prsim:"\u227e",prurel:"\u22b0",Pscr:"\ud835\udcab",pscr:"\ud835\udcc5",Psi:"\u03a8",psi:"\u03c8",puncsp:"\u2008",Qfr:"\ud835\udd14",qfr:"\ud835\udd2e",qint:"\u2a0c",Qopf:"\u211a",qopf:"\ud835\udd62",qprime:"\u2057",Qscr:"\ud835\udcac",qscr:"\ud835\udcc6",quaternions:"\u210d",quatint:"\u2a16",quest:"?",questeq:"\u225f",QUOT:'"',quot:'"',rAarr:"\u21db",race:"\u223d\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221a",raemptyv:"\u29b3",Rang:"\u27eb",rang:"\u27e9",rangd:"\u2992",range:"\u29a5",rangle:"\u27e9",raquo:"\xbb",Rarr:"\u21a0",rArr:"\u21d2",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21e5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291e",rarrhk:"\u21aa",rarrlp:"\u21ac",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21a3",rarrw:"\u219d",rAtail:"\u291c",ratail:"\u291a",ratio:"\u2236",rationals:"\u211a",RBarr:"\u2910",rBarr:"\u290f",rbarr:"\u290d",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298c",rbrksld:"\u298e",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201d",rdquor:"\u201d",rdsh:"\u21b3",Re:"\u211c",real:"\u211c",realine:"\u211b",realpart:"\u211c",reals:"\u211d",rect:"\u25ad",REG:"\xae",reg:"\xae",ReverseElement:"\u220b",ReverseEquilibrium:"\u21cb",ReverseUpEquilibrium:"\u296f",rfisht:"\u297d",rfloor:"\u230b",Rfr:"\u211c",rfr:"\ud835\udd2f",rHar:"\u2964",rhard:"\u21c1",rharu:"\u21c0",rharul:"\u296c",Rho:"\u03a1",rho:"\u03c1",rhov:"\u03f1",RightAngleBracket:"\u27e9",RightArrow:"\u2192",Rightarrow:"\u21d2",rightarrow:"\u2192",RightArrowBar:"\u21e5",RightArrowLeftArrow:"\u21c4",rightarrowtail:"\u21a3",RightCeiling:"\u2309",RightDoubleBracket:"\u27e7",RightDownTeeVector:"\u295d",RightDownVector:"\u21c2",RightDownVectorBar:"\u2955",RightFloor:"\u230b",rightharpoondown:"\u21c1",rightharpoonup:"\u21c0",rightleftarrows:"\u21c4",rightleftharpoons:"\u21cc",rightrightarrows:"\u21c9",rightsquigarrow:"\u219d",RightTee:"\u22a2",RightTeeArrow:"\u21a6",RightTeeVector:"\u295b",rightthreetimes:"\u22cc",RightTriangle:"\u22b3",RightTriangleBar:"\u29d0",RightTriangleEqual:"\u22b5",RightUpDownVector:"\u294f",RightUpTeeVector:"\u295c",RightUpVector:"\u21be",RightUpVectorBar:"\u2954",RightVector:"\u21c0",RightVectorBar:"\u2953",ring:"\u02da",risingdotseq:"\u2253",rlarr:"\u21c4",rlhar:"\u21cc",rlm:"\u200f",rmoust:"\u23b1",rmoustache:"\u23b1",rnmid:"\u2aee",roang:"\u27ed",roarr:"\u21fe",robrk:"\u27e7",ropar:"\u2986",Ropf:"\u211d",ropf:"\ud835\udd63",roplus:"\u2a2e",rotimes:"\u2a35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2a12",rrarr:"\u21c9",Rrightarrow:"\u21db",rsaquo:"\u203a",Rscr:"\u211b",rscr:"\ud835\udcc7",Rsh:"\u21b1",rsh:"\u21b1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22cc",rtimes:"\u22ca",rtri:"\u25b9",rtrie:"\u22b5",rtrif:"\u25b8",rtriltri:"\u29ce",RuleDelayed:"\u29f4",ruluhar:"\u2968",rx:"\u211e",Sacute:"\u015a",sacute:"\u015b",sbquo:"\u201a",Sc:"\u2abc",sc:"\u227b",scap:"\u2ab8",Scaron:"\u0160",scaron:"\u0161",sccue:"\u227d",scE:"\u2ab4",sce:"\u2ab0",Scedil:"\u015e",scedil:"\u015f",Scirc:"\u015c",scirc:"\u015d",scnap:"\u2aba",scnE:"\u2ab6",scnsim:"\u22e9",scpolint:"\u2a13",scsim:"\u227f",Scy:"\u0421",scy:"\u0441",sdot:"\u22c5",sdotb:"\u22a1",sdote:"\u2a66",searhk:"\u2925",seArr:"\u21d8",searr:"\u2198",searrow:"\u2198",sect:"\xa7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\ud835\udd16",sfr:"\ud835\udd30",sfrown:"\u2322",sharp:"\u266f",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xad",Sigma:"\u03a3",sigma:"\u03c3",sigmaf:"\u03c2",sigmav:"\u03c2",sim:"\u223c",simdot:"\u2a6a",sime:"\u2243",simeq:"\u2243",simg:"\u2a9e",simgE:"\u2aa0",siml:"\u2a9d",simlE:"\u2a9f",simne:"\u2246",simplus:"\u2a24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2a33",smeparsl:"\u29e4",smid:"\u2223",smile:"\u2323",smt:"\u2aaa",smte:"\u2aac",smtes:"\u2aac\ufe00",SOFTcy:"\u042c",softcy:"\u044c",sol:"/",solb:"\u29c4",solbar:"\u233f",Sopf:"\ud835\udd4a",sopf:"\ud835\udd64",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\ufe00",sqcup:"\u2294",sqcups:"\u2294\ufe00",Sqrt:"\u221a",sqsub:"\u228f",sqsube:"\u2291",sqsubset:"\u228f",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25a1",Square:"\u25a1",square:"\u25a1",SquareIntersection:"\u2293",SquareSubset:"\u228f",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25aa",squf:"\u25aa",srarr:"\u2192",Sscr:"\ud835\udcae",sscr:"\ud835\udcc8",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22c6",Star:"\u22c6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03f5",straightphi:"\u03d5",strns:"\xaf",Sub:"\u22d0",sub:"\u2282",subdot:"\u2abd",subE:"\u2ac5",sube:"\u2286",subedot:"\u2ac3",submult:"\u2ac1",subnE:"\u2acb",subne:"\u228a",subplus:"\u2abf",subrarr:"\u2979",Subset:"\u22d0",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2ac5",SubsetEqual:"\u2286",subsetneq:"\u228a",subsetneqq:"\u2acb",subsim:"\u2ac7",subsub:"\u2ad5",subsup:"\u2ad3",succ:"\u227b",succapprox:"\u2ab8",succcurlyeq:"\u227d",Succeeds:"\u227b",SucceedsEqual:"\u2ab0",SucceedsSlantEqual:"\u227d",SucceedsTilde:"\u227f",succeq:"\u2ab0",succnapprox:"\u2aba",succneqq:"\u2ab6",succnsim:"\u22e9",succsim:"\u227f",SuchThat:"\u220b",Sum:"\u2211",sum:"\u2211",sung:"\u266a",Sup:"\u22d1",sup:"\u2283",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",supdot:"\u2abe",supdsub:"\u2ad8",supE:"\u2ac6",supe:"\u2287",supedot:"\u2ac4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27c9",suphsub:"\u2ad7",suplarr:"\u297b",supmult:"\u2ac2",supnE:"\u2acc",supne:"\u228b",supplus:"\u2ac0",Supset:"\u22d1",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2ac6",supsetneq:"\u228b",supsetneqq:"\u2acc",supsim:"\u2ac8",supsub:"\u2ad4",supsup:"\u2ad6",swarhk:"\u2926",swArr:"\u21d9",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292a",szlig:"\xdf",Tab:"\t",target:"\u2316",Tau:"\u03a4",tau:"\u03c4",tbrk:"\u23b4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20db",telrec:"\u2315",Tfr:"\ud835\udd17",tfr:"\ud835\udd31",there4:"\u2234",Therefore:"\u2234",therefore:"\u2234",Theta:"\u0398",theta:"\u03b8",thetasym:"\u03d1",thetav:"\u03d1",thickapprox:"\u2248",thicksim:"\u223c",ThickSpace:"\u205f\u200a",thinsp:"\u2009",ThinSpace:"\u2009",thkap:"\u2248",thksim:"\u223c",THORN:"\xde",thorn:"\xfe",Tilde:"\u223c",tilde:"\u02dc",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",times:"\xd7",timesb:"\u22a0",timesbar:"\u2a31",timesd:"\u2a30",tint:"\u222d",toea:"\u2928",top:"\u22a4",topbot:"\u2336",topcir:"\u2af1",Topf:"\ud835\udd4b",topf:"\ud835\udd65",topfork:"\u2ada",tosa:"\u2929",tprime:"\u2034",TRADE:"\u2122",trade:"\u2122",triangle:"\u25b5",triangledown:"\u25bf",triangleleft:"\u25c3",trianglelefteq:"\u22b4",triangleq:"\u225c",triangleright:"\u25b9",trianglerighteq:"\u22b5",tridot:"\u25ec",trie:"\u225c",triminus:"\u2a3a",TripleDot:"\u20db",triplus:"\u2a39",trisb:"\u29cd",tritime:"\u2a3b",trpezium:"\u23e2",Tscr:"\ud835\udcaf",tscr:"\ud835\udcc9",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040b",tshcy:"\u045b",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226c",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",Uacute:"\xda",uacute:"\xfa",Uarr:"\u219f",uArr:"\u21d1",uarr:"\u2191",Uarrocir:"\u2949",Ubrcy:"\u040e",ubrcy:"\u045e",Ubreve:"\u016c",ubreve:"\u016d",Ucirc:"\xdb",ucirc:"\xfb",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21c5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296e",ufisht:"\u297e",Ufr:"\ud835\udd18",ufr:"\ud835\udd32",Ugrave:"\xd9",ugrave:"\xf9",uHar:"\u2963",uharl:"\u21bf",uharr:"\u21be",uhblk:"\u2580",ulcorn:"\u231c",ulcorner:"\u231c",ulcrop:"\u230f",ultri:"\u25f8",Umacr:"\u016a",umacr:"\u016b",uml:"\xa8",UnderBar:"_",UnderBrace:"\u23df",UnderBracket:"\u23b5",UnderParenthesis:"\u23dd",Union:"\u22c3",UnionPlus:"\u228e",Uogon:"\u0172",uogon:"\u0173",Uopf:"\ud835\udd4c",uopf:"\ud835\udd66",UpArrow:"\u2191",Uparrow:"\u21d1",uparrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21c5",UpDownArrow:"\u2195",Updownarrow:"\u21d5",updownarrow:"\u2195",UpEquilibrium:"\u296e",upharpoonleft:"\u21bf",upharpoonright:"\u21be",uplus:"\u228e",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03d2",upsi:"\u03c5",upsih:"\u03d2",Upsilon:"\u03a5",upsilon:"\u03c5",UpTee:"\u22a5",UpTeeArrow:"\u21a5",upuparrows:"\u21c8",urcorn:"\u231d",urcorner:"\u231d",urcrop:"\u230e",Uring:"\u016e",uring:"\u016f",urtri:"\u25f9",Uscr:"\ud835\udcb0",uscr:"\ud835\udcca",utdot:"\u22f0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25b5",utrif:"\u25b4",uuarr:"\u21c8",Uuml:"\xdc",uuml:"\xfc",uwangle:"\u29a7",vangrt:"\u299c",varepsilon:"\u03f5",varkappa:"\u03f0",varnothing:"\u2205",varphi:"\u03d5",varpi:"\u03d6",varpropto:"\u221d",vArr:"\u21d5",varr:"\u2195",varrho:"\u03f1",varsigma:"\u03c2",varsubsetneq:"\u228a\ufe00",varsubsetneqq:"\u2acb\ufe00",varsupsetneq:"\u228b\ufe00",varsupsetneqq:"\u2acc\ufe00",vartheta:"\u03d1",vartriangleleft:"\u22b2",vartriangleright:"\u22b3",Vbar:"\u2aeb",vBar:"\u2ae8",vBarv:"\u2ae9",Vcy:"\u0412",vcy:"\u0432",VDash:"\u22ab",Vdash:"\u22a9",vDash:"\u22a8",vdash:"\u22a2",Vdashl:"\u2ae6",Vee:"\u22c1",vee:"\u2228",veebar:"\u22bb",veeeq:"\u225a",vellip:"\u22ee",Verbar:"\u2016",verbar:"|",Vert:"\u2016",vert:"|",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200a",Vfr:"\ud835\udd19",vfr:"\ud835\udd33",vltri:"\u22b2",vnsub:"\u2282\u20d2",vnsup:"\u2283\u20d2",Vopf:"\ud835\udd4d",vopf:"\ud835\udd67",vprop:"\u221d",vrtri:"\u22b3",Vscr:"\ud835\udcb1",vscr:"\ud835\udccb",vsubnE:"\u2acb\ufe00",vsubne:"\u228a\ufe00",vsupnE:"\u2acc\ufe00",vsupne:"\u228b\ufe00",Vvdash:"\u22aa",vzigzag:"\u299a",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2a5f",Wedge:"\u22c0",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\ud835\udd1a",wfr:"\ud835\udd34",Wopf:"\ud835\udd4e",wopf:"\ud835\udd68",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\ud835\udcb2",wscr:"\ud835\udccc",xcap:"\u22c2",xcirc:"\u25ef",xcup:"\u22c3",xdtri:"\u25bd",Xfr:"\ud835\udd1b",xfr:"\ud835\udd35",xhArr:"\u27fa",xharr:"\u27f7",Xi:"\u039e",xi:"\u03be",xlArr:"\u27f8",xlarr:"\u27f5",xmap:"\u27fc",xnis:"\u22fb",xodot:"\u2a00",Xopf:"\ud835\udd4f",xopf:"\ud835\udd69",xoplus:"\u2a01",xotime:"\u2a02",xrArr:"\u27f9",xrarr:"\u27f6",Xscr:"\ud835\udcb3",xscr:"\ud835\udccd",xsqcup:"\u2a06",xuplus:"\u2a04",xutri:"\u25b3",xvee:"\u22c1",xwedge:"\u22c0",Yacute:"\xdd",yacute:"\xfd",YAcy:"\u042f",yacy:"\u044f",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042b",ycy:"\u044b",yen:"\xa5",Yfr:"\ud835\udd1c",yfr:"\ud835\udd36",YIcy:"\u0407",yicy:"\u0457",Yopf:"\ud835\udd50",yopf:"\ud835\udd6a",Yscr:"\ud835\udcb4",yscr:"\ud835\udcce",YUcy:"\u042e",yucy:"\u044e",Yuml:"\u0178",yuml:"\xff",Zacute:"\u0179",zacute:"\u017a",Zcaron:"\u017d",zcaron:"\u017e",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017b",zdot:"\u017c",zeetrf:"\u2128",ZeroWidthSpace:"\u200b",Zeta:"\u0396",zeta:"\u03b6",Zfr:"\u2128",zfr:"\ud835\udd37",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21dd",Zopf:"\u2124",zopf:"\ud835\udd6b",Zscr:"\ud835\udcb5",zscr:"\ud835\udccf",zwj:"\u200d",zwnj:"\u200c"},t.NGSP_UNICODE="\ue500",t.NAMED_ENTITIES.ngsp=t.NGSP_UNICODE}));
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
class bi{constructor({closedByChildren:e,implicitNamespacePrefix:t,contentType:r=vi.TagContentType.PARSABLE_DATA,closedByParent:n=!1,isVoid:i=!1,ignoreFirstLf:s=!1}={}){this.closedByChildren={},this.closedByParent=!1,this.canSelfClose=!1,e&&e.length>0&&e.forEach((e=>this.closedByChildren[e]=!0)),this.isVoid=i,this.closedByParent=n||i,this.implicitNamespacePrefix=t||null,this.contentType=r,this.ignoreFirstLf=s}isClosedByChild(e){return this.isVoid||e.toLowerCase()in this.closedByChildren}}var Fi=bi;let Ai,wi;var Ni=function(e){return wi||(Ai=new bi,wi={base:new bi({isVoid:!0}),meta:new bi({isVoid:!0}),area:new bi({isVoid:!0}),embed:new bi({isVoid:!0}),link:new bi({isVoid:!0}),img:new bi({isVoid:!0}),input:new bi({isVoid:!0}),param:new bi({isVoid:!0}),hr:new bi({isVoid:!0}),br:new bi({isVoid:!0}),source:new bi({isVoid:!0}),track:new bi({isVoid:!0}),wbr:new bi({isVoid:!0}),p:new bi({closedByChildren:["address","article","aside","blockquote","div","dl","fieldset","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","main","nav","ol","p","pre","section","table","ul"],closedByParent:!0}),thead:new bi({closedByChildren:["tbody","tfoot"]}),tbody:new bi({closedByChildren:["tbody","tfoot"],closedByParent:!0}),tfoot:new bi({closedByChildren:["tbody"],closedByParent:!0}),tr:new bi({closedByChildren:["tr"],closedByParent:!0}),td:new bi({closedByChildren:["td","th"],closedByParent:!0}),th:new bi({closedByChildren:["td","th"],closedByParent:!0}),col:new bi({isVoid:!0}),svg:new bi({implicitNamespacePrefix:"svg"}),math:new bi({implicitNamespacePrefix:"math"}),li:new bi({closedByChildren:["li"],closedByParent:!0}),dt:new bi({closedByChildren:["dt","dd"]}),dd:new bi({closedByChildren:["dt","dd"],closedByParent:!0}),rb:new bi({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rt:new bi({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rtc:new bi({closedByChildren:["rb","rtc","rp"],closedByParent:!0}),rp:new bi({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),optgroup:new bi({closedByChildren:["optgroup"],closedByParent:!0}),option:new bi({closedByChildren:["option","optgroup"],closedByParent:!0}),pre:new bi({ignoreFirstLf:!0}),listing:new bi({ignoreFirstLf:!0}),style:new bi({contentType:vi.TagContentType.RAW_TEXT}),script:new bi({contentType:vi.TagContentType.RAW_TEXT}),title:new bi({contentType:vi.TagContentType.ESCAPABLE_RAW_TEXT}),textarea:new bi({contentType:vi.TagContentType.ESCAPABLE_RAW_TEXT,ignoreFirstLf:!0})}),wi[e]||Ai},ki=Object.defineProperty({HtmlTagDefinition:Fi,getHtmlTagDefinition:Ni},"__esModule",{value:!0});var Oi=
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
class{constructor(e,t=-1){this.path=e,this.position=t}get empty(){return!this.path||!this.path.length}get head(){return this.path[0]}get tail(){return this.path[this.path.length-1]}parentOf(e){return e&&this.path[this.path.indexOf(e)-1]}childOf(e){return this.path[this.path.indexOf(e)+1]}first(e){for(let t=this.path.length-1;t>=0;t--){let r=this.path[t];if(r instanceof e)return r}}push(e){this.path.push(e)}pop(){return this.path.pop()}},xi=Object.defineProperty({AstPath:Oi},"__esModule",{value:!0});var Ii=
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
class{constructor(e,t,r){this.value=e,this.sourceSpan=t,this.i18n=r,this.type="text"}visit(e,t){return e.visitText(this,t)}};var Pi=class{constructor(e,t){this.value=e,this.sourceSpan=t,this.type="cdata"}visit(e,t){return e.visitCdata(this,t)}};var Ri=class{constructor(e,t,r,n,i,s){this.switchValue=e,this.type=t,this.cases=r,this.sourceSpan=n,this.switchValueSourceSpan=i,this.i18n=s}visit(e,t){return e.visitExpansion(this,t)}};var Li=class{constructor(e,t,r,n,i){this.value=e,this.expression=t,this.sourceSpan=r,this.valueSourceSpan=n,this.expSourceSpan=i}visit(e,t){return e.visitExpansionCase(this,t)}};var Bi=class{constructor(e,t,r,n=null,i=null,s=null){this.name=e,this.value=t,this.sourceSpan=r,this.valueSpan=n,this.nameSpan=i,this.i18n=s,this.type="attribute"}visit(e,t){return e.visitAttribute(this,t)}};class $i{constructor(e,t,r,n,i=null,s=null,o=null,a=null){this.name=e,this.attrs=t,this.children=r,this.sourceSpan=n,this.startSourceSpan=i,this.endSourceSpan=s,this.nameSpan=o,this.i18n=a,this.type="element"}visit(e,t){return e.visitElement(this,t)}}var qi=$i;var Mi=class{constructor(e,t){this.value=e,this.sourceSpan=t,this.type="comment"}visit(e,t){return e.visitComment(this,t)}};var Ui=class{constructor(e,t){this.value=e,this.sourceSpan=t,this.type="docType"}visit(e,t){return e.visitDocType(this,t)}};function Gi(e,t,r=null){const n=[],i=e.visit?t=>e.visit(t,r)||t.visit(e,r):t=>t.visit(e,r);return t.forEach((e=>{const t=i(e);t&&n.push(t)})),n}var ji=Gi;class Vi{constructor(){}visitElement(e,t){this.visitChildren(t,(t=>{t(e.attrs),t(e.children)}))}visitAttribute(e,t){}visitText(e,t){}visitCdata(e,t){}visitComment(e,t){}visitDocType(e,t){}visitExpansion(e,t){return this.visitChildren(t,(t=>{t(e.cases)}))}visitExpansionCase(e,t){}visitChildren(e,t){let r=[],n=this;return t((function(t){t&&r.push(Gi(n,t,e))})),Array.prototype.concat.apply([],r)}}var Hi=Vi;function Xi(e){const t=e.sourceSpan.start.offset;let r=e.sourceSpan.end.offset;return e instanceof $i&&(e.endSourceSpan?r=e.endSourceSpan.end.offset:e.children&&e.children.length&&(r=Xi(e.children[e.children.length-1]).end)),{start:t,end:r}}var zi=function(e,t){const r=[];return Gi(new class extends Vi{visit(e,n){const i=Xi(e);if(!(i.start<=t&&t<i.end))return!0;r.push(e)}},e),new xi.AstPath(r,t)},Wi=Object.defineProperty({Text:Ii,CDATA:Pi,Expansion:Ri,ExpansionCase:Li,Attribute:Bi,Element:qi,Comment:Mi,DocType:Ui,visitAll:ji,RecursiveVisitor:Hi,findNode:zi},"__esModule",{value:!0});var Yi=
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function(e,t){if(null!=t){if(!Array.isArray(t))throw new Error(`Expected '${e}' to be an array of strings.`);for(let r=0;r<t.length;r+=1)if("string"!=typeof t[r])throw new Error(`Expected '${e}' to be an array of strings.`)}};const Qi=[/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//];var Ji=function(e,t){if(!(null==t||Array.isArray(t)&&2==t.length))throw new Error(`Expected '${e}' to be an array, [start, end].`);if(null!=t){const e=t[0],r=t[1];Qi.forEach((t=>{if(t.test(e)||t.test(r))throw new Error(`['${e}', '${r}'] contains unusable interpolation symbol.`)}))}},Ki=Object.defineProperty({assertArrayOfStrings:Yi,assertInterpolationSymbols:Ji},"__esModule",{value:!0}),Zi=t((function(e,t){
/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
Object.defineProperty(t,"__esModule",{value:!0});class r{constructor(e,t){this.start=e,this.end=t}static fromArray(e){return e?(Ki.assertInterpolationSymbols("interpolation",e),new r(e[0],e[1])):t.DEFAULT_INTERPOLATION_CONFIG}}t.InterpolationConfig=r,t.DEFAULT_INTERPOLATION_CONFIG=new r("{{","}}")})),es=t((function(e,t){
/**
   * @license
   * Copyright Google Inc. All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   */
Object.defineProperty(t,"__esModule",{value:!0});const n=r;var i;!function(e){e[e.TAG_OPEN_START=0]="TAG_OPEN_START",e[e.TAG_OPEN_END=1]="TAG_OPEN_END",e[e.TAG_OPEN_END_VOID=2]="TAG_OPEN_END_VOID",e[e.TAG_CLOSE=3]="TAG_CLOSE",e[e.TEXT=4]="TEXT",e[e.ESCAPABLE_RAW_TEXT=5]="ESCAPABLE_RAW_TEXT",e[e.RAW_TEXT=6]="RAW_TEXT",e[e.COMMENT_START=7]="COMMENT_START",e[e.COMMENT_END=8]="COMMENT_END",e[e.CDATA_START=9]="CDATA_START",e[e.CDATA_END=10]="CDATA_END",e[e.ATTR_NAME=11]="ATTR_NAME",e[e.ATTR_QUOTE=12]="ATTR_QUOTE",e[e.ATTR_VALUE=13]="ATTR_VALUE",e[e.DOC_TYPE_START=14]="DOC_TYPE_START",e[e.DOC_TYPE_END=15]="DOC_TYPE_END",e[e.EXPANSION_FORM_START=16]="EXPANSION_FORM_START",e[e.EXPANSION_CASE_VALUE=17]="EXPANSION_CASE_VALUE",e[e.EXPANSION_CASE_EXP_START=18]="EXPANSION_CASE_EXP_START",e[e.EXPANSION_CASE_EXP_END=19]="EXPANSION_CASE_EXP_END",e[e.EXPANSION_FORM_END=20]="EXPANSION_FORM_END",e[e.EOF=21]="EOF"}(i=t.TokenType||(t.TokenType={}));class s{constructor(e,t,r){this.type=e,this.parts=t,this.sourceSpan=r}}t.Token=s;class o extends B.ParseError{constructor(e,t,r){super(r,e),this.tokenType=t}}t.TokenError=o;class a{constructor(e,t){this.tokens=e,this.errors=t}}t.TokenizeResult=a,t.tokenize=function(e,t,r,n={}){return new D(new B.ParseSourceFile(e,t),r,n).tokenize()};const u=/\r\n?/g;function c(e){return`Unexpected character "${e===n.$EOF?"EOF":String.fromCharCode(e)}"`}function l(e){return`Unknown entity "${e}" - use the "&#<decimal>;" or  "&#x<hex>;" syntax`}class p{constructor(e){this.error=e}}class D{constructor(e,t,r){this._getTagContentType=t,this._currentTokenStart=null,this._currentTokenType=null,this._expansionCaseStack=[],this._inInterpolation=!1,this._fullNameStack=[],this.tokens=[],this.errors=[],this._tokenizeIcu=r.tokenizeExpansionForms||!1,this._interpolationConfig=r.interpolationConfig||Zi.DEFAULT_INTERPOLATION_CONFIG,this._leadingTriviaCodePoints=r.leadingTriviaChars&&r.leadingTriviaChars.map((e=>e.codePointAt(0)||0)),this._canSelfClose=r.canSelfClose||!1,this._allowHtmComponentClosingTags=r.allowHtmComponentClosingTags||!1;const n=r.range||{endPos:e.content.length,startPos:0,startLine:0,startCol:0};this._cursor=r.escapedString?new C(e,n):new E(e,n);try{this._cursor.init()}catch(e){this.handleError(e)}}_processCarriageReturns(e){return e.replace(u,"\n")}tokenize(){for(;this._cursor.peek()!==n.$EOF;){const e=this._cursor.clone();try{if(this._attemptCharCode(n.$LT))if(this._attemptCharCode(n.$BANG))this._attemptStr("[CDATA[")?this._consumeCdata(e):this._attemptStr("--")?this._consumeComment(e):this._attemptStrCaseInsensitive("doctype")?this._consumeDocType(e):this._consumeBogusComment(e);else if(this._attemptCharCode(n.$SLASH))this._consumeTagClose(e);else{const t=this._cursor.clone();this._attemptCharCode(n.$QUESTION)?(this._cursor=t,this._consumeBogusComment(e)):this._consumeTagOpen(e)}else this._tokenizeIcu&&this._tokenizeExpansionForm()||this._consumeText()}catch(e){this.handleError(e)}}return this._beginToken(i.EOF),this._endToken([]),new a(function(e){const t=[];let r;for(let n=0;n<e.length;n++){const s=e[n];r&&r.type==i.TEXT&&s.type==i.TEXT?(r.parts[0]+=s.parts[0],r.sourceSpan.end=s.sourceSpan.end):(r=s,t.push(r))}return t}(this.tokens),this.errors)}_tokenizeExpansionForm(){if(this.isExpansionFormStart())return this._consumeExpansionFormStart(),!0;if(((e=this._cursor.peek())===n.$EQ||n.isAsciiLetter(e)||n.isDigit(e))&&this._isInExpansionForm())return this._consumeExpansionCaseStart(),!0;var e;if(this._cursor.peek()===n.$RBRACE){if(this._isInExpansionCase())return this._consumeExpansionCaseEnd(),!0;if(this._isInExpansionForm())return this._consumeExpansionFormEnd(),!0}return!1}_beginToken(e,t=this._cursor.clone()){this._currentTokenStart=t,this._currentTokenType=e}_endToken(e,t=this._cursor.clone()){if(null===this._currentTokenStart)throw new o("Programming error - attempted to end a token when there was no start to the token",this._currentTokenType,this._cursor.getSpan(t));if(null===this._currentTokenType)throw new o("Programming error - attempted to end a token which has no token type",null,this._cursor.getSpan(this._currentTokenStart));const r=new s(this._currentTokenType,e,this._cursor.getSpan(this._currentTokenStart,this._leadingTriviaCodePoints));return this.tokens.push(r),this._currentTokenStart=null,this._currentTokenType=null,r}_createError(e,t){this._isInExpansionForm()&&(e+=' (Do you have an unescaped "{" in your template? Use "{{ \'{\' }}") to escape it.)');const r=new o(e,this._currentTokenType,t);return this._currentTokenStart=null,this._currentTokenType=null,new p(r)}handleError(e){if(e instanceof T&&(e=this._createError(e.msg,this._cursor.getSpan(e.cursor))),!(e instanceof p))throw e;this.errors.push(e.error)}_attemptCharCode(e){return this._cursor.peek()===e&&(this._cursor.advance(),!0)}_attemptCharCodeCaseInsensitive(e){return t=this._cursor.peek(),r=e,g(t)==g(r)&&(this._cursor.advance(),!0);var t,r}_requireCharCode(e){const t=this._cursor.clone();if(!this._attemptCharCode(e))throw this._createError(c(this._cursor.peek()),this._cursor.getSpan(t))}_attemptStr(e){const t=e.length;if(this._cursor.charsLeft()<t)return!1;const r=this._cursor.clone();for(let n=0;n<t;n++)if(!this._attemptCharCode(e.charCodeAt(n)))return this._cursor=r,!1;return!0}_attemptStrCaseInsensitive(e){for(let t=0;t<e.length;t++)if(!this._attemptCharCodeCaseInsensitive(e.charCodeAt(t)))return!1;return!0}_requireStr(e){const t=this._cursor.clone();if(!this._attemptStr(e))throw this._createError(c(this._cursor.peek()),this._cursor.getSpan(t))}_requireStrCaseInsensitive(e){const t=this._cursor.clone();if(!this._attemptStrCaseInsensitive(e))throw this._createError(c(this._cursor.peek()),this._cursor.getSpan(t))}_attemptCharCodeUntilFn(e){for(;!e(this._cursor.peek());)this._cursor.advance()}_requireCharCodeUntilFn(e,t){const r=this._cursor.clone();this._attemptCharCodeUntilFn(e);if(this._cursor.clone().diff(r)<t)throw this._createError(c(this._cursor.peek()),this._cursor.getSpan(r))}_attemptUntilChar(e){for(;this._cursor.peek()!==e;)this._cursor.advance()}_readChar(e){if(e&&this._cursor.peek()===n.$AMPERSAND)return this._decodeEntity();{const e=String.fromCodePoint(this._cursor.peek());return this._cursor.advance(),e}}_decodeEntity(){const e=this._cursor.clone();if(this._cursor.advance(),!this._attemptCharCode(n.$HASH)){const t=this._cursor.clone();if(this._attemptCharCodeUntilFn(m),this._cursor.peek()!=n.$SEMICOLON)return this._cursor=t,"&";const r=this._cursor.getChars(t);this._cursor.advance();const i=vi.NAMED_ENTITIES[r];if(!i)throw this._createError(l(r),this._cursor.getSpan(e));return i}{const t=this._attemptCharCode(n.$x)||this._attemptCharCode(n.$X),r=this._cursor.clone();if(this._attemptCharCodeUntilFn(f),this._cursor.peek()!=n.$SEMICOLON)throw this._createError(c(this._cursor.peek()),this._cursor.getSpan());const i=this._cursor.getChars(r);this._cursor.advance();try{const e=parseInt(i,t?16:10);return String.fromCharCode(e)}catch(t){throw this._createError(l(this._cursor.getChars(e)),this._cursor.getSpan())}}}_consumeRawText(e,t){this._beginToken(e?i.ESCAPABLE_RAW_TEXT:i.RAW_TEXT);const r=[];for(;;){const n=this._cursor.clone(),i=t();if(this._cursor=n,i)break;r.push(this._readChar(e))}return this._endToken([this._processCarriageReturns(r.join(""))])}_consumeComment(e){this._beginToken(i.COMMENT_START,e),this._endToken([]),this._consumeRawText(!1,(()=>this._attemptStr("--\x3e"))),this._beginToken(i.COMMENT_END),this._requireStr("--\x3e"),this._endToken([])}_consumeBogusComment(e){this._beginToken(i.COMMENT_START,e),this._endToken([]),this._consumeRawText(!1,(()=>this._cursor.peek()===n.$GT)),this._beginToken(i.COMMENT_END),this._cursor.advance(),this._endToken([])}_consumeCdata(e){this._beginToken(i.CDATA_START,e),this._endToken([]),this._consumeRawText(!1,(()=>this._attemptStr("]]>"))),this._beginToken(i.CDATA_END),this._requireStr("]]>"),this._endToken([])}_consumeDocType(e){this._beginToken(i.DOC_TYPE_START,e),this._endToken([]),this._consumeRawText(!1,(()=>this._cursor.peek()===n.$GT)),this._beginToken(i.DOC_TYPE_END),this._cursor.advance(),this._endToken([])}_consumePrefixAndName(){const e=this._cursor.clone();let t="";for(;this._cursor.peek()!==n.$COLON&&!(((r=this._cursor.peek())<n.$a||n.$z<r)&&(r<n.$A||n.$Z<r)&&(r<n.$0||r>n.$9));)this._cursor.advance();var r;let i;this._cursor.peek()===n.$COLON?(t=this._cursor.getChars(e),this._cursor.advance(),i=this._cursor.clone()):i=e,this._requireCharCodeUntilFn(d,""===t?0:1);return[t,this._cursor.getChars(i)]}_consumeTagOpen(e){let t,r,s,o=this.tokens.length;const a=this._cursor.clone(),u=[];try{if(!n.isAsciiLetter(this._cursor.peek()))throw this._createError(c(this._cursor.peek()),this._cursor.getSpan(e));for(s=this._consumeTagOpenStart(e),r=s.parts[0],t=s.parts[1],this._attemptCharCodeUntilFn(h);this._cursor.peek()!==n.$SLASH&&this._cursor.peek()!==n.$GT;){const[e,t]=this._consumeAttributeName();if(this._attemptCharCodeUntilFn(h),this._attemptCharCode(n.$EQ)){this._attemptCharCodeUntilFn(h);const r=this._consumeAttributeValue();u.push({prefix:e,name:t,value:r})}else u.push({prefix:e,name:t});this._attemptCharCodeUntilFn(h)}this._consumeTagOpenEnd()}catch(t){if(t instanceof p)return this._cursor=a,s&&(this.tokens.length=o),this._beginToken(i.TEXT,e),void this._endToken(["<"]);throw t}if(this._canSelfClose&&this.tokens[this.tokens.length-1].type===i.TAG_OPEN_END_VOID)return;const l=this._getTagContentType(t,r,this._fullNameStack.length>0,u);this._handleFullNameStackForTagOpen(r,t),l===vi.TagContentType.RAW_TEXT?this._consumeRawTextWithTagClose(r,t,!1):l===vi.TagContentType.ESCAPABLE_RAW_TEXT&&this._consumeRawTextWithTagClose(r,t,!0)}_consumeRawTextWithTagClose(e,t,r){this._consumeRawText(r,(()=>!!this._attemptCharCode(n.$LT)&&(!!this._attemptCharCode(n.$SLASH)&&(this._attemptCharCodeUntilFn(h),!!this._attemptStrCaseInsensitive(e?`${e}:${t}`:t)&&(this._attemptCharCodeUntilFn(h),this._attemptCharCode(n.$GT)))))),this._beginToken(i.TAG_CLOSE),this._requireCharCodeUntilFn((e=>e===n.$GT),3),this._cursor.advance(),this._endToken([e,t]),this._handleFullNameStackForTagClose(e,t)}_consumeTagOpenStart(e){this._beginToken(i.TAG_OPEN_START,e);const t=this._consumePrefixAndName();return this._endToken(t)}_consumeAttributeName(){const e=this._cursor.peek();if(e===n.$SQ||e===n.$DQ)throw this._createError(c(e),this._cursor.getSpan());this._beginToken(i.ATTR_NAME);const t=this._consumePrefixAndName();return this._endToken(t),t}_consumeAttributeValue(){let e;if(this._cursor.peek()===n.$SQ||this._cursor.peek()===n.$DQ){this._beginToken(i.ATTR_QUOTE);const t=this._cursor.peek();this._cursor.advance(),this._endToken([String.fromCodePoint(t)]),this._beginToken(i.ATTR_VALUE);const r=[];for(;this._cursor.peek()!==t;)r.push(this._readChar(!0));e=this._processCarriageReturns(r.join("")),this._endToken([e]),this._beginToken(i.ATTR_QUOTE),this._cursor.advance(),this._endToken([String.fromCodePoint(t)])}else{this._beginToken(i.ATTR_VALUE);const t=this._cursor.clone();this._requireCharCodeUntilFn(d,1),e=this._processCarriageReturns(this._cursor.getChars(t)),this._endToken([e])}return e}_consumeTagOpenEnd(){const e=this._attemptCharCode(n.$SLASH)?i.TAG_OPEN_END_VOID:i.TAG_OPEN_END;this._beginToken(e),this._requireCharCode(n.$GT),this._endToken([])}_consumeTagClose(e){if(this._beginToken(i.TAG_CLOSE,e),this._attemptCharCodeUntilFn(h),this._allowHtmComponentClosingTags&&this._attemptCharCode(n.$SLASH))this._attemptCharCodeUntilFn(h),this._requireCharCode(n.$GT),this._endToken([]);else{const[e,t]=this._consumePrefixAndName();this._attemptCharCodeUntilFn(h),this._requireCharCode(n.$GT),this._endToken([e,t]),this._handleFullNameStackForTagClose(e,t)}}_consumeExpansionFormStart(){this._beginToken(i.EXPANSION_FORM_START),this._requireCharCode(n.$LBRACE),this._endToken([]),this._expansionCaseStack.push(i.EXPANSION_FORM_START),this._beginToken(i.RAW_TEXT);const e=this._readUntil(n.$COMMA);this._endToken([e]),this._requireCharCode(n.$COMMA),this._attemptCharCodeUntilFn(h),this._beginToken(i.RAW_TEXT);const t=this._readUntil(n.$COMMA);this._endToken([t]),this._requireCharCode(n.$COMMA),this._attemptCharCodeUntilFn(h)}_consumeExpansionCaseStart(){this._beginToken(i.EXPANSION_CASE_VALUE);const e=this._readUntil(n.$LBRACE).trim();this._endToken([e]),this._attemptCharCodeUntilFn(h),this._beginToken(i.EXPANSION_CASE_EXP_START),this._requireCharCode(n.$LBRACE),this._endToken([]),this._attemptCharCodeUntilFn(h),this._expansionCaseStack.push(i.EXPANSION_CASE_EXP_START)}_consumeExpansionCaseEnd(){this._beginToken(i.EXPANSION_CASE_EXP_END),this._requireCharCode(n.$RBRACE),this._endToken([]),this._attemptCharCodeUntilFn(h),this._expansionCaseStack.pop()}_consumeExpansionFormEnd(){this._beginToken(i.EXPANSION_FORM_END),this._requireCharCode(n.$RBRACE),this._endToken([]),this._expansionCaseStack.pop()}_consumeText(){const e=this._cursor.clone();this._beginToken(i.TEXT,e);const t=[];do{this._interpolationConfig&&this._attemptStr(this._interpolationConfig.start)?(t.push(this._interpolationConfig.start),this._inInterpolation=!0):this._interpolationConfig&&this._inInterpolation&&this._attemptStr(this._interpolationConfig.end)?(t.push(this._interpolationConfig.end),this._inInterpolation=!1):t.push(this._readChar(!0))}while(!this._isTextEnd());this._endToken([this._processCarriageReturns(t.join(""))])}_isTextEnd(){if(this._cursor.peek()===n.$LT||this._cursor.peek()===n.$EOF)return!0;if(this._tokenizeIcu&&!this._inInterpolation){if(this.isExpansionFormStart())return!0;if(this._cursor.peek()===n.$RBRACE&&this._isInExpansionCase())return!0}return!1}_readUntil(e){const t=this._cursor.clone();return this._attemptUntilChar(e),this._cursor.getChars(t)}_isInExpansionCase(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===i.EXPANSION_CASE_EXP_START}_isInExpansionForm(){return this._expansionCaseStack.length>0&&this._expansionCaseStack[this._expansionCaseStack.length-1]===i.EXPANSION_FORM_START}isExpansionFormStart(){if(this._cursor.peek()!==n.$LBRACE)return!1;if(this._interpolationConfig){const e=this._cursor.clone(),t=this._attemptStr(this._interpolationConfig.start);return this._cursor=e,!t}return!0}_handleFullNameStackForTagOpen(e,t){const r=vi.mergeNsAndName(e,t);0!==this._fullNameStack.length&&this._fullNameStack[this._fullNameStack.length-1]!==r||this._fullNameStack.push(r)}_handleFullNameStackForTagClose(e,t){const r=vi.mergeNsAndName(e,t);0!==this._fullNameStack.length&&this._fullNameStack[this._fullNameStack.length-1]===r&&this._fullNameStack.pop()}}function h(e){return!n.isWhitespace(e)||e===n.$EOF}function d(e){return n.isWhitespace(e)||e===n.$GT||e===n.$SLASH||e===n.$SQ||e===n.$DQ||e===n.$EQ}function f(e){return e==n.$SEMICOLON||e==n.$EOF||!n.isAsciiHexDigit(e)}function m(e){return e==n.$SEMICOLON||e==n.$EOF||!n.isAsciiLetter(e)}function g(e){return e>=n.$a&&e<=n.$z?e-n.$a+n.$A:e}class E{constructor(e,t){if(e instanceof E)this.file=e.file,this.input=e.input,this.end=e.end,this.state=Object.assign({},e.state);else{if(!t)throw new Error("Programming error: the range argument must be provided with a file argument.");this.file=e,this.input=e.content,this.end=t.endPos,this.state={peek:-1,offset:t.startPos,line:t.startLine,column:t.startCol}}}clone(){return new E(this)}peek(){return this.state.peek}charsLeft(){return this.end-this.state.offset}diff(e){return this.state.offset-e.state.offset}advance(){this.advanceState(this.state)}init(){this.updatePeek(this.state)}getSpan(e,t){if(e=e||this,t)for(e=e.clone();this.diff(e)>0&&-1!==t.indexOf(e.peek());)e.advance();return new B.ParseSourceSpan(new B.ParseLocation(e.file,e.state.offset,e.state.line,e.state.column),new B.ParseLocation(this.file,this.state.offset,this.state.line,this.state.column))}getChars(e){return this.input.substring(e.state.offset,this.state.offset)}charAt(e){return this.input.charCodeAt(e)}advanceState(e){if(e.offset>=this.end)throw this.state=e,new T('Unexpected character "EOF"',this);const t=this.charAt(e.offset);t===n.$LF?(e.line++,e.column=0):n.isNewLine(t)||e.column++,e.offset++,this.updatePeek(e)}updatePeek(e){e.peek=e.offset>=this.end?n.$EOF:this.charAt(e.offset)}}class C extends E{constructor(e,t){e instanceof C?(super(e),this.internalState=Object.assign({},e.internalState)):(super(e,t),this.internalState=this.state)}advance(){this.state=this.internalState,super.advance(),this.processEscapeSequence()}init(){super.init(),this.processEscapeSequence()}clone(){return new C(this)}getChars(e){const t=e.clone();let r="";for(;t.internalState.offset<this.internalState.offset;)r+=String.fromCodePoint(t.peek()),t.advance();return r}processEscapeSequence(){const e=()=>this.internalState.peek;if(e()===n.$BACKSLASH)if(this.internalState=Object.assign({},this.state),this.advanceState(this.internalState),e()===n.$n)this.state.peek=n.$LF;else if(e()===n.$r)this.state.peek=n.$CR;else if(e()===n.$v)this.state.peek=n.$VTAB;else if(e()===n.$t)this.state.peek=n.$TAB;else if(e()===n.$b)this.state.peek=n.$BSPACE;else if(e()===n.$f)this.state.peek=n.$FF;else if(e()===n.$u)if(this.advanceState(this.internalState),e()===n.$LBRACE){this.advanceState(this.internalState);const t=this.clone();let r=0;for(;e()!==n.$RBRACE;)this.advanceState(this.internalState),r++;this.state.peek=this.decodeHexDigits(t,r)}else{const e=this.clone();this.advanceState(this.internalState),this.advanceState(this.internalState),this.advanceState(this.internalState),this.state.peek=this.decodeHexDigits(e,4)}else if(e()===n.$x){this.advanceState(this.internalState);const e=this.clone();this.advanceState(this.internalState),this.state.peek=this.decodeHexDigits(e,2)}else if(n.isOctalDigit(e())){let t="",r=0,i=this.clone();for(;n.isOctalDigit(e())&&r<3;)i=this.clone(),t+=String.fromCodePoint(e()),this.advanceState(this.internalState),r++;this.state.peek=parseInt(t,8),this.internalState=i.internalState}else n.isNewLine(this.internalState.peek)?(this.advanceState(this.internalState),this.state=this.internalState):this.state.peek=this.internalState.peek}decodeHexDigits(e,t){const r=this.input.substr(e.internalState.offset,t),n=parseInt(r,16);if(isNaN(n))throw e.state=e.internalState,new T("Invalid hexadecimal escape sequence",e);return n}}class T{constructor(e,t){this.msg=e,this.cursor=t}}t.CursorError=T}));
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
class ts extends B.ParseError{constructor(e,t,r){super(t,r),this.elementName=e}static create(e,t,r){return new ts(e,t,r)}}var rs=ts;class ns{constructor(e,t){this.rootNodes=e,this.errors=t}}var is=ns;var ss=class{constructor(e){this.getTagDefinition=e}parse(e,t,r,n=!1,i){const s=e=>(t,...r)=>e(t.toLowerCase(),...r),o=n?this.getTagDefinition:s(this.getTagDefinition),a=e=>o(e).contentType,u=n?i:s(i),c=i?(e,t,r,n)=>{const i=u(e,t,r,n);return void 0!==i?i:a(e)}:a,l=es.tokenize(e,t,c,r),p=r&&r.canSelfClose||!1,D=r&&r.allowHtmComponentClosingTags||!1,h=new os(l.tokens,o,p,D,n).build();return new ns(h.rootNodes,l.errors.concat(h.errors))}};class os{constructor(e,t,r,n,i){this.tokens=e,this.getTagDefinition=t,this.canSelfClose=r,this.allowHtmComponentClosingTags=n,this.isTagNameCaseSensitive=i,this._index=-1,this._rootNodes=[],this._errors=[],this._elementStack=[],this._advance()}build(){for(;this._peek.type!==es.TokenType.EOF;)this._peek.type===es.TokenType.TAG_OPEN_START?this._consumeStartTag(this._advance()):this._peek.type===es.TokenType.TAG_CLOSE?(this._closeVoidElement(),this._consumeEndTag(this._advance())):this._peek.type===es.TokenType.CDATA_START?(this._closeVoidElement(),this._consumeCdata(this._advance())):this._peek.type===es.TokenType.COMMENT_START?(this._closeVoidElement(),this._consumeComment(this._advance())):this._peek.type===es.TokenType.TEXT||this._peek.type===es.TokenType.RAW_TEXT||this._peek.type===es.TokenType.ESCAPABLE_RAW_TEXT?(this._closeVoidElement(),this._consumeText(this._advance())):this._peek.type===es.TokenType.EXPANSION_FORM_START?this._consumeExpansion(this._advance()):this._peek.type===es.TokenType.DOC_TYPE_START?this._consumeDocType(this._advance()):this._advance();return new ns(this._rootNodes,this._errors)}_advance(){const e=this._peek;return this._index<this.tokens.length-1&&this._index++,this._peek=this.tokens[this._index],e}_advanceIf(e){return this._peek.type===e?this._advance():null}_consumeCdata(e){const t=this._advance(),r=this._getText(t),n=this._advanceIf(es.TokenType.CDATA_END);this._addToParent(new Wi.CDATA(r,new B.ParseSourceSpan(e.sourceSpan.start,(n||t).sourceSpan.end)))}_consumeComment(e){const t=this._advanceIf(es.TokenType.RAW_TEXT),r=this._advanceIf(es.TokenType.COMMENT_END),n=null!=t?t.parts[0].trim():null,i=new B.ParseSourceSpan(e.sourceSpan.start,(r||t||e).sourceSpan.end);this._addToParent(new Wi.Comment(n,i))}_consumeDocType(e){const t=this._advanceIf(es.TokenType.RAW_TEXT),r=this._advanceIf(es.TokenType.DOC_TYPE_END),n=null!=t?t.parts[0].trim():null,i=new B.ParseSourceSpan(e.sourceSpan.start,(r||t||e).sourceSpan.end);this._addToParent(new Wi.DocType(n,i))}_consumeExpansion(e){const t=this._advance(),r=this._advance(),n=[];for(;this._peek.type===es.TokenType.EXPANSION_CASE_VALUE;){const e=this._parseExpansionCase();if(!e)return;n.push(e)}if(this._peek.type!==es.TokenType.EXPANSION_FORM_END)return void this._errors.push(ts.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '}'."));const i=new B.ParseSourceSpan(e.sourceSpan.start,this._peek.sourceSpan.end);this._addToParent(new Wi.Expansion(t.parts[0],r.parts[0],n,i,t.sourceSpan)),this._advance()}_parseExpansionCase(){const e=this._advance();if(this._peek.type!==es.TokenType.EXPANSION_CASE_EXP_START)return this._errors.push(ts.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '{'.")),null;const t=this._advance(),r=this._collectExpansionExpTokens(t);if(!r)return null;const n=this._advance();r.push(new es.Token(es.TokenType.EOF,[],n.sourceSpan));const i=new os(r,this.getTagDefinition,this.canSelfClose,this.allowHtmComponentClosingTags,this.isTagNameCaseSensitive).build();if(i.errors.length>0)return this._errors=this._errors.concat(i.errors),null;const s=new B.ParseSourceSpan(e.sourceSpan.start,n.sourceSpan.end),o=new B.ParseSourceSpan(t.sourceSpan.start,n.sourceSpan.end);return new Wi.ExpansionCase(e.parts[0],i.rootNodes,s,e.sourceSpan,o)}_collectExpansionExpTokens(e){const t=[],r=[es.TokenType.EXPANSION_CASE_EXP_START];for(;;){if(this._peek.type!==es.TokenType.EXPANSION_FORM_START&&this._peek.type!==es.TokenType.EXPANSION_CASE_EXP_START||r.push(this._peek.type),this._peek.type===es.TokenType.EXPANSION_CASE_EXP_END){if(!as(r,es.TokenType.EXPANSION_CASE_EXP_START))return this._errors.push(ts.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;if(r.pop(),0==r.length)return t}if(this._peek.type===es.TokenType.EXPANSION_FORM_END){if(!as(r,es.TokenType.EXPANSION_FORM_START))return this._errors.push(ts.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;r.pop()}if(this._peek.type===es.TokenType.EOF)return this._errors.push(ts.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;t.push(this._advance())}}_getText(e){let t=e.parts[0];if(t.length>0&&"\n"==t[0]){const e=this._getParentElement();null!=e&&0==e.children.length&&this.getTagDefinition(e.name).ignoreFirstLf&&(t=t.substring(1))}return t}_consumeText(e){const t=this._getText(e);t.length>0&&this._addToParent(new Wi.Text(t,e.sourceSpan))}_closeVoidElement(){const e=this._getParentElement();e&&this.getTagDefinition(e.name).isVoid&&this._elementStack.pop()}_consumeStartTag(e){const t=e.parts[0],r=e.parts[1],n=[];for(;this._peek.type===es.TokenType.ATTR_NAME;)n.push(this._consumeAttr(this._advance()));const i=this._getElementFullName(t,r,this._getParentElement());let s=!1;if(this._peek.type===es.TokenType.TAG_OPEN_END_VOID){this._advance(),s=!0;const t=this.getTagDefinition(i);this.canSelfClose||t.canSelfClose||null!==vi.getNsPrefix(i)||t.isVoid||this._errors.push(ts.create(i,e.sourceSpan,`Only void and foreign elements can be self closed "${e.parts[1]}"`))}else this._peek.type===es.TokenType.TAG_OPEN_END&&(this._advance(),s=!1);const o=this._peek.sourceSpan.start,a=new B.ParseSourceSpan(e.sourceSpan.start,o),u=new B.ParseSourceSpan(e.sourceSpan.start.moveBy(1),e.sourceSpan.end),c=new Wi.Element(i,n,[],a,a,void 0,u);this._pushElement(c),s&&(this._popElement(i),c.endSourceSpan=a)}_pushElement(e){const t=this._getParentElement();t&&this.getTagDefinition(t.name).isClosedByChild(e.name)&&this._elementStack.pop(),this._addToParent(e),this._elementStack.push(e)}_consumeEndTag(e){const t=this.allowHtmComponentClosingTags&&0===e.parts.length?null:this._getElementFullName(e.parts[0],e.parts[1],this._getParentElement());if(this._getParentElement()&&(this._getParentElement().endSourceSpan=e.sourceSpan),t&&this.getTagDefinition(t).isVoid)this._errors.push(ts.create(t,e.sourceSpan,`Void elements do not have end tags "${e.parts[1]}"`));else if(!this._popElement(t)){const r=`Unexpected closing tag "${t}". It may happen when the tag has already been closed by another tag. For more info see https://www.w3.org/TR/html5/syntax.html#closing-elements-that-have-implied-end-tags`;this._errors.push(ts.create(t,e.sourceSpan,r))}}_popElement(e){for(let t=this._elementStack.length-1;t>=0;t--){const r=this._elementStack[t];if(!e||(vi.getNsPrefix(r.name)?r.name==e:r.name.toLowerCase()==e.toLowerCase()))return this._elementStack.splice(t,this._elementStack.length-t),!0;if(!this.getTagDefinition(r.name).closedByParent)return!1}return!1}_consumeAttr(e){const t=vi.mergeNsAndName(e.parts[0],e.parts[1]);let r,n,i=e.sourceSpan.end,s="";if(this._peek.type===es.TokenType.ATTR_QUOTE){n=this._advance().sourceSpan.start}if(this._peek.type===es.TokenType.ATTR_VALUE){const e=this._advance();s=e.parts[0],i=e.sourceSpan.end,r=e.sourceSpan}if(this._peek.type===es.TokenType.ATTR_QUOTE){i=this._advance().sourceSpan.end,r=new B.ParseSourceSpan(n,i)}return new Wi.Attribute(t,s,new B.ParseSourceSpan(e.sourceSpan.start,i),r,e.sourceSpan)}_getParentElement(){return this._elementStack.length>0?this._elementStack[this._elementStack.length-1]:null}_getParentElementSkippingContainers(){let e=null;for(let t=this._elementStack.length-1;t>=0;t--){if(!vi.isNgContainer(this._elementStack[t].name))return{parent:this._elementStack[t],container:e};e=this._elementStack[t]}return{parent:null,container:e}}_addToParent(e){const t=this._getParentElement();null!=t?t.children.push(e):this._rootNodes.push(e)}_insertBeforeContainer(e,t,r){if(t){if(e){const n=e.children.indexOf(t);e.children[n]=r}else this._rootNodes.push(r);r.children.push(t),this._elementStack.splice(this._elementStack.indexOf(t),0,r)}else this._addToParent(r),this._elementStack.push(r)}_getElementFullName(e,t,r){return""===e&&""===(e=this.getTagDefinition(t).implicitNamespacePrefix||"")&&null!=r&&(e=vi.getNsPrefix(r.name)),vi.mergeNsAndName(e,t)}}function as(e,t){return e.length>0&&e[e.length-1]===t}var us=Object.defineProperty({TreeError:rs,ParseTreeResult:is,Parser:ss},"__esModule",{value:!0}),cs=us,ls=cs.ParseTreeResult,ps=cs.TreeError;
/**
 * @license
 * Copyright Google Inc. All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */class Ds extends us.Parser{constructor(){super(ki.getHtmlTagDefinition)}parse(e,t,r,n=!1,i){return super.parse(e,t,r,n,i)}}var hs=Ds,ds=Object.defineProperty({ParseTreeResult:ls,TreeError:ps,HtmlParser:hs},"__esModule",{value:!0}),fs=vi.TagContentType;let ms=null;var gs=function(e,t={}){const{canSelfClose:r=!1,allowHtmComponentClosingTags:n=!1,isTagNameCaseSensitive:i=!1,getTagContentType:s}=t;return(ms||(ms=new ds.HtmlParser),ms).parse(e,"angular-html-parser",{tokenizeExpansionForms:!1,interpolationConfig:void 0,canSelfClose:r,allowHtmComponentClosingTags:n},i,s)},Es=Object.defineProperty({TagContentType:fs,parse:gs},"__esModule",{value:!0});const{ParseSourceSpan:Cs,ParseLocation:Ts,ParseSourceFile:ys}=B,{inferParserByLanguage:Ss}=Pn,{HTML_ELEMENT_ATTRIBUTES:_s,HTML_TAGS:vs,isUnknownNamespace:bs}=Di,{hasPragma:Fs}=hi,{Node:As}=Ci,{parseIeConditionalComment:ws}=Si,{locStart:Ns,locEnd:ks}=_i;function Os(e,{recognizeSelfClosing:t,normalizeTagName:r,normalizeAttributeName:n,allowHtmComponentClosingTags:i,isTagNameCaseSensitive:s,getTagContentType:o},a){const u=Es,{RecursiveVisitor:c,visitAll:l}=Wi,{ParseSourceSpan:p}=B,{getHtmlTagDefinition:D}=ki;let{rootNodes:h,errors:d}=u.parse(e,{canSelfClose:t,allowHtmComponentClosingTags:i,isTagNameCaseSensitive:s,getTagContentType:o});if("vue"===a.parser){if(h.some((e=>"docType"===e.type&&"html"===e.value||"element"===e.type&&"html"===e.name.toLowerCase()))){t=!0,r=!0,n=!0,i=!0,s=!1;const o=u.parse(e,{canSelfClose:t,allowHtmComponentClosingTags:i,isTagNameCaseSensitive:s});h=o.rootNodes,d=o.errors}else{const r=e=>{if(!e)return!1;if("element"!==e.type||"template"!==e.name)return!1;const t=e.attrs.find((e=>"lang"===e.name)),r=t&&t.value;return!r||"html"===Ss(r,a)};if(h.some(r)){let n;const o=()=>u.parse(e,{canSelfClose:t,allowHtmComponentClosingTags:i,isTagNameCaseSensitive:s}),a=()=>n||(n=o()),c=e=>a().rootNodes.find((({startSourceSpan:t})=>t&&t.start.offset===e.startSourceSpan.start.offset));for(let e=0;e<h.length;e++){const t=h[e],{endSourceSpan:n,startSourceSpan:i}=t;if(null===n){d=a().errors,h[e]=c(t)||t}else if(r(t)){const r=a(),s=i.end.offset,o=n.start.offset;for(const e of r.errors){const{offset:t}=e.span.start;if(s<t&&t<o){d=[e];break}}h[e]=c(t)||t}}}}}if(d.length>0){const{msg:e,span:{start:t,end:r}}=d[0];throw U(e,{start:{line:t.line+1,column:t.col+1},end:{line:r.line+1,column:r.col+1}})}const f=e=>{const t=e.name.startsWith(":")?e.name.slice(1).split(":")[0]:null,r=e.nameSpan.toString(),n=null!==t&&r.startsWith(`${t}:`),i=n?r.slice(t.length+1):r;e.name=i,e.namespace=t,e.hasExplicitNamespace=n},m=(e,t)=>{const r=e.toLowerCase();return t(r)?r:e};return l(new class extends c{visit(e){(e=>{if("element"===e.type){f(e);for(const t of e.attrs)f(t),t.valueSpan?(t.value=t.valueSpan.toString(),/["']/.test(t.value[0])&&(t.value=t.value.slice(1,-1))):t.value=null}else"comment"===e.type?e.value=e.sourceSpan.toString().slice("\x3c!--".length,-"--\x3e".length):"text"===e.type&&(e.value=e.sourceSpan.toString())})(e),(e=>{if("element"===e.type){const t=D(s?e.name:e.name.toLowerCase());!e.namespace||e.namespace===t.implicitNamespacePrefix||bs(e)?e.tagDefinition=t:e.tagDefinition=D("")}})(e),(e=>{if("element"===e.type&&(!r||e.namespace&&e.namespace!==e.tagDefinition.implicitNamespacePrefix&&!bs(e)||(e.name=m(e.name,(e=>e in vs))),n)){const t=_s[e.name]||Object.create(null);for(const r of e.attrs)r.namespace||(r.name=m(r.name,(r=>e.name in _s&&(r in _s["*"]||r in t))))}})(e),(e=>{e.sourceSpan&&e.endSourceSpan&&(e.sourceSpan=new p(e.sourceSpan.start,e.endSourceSpan.end))})(e)}},h),h}function xs(e,t,r,n=!0){const{frontMatter:i,content:s}=n?q(e):{frontMatter:null,content:e},o=new ys(e,t.filepath),a=new Ts(o,0,0,0),u=a.moveBy(e.length),c={type:"root",sourceSpan:new Cs(a,u),children:Os(s,r,t)};if(i){const e=new Ts(o,0,0,0),t=e.moveBy(i.raw.length);i.sourceSpan=new Cs(e,t),c.children.unshift(i)}const l=new As(c),p=(n,i)=>{const{offset:s}=i,o=xs(e.slice(0,s).replace(/[^\n\r]/g," ")+n,t,r,!1);o.sourceSpan=new Cs(i,M(o.children).sourceSpan.end);const a=o.children[0];return a.length===s?o.children.shift():(a.sourceSpan=new Cs(a.sourceSpan.start.moveBy(s),a.sourceSpan.end),a.value=a.value.slice(s)),o};return l.map((e=>{if("comment"===e.type){const t=ws(e,p);if(t)return t}return e}))}function Is({recognizeSelfClosing:e=!1,normalizeTagName:t=!1,normalizeAttributeName:r=!1,allowHtmComponentClosingTags:n=!1,isTagNameCaseSensitive:i=!1,getTagContentType:s}={}){return{parse:(o,a,u)=>xs(o,u,{recognizeSelfClosing:e,normalizeTagName:t,normalizeAttributeName:r,allowHtmComponentClosingTags:n,isTagNameCaseSensitive:i,getTagContentType:s}),hasPragma:Fs,astFormat:"html",locStart:Ns,locEnd:ks}}var Ps={parsers:{html:Is({recognizeSelfClosing:!0,normalizeTagName:!0,normalizeAttributeName:!0,allowHtmComponentClosingTags:!0}),angular:Is(),vue:Is({recognizeSelfClosing:!0,isTagNameCaseSensitive:!0,getTagContentType:(e,t,r,n)=>{if("html"!==e.toLowerCase()&&!r&&("template"!==e||n.some((({name:e,value:t})=>"lang"===e&&"html"!==t))))return Es.TagContentType.RAW_TEXT}}),lwc:Is()}};export default Ps;
