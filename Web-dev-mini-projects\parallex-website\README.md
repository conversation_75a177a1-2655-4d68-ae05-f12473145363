# Welcome 🖐 to the Parallex Website
This is a basic HTML, CSS, JAVASCRIPT website where images of this website has their position fixed that results in giving the parallex look and that is why it is called Parallex Website. This gives the look of modern website.


## 💻Tech Stack
<br>

![HTML](https://img.shields.io/badge/html5%20-%23E34F26.svg?&style=for-the-badge&logo=html5&logoColor=white)
![CSS](https://img.shields.io/badge/css3%20-%231572B6.svg?&style=for-the-badge&logo=css3&logoColor=white)
![JS](https://img.shields.io/badge/javascript%20-%23323330.svg?&style=for-the-badge&logo=javascript&logoColor=%23F7DF1E)

<br>


### How to get the project on your local machine:

---

- Download or clone the repository

```
git clone https://github.com/Ayushparikh-code/Web-dev-mini-projects.git
```

- Go to the directory
- Open parallex-website folder
- Open the terminal and run 
```
node app.js
```
- Open *localhost:3000* in your browser.

*** 
###Remember: 
If you are using node then you need to reload your server everytime you make changes in your file. Otherwise if you have nodemon installed, you can use nodemon. 
***


<br>

##Screenshot

![Demo1](public/images/demo.png)
<br>


## Happy Coding!