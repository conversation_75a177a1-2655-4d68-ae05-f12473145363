# Nextflix 
A simple Netflix Clone made using [Next.js](https://nextjs.org/) ⚡

Currently, I have implemented the basic UI with media details fetch functionality.


Deployed it using vercel [here](https://nextflix-azure.vercel.app/).

Please leave a ⭐ as motivation if you liked the implementation 😄


## Demo
![Demo](/public/assets/demo.gif)
<br />
<br />

## Built with
* [Next.js](https://nextjs.org/)
* [Typescript](https://www.typescriptlang.org/)
* [Sass](https://sass-lang.com/)
* [TMDB API](https://www.themoviedb.org/)


## Running the project
This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

In the project directory, you can run:

#### `yarn start`

It runs the app in the development mode.<br />
Open [http://localhost:3000](http://localhost:3000) to view it in the browser. 


