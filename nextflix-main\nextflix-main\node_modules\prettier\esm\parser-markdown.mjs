var e=function(){for(var e={},r=0;r<arguments.length;r++){var n=arguments[r];for(var u in n)t.call(n,u)&&(e[u]=n[u])}return e},t=Object.prototype.hasOwnProperty;var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function u(e){var t={exports:{}};return e(t,t.exports),t.exports}var i=void 0!==i?i:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},o=[],a=[],c="undefined"!=typeof Uint8Array?Uint8Array:Array,s=!1;function l(){s=!0;for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t=0,r=e.length;t<r;++t)o[t]=e[t],a[e.charCodeAt(t)]=t;a["-".charCodeAt(0)]=62,a["_".charCodeAt(0)]=63}function f(e){return o[e>>18&63]+o[e>>12&63]+o[e>>6&63]+o[63&e]}function D(e,t,r){for(var n,u=[],i=t;i<r;i+=3)n=(e[i]<<16)+(e[i+1]<<8)+e[i+2],u.push(f(n));return u.join("")}function p(e){var t;s||l();for(var r=e.length,n=r%3,u="",i=[],a=16383,c=0,f=r-n;c<f;c+=a)i.push(D(e,c,c+a>f?f:c+a));return 1===n?(t=e[r-1],u+=o[t>>2],u+=o[t<<4&63],u+="=="):2===n&&(t=(e[r-2]<<8)+e[r-1],u+=o[t>>10],u+=o[t>>4&63],u+=o[t<<2&63],u+="="),i.push(u),i.join("")}function h(e,t,r,n,u){var i,o,a=8*u-n-1,c=(1<<a)-1,s=c>>1,l=-7,f=r?u-1:0,D=r?-1:1,p=e[t+f];for(f+=D,i=p&(1<<-l)-1,p>>=-l,l+=a;l>0;i=256*i+e[t+f],f+=D,l-=8);for(o=i&(1<<-l)-1,i>>=-l,l+=n;l>0;o=256*o+e[t+f],f+=D,l-=8);if(0===i)i=1-s;else{if(i===c)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,n),i-=s}return(p?-1:1)*o*Math.pow(2,i-n)}function d(e,t,r,n,u,i){var o,a,c,s=8*i-u-1,l=(1<<s)-1,f=l>>1,D=23===u?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:i-1,h=n?1:-1,d=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(c=Math.pow(2,-o))<1&&(o--,c*=2),(t+=o+f>=1?D/c:D*Math.pow(2,1-f))*c>=2&&(o++,c/=2),o+f>=l?(a=0,o=l):o+f>=1?(a=(t*c-1)*Math.pow(2,u),o+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,u),o=0));u>=8;e[r+p]=255&a,p+=h,a/=256,u-=8);for(o=o<<u|a,s+=u;s>0;e[r+p]=255&o,p+=h,o/=256,s-=8);e[r+p-h]|=128*d}var g={}.toString,m=Array.isArray||function(e){return"[object Array]"==g.call(e)};function E(){return v.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function y(e,t){if(E()<t)throw new RangeError("Invalid typed array length");return v.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=v.prototype:(null===e&&(e=new v(t)),e.length=t),e}function v(e,t,r){if(!(v.TYPED_ARRAY_SUPPORT||this instanceof v))return new v(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return A(this,e)}return b(this,e,t,r)}function b(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);v.TYPED_ARRAY_SUPPORT?(e=t).__proto__=v.prototype:e=w(e,t);return e}(e,t,r,n):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!v.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|O(t,r),u=(e=y(e,n)).write(t,r);u!==n&&(e=e.slice(0,u));return e}(e,t,r):function(e,t){if(k(t)){var r=0|F(t.length);return 0===(e=y(e,r)).length||t.copy(e,0,0,r),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(n=t.length)!=n?y(e,0):w(e,t);if("Buffer"===t.type&&m(t.data))return w(e,t.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function C(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function A(e,t){if(C(t),e=y(e,t<0?0:0|F(t)),!v.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function w(e,t){var r=t.length<0?0:0|F(t.length);e=y(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function F(e){if(e>=E())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+E().toString(16)+" bytes");return 0|e}function k(e){return!(null==e||!e._isBuffer)}function O(e,t){if(k(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return Q(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return ee(e).length;default:if(n)return Q(e).length;t=(""+t).toLowerCase(),n=!0}}function T(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return M(this,t,r);case"utf8":case"utf-8":return _(this,t,r);case"ascii":return U(this,t,r);case"latin1":case"binary":return $(this,t,r);case"base64":return j(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return z(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function S(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function I(e,t,r,n,u){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=u?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(u)return-1;r=e.length-1}else if(r<0){if(!u)return-1;r=0}if("string"==typeof t&&(t=v.from(t,n)),k(t))return 0===t.length?-1:R(e,t,r,n,u);if("number"==typeof t)return t&=255,v.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?u?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):R(e,[t],r,n,u);throw new TypeError("val must be string, number or Buffer")}function R(e,t,r,n,u){var i,o=1,a=e.length,c=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;o=2,a/=2,c/=2,r/=2}function s(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(u){var l=-1;for(i=r;i<a;i++)if(s(e,i)===s(t,-1===l?0:i-l)){if(-1===l&&(l=i),i-l+1===c)return l*o}else-1!==l&&(i-=i-l),l=-1}else for(r+c>a&&(r=a-c),i=r;i>=0;i--){for(var f=!0,D=0;D<c;D++)if(s(e,i+D)!==s(t,D)){f=!1;break}if(f)return i}return-1}function x(e,t,r,n){r=Number(r)||0;var u=e.length-r;n?(n=Number(n))>u&&(n=u):n=u;var i=t.length;if(i%2!=0)throw new TypeError("Invalid hex string");n>i/2&&(n=i/2);for(var o=0;o<n;++o){var a=parseInt(t.substr(2*o,2),16);if(isNaN(a))return o;e[r+o]=a}return o}function B(e,t,r,n){return te(Q(t,e.length-r),e,r,n)}function N(e,t,r,n){return te(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function L(e,t,r,n){return N(e,t,r,n)}function P(e,t,r,n){return te(ee(t),e,r,n)}function q(e,t,r,n){return te(function(e,t){for(var r,n,u,i=[],o=0;o<e.length&&!((t-=2)<0);++o)n=(r=e.charCodeAt(o))>>8,u=r%256,i.push(u),i.push(n);return i}(t,e.length-r),e,r,n)}function j(e,t,r){return 0===t&&r===e.length?p(e):p(e.slice(t,r))}function _(e,t,r){r=Math.min(e.length,r);for(var n=[],u=t;u<r;){var i,o,a,c,s=e[u],l=null,f=s>239?4:s>223?3:s>191?2:1;if(u+f<=r)switch(f){case 1:s<128&&(l=s);break;case 2:128==(192&(i=e[u+1]))&&(c=(31&s)<<6|63&i)>127&&(l=c);break;case 3:i=e[u+1],o=e[u+2],128==(192&i)&&128==(192&o)&&(c=(15&s)<<12|(63&i)<<6|63&o)>2047&&(c<55296||c>57343)&&(l=c);break;case 4:i=e[u+1],o=e[u+2],a=e[u+3],128==(192&i)&&128==(192&o)&&128==(192&a)&&(c=(15&s)<<18|(63&i)<<12|(63&o)<<6|63&a)>65535&&c<1114112&&(l=c)}null===l?(l=65533,f=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),u+=f}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}v.TYPED_ARRAY_SUPPORT=void 0===i.TYPED_ARRAY_SUPPORT||i.TYPED_ARRAY_SUPPORT,v.poolSize=8192,v._augment=function(e){return e.__proto__=v.prototype,e},v.from=function(e,t,r){return b(null,e,t,r)},v.TYPED_ARRAY_SUPPORT&&(v.prototype.__proto__=Uint8Array.prototype,v.__proto__=Uint8Array),v.alloc=function(e,t,r){return function(e,t,r,n){return C(t),t<=0?y(e,t):void 0!==r?"string"==typeof n?y(e,t).fill(r,n):y(e,t).fill(r):y(e,t)}(null,e,t,r)},v.allocUnsafe=function(e){return A(null,e)},v.allocUnsafeSlow=function(e){return A(null,e)},v.isBuffer=function(e){return null!=e&&(!!e._isBuffer||re(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&re(e.slice(0,0))}(e))},v.compare=function(e,t){if(!k(e)||!k(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,u=0,i=Math.min(r,n);u<i;++u)if(e[u]!==t[u]){r=e[u],n=t[u];break}return r<n?-1:n<r?1:0},v.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},v.concat=function(e,t){if(!m(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return v.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=v.allocUnsafe(t),u=0;for(r=0;r<e.length;++r){var i=e[r];if(!k(i))throw new TypeError('"list" argument must be an Array of Buffers');i.copy(n,u),u+=i.length}return n},v.byteLength=O,v.prototype._isBuffer=!0,v.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)S(this,t,t+1);return this},v.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)S(this,t,t+3),S(this,t+1,t+2);return this},v.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)S(this,t,t+7),S(this,t+1,t+6),S(this,t+2,t+5),S(this,t+3,t+4);return this},v.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?_(this,0,e):T.apply(this,arguments)},v.prototype.equals=function(e){if(!k(e))throw new TypeError("Argument must be a Buffer");return this===e||0===v.compare(this,e)},v.prototype.inspect=function(){var e="";return this.length>0&&(e=this.toString("hex",0,50).match(/.{2}/g).join(" "),this.length>50&&(e+=" ... ")),"<Buffer "+e+">"},v.prototype.compare=function(e,t,r,n,u){if(!k(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===u&&(u=this.length),t<0||r>e.length||n<0||u>this.length)throw new RangeError("out of range index");if(n>=u&&t>=r)return 0;if(n>=u)return-1;if(t>=r)return 1;if(this===e)return 0;for(var i=(u>>>=0)-(n>>>=0),o=(r>>>=0)-(t>>>=0),a=Math.min(i,o),c=this.slice(n,u),s=e.slice(t,r),l=0;l<a;++l)if(c[l]!==s[l]){i=c[l],o=s[l];break}return i<o?-1:o<i?1:0},v.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},v.prototype.indexOf=function(e,t,r){return I(this,e,t,r,!0)},v.prototype.lastIndexOf=function(e,t,r){return I(this,e,t,r,!1)},v.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var u=this.length-t;if((void 0===r||r>u)&&(r=u),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var i=!1;;)switch(n){case"hex":return x(this,e,t,r);case"utf8":case"utf-8":return B(this,e,t,r);case"ascii":return N(this,e,t,r);case"latin1":case"binary":return L(this,e,t,r);case"base64":return P(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return q(this,e,t,r);default:if(i)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),i=!0}},v.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function U(e,t,r){var n="";r=Math.min(e.length,r);for(var u=t;u<r;++u)n+=String.fromCharCode(127&e[u]);return n}function $(e,t,r){var n="";r=Math.min(e.length,r);for(var u=t;u<r;++u)n+=String.fromCharCode(e[u]);return n}function M(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var u="",i=t;i<r;++i)u+=K(e[i]);return u}function z(e,t,r){for(var n=e.slice(t,r),u="",i=0;i<n.length;i+=2)u+=String.fromCharCode(n[i]+256*n[i+1]);return u}function G(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function V(e,t,r,n,u,i){if(!k(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>u||t<i)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function Y(e,t,r,n){t<0&&(t=65535+t+1);for(var u=0,i=Math.min(e.length-r,2);u<i;++u)e[r+u]=(t&255<<8*(n?u:1-u))>>>8*(n?u:1-u)}function H(e,t,r,n){t<0&&(t=4294967295+t+1);for(var u=0,i=Math.min(e.length-r,4);u<i;++u)e[r+u]=t>>>8*(n?u:3-u)&255}function X(e,t,r,n,u,i){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function W(e,t,r,n,u){return u||X(e,0,r,4),d(e,t,r,n,23,4),r+4}function Z(e,t,r,n,u){return u||X(e,0,r,8),d(e,t,r,n,52,8),r+8}v.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),v.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=v.prototype;else{var u=t-e;r=new v(u,void 0);for(var i=0;i<u;++i)r[i]=this[i+e]}return r},v.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||G(e,t,this.length);for(var n=this[e],u=1,i=0;++i<t&&(u*=256);)n+=this[e+i]*u;return n},v.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||G(e,t,this.length);for(var n=this[e+--t],u=1;t>0&&(u*=256);)n+=this[e+--t]*u;return n},v.prototype.readUInt8=function(e,t){return t||G(e,1,this.length),this[e]},v.prototype.readUInt16LE=function(e,t){return t||G(e,2,this.length),this[e]|this[e+1]<<8},v.prototype.readUInt16BE=function(e,t){return t||G(e,2,this.length),this[e]<<8|this[e+1]},v.prototype.readUInt32LE=function(e,t){return t||G(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},v.prototype.readUInt32BE=function(e,t){return t||G(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},v.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||G(e,t,this.length);for(var n=this[e],u=1,i=0;++i<t&&(u*=256);)n+=this[e+i]*u;return n>=(u*=128)&&(n-=Math.pow(2,8*t)),n},v.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||G(e,t,this.length);for(var n=t,u=1,i=this[e+--n];n>0&&(u*=256);)i+=this[e+--n]*u;return i>=(u*=128)&&(i-=Math.pow(2,8*t)),i},v.prototype.readInt8=function(e,t){return t||G(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},v.prototype.readInt16LE=function(e,t){t||G(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},v.prototype.readInt16BE=function(e,t){t||G(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},v.prototype.readInt32LE=function(e,t){return t||G(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},v.prototype.readInt32BE=function(e,t){return t||G(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},v.prototype.readFloatLE=function(e,t){return t||G(e,4,this.length),h(this,e,!0,23,4)},v.prototype.readFloatBE=function(e,t){return t||G(e,4,this.length),h(this,e,!1,23,4)},v.prototype.readDoubleLE=function(e,t){return t||G(e,8,this.length),h(this,e,!0,52,8)},v.prototype.readDoubleBE=function(e,t){return t||G(e,8,this.length),h(this,e,!1,52,8)},v.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||V(this,e,t,r,Math.pow(2,8*r)-1,0);var u=1,i=0;for(this[t]=255&e;++i<r&&(u*=256);)this[t+i]=e/u&255;return t+r},v.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||V(this,e,t,r,Math.pow(2,8*r)-1,0);var u=r-1,i=1;for(this[t+u]=255&e;--u>=0&&(i*=256);)this[t+u]=e/i&255;return t+r},v.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,1,255,0),v.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},v.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,2,65535,0),v.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Y(this,e,t,!0),t+2},v.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,2,65535,0),v.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Y(this,e,t,!1),t+2},v.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,4,4294967295,0),v.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):H(this,e,t,!0),t+4},v.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,4,4294967295,0),v.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):H(this,e,t,!1),t+4},v.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var u=Math.pow(2,8*r-1);V(this,e,t,r,u-1,-u)}var i=0,o=1,a=0;for(this[t]=255&e;++i<r&&(o*=256);)e<0&&0===a&&0!==this[t+i-1]&&(a=1),this[t+i]=(e/o>>0)-a&255;return t+r},v.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var u=Math.pow(2,8*r-1);V(this,e,t,r,u-1,-u)}var i=r-1,o=1,a=0;for(this[t+i]=255&e;--i>=0&&(o*=256);)e<0&&0===a&&0!==this[t+i+1]&&(a=1),this[t+i]=(e/o>>0)-a&255;return t+r},v.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,1,127,-128),v.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},v.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,2,32767,-32768),v.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Y(this,e,t,!0),t+2},v.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,2,32767,-32768),v.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Y(this,e,t,!1),t+2},v.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,4,2147483647,-2147483648),v.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):H(this,e,t,!0),t+4},v.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||V(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),v.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):H(this,e,t,!1),t+4},v.prototype.writeFloatLE=function(e,t,r){return W(this,e,t,!0,r)},v.prototype.writeFloatBE=function(e,t,r){return W(this,e,t,!1,r)},v.prototype.writeDoubleLE=function(e,t,r){return Z(this,e,t,!0,r)},v.prototype.writeDoubleBE=function(e,t,r){return Z(this,e,t,!1,r)},v.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var u,i=n-r;if(this===e&&r<t&&t<n)for(u=i-1;u>=0;--u)e[u+t]=this[u+r];else if(i<1e3||!v.TYPED_ARRAY_SUPPORT)for(u=0;u<i;++u)e[u+t]=this[u+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+i),t);return i},v.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var u=e.charCodeAt(0);u<256&&(e=u)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!v.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var i;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=k(e)?e:Q(new v(e,n).toString()),a=o.length;for(i=0;i<r-t;++i)this[i+t]=o[i%a]}return this};var J=/[^+\/0-9A-Za-z-_]/g;function K(e){return e<16?"0"+e.toString(16):e.toString(16)}function Q(e,t){var r;t=t||1/0;for(var n=e.length,u=null,i=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!u){if(r>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(o+1===n){(t-=3)>-1&&i.push(239,191,189);continue}u=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),u=r;continue}r=65536+(u-55296<<10|r-56320)}else u&&(t-=3)>-1&&i.push(239,191,189);if(u=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return i}function ee(e){return function(e){var t,r,n,u,i,o;s||l();var f=e.length;if(f%4>0)throw new Error("Invalid string. Length must be a multiple of 4");i="="===e[f-2]?2:"="===e[f-1]?1:0,o=new c(3*f/4-i),n=i>0?f-4:f;var D=0;for(t=0,r=0;t<n;t+=4,r+=3)u=a[e.charCodeAt(t)]<<18|a[e.charCodeAt(t+1)]<<12|a[e.charCodeAt(t+2)]<<6|a[e.charCodeAt(t+3)],o[D++]=u>>16&255,o[D++]=u>>8&255,o[D++]=255&u;return 2===i?(u=a[e.charCodeAt(t)]<<2|a[e.charCodeAt(t+1)]>>4,o[D++]=255&u):1===i&&(u=a[e.charCodeAt(t)]<<10|a[e.charCodeAt(t+1)]<<4|a[e.charCodeAt(t+2)]>>2,o[D++]=u>>8&255,o[D++]=255&u),o}(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(J,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function te(e,t,r,n){for(var u=0;u<n&&!(u+r>=t.length||u>=e.length);++u)t[u+r]=e[u];return u}function re(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}function ne(){throw new Error("setTimeout has not been defined")}function ue(){throw new Error("clearTimeout has not been defined")}var ie=ne,oe=ue;function ae(e){if(ie===setTimeout)return setTimeout(e,0);if((ie===ne||!ie)&&setTimeout)return ie=setTimeout,setTimeout(e,0);try{return ie(e,0)}catch(t){try{return ie.call(null,e,0)}catch(t){return ie.call(this,e,0)}}}"function"==typeof i.setTimeout&&(ie=setTimeout),"function"==typeof i.clearTimeout&&(oe=clearTimeout);var ce,se=[],le=!1,fe=-1;function De(){le&&ce&&(le=!1,ce.length?se=ce.concat(se):fe=-1,se.length&&pe())}function pe(){if(!le){var e=ae(De);le=!0;for(var t=se.length;t;){for(ce=se,se=[];++fe<t;)ce&&ce[fe].run();fe=-1,t=se.length}ce=null,le=!1,function(e){if(oe===clearTimeout)return clearTimeout(e);if((oe===ue||!oe)&&clearTimeout)return oe=clearTimeout,clearTimeout(e);try{oe(e)}catch(t){try{return oe.call(null,e)}catch(t){return oe.call(this,e)}}}(e)}}function he(e,t){this.fun=e,this.array=t}he.prototype.run=function(){this.fun.apply(null,this.array)};function de(){}var ge=de,me=de,Ee=de,ye=de,ve=de,be=de,Ce=de;var Ae=i.performance||{},we=Ae.now||Ae.mozNow||Ae.msNow||Ae.oNow||Ae.webkitNow||function(){return(new Date).getTime()};var Fe=new Date;var ke={nextTick:function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];se.push(new he(e,t)),1!==se.length||le||ae(pe)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:ge,addListener:me,once:Ee,off:ye,removeListener:ve,removeAllListeners:be,emit:Ce,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*we.call(Ae),r=Math.floor(t),n=Math.floor(t%1*1e9);return e&&(r-=e[0],(n-=e[1])<0&&(r--,n+=1e9)),[r,n]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-Fe)/1e3}},Oe="function"==typeof Object.create?function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e},Te=/%[sdj%]/g;function Se(e){if(!Ve(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(Ne(arguments[r]));return t.join(" ")}r=1;for(var n=arguments,u=n.length,i=String(e).replace(Te,(function(e){if("%%"===e)return"%";if(r>=u)return e;switch(e){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(e){return"[Circular]"}default:return e}})),o=n[r];r<u;o=n[++r])Me(o)||!We(o)?i+=" "+o:i+=" "+Ne(o);return i}function Ie(e,t){if(He(i.process))return function(){return Ie(e,t).apply(this,arguments)};var r=!1;return function(){return r||(console.error(t),r=!0),e.apply(this,arguments)}}var Re,xe={};function Be(e){if(He(Re)&&(Re=""),e=e.toUpperCase(),!xe[e])if(new RegExp("\\b"+e+"\\b","i").test(Re)){xe[e]=function(){var t=Se.apply(null,arguments);console.error("%s %d: %s",e,0,t)}}else xe[e]=function(){};return xe[e]}function Ne(e,t){var r={seen:[],stylize:Pe};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),$e(t)?r.showHidden=t:t&&ot(r,t),He(r.showHidden)&&(r.showHidden=!1),He(r.depth)&&(r.depth=2),He(r.colors)&&(r.colors=!1),He(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=Le),qe(r,e,r.depth)}function Le(e,t){var r=Ne.styles[t];return r?"\x1b["+Ne.colors[r][0]+"m"+e+"\x1b["+Ne.colors[r][1]+"m":e}function Pe(e,t){return e}function qe(e,t,r){if(e.customInspect&&t&&Ke(t.inspect)&&t.inspect!==Ne&&(!t.constructor||t.constructor.prototype!==t)){var n=t.inspect(r,e);return Ve(n)||(n=qe(e,n,r)),n}var u=function(e,t){if(He(t))return e.stylize("undefined","undefined");if(Ve(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}if(Ge(t))return e.stylize(""+t,"number");if($e(t))return e.stylize(""+t,"boolean");if(Me(t))return e.stylize("null","null")}(e,t);if(u)return u;var i=Object.keys(t),o=function(e){var t={};return e.forEach((function(e,r){t[e]=!0})),t}(i);if(e.showHidden&&(i=Object.getOwnPropertyNames(t)),Je(t)&&(i.indexOf("message")>=0||i.indexOf("description")>=0))return je(t);if(0===i.length){if(Ke(t)){var a=t.name?": "+t.name:"";return e.stylize("[Function"+a+"]","special")}if(Xe(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(Ze(t))return e.stylize(Date.prototype.toString.call(t),"date");if(Je(t))return je(t)}var c,s="",l=!1,f=["{","}"];(Ue(t)&&(l=!0,f=["[","]"]),Ke(t))&&(s=" [Function"+(t.name?": "+t.name:"")+"]");return Xe(t)&&(s=" "+RegExp.prototype.toString.call(t)),Ze(t)&&(s=" "+Date.prototype.toUTCString.call(t)),Je(t)&&(s=" "+je(t)),0!==i.length||l&&0!=t.length?r<0?Xe(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),c=l?function(e,t,r,n,u){for(var i=[],o=0,a=t.length;o<a;++o)at(t,String(o))?i.push(_e(e,t,r,n,String(o),!0)):i.push("");return u.forEach((function(u){u.match(/^\d+$/)||i.push(_e(e,t,r,n,u,!0))})),i}(e,t,r,o,i):i.map((function(n){return _e(e,t,r,o,n,l)})),e.seen.pop(),function(e,t,r){if(e.reduce((function(e,t){return t.indexOf("\n"),e+t.replace(/\u001b\[\d\d?m/g,"").length+1}),0)>60)return r[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1];return r[0]+t+" "+e.join(", ")+" "+r[1]}(c,s,f)):f[0]+s+f[1]}function je(e){return"["+Error.prototype.toString.call(e)+"]"}function _e(e,t,r,n,u,i){var o,a,c;if((c=Object.getOwnPropertyDescriptor(t,u)||{value:t[u]}).get?a=c.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):c.set&&(a=e.stylize("[Setter]","special")),at(n,u)||(o="["+u+"]"),a||(e.seen.indexOf(c.value)<0?(a=Me(r)?qe(e,c.value,null):qe(e,c.value,r-1)).indexOf("\n")>-1&&(a=i?a.split("\n").map((function(e){return"  "+e})).join("\n").substr(2):"\n"+a.split("\n").map((function(e){return"   "+e})).join("\n")):a=e.stylize("[Circular]","special")),He(o)){if(i&&u.match(/^\d+$/))return a;(o=JSON.stringify(""+u)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(o=o.substr(1,o.length-2),o=e.stylize(o,"name")):(o=o.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),o=e.stylize(o,"string"))}return o+": "+a}function Ue(e){return Array.isArray(e)}function $e(e){return"boolean"==typeof e}function Me(e){return null===e}function ze(e){return null==e}function Ge(e){return"number"==typeof e}function Ve(e){return"string"==typeof e}function Ye(e){return"symbol"==typeof e}function He(e){return void 0===e}function Xe(e){return We(e)&&"[object RegExp]"===tt(e)}function We(e){return"object"==typeof e&&null!==e}function Ze(e){return We(e)&&"[object Date]"===tt(e)}function Je(e){return We(e)&&("[object Error]"===tt(e)||e instanceof Error)}function Ke(e){return"function"==typeof e}function Qe(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e}function et(e){return v.isBuffer(e)}function tt(e){return Object.prototype.toString.call(e)}function rt(e){return e<10?"0"+e.toString(10):e.toString(10)}Ne.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},Ne.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};var nt=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function ut(){var e=new Date,t=[rt(e.getHours()),rt(e.getMinutes()),rt(e.getSeconds())].join(":");return[e.getDate(),nt[e.getMonth()],t].join(" ")}function it(){console.log("%s - %s",ut(),Se.apply(null,arguments))}function ot(e,t){if(!t||!We(t))return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e}function at(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var ct={inherits:Oe,_extend:ot,log:it,isBuffer:et,isPrimitive:Qe,isFunction:Ke,isError:Je,isDate:Ze,isObject:We,isRegExp:Xe,isUndefined:He,isSymbol:Ye,isString:Ve,isNumber:Ge,isNullOrUndefined:ze,isNull:Me,isBoolean:$e,isArray:Ue,inspect:Ne,deprecate:Ie,format:Se,debuglog:Be},st=Object.freeze({__proto__:null,format:Se,deprecate:Ie,debuglog:Be,inspect:Ne,isArray:Ue,isBoolean:$e,isNull:Me,isNullOrUndefined:ze,isNumber:Ge,isString:Ve,isSymbol:Ye,isUndefined:He,isRegExp:Xe,isObject:We,isDate:Ze,isError:Je,isFunction:Ke,isPrimitive:Qe,isBuffer:et,log:it,inherits:Oe,_extend:ot,default:ct}),lt=u((function(e){"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}})),ft=n(st),Dt=u((function(e){try{var t=ft;if("function"!=typeof t.inherits)throw"";e.exports=t.inherits}catch(t){e.exports=lt}})),pt=function(t){var r,n,u;for(n in Dt(o,t),Dt(i,o),r=o.prototype)(u=r[n])&&"object"==typeof u&&(r[n]="concat"in u?u.concat():e(u));return o;function i(e){return t.apply(this,e)}function o(){return this instanceof o?t.apply(this,arguments):new i(arguments)}};var ht=function(e,t,r){return function(){var n=r||this,u=n[e];return n[e]=!t,i;function i(){n[e]=u}}};var dt=function(e){var t=String(e),r=[],n=/\r?\n|\r/g;for(;n.exec(t);)r.push(n.lastIndex);return r.push(t.length+1),{toPoint:u,toPosition:u,toOffset:function(e){var t,n=e&&e.line,u=e&&e.column;isNaN(n)||isNaN(u)||!(n-1 in r)||(t=(r[n-2]||0)+u-1||0);return t>-1&&t<r[r.length-1]?t:-1}};function u(e){var t=-1;if(e>-1&&e<r[r.length-1])for(;++t<r.length;)if(r[t]>e)return{line:t+1,column:e-(r[t-1]||0)+1,offset:e};return{}}};var gt=function(e,t){return function(r){var n,u=0,i=r.indexOf("\\"),o=e[t],a=[];for(;-1!==i;)a.push(r.slice(u,i)),u=i+1,(n=r.charAt(u))&&-1!==o.indexOf(n)||a.push("\\"),i=r.indexOf("\\",u+1);return a.push(r.slice(u)),a.join("")}};var mt={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Acirc:"\xc2",Agrave:"\xc0",Aring:"\xc5",Atilde:"\xc3",Auml:"\xc4",COPY:"\xa9",Ccedil:"\xc7",ETH:"\xd0",Eacute:"\xc9",Ecirc:"\xca",Egrave:"\xc8",Euml:"\xcb",GT:">",Iacute:"\xcd",Icirc:"\xce",Igrave:"\xcc",Iuml:"\xcf",LT:"<",Ntilde:"\xd1",Oacute:"\xd3",Ocirc:"\xd4",Ograve:"\xd2",Oslash:"\xd8",Otilde:"\xd5",Ouml:"\xd6",QUOT:'"',REG:"\xae",THORN:"\xde",Uacute:"\xda",Ucirc:"\xdb",Ugrave:"\xd9",Uuml:"\xdc",Yacute:"\xdd",aacute:"\xe1",acirc:"\xe2",acute:"\xb4",aelig:"\xe6",agrave:"\xe0",amp:"&",aring:"\xe5",atilde:"\xe3",auml:"\xe4",brvbar:"\xa6",ccedil:"\xe7",cedil:"\xb8",cent:"\xa2",copy:"\xa9",curren:"\xa4",deg:"\xb0",divide:"\xf7",eacute:"\xe9",ecirc:"\xea",egrave:"\xe8",eth:"\xf0",euml:"\xeb",frac12:"\xbd",frac14:"\xbc",frac34:"\xbe",gt:">",iacute:"\xed",icirc:"\xee",iexcl:"\xa1",igrave:"\xec",iquest:"\xbf",iuml:"\xef",laquo:"\xab",lt:"<",macr:"\xaf",micro:"\xb5",middot:"\xb7",nbsp:"\xa0",not:"\xac",ntilde:"\xf1",oacute:"\xf3",ocirc:"\xf4",ograve:"\xf2",ordf:"\xaa",ordm:"\xba",oslash:"\xf8",otilde:"\xf5",ouml:"\xf6",para:"\xb6",plusmn:"\xb1",pound:"\xa3",quot:'"',raquo:"\xbb",reg:"\xae",sect:"\xa7",shy:"\xad",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",szlig:"\xdf",thorn:"\xfe",times:"\xd7",uacute:"\xfa",ucirc:"\xfb",ugrave:"\xf9",uml:"\xa8",uuml:"\xfc",yacute:"\xfd",yen:"\xa5",yuml:"\xff"},Et={0:"\ufffd",128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"},yt=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=48&&t<=57};var vt=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=97&&t<=102||t>=65&&t<=70||t>=48&&t<=57};var bt=function(e){var t="string"==typeof e?e.charCodeAt(0):e;return t>=97&&t<=122||t>=65&&t<=90};var Ct=function(e){return bt(e)||yt(e)};var At={AEli:"\xc6",AElig:"\xc6",AM:"&",AMP:"&",Aacut:"\xc1",Aacute:"\xc1",Abreve:"\u0102",Acir:"\xc2",Acirc:"\xc2",Acy:"\u0410",Afr:"\ud835\udd04",Agrav:"\xc0",Agrave:"\xc0",Alpha:"\u0391",Amacr:"\u0100",And:"\u2a53",Aogon:"\u0104",Aopf:"\ud835\udd38",ApplyFunction:"\u2061",Arin:"\xc5",Aring:"\xc5",Ascr:"\ud835\udc9c",Assign:"\u2254",Atild:"\xc3",Atilde:"\xc3",Aum:"\xc4",Auml:"\xc4",Backslash:"\u2216",Barv:"\u2ae7",Barwed:"\u2306",Bcy:"\u0411",Because:"\u2235",Bernoullis:"\u212c",Beta:"\u0392",Bfr:"\ud835\udd05",Bopf:"\ud835\udd39",Breve:"\u02d8",Bscr:"\u212c",Bumpeq:"\u224e",CHcy:"\u0427",COP:"\xa9",COPY:"\xa9",Cacute:"\u0106",Cap:"\u22d2",CapitalDifferentialD:"\u2145",Cayleys:"\u212d",Ccaron:"\u010c",Ccedi:"\xc7",Ccedil:"\xc7",Ccirc:"\u0108",Cconint:"\u2230",Cdot:"\u010a",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"\u212d",Chi:"\u03a7",CircleDot:"\u2299",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201d",CloseCurlyQuote:"\u2019",Colon:"\u2237",Colone:"\u2a74",Congruent:"\u2261",Conint:"\u222f",ContourIntegral:"\u222e",Copf:"\u2102",Coproduct:"\u2210",CounterClockwiseContourIntegral:"\u2233",Cross:"\u2a2f",Cscr:"\ud835\udc9e",Cup:"\u22d3",CupCap:"\u224d",DD:"\u2145",DDotrahd:"\u2911",DJcy:"\u0402",DScy:"\u0405",DZcy:"\u040f",Dagger:"\u2021",Darr:"\u21a1",Dashv:"\u2ae4",Dcaron:"\u010e",Dcy:"\u0414",Del:"\u2207",Delta:"\u0394",Dfr:"\ud835\udd07",DiacriticalAcute:"\xb4",DiacriticalDot:"\u02d9",DiacriticalDoubleAcute:"\u02dd",DiacriticalGrave:"`",DiacriticalTilde:"\u02dc",Diamond:"\u22c4",DifferentialD:"\u2146",Dopf:"\ud835\udd3b",Dot:"\xa8",DotDot:"\u20dc",DotEqual:"\u2250",DoubleContourIntegral:"\u222f",DoubleDot:"\xa8",DoubleDownArrow:"\u21d3",DoubleLeftArrow:"\u21d0",DoubleLeftRightArrow:"\u21d4",DoubleLeftTee:"\u2ae4",DoubleLongLeftArrow:"\u27f8",DoubleLongLeftRightArrow:"\u27fa",DoubleLongRightArrow:"\u27f9",DoubleRightArrow:"\u21d2",DoubleRightTee:"\u22a8",DoubleUpArrow:"\u21d1",DoubleUpDownArrow:"\u21d5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21f5",DownBreve:"\u0311",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295e",DownLeftVector:"\u21bd",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295f",DownRightVector:"\u21c1",DownRightVectorBar:"\u2957",DownTee:"\u22a4",DownTeeArrow:"\u21a7",Downarrow:"\u21d3",Dscr:"\ud835\udc9f",Dstrok:"\u0110",ENG:"\u014a",ET:"\xd0",ETH:"\xd0",Eacut:"\xc9",Eacute:"\xc9",Ecaron:"\u011a",Ecir:"\xca",Ecirc:"\xca",Ecy:"\u042d",Edot:"\u0116",Efr:"\ud835\udd08",Egrav:"\xc8",Egrave:"\xc8",Element:"\u2208",Emacr:"\u0112",EmptySmallSquare:"\u25fb",EmptyVerySmallSquare:"\u25ab",Eogon:"\u0118",Eopf:"\ud835\udd3c",Epsilon:"\u0395",Equal:"\u2a75",EqualTilde:"\u2242",Equilibrium:"\u21cc",Escr:"\u2130",Esim:"\u2a73",Eta:"\u0397",Eum:"\xcb",Euml:"\xcb",Exists:"\u2203",ExponentialE:"\u2147",Fcy:"\u0424",Ffr:"\ud835\udd09",FilledSmallSquare:"\u25fc",FilledVerySmallSquare:"\u25aa",Fopf:"\ud835\udd3d",ForAll:"\u2200",Fouriertrf:"\u2131",Fscr:"\u2131",GJcy:"\u0403",G:">",GT:">",Gamma:"\u0393",Gammad:"\u03dc",Gbreve:"\u011e",Gcedil:"\u0122",Gcirc:"\u011c",Gcy:"\u0413",Gdot:"\u0120",Gfr:"\ud835\udd0a",Gg:"\u22d9",Gopf:"\ud835\udd3e",GreaterEqual:"\u2265",GreaterEqualLess:"\u22db",GreaterFullEqual:"\u2267",GreaterGreater:"\u2aa2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2a7e",GreaterTilde:"\u2273",Gscr:"\ud835\udca2",Gt:"\u226b",HARDcy:"\u042a",Hacek:"\u02c7",Hat:"^",Hcirc:"\u0124",Hfr:"\u210c",HilbertSpace:"\u210b",Hopf:"\u210d",HorizontalLine:"\u2500",Hscr:"\u210b",Hstrok:"\u0126",HumpDownHump:"\u224e",HumpEqual:"\u224f",IEcy:"\u0415",IJlig:"\u0132",IOcy:"\u0401",Iacut:"\xcd",Iacute:"\xcd",Icir:"\xce",Icirc:"\xce",Icy:"\u0418",Idot:"\u0130",Ifr:"\u2111",Igrav:"\xcc",Igrave:"\xcc",Im:"\u2111",Imacr:"\u012a",ImaginaryI:"\u2148",Implies:"\u21d2",Int:"\u222c",Integral:"\u222b",Intersection:"\u22c2",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",Iogon:"\u012e",Iopf:"\ud835\udd40",Iota:"\u0399",Iscr:"\u2110",Itilde:"\u0128",Iukcy:"\u0406",Ium:"\xcf",Iuml:"\xcf",Jcirc:"\u0134",Jcy:"\u0419",Jfr:"\ud835\udd0d",Jopf:"\ud835\udd41",Jscr:"\ud835\udca5",Jsercy:"\u0408",Jukcy:"\u0404",KHcy:"\u0425",KJcy:"\u040c",Kappa:"\u039a",Kcedil:"\u0136",Kcy:"\u041a",Kfr:"\ud835\udd0e",Kopf:"\ud835\udd42",Kscr:"\ud835\udca6",LJcy:"\u0409",L:"<",LT:"<",Lacute:"\u0139",Lambda:"\u039b",Lang:"\u27ea",Laplacetrf:"\u2112",Larr:"\u219e",Lcaron:"\u013d",Lcedil:"\u013b",Lcy:"\u041b",LeftAngleBracket:"\u27e8",LeftArrow:"\u2190",LeftArrowBar:"\u21e4",LeftArrowRightArrow:"\u21c6",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27e6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21c3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230a",LeftRightArrow:"\u2194",LeftRightVector:"\u294e",LeftTee:"\u22a3",LeftTeeArrow:"\u21a4",LeftTeeVector:"\u295a",LeftTriangle:"\u22b2",LeftTriangleBar:"\u29cf",LeftTriangleEqual:"\u22b4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21bf",LeftUpVectorBar:"\u2958",LeftVector:"\u21bc",LeftVectorBar:"\u2952",Leftarrow:"\u21d0",Leftrightarrow:"\u21d4",LessEqualGreater:"\u22da",LessFullEqual:"\u2266",LessGreater:"\u2276",LessLess:"\u2aa1",LessSlantEqual:"\u2a7d",LessTilde:"\u2272",Lfr:"\ud835\udd0f",Ll:"\u22d8",Lleftarrow:"\u21da",Lmidot:"\u013f",LongLeftArrow:"\u27f5",LongLeftRightArrow:"\u27f7",LongRightArrow:"\u27f6",Longleftarrow:"\u27f8",Longleftrightarrow:"\u27fa",Longrightarrow:"\u27f9",Lopf:"\ud835\udd43",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",Lscr:"\u2112",Lsh:"\u21b0",Lstrok:"\u0141",Lt:"\u226a",Map:"\u2905",Mcy:"\u041c",MediumSpace:"\u205f",Mellintrf:"\u2133",Mfr:"\ud835\udd10",MinusPlus:"\u2213",Mopf:"\ud835\udd44",Mscr:"\u2133",Mu:"\u039c",NJcy:"\u040a",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041d",NegativeMediumSpace:"\u200b",NegativeThickSpace:"\u200b",NegativeThinSpace:"\u200b",NegativeVeryThinSpace:"\u200b",NestedGreaterGreater:"\u226b",NestedLessLess:"\u226a",NewLine:"\n",Nfr:"\ud835\udd11",NoBreak:"\u2060",NonBreakingSpace:"\xa0",Nopf:"\u2115",Not:"\u2aec",NotCongruent:"\u2262",NotCupCap:"\u226d",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226f",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226b\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2a7e\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224e\u0338",NotHumpEqual:"\u224f\u0338",NotLeftTriangle:"\u22ea",NotLeftTriangleBar:"\u29cf\u0338",NotLeftTriangleEqual:"\u22ec",NotLess:"\u226e",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226a\u0338",NotLessSlantEqual:"\u2a7d\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2aa2\u0338",NotNestedLessLess:"\u2aa1\u0338",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2aaf\u0338",NotPrecedesSlantEqual:"\u22e0",NotReverseElement:"\u220c",NotRightTriangle:"\u22eb",NotRightTriangleBar:"\u29d0\u0338",NotRightTriangleEqual:"\u22ed",NotSquareSubset:"\u228f\u0338",NotSquareSubsetEqual:"\u22e2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22e3",NotSubset:"\u2282\u20d2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2ab0\u0338",NotSucceedsSlantEqual:"\u22e1",NotSucceedsTilde:"\u227f\u0338",NotSuperset:"\u2283\u20d2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",Nscr:"\ud835\udca9",Ntild:"\xd1",Ntilde:"\xd1",Nu:"\u039d",OElig:"\u0152",Oacut:"\xd3",Oacute:"\xd3",Ocir:"\xd4",Ocirc:"\xd4",Ocy:"\u041e",Odblac:"\u0150",Ofr:"\ud835\udd12",Ograv:"\xd2",Ograve:"\xd2",Omacr:"\u014c",Omega:"\u03a9",Omicron:"\u039f",Oopf:"\ud835\udd46",OpenCurlyDoubleQuote:"\u201c",OpenCurlyQuote:"\u2018",Or:"\u2a54",Oscr:"\ud835\udcaa",Oslas:"\xd8",Oslash:"\xd8",Otild:"\xd5",Otilde:"\xd5",Otimes:"\u2a37",Oum:"\xd6",Ouml:"\xd6",OverBar:"\u203e",OverBrace:"\u23de",OverBracket:"\u23b4",OverParenthesis:"\u23dc",PartialD:"\u2202",Pcy:"\u041f",Pfr:"\ud835\udd13",Phi:"\u03a6",Pi:"\u03a0",PlusMinus:"\xb1",Poincareplane:"\u210c",Popf:"\u2119",Pr:"\u2abb",Precedes:"\u227a",PrecedesEqual:"\u2aaf",PrecedesSlantEqual:"\u227c",PrecedesTilde:"\u227e",Prime:"\u2033",Product:"\u220f",Proportion:"\u2237",Proportional:"\u221d",Pscr:"\ud835\udcab",Psi:"\u03a8",QUO:'"',QUOT:'"',Qfr:"\ud835\udd14",Qopf:"\u211a",Qscr:"\ud835\udcac",RBarr:"\u2910",RE:"\xae",REG:"\xae",Racute:"\u0154",Rang:"\u27eb",Rarr:"\u21a0",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",Re:"\u211c",ReverseElement:"\u220b",ReverseEquilibrium:"\u21cb",ReverseUpEquilibrium:"\u296f",Rfr:"\u211c",Rho:"\u03a1",RightAngleBracket:"\u27e9",RightArrow:"\u2192",RightArrowBar:"\u21e5",RightArrowLeftArrow:"\u21c4",RightCeiling:"\u2309",RightDoubleBracket:"\u27e7",RightDownTeeVector:"\u295d",RightDownVector:"\u21c2",RightDownVectorBar:"\u2955",RightFloor:"\u230b",RightTee:"\u22a2",RightTeeArrow:"\u21a6",RightTeeVector:"\u295b",RightTriangle:"\u22b3",RightTriangleBar:"\u29d0",RightTriangleEqual:"\u22b5",RightUpDownVector:"\u294f",RightUpTeeVector:"\u295c",RightUpVector:"\u21be",RightUpVectorBar:"\u2954",RightVector:"\u21c0",RightVectorBar:"\u2953",Rightarrow:"\u21d2",Ropf:"\u211d",RoundImplies:"\u2970",Rrightarrow:"\u21db",Rscr:"\u211b",Rsh:"\u21b1",RuleDelayed:"\u29f4",SHCHcy:"\u0429",SHcy:"\u0428",SOFTcy:"\u042c",Sacute:"\u015a",Sc:"\u2abc",Scaron:"\u0160",Scedil:"\u015e",Scirc:"\u015c",Scy:"\u0421",Sfr:"\ud835\udd16",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",Sigma:"\u03a3",SmallCircle:"\u2218",Sopf:"\ud835\udd4a",Sqrt:"\u221a",Square:"\u25a1",SquareIntersection:"\u2293",SquareSubset:"\u228f",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",Sscr:"\ud835\udcae",Star:"\u22c6",Sub:"\u22d0",Subset:"\u22d0",SubsetEqual:"\u2286",Succeeds:"\u227b",SucceedsEqual:"\u2ab0",SucceedsSlantEqual:"\u227d",SucceedsTilde:"\u227f",SuchThat:"\u220b",Sum:"\u2211",Sup:"\u22d1",Superset:"\u2283",SupersetEqual:"\u2287",Supset:"\u22d1",THOR:"\xde",THORN:"\xde",TRADE:"\u2122",TSHcy:"\u040b",TScy:"\u0426",Tab:"\t",Tau:"\u03a4",Tcaron:"\u0164",Tcedil:"\u0162",Tcy:"\u0422",Tfr:"\ud835\udd17",Therefore:"\u2234",Theta:"\u0398",ThickSpace:"\u205f\u200a",ThinSpace:"\u2009",Tilde:"\u223c",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",Topf:"\ud835\udd4b",TripleDot:"\u20db",Tscr:"\ud835\udcaf",Tstrok:"\u0166",Uacut:"\xda",Uacute:"\xda",Uarr:"\u219f",Uarrocir:"\u2949",Ubrcy:"\u040e",Ubreve:"\u016c",Ucir:"\xdb",Ucirc:"\xdb",Ucy:"\u0423",Udblac:"\u0170",Ufr:"\ud835\udd18",Ugrav:"\xd9",Ugrave:"\xd9",Umacr:"\u016a",UnderBar:"_",UnderBrace:"\u23df",UnderBracket:"\u23b5",UnderParenthesis:"\u23dd",Union:"\u22c3",UnionPlus:"\u228e",Uogon:"\u0172",Uopf:"\ud835\udd4c",UpArrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21c5",UpDownArrow:"\u2195",UpEquilibrium:"\u296e",UpTee:"\u22a5",UpTeeArrow:"\u21a5",Uparrow:"\u21d1",Updownarrow:"\u21d5",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03d2",Upsilon:"\u03a5",Uring:"\u016e",Uscr:"\ud835\udcb0",Utilde:"\u0168",Uum:"\xdc",Uuml:"\xdc",VDash:"\u22ab",Vbar:"\u2aeb",Vcy:"\u0412",Vdash:"\u22a9",Vdashl:"\u2ae6",Vee:"\u22c1",Verbar:"\u2016",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200a",Vfr:"\ud835\udd19",Vopf:"\ud835\udd4d",Vscr:"\ud835\udcb1",Vvdash:"\u22aa",Wcirc:"\u0174",Wedge:"\u22c0",Wfr:"\ud835\udd1a",Wopf:"\ud835\udd4e",Wscr:"\ud835\udcb2",Xfr:"\ud835\udd1b",Xi:"\u039e",Xopf:"\ud835\udd4f",Xscr:"\ud835\udcb3",YAcy:"\u042f",YIcy:"\u0407",YUcy:"\u042e",Yacut:"\xdd",Yacute:"\xdd",Ycirc:"\u0176",Ycy:"\u042b",Yfr:"\ud835\udd1c",Yopf:"\ud835\udd50",Yscr:"\ud835\udcb4",Yuml:"\u0178",ZHcy:"\u0416",Zacute:"\u0179",Zcaron:"\u017d",Zcy:"\u0417",Zdot:"\u017b",ZeroWidthSpace:"\u200b",Zeta:"\u0396",Zfr:"\u2128",Zopf:"\u2124",Zscr:"\ud835\udcb5",aacut:"\xe1",aacute:"\xe1",abreve:"\u0103",ac:"\u223e",acE:"\u223e\u0333",acd:"\u223f",acir:"\xe2",acirc:"\xe2",acut:"\xb4",acute:"\xb4",acy:"\u0430",aeli:"\xe6",aelig:"\xe6",af:"\u2061",afr:"\ud835\udd1e",agrav:"\xe0",agrave:"\xe0",alefsym:"\u2135",aleph:"\u2135",alpha:"\u03b1",amacr:"\u0101",amalg:"\u2a3f",am:"&",amp:"&",and:"\u2227",andand:"\u2a55",andd:"\u2a5c",andslope:"\u2a58",andv:"\u2a5a",ang:"\u2220",ange:"\u29a4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29a8",angmsdab:"\u29a9",angmsdac:"\u29aa",angmsdad:"\u29ab",angmsdae:"\u29ac",angmsdaf:"\u29ad",angmsdag:"\u29ae",angmsdah:"\u29af",angrt:"\u221f",angrtvb:"\u22be",angrtvbd:"\u299d",angsph:"\u2222",angst:"\xc5",angzarr:"\u237c",aogon:"\u0105",aopf:"\ud835\udd52",ap:"\u2248",apE:"\u2a70",apacir:"\u2a6f",ape:"\u224a",apid:"\u224b",apos:"'",approx:"\u2248",approxeq:"\u224a",arin:"\xe5",aring:"\xe5",ascr:"\ud835\udcb6",ast:"*",asymp:"\u2248",asympeq:"\u224d",atild:"\xe3",atilde:"\xe3",aum:"\xe4",auml:"\xe4",awconint:"\u2233",awint:"\u2a11",bNot:"\u2aed",backcong:"\u224c",backepsilon:"\u03f6",backprime:"\u2035",backsim:"\u223d",backsimeq:"\u22cd",barvee:"\u22bd",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23b5",bbrktbrk:"\u23b6",bcong:"\u224c",bcy:"\u0431",bdquo:"\u201e",becaus:"\u2235",because:"\u2235",bemptyv:"\u29b0",bepsi:"\u03f6",bernou:"\u212c",beta:"\u03b2",beth:"\u2136",between:"\u226c",bfr:"\ud835\udd1f",bigcap:"\u22c2",bigcirc:"\u25ef",bigcup:"\u22c3",bigodot:"\u2a00",bigoplus:"\u2a01",bigotimes:"\u2a02",bigsqcup:"\u2a06",bigstar:"\u2605",bigtriangledown:"\u25bd",bigtriangleup:"\u25b3",biguplus:"\u2a04",bigvee:"\u22c1",bigwedge:"\u22c0",bkarow:"\u290d",blacklozenge:"\u29eb",blacksquare:"\u25aa",blacktriangle:"\u25b4",blacktriangledown:"\u25be",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20e5",bnequiv:"\u2261\u20e5",bnot:"\u2310",bopf:"\ud835\udd53",bot:"\u22a5",bottom:"\u22a5",bowtie:"\u22c8",boxDL:"\u2557",boxDR:"\u2554",boxDl:"\u2556",boxDr:"\u2553",boxH:"\u2550",boxHD:"\u2566",boxHU:"\u2569",boxHd:"\u2564",boxHu:"\u2567",boxUL:"\u255d",boxUR:"\u255a",boxUl:"\u255c",boxUr:"\u2559",boxV:"\u2551",boxVH:"\u256c",boxVL:"\u2563",boxVR:"\u2560",boxVh:"\u256b",boxVl:"\u2562",boxVr:"\u255f",boxbox:"\u29c9",boxdL:"\u2555",boxdR:"\u2552",boxdl:"\u2510",boxdr:"\u250c",boxh:"\u2500",boxhD:"\u2565",boxhU:"\u2568",boxhd:"\u252c",boxhu:"\u2534",boxminus:"\u229f",boxplus:"\u229e",boxtimes:"\u22a0",boxuL:"\u255b",boxuR:"\u2558",boxul:"\u2518",boxur:"\u2514",boxv:"\u2502",boxvH:"\u256a",boxvL:"\u2561",boxvR:"\u255e",boxvh:"\u253c",boxvl:"\u2524",boxvr:"\u251c",bprime:"\u2035",breve:"\u02d8",brvba:"\xa6",brvbar:"\xa6",bscr:"\ud835\udcb7",bsemi:"\u204f",bsim:"\u223d",bsime:"\u22cd",bsol:"\\",bsolb:"\u29c5",bsolhsub:"\u27c8",bull:"\u2022",bullet:"\u2022",bump:"\u224e",bumpE:"\u2aae",bumpe:"\u224f",bumpeq:"\u224f",cacute:"\u0107",cap:"\u2229",capand:"\u2a44",capbrcup:"\u2a49",capcap:"\u2a4b",capcup:"\u2a47",capdot:"\u2a40",caps:"\u2229\ufe00",caret:"\u2041",caron:"\u02c7",ccaps:"\u2a4d",ccaron:"\u010d",ccedi:"\xe7",ccedil:"\xe7",ccirc:"\u0109",ccups:"\u2a4c",ccupssm:"\u2a50",cdot:"\u010b",cedi:"\xb8",cedil:"\xb8",cemptyv:"\u29b2",cen:"\xa2",cent:"\xa2",centerdot:"\xb7",cfr:"\ud835\udd20",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",chi:"\u03c7",cir:"\u25cb",cirE:"\u29c3",circ:"\u02c6",circeq:"\u2257",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",circledR:"\xae",circledS:"\u24c8",circledast:"\u229b",circledcirc:"\u229a",circleddash:"\u229d",cire:"\u2257",cirfnint:"\u2a10",cirmid:"\u2aef",cirscir:"\u29c2",clubs:"\u2663",clubsuit:"\u2663",colon:":",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2a6d",conint:"\u222e",copf:"\ud835\udd54",coprod:"\u2210",cop:"\xa9",copy:"\xa9",copysr:"\u2117",crarr:"\u21b5",cross:"\u2717",cscr:"\ud835\udcb8",csub:"\u2acf",csube:"\u2ad1",csup:"\u2ad0",csupe:"\u2ad2",ctdot:"\u22ef",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22de",cuesc:"\u22df",cularr:"\u21b6",cularrp:"\u293d",cup:"\u222a",cupbrcap:"\u2a48",cupcap:"\u2a46",cupcup:"\u2a4a",cupdot:"\u228d",cupor:"\u2a45",cups:"\u222a\ufe00",curarr:"\u21b7",curarrm:"\u293c",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",curlyvee:"\u22ce",curlywedge:"\u22cf",curre:"\xa4",curren:"\xa4",curvearrowleft:"\u21b6",curvearrowright:"\u21b7",cuvee:"\u22ce",cuwed:"\u22cf",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232d",dArr:"\u21d3",dHar:"\u2965",dagger:"\u2020",daleth:"\u2138",darr:"\u2193",dash:"\u2010",dashv:"\u22a3",dbkarow:"\u290f",dblac:"\u02dd",dcaron:"\u010f",dcy:"\u0434",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21ca",ddotseq:"\u2a77",de:"\xb0",deg:"\xb0",delta:"\u03b4",demptyv:"\u29b1",dfisht:"\u297f",dfr:"\ud835\udd21",dharl:"\u21c3",dharr:"\u21c2",diam:"\u22c4",diamond:"\u22c4",diamondsuit:"\u2666",diams:"\u2666",die:"\xa8",digamma:"\u03dd",disin:"\u22f2",div:"\xf7",divid:"\xf7",divide:"\xf7",divideontimes:"\u22c7",divonx:"\u22c7",djcy:"\u0452",dlcorn:"\u231e",dlcrop:"\u230d",dollar:"$",dopf:"\ud835\udd55",dot:"\u02d9",doteq:"\u2250",doteqdot:"\u2251",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22a1",doublebarwedge:"\u2306",downarrow:"\u2193",downdownarrows:"\u21ca",downharpoonleft:"\u21c3",downharpoonright:"\u21c2",drbkarow:"\u2910",drcorn:"\u231f",drcrop:"\u230c",dscr:"\ud835\udcb9",dscy:"\u0455",dsol:"\u29f6",dstrok:"\u0111",dtdot:"\u22f1",dtri:"\u25bf",dtrif:"\u25be",duarr:"\u21f5",duhar:"\u296f",dwangle:"\u29a6",dzcy:"\u045f",dzigrarr:"\u27ff",eDDot:"\u2a77",eDot:"\u2251",eacut:"\xe9",eacute:"\xe9",easter:"\u2a6e",ecaron:"\u011b",ecir:"\xea",ecirc:"\xea",ecolon:"\u2255",ecy:"\u044d",edot:"\u0117",ee:"\u2147",efDot:"\u2252",efr:"\ud835\udd22",eg:"\u2a9a",egrav:"\xe8",egrave:"\xe8",egs:"\u2a96",egsdot:"\u2a98",el:"\u2a99",elinters:"\u23e7",ell:"\u2113",els:"\u2a95",elsdot:"\u2a97",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",emptyv:"\u2205",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",eng:"\u014b",ensp:"\u2002",eogon:"\u0119",eopf:"\ud835\udd56",epar:"\u22d5",eparsl:"\u29e3",eplus:"\u2a71",epsi:"\u03b5",epsilon:"\u03b5",epsiv:"\u03f5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2a96",eqslantless:"\u2a95",equals:"=",equest:"\u225f",equiv:"\u2261",equivDD:"\u2a78",eqvparsl:"\u29e5",erDot:"\u2253",erarr:"\u2971",escr:"\u212f",esdot:"\u2250",esim:"\u2242",eta:"\u03b7",et:"\xf0",eth:"\xf0",eum:"\xeb",euml:"\xeb",euro:"\u20ac",excl:"!",exist:"\u2203",expectation:"\u2130",exponentiale:"\u2147",fallingdotseq:"\u2252",fcy:"\u0444",female:"\u2640",ffilig:"\ufb03",fflig:"\ufb00",ffllig:"\ufb04",ffr:"\ud835\udd23",filig:"\ufb01",fjlig:"fj",flat:"\u266d",fllig:"\ufb02",fltns:"\u25b1",fnof:"\u0192",fopf:"\ud835\udd57",forall:"\u2200",fork:"\u22d4",forkv:"\u2ad9",fpartint:"\u2a0d",frac1:"\xbc",frac12:"\xbd",frac13:"\u2153",frac14:"\xbc",frac15:"\u2155",frac16:"\u2159",frac18:"\u215b",frac23:"\u2154",frac25:"\u2156",frac3:"\xbe",frac34:"\xbe",frac35:"\u2157",frac38:"\u215c",frac45:"\u2158",frac56:"\u215a",frac58:"\u215d",frac78:"\u215e",frasl:"\u2044",frown:"\u2322",fscr:"\ud835\udcbb",gE:"\u2267",gEl:"\u2a8c",gacute:"\u01f5",gamma:"\u03b3",gammad:"\u03dd",gap:"\u2a86",gbreve:"\u011f",gcirc:"\u011d",gcy:"\u0433",gdot:"\u0121",ge:"\u2265",gel:"\u22db",geq:"\u2265",geqq:"\u2267",geqslant:"\u2a7e",ges:"\u2a7e",gescc:"\u2aa9",gesdot:"\u2a80",gesdoto:"\u2a82",gesdotol:"\u2a84",gesl:"\u22db\ufe00",gesles:"\u2a94",gfr:"\ud835\udd24",gg:"\u226b",ggg:"\u22d9",gimel:"\u2137",gjcy:"\u0453",gl:"\u2277",glE:"\u2a92",gla:"\u2aa5",glj:"\u2aa4",gnE:"\u2269",gnap:"\u2a8a",gnapprox:"\u2a8a",gne:"\u2a88",gneq:"\u2a88",gneqq:"\u2269",gnsim:"\u22e7",gopf:"\ud835\udd58",grave:"`",gscr:"\u210a",gsim:"\u2273",gsime:"\u2a8e",gsiml:"\u2a90",g:">",gt:">",gtcc:"\u2aa7",gtcir:"\u2a7a",gtdot:"\u22d7",gtlPar:"\u2995",gtquest:"\u2a7c",gtrapprox:"\u2a86",gtrarr:"\u2978",gtrdot:"\u22d7",gtreqless:"\u22db",gtreqqless:"\u2a8c",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\ufe00",gvnE:"\u2269\ufe00",hArr:"\u21d4",hairsp:"\u200a",half:"\xbd",hamilt:"\u210b",hardcy:"\u044a",harr:"\u2194",harrcir:"\u2948",harrw:"\u21ad",hbar:"\u210f",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22b9",hfr:"\ud835\udd25",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21ff",homtht:"\u223b",hookleftarrow:"\u21a9",hookrightarrow:"\u21aa",hopf:"\ud835\udd59",horbar:"\u2015",hscr:"\ud835\udcbd",hslash:"\u210f",hstrok:"\u0127",hybull:"\u2043",hyphen:"\u2010",iacut:"\xed",iacute:"\xed",ic:"\u2063",icir:"\xee",icirc:"\xee",icy:"\u0438",iecy:"\u0435",iexc:"\xa1",iexcl:"\xa1",iff:"\u21d4",ifr:"\ud835\udd26",igrav:"\xec",igrave:"\xec",ii:"\u2148",iiiint:"\u2a0c",iiint:"\u222d",iinfin:"\u29dc",iiota:"\u2129",ijlig:"\u0133",imacr:"\u012b",image:"\u2111",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22b7",imped:"\u01b5",in:"\u2208",incare:"\u2105",infin:"\u221e",infintie:"\u29dd",inodot:"\u0131",int:"\u222b",intcal:"\u22ba",integers:"\u2124",intercal:"\u22ba",intlarhk:"\u2a17",intprod:"\u2a3c",iocy:"\u0451",iogon:"\u012f",iopf:"\ud835\udd5a",iota:"\u03b9",iprod:"\u2a3c",iques:"\xbf",iquest:"\xbf",iscr:"\ud835\udcbe",isin:"\u2208",isinE:"\u22f9",isindot:"\u22f5",isins:"\u22f4",isinsv:"\u22f3",isinv:"\u2208",it:"\u2062",itilde:"\u0129",iukcy:"\u0456",ium:"\xef",iuml:"\xef",jcirc:"\u0135",jcy:"\u0439",jfr:"\ud835\udd27",jmath:"\u0237",jopf:"\ud835\udd5b",jscr:"\ud835\udcbf",jsercy:"\u0458",jukcy:"\u0454",kappa:"\u03ba",kappav:"\u03f0",kcedil:"\u0137",kcy:"\u043a",kfr:"\ud835\udd28",kgreen:"\u0138",khcy:"\u0445",kjcy:"\u045c",kopf:"\ud835\udd5c",kscr:"\ud835\udcc0",lAarr:"\u21da",lArr:"\u21d0",lAtail:"\u291b",lBarr:"\u290e",lE:"\u2266",lEg:"\u2a8b",lHar:"\u2962",lacute:"\u013a",laemptyv:"\u29b4",lagran:"\u2112",lambda:"\u03bb",lang:"\u27e8",langd:"\u2991",langle:"\u27e8",lap:"\u2a85",laqu:"\xab",laquo:"\xab",larr:"\u2190",larrb:"\u21e4",larrbfs:"\u291f",larrfs:"\u291d",larrhk:"\u21a9",larrlp:"\u21ab",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21a2",lat:"\u2aab",latail:"\u2919",late:"\u2aad",lates:"\u2aad\ufe00",lbarr:"\u290c",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298b",lbrksld:"\u298f",lbrkslu:"\u298d",lcaron:"\u013e",lcedil:"\u013c",lceil:"\u2308",lcub:"{",lcy:"\u043b",ldca:"\u2936",ldquo:"\u201c",ldquor:"\u201e",ldrdhar:"\u2967",ldrushar:"\u294b",ldsh:"\u21b2",le:"\u2264",leftarrow:"\u2190",leftarrowtail:"\u21a2",leftharpoondown:"\u21bd",leftharpoonup:"\u21bc",leftleftarrows:"\u21c7",leftrightarrow:"\u2194",leftrightarrows:"\u21c6",leftrightharpoons:"\u21cb",leftrightsquigarrow:"\u21ad",leftthreetimes:"\u22cb",leg:"\u22da",leq:"\u2264",leqq:"\u2266",leqslant:"\u2a7d",les:"\u2a7d",lescc:"\u2aa8",lesdot:"\u2a7f",lesdoto:"\u2a81",lesdotor:"\u2a83",lesg:"\u22da\ufe00",lesges:"\u2a93",lessapprox:"\u2a85",lessdot:"\u22d6",lesseqgtr:"\u22da",lesseqqgtr:"\u2a8b",lessgtr:"\u2276",lesssim:"\u2272",lfisht:"\u297c",lfloor:"\u230a",lfr:"\ud835\udd29",lg:"\u2276",lgE:"\u2a91",lhard:"\u21bd",lharu:"\u21bc",lharul:"\u296a",lhblk:"\u2584",ljcy:"\u0459",ll:"\u226a",llarr:"\u21c7",llcorner:"\u231e",llhard:"\u296b",lltri:"\u25fa",lmidot:"\u0140",lmoust:"\u23b0",lmoustache:"\u23b0",lnE:"\u2268",lnap:"\u2a89",lnapprox:"\u2a89",lne:"\u2a87",lneq:"\u2a87",lneqq:"\u2268",lnsim:"\u22e6",loang:"\u27ec",loarr:"\u21fd",lobrk:"\u27e6",longleftarrow:"\u27f5",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",longrightarrow:"\u27f6",looparrowleft:"\u21ab",looparrowright:"\u21ac",lopar:"\u2985",lopf:"\ud835\udd5d",loplus:"\u2a2d",lotimes:"\u2a34",lowast:"\u2217",lowbar:"_",loz:"\u25ca",lozenge:"\u25ca",lozf:"\u29eb",lpar:"(",lparlt:"\u2993",lrarr:"\u21c6",lrcorner:"\u231f",lrhar:"\u21cb",lrhard:"\u296d",lrm:"\u200e",lrtri:"\u22bf",lsaquo:"\u2039",lscr:"\ud835\udcc1",lsh:"\u21b0",lsim:"\u2272",lsime:"\u2a8d",lsimg:"\u2a8f",lsqb:"[",lsquo:"\u2018",lsquor:"\u201a",lstrok:"\u0142",l:"<",lt:"<",ltcc:"\u2aa6",ltcir:"\u2a79",ltdot:"\u22d6",lthree:"\u22cb",ltimes:"\u22c9",ltlarr:"\u2976",ltquest:"\u2a7b",ltrPar:"\u2996",ltri:"\u25c3",ltrie:"\u22b4",ltrif:"\u25c2",lurdshar:"\u294a",luruhar:"\u2966",lvertneqq:"\u2268\ufe00",lvnE:"\u2268\ufe00",mDDot:"\u223a",mac:"\xaf",macr:"\xaf",male:"\u2642",malt:"\u2720",maltese:"\u2720",map:"\u21a6",mapsto:"\u21a6",mapstodown:"\u21a7",mapstoleft:"\u21a4",mapstoup:"\u21a5",marker:"\u25ae",mcomma:"\u2a29",mcy:"\u043c",mdash:"\u2014",measuredangle:"\u2221",mfr:"\ud835\udd2a",mho:"\u2127",micr:"\xb5",micro:"\xb5",mid:"\u2223",midast:"*",midcir:"\u2af0",middo:"\xb7",middot:"\xb7",minus:"\u2212",minusb:"\u229f",minusd:"\u2238",minusdu:"\u2a2a",mlcp:"\u2adb",mldr:"\u2026",mnplus:"\u2213",models:"\u22a7",mopf:"\ud835\udd5e",mp:"\u2213",mscr:"\ud835\udcc2",mstpos:"\u223e",mu:"\u03bc",multimap:"\u22b8",mumap:"\u22b8",nGg:"\u22d9\u0338",nGt:"\u226b\u20d2",nGtv:"\u226b\u0338",nLeftarrow:"\u21cd",nLeftrightarrow:"\u21ce",nLl:"\u22d8\u0338",nLt:"\u226a\u20d2",nLtv:"\u226a\u0338",nRightarrow:"\u21cf",nVDash:"\u22af",nVdash:"\u22ae",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20d2",nap:"\u2249",napE:"\u2a70\u0338",napid:"\u224b\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266e",natural:"\u266e",naturals:"\u2115",nbs:"\xa0",nbsp:"\xa0",nbump:"\u224e\u0338",nbumpe:"\u224f\u0338",ncap:"\u2a43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2a6d\u0338",ncup:"\u2a42",ncy:"\u043d",ndash:"\u2013",ne:"\u2260",neArr:"\u21d7",nearhk:"\u2924",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",nfr:"\ud835\udd2b",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2a7e\u0338",nges:"\u2a7e\u0338",ngsim:"\u2275",ngt:"\u226f",ngtr:"\u226f",nhArr:"\u21ce",nharr:"\u21ae",nhpar:"\u2af2",ni:"\u220b",nis:"\u22fc",nisd:"\u22fa",niv:"\u220b",njcy:"\u045a",nlArr:"\u21cd",nlE:"\u2266\u0338",nlarr:"\u219a",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219a",nleftrightarrow:"\u21ae",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2a7d\u0338",nles:"\u2a7d\u0338",nless:"\u226e",nlsim:"\u2274",nlt:"\u226e",nltri:"\u22ea",nltrie:"\u22ec",nmid:"\u2224",nopf:"\ud835\udd5f",no:"\xac",not:"\xac",notin:"\u2209",notinE:"\u22f9\u0338",notindot:"\u22f5\u0338",notinva:"\u2209",notinvb:"\u22f7",notinvc:"\u22f6",notni:"\u220c",notniva:"\u220c",notnivb:"\u22fe",notnivc:"\u22fd",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2afd\u20e5",npart:"\u2202\u0338",npolint:"\u2a14",npr:"\u2280",nprcue:"\u22e0",npre:"\u2aaf\u0338",nprec:"\u2280",npreceq:"\u2aaf\u0338",nrArr:"\u21cf",nrarr:"\u219b",nrarrc:"\u2933\u0338",nrarrw:"\u219d\u0338",nrightarrow:"\u219b",nrtri:"\u22eb",nrtrie:"\u22ed",nsc:"\u2281",nsccue:"\u22e1",nsce:"\u2ab0\u0338",nscr:"\ud835\udcc3",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22e2",nsqsupe:"\u22e3",nsub:"\u2284",nsubE:"\u2ac5\u0338",nsube:"\u2288",nsubset:"\u2282\u20d2",nsubseteq:"\u2288",nsubseteqq:"\u2ac5\u0338",nsucc:"\u2281",nsucceq:"\u2ab0\u0338",nsup:"\u2285",nsupE:"\u2ac6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20d2",nsupseteq:"\u2289",nsupseteqq:"\u2ac6\u0338",ntgl:"\u2279",ntild:"\xf1",ntilde:"\xf1",ntlg:"\u2278",ntriangleleft:"\u22ea",ntrianglelefteq:"\u22ec",ntriangleright:"\u22eb",ntrianglerighteq:"\u22ed",nu:"\u03bd",num:"#",numero:"\u2116",numsp:"\u2007",nvDash:"\u22ad",nvHarr:"\u2904",nvap:"\u224d\u20d2",nvdash:"\u22ac",nvge:"\u2265\u20d2",nvgt:">\u20d2",nvinfin:"\u29de",nvlArr:"\u2902",nvle:"\u2264\u20d2",nvlt:"<\u20d2",nvltrie:"\u22b4\u20d2",nvrArr:"\u2903",nvrtrie:"\u22b5\u20d2",nvsim:"\u223c\u20d2",nwArr:"\u21d6",nwarhk:"\u2923",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",oS:"\u24c8",oacut:"\xf3",oacute:"\xf3",oast:"\u229b",ocir:"\xf4",ocirc:"\xf4",ocy:"\u043e",odash:"\u229d",odblac:"\u0151",odiv:"\u2a38",odot:"\u2299",odsold:"\u29bc",oelig:"\u0153",ofcir:"\u29bf",ofr:"\ud835\udd2c",ogon:"\u02db",ograv:"\xf2",ograve:"\xf2",ogt:"\u29c1",ohbar:"\u29b5",ohm:"\u03a9",oint:"\u222e",olarr:"\u21ba",olcir:"\u29be",olcross:"\u29bb",oline:"\u203e",olt:"\u29c0",omacr:"\u014d",omega:"\u03c9",omicron:"\u03bf",omid:"\u29b6",ominus:"\u2296",oopf:"\ud835\udd60",opar:"\u29b7",operp:"\u29b9",oplus:"\u2295",or:"\u2228",orarr:"\u21bb",ord:"\xba",order:"\u2134",orderof:"\u2134",ordf:"\xaa",ordm:"\xba",origof:"\u22b6",oror:"\u2a56",orslope:"\u2a57",orv:"\u2a5b",oscr:"\u2134",oslas:"\xf8",oslash:"\xf8",osol:"\u2298",otild:"\xf5",otilde:"\xf5",otimes:"\u2297",otimesas:"\u2a36",oum:"\xf6",ouml:"\xf6",ovbar:"\u233d",par:"\xb6",para:"\xb6",parallel:"\u2225",parsim:"\u2af3",parsl:"\u2afd",part:"\u2202",pcy:"\u043f",percnt:"%",period:".",permil:"\u2030",perp:"\u22a5",pertenk:"\u2031",pfr:"\ud835\udd2d",phi:"\u03c6",phiv:"\u03d5",phmmat:"\u2133",phone:"\u260e",pi:"\u03c0",pitchfork:"\u22d4",piv:"\u03d6",planck:"\u210f",planckh:"\u210e",plankv:"\u210f",plus:"+",plusacir:"\u2a23",plusb:"\u229e",pluscir:"\u2a22",plusdo:"\u2214",plusdu:"\u2a25",pluse:"\u2a72",plusm:"\xb1",plusmn:"\xb1",plussim:"\u2a26",plustwo:"\u2a27",pm:"\xb1",pointint:"\u2a15",popf:"\ud835\udd61",poun:"\xa3",pound:"\xa3",pr:"\u227a",prE:"\u2ab3",prap:"\u2ab7",prcue:"\u227c",pre:"\u2aaf",prec:"\u227a",precapprox:"\u2ab7",preccurlyeq:"\u227c",preceq:"\u2aaf",precnapprox:"\u2ab9",precneqq:"\u2ab5",precnsim:"\u22e8",precsim:"\u227e",prime:"\u2032",primes:"\u2119",prnE:"\u2ab5",prnap:"\u2ab9",prnsim:"\u22e8",prod:"\u220f",profalar:"\u232e",profline:"\u2312",profsurf:"\u2313",prop:"\u221d",propto:"\u221d",prsim:"\u227e",prurel:"\u22b0",pscr:"\ud835\udcc5",psi:"\u03c8",puncsp:"\u2008",qfr:"\ud835\udd2e",qint:"\u2a0c",qopf:"\ud835\udd62",qprime:"\u2057",qscr:"\ud835\udcc6",quaternions:"\u210d",quatint:"\u2a16",quest:"?",questeq:"\u225f",quo:'"',quot:'"',rAarr:"\u21db",rArr:"\u21d2",rAtail:"\u291c",rBarr:"\u290f",rHar:"\u2964",race:"\u223d\u0331",racute:"\u0155",radic:"\u221a",raemptyv:"\u29b3",rang:"\u27e9",rangd:"\u2992",range:"\u29a5",rangle:"\u27e9",raqu:"\xbb",raquo:"\xbb",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21e5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291e",rarrhk:"\u21aa",rarrlp:"\u21ac",rarrpl:"\u2945",rarrsim:"\u2974",rarrtl:"\u21a3",rarrw:"\u219d",ratail:"\u291a",ratio:"\u2236",rationals:"\u211a",rbarr:"\u290d",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298c",rbrksld:"\u298e",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201d",rdquor:"\u201d",rdsh:"\u21b3",real:"\u211c",realine:"\u211b",realpart:"\u211c",reals:"\u211d",rect:"\u25ad",re:"\xae",reg:"\xae",rfisht:"\u297d",rfloor:"\u230b",rfr:"\ud835\udd2f",rhard:"\u21c1",rharu:"\u21c0",rharul:"\u296c",rho:"\u03c1",rhov:"\u03f1",rightarrow:"\u2192",rightarrowtail:"\u21a3",rightharpoondown:"\u21c1",rightharpoonup:"\u21c0",rightleftarrows:"\u21c4",rightleftharpoons:"\u21cc",rightrightarrows:"\u21c9",rightsquigarrow:"\u219d",rightthreetimes:"\u22cc",ring:"\u02da",risingdotseq:"\u2253",rlarr:"\u21c4",rlhar:"\u21cc",rlm:"\u200f",rmoust:"\u23b1",rmoustache:"\u23b1",rnmid:"\u2aee",roang:"\u27ed",roarr:"\u21fe",robrk:"\u27e7",ropar:"\u2986",ropf:"\ud835\udd63",roplus:"\u2a2e",rotimes:"\u2a35",rpar:")",rpargt:"\u2994",rppolint:"\u2a12",rrarr:"\u21c9",rsaquo:"\u203a",rscr:"\ud835\udcc7",rsh:"\u21b1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22cc",rtimes:"\u22ca",rtri:"\u25b9",rtrie:"\u22b5",rtrif:"\u25b8",rtriltri:"\u29ce",ruluhar:"\u2968",rx:"\u211e",sacute:"\u015b",sbquo:"\u201a",sc:"\u227b",scE:"\u2ab4",scap:"\u2ab8",scaron:"\u0161",sccue:"\u227d",sce:"\u2ab0",scedil:"\u015f",scirc:"\u015d",scnE:"\u2ab6",scnap:"\u2aba",scnsim:"\u22e9",scpolint:"\u2a13",scsim:"\u227f",scy:"\u0441",sdot:"\u22c5",sdotb:"\u22a1",sdote:"\u2a66",seArr:"\u21d8",searhk:"\u2925",searr:"\u2198",searrow:"\u2198",sec:"\xa7",sect:"\xa7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",sfr:"\ud835\udd30",sfrown:"\u2322",sharp:"\u266f",shchcy:"\u0449",shcy:"\u0448",shortmid:"\u2223",shortparallel:"\u2225",sh:"\xad",shy:"\xad",sigma:"\u03c3",sigmaf:"\u03c2",sigmav:"\u03c2",sim:"\u223c",simdot:"\u2a6a",sime:"\u2243",simeq:"\u2243",simg:"\u2a9e",simgE:"\u2aa0",siml:"\u2a9d",simlE:"\u2a9f",simne:"\u2246",simplus:"\u2a24",simrarr:"\u2972",slarr:"\u2190",smallsetminus:"\u2216",smashp:"\u2a33",smeparsl:"\u29e4",smid:"\u2223",smile:"\u2323",smt:"\u2aaa",smte:"\u2aac",smtes:"\u2aac\ufe00",softcy:"\u044c",sol:"/",solb:"\u29c4",solbar:"\u233f",sopf:"\ud835\udd64",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\ufe00",sqcup:"\u2294",sqcups:"\u2294\ufe00",sqsub:"\u228f",sqsube:"\u2291",sqsubset:"\u228f",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25a1",square:"\u25a1",squarf:"\u25aa",squf:"\u25aa",srarr:"\u2192",sscr:"\ud835\udcc8",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22c6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03f5",straightphi:"\u03d5",strns:"\xaf",sub:"\u2282",subE:"\u2ac5",subdot:"\u2abd",sube:"\u2286",subedot:"\u2ac3",submult:"\u2ac1",subnE:"\u2acb",subne:"\u228a",subplus:"\u2abf",subrarr:"\u2979",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2ac5",subsetneq:"\u228a",subsetneqq:"\u2acb",subsim:"\u2ac7",subsub:"\u2ad5",subsup:"\u2ad3",succ:"\u227b",succapprox:"\u2ab8",succcurlyeq:"\u227d",succeq:"\u2ab0",succnapprox:"\u2aba",succneqq:"\u2ab6",succnsim:"\u22e9",succsim:"\u227f",sum:"\u2211",sung:"\u266a",sup:"\u2283",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",supE:"\u2ac6",supdot:"\u2abe",supdsub:"\u2ad8",supe:"\u2287",supedot:"\u2ac4",suphsol:"\u27c9",suphsub:"\u2ad7",suplarr:"\u297b",supmult:"\u2ac2",supnE:"\u2acc",supne:"\u228b",supplus:"\u2ac0",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2ac6",supsetneq:"\u228b",supsetneqq:"\u2acc",supsim:"\u2ac8",supsub:"\u2ad4",supsup:"\u2ad6",swArr:"\u21d9",swarhk:"\u2926",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292a",szli:"\xdf",szlig:"\xdf",target:"\u2316",tau:"\u03c4",tbrk:"\u23b4",tcaron:"\u0165",tcedil:"\u0163",tcy:"\u0442",tdot:"\u20db",telrec:"\u2315",tfr:"\ud835\udd31",there4:"\u2234",therefore:"\u2234",theta:"\u03b8",thetasym:"\u03d1",thetav:"\u03d1",thickapprox:"\u2248",thicksim:"\u223c",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223c",thor:"\xfe",thorn:"\xfe",tilde:"\u02dc",time:"\xd7",times:"\xd7",timesb:"\u22a0",timesbar:"\u2a31",timesd:"\u2a30",tint:"\u222d",toea:"\u2928",top:"\u22a4",topbot:"\u2336",topcir:"\u2af1",topf:"\ud835\udd65",topfork:"\u2ada",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",triangle:"\u25b5",triangledown:"\u25bf",triangleleft:"\u25c3",trianglelefteq:"\u22b4",triangleq:"\u225c",triangleright:"\u25b9",trianglerighteq:"\u22b5",tridot:"\u25ec",trie:"\u225c",triminus:"\u2a3a",triplus:"\u2a39",trisb:"\u29cd",tritime:"\u2a3b",trpezium:"\u23e2",tscr:"\ud835\udcc9",tscy:"\u0446",tshcy:"\u045b",tstrok:"\u0167",twixt:"\u226c",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",uArr:"\u21d1",uHar:"\u2963",uacut:"\xfa",uacute:"\xfa",uarr:"\u2191",ubrcy:"\u045e",ubreve:"\u016d",ucir:"\xfb",ucirc:"\xfb",ucy:"\u0443",udarr:"\u21c5",udblac:"\u0171",udhar:"\u296e",ufisht:"\u297e",ufr:"\ud835\udd32",ugrav:"\xf9",ugrave:"\xf9",uharl:"\u21bf",uharr:"\u21be",uhblk:"\u2580",ulcorn:"\u231c",ulcorner:"\u231c",ulcrop:"\u230f",ultri:"\u25f8",umacr:"\u016b",um:"\xa8",uml:"\xa8",uogon:"\u0173",uopf:"\ud835\udd66",uparrow:"\u2191",updownarrow:"\u2195",upharpoonleft:"\u21bf",upharpoonright:"\u21be",uplus:"\u228e",upsi:"\u03c5",upsih:"\u03d2",upsilon:"\u03c5",upuparrows:"\u21c8",urcorn:"\u231d",urcorner:"\u231d",urcrop:"\u230e",uring:"\u016f",urtri:"\u25f9",uscr:"\ud835\udcca",utdot:"\u22f0",utilde:"\u0169",utri:"\u25b5",utrif:"\u25b4",uuarr:"\u21c8",uum:"\xfc",uuml:"\xfc",uwangle:"\u29a7",vArr:"\u21d5",vBar:"\u2ae8",vBarv:"\u2ae9",vDash:"\u22a8",vangrt:"\u299c",varepsilon:"\u03f5",varkappa:"\u03f0",varnothing:"\u2205",varphi:"\u03d5",varpi:"\u03d6",varpropto:"\u221d",varr:"\u2195",varrho:"\u03f1",varsigma:"\u03c2",varsubsetneq:"\u228a\ufe00",varsubsetneqq:"\u2acb\ufe00",varsupsetneq:"\u228b\ufe00",varsupsetneqq:"\u2acc\ufe00",vartheta:"\u03d1",vartriangleleft:"\u22b2",vartriangleright:"\u22b3",vcy:"\u0432",vdash:"\u22a2",vee:"\u2228",veebar:"\u22bb",veeeq:"\u225a",vellip:"\u22ee",verbar:"|",vert:"|",vfr:"\ud835\udd33",vltri:"\u22b2",vnsub:"\u2282\u20d2",vnsup:"\u2283\u20d2",vopf:"\ud835\udd67",vprop:"\u221d",vrtri:"\u22b3",vscr:"\ud835\udccb",vsubnE:"\u2acb\ufe00",vsubne:"\u228a\ufe00",vsupnE:"\u2acc\ufe00",vsupne:"\u228b\ufe00",vzigzag:"\u299a",wcirc:"\u0175",wedbar:"\u2a5f",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",wfr:"\ud835\udd34",wopf:"\ud835\udd68",wp:"\u2118",wr:"\u2240",wreath:"\u2240",wscr:"\ud835\udccc",xcap:"\u22c2",xcirc:"\u25ef",xcup:"\u22c3",xdtri:"\u25bd",xfr:"\ud835\udd35",xhArr:"\u27fa",xharr:"\u27f7",xi:"\u03be",xlArr:"\u27f8",xlarr:"\u27f5",xmap:"\u27fc",xnis:"\u22fb",xodot:"\u2a00",xopf:"\ud835\udd69",xoplus:"\u2a01",xotime:"\u2a02",xrArr:"\u27f9",xrarr:"\u27f6",xscr:"\ud835\udccd",xsqcup:"\u2a06",xuplus:"\u2a04",xutri:"\u25b3",xvee:"\u22c1",xwedge:"\u22c0",yacut:"\xfd",yacute:"\xfd",yacy:"\u044f",ycirc:"\u0177",ycy:"\u044b",ye:"\xa5",yen:"\xa5",yfr:"\ud835\udd36",yicy:"\u0457",yopf:"\ud835\udd6a",yscr:"\ud835\udcce",yucy:"\u044e",yum:"\xff",yuml:"\xff",zacute:"\u017a",zcaron:"\u017e",zcy:"\u0437",zdot:"\u017c",zeetrf:"\u2128",zeta:"\u03b6",zfr:"\ud835\udd37",zhcy:"\u0436",zigrarr:"\u21dd",zopf:"\ud835\udd6b",zscr:"\ud835\udccf",zwj:"\u200d",zwnj:"\u200c"},wt=function(e){return!!Ft.call(At,e)&&At[e]},Ft={}.hasOwnProperty;var kt=function(e,t){var r,n,u={};t||(t={});for(n in It)r=t[n],u[n]=null==r?It[n]:r;(u.position.indent||u.position.start)&&(u.indent=u.position.indent||[],u.position=u.position.start);return function(e,t){var r,n,u,i,o,a,c,s,l,f,D,p,h,d,g,m,E,y,v,b=t.additional,C=t.nonTerminated,A=t.text,w=t.reference,F=t.warning,k=t.textContext,O=t.referenceContext,T=t.warningContext,S=t.position,I=t.indent||[],R=e.length,x=0,B=-1,N=S.column||1,L=S.line||1,P="",q=[];"string"==typeof b&&(b=b.charCodeAt(0));m=j(),s=F?_:St,x--,R++;for(;++x<R;)if(10===o&&(N=I[B]||1),38===(o=e.charCodeAt(x))){if(9===(c=e.charCodeAt(x+1))||10===c||12===c||32===c||38===c||60===c||c!=c||b&&c===b){P+=Tt(o),N++;continue}for(p=h=x+1,v=h,35===c?(v=++p,88===(c=e.charCodeAt(v))||120===c?(d=Rt,v=++p):d=xt):d="named",r="",D="",i="",g=Nt[d],v--;++v<R&&g(c=e.charCodeAt(v));)i+=Tt(c),"named"===d&&Ot.call(mt,i)&&(r=i,D=mt[i]);(u=59===e.charCodeAt(v))&&(v++,(n="named"===d&&wt(i))&&(r=i,D=n)),y=1+v-h,(u||C)&&(i?"named"===d?(u&&!D?s(5,1):(r!==i&&(y=1+(v=p+r.length)-p,u=!1),u||(l=r?1:3,t.attribute?61===(c=e.charCodeAt(v))?(s(l,y),D=null):Ct(c)?D=null:s(l,y):s(l,y))),a=D):(u||s(2,y),Pt(a=parseInt(i,Bt[d]))?(s(7,y),a=Tt(65533)):a in Et?(s(6,y),a=Et[a]):(f="",qt(a)&&s(6,y),a>65535&&(f+=Tt((a-=65536)>>>10|55296),a=56320|1023&a),a=f+Tt(a))):"named"!==d&&s(4,y)),a?(U(),m=j(),x=v-1,N+=v-h+1,q.push(a),(E=j()).offset++,w&&w.call(O,a,{start:m,end:E},e.slice(h-1,v)),m=E):(i=e.slice(h-1,v),P+=i,N+=i.length,x=v-1)}else 10===o&&(L++,B++,N=0),o==o?(P+=Tt(o),N++):U();return q.join("");function j(){return{line:L,column:N,offset:x+(S.offset||0)}}function _(e,t){var r=j();r.column+=t,r.offset+=t,F.call(T,Lt[e],r,e)}function U(){P&&(q.push(P),A&&A.call(k,P,{start:m,end:j()}),P="")}}(e,u)},Ot={}.hasOwnProperty,Tt=String.fromCharCode,St=Function.prototype,It={warning:null,reference:null,text:null,warningContext:null,referenceContext:null,textContext:null,position:{},additional:null,attribute:!1,nonTerminated:!0},Rt="hexadecimal",xt="decimal",Bt={hexadecimal:16,decimal:10},Nt={};Nt.named=Ct,Nt[xt]=yt,Nt[Rt]=vt;var Lt={};function Pt(e){return e>=55296&&e<=57343||e>1114111}function qt(e){return e>=1&&e<=8||11===e||e>=13&&e<=31||e>=127&&e<=159||e>=64976&&e<=65007||65535==(65535&e)||65534==(65535&e)}Lt[1]="Named character references must be terminated by a semicolon",Lt[2]="Numeric character references must be terminated by a semicolon",Lt[3]="Named character references cannot be empty",Lt[4]="Numeric character references cannot be empty",Lt[5]="Named character references must be known",Lt[6]="Numeric character references cannot be disallowed",Lt[7]="Numeric character references cannot be outside the permissible Unicode range";var jt=function(t){return n.raw=u,n;function r(e){for(var r=t.offset,n=e.line,u=[];++n&&n in r;)u.push((r[n]||0)+1);return{start:e,indent:u}}function n(e,n,u){kt(e,{position:r(n),warning:i,text:u,reference:u,textContext:t,referenceContext:t})}function u(t,n,u){return kt(t,e(u,{position:r(n),warning:i}))}function i(e,r,n){3!==n&&t.file.message(e,r)}};var _t=function(e){return function(t,r){var n,u,i,o,a,c=this,s=c.offset,l=[],f=c[e+"Methods"],D=c[e+"Tokenizers"],p=r.line,h=r.column;if(!t)return l;C.now=m,C.file=c.file,d("");for(;t;){for(n=-1,u=f.length,o=!1;++n<u&&(!(i=D[f[n]])||i.onlyAtStart&&!c.atStart||i.notInList&&c.inList||i.notInBlock&&c.inBlock||i.notInLink&&c.inLink||(a=t.length,i.apply(c,[C,t]),!(o=a!==t.length))););o||c.file.fail(new Error("Infinite loop"),C.now())}return c.eof=m(),l;function d(e){for(var t=-1,r=e.indexOf("\n");-1!==r;)p++,t=r,r=e.indexOf("\n",r+1);-1===t?h+=e.length:h=e.length-t,p in s&&(-1!==t?h+=s[p]:h<=s[p]&&(h=s[p]+1))}function g(){var e=[],t=p+1;return function(){for(var r=p+1;t<r;)e.push((s[t]||0)+1),t++;return e}}function m(){var e={line:p,column:h};return e.offset=c.toOffset(e),e}function E(e){this.start=e,this.end=m()}function y(e){t.slice(0,e.length)!==e&&c.file.fail(new Error("Incorrectly eaten value: please report this warning on https://git.io/vg5Ft"),m())}function v(){var e=m();return t;function t(t,r){var n=t.position,u=n?n.start:e,i=[],o=n&&n.end.line,a=e.line;if(t.position=new E(u),n&&r&&n.indent){if(i=n.indent,o<a){for(;++o<a;)i.push((s[o]||0)+1);i.push(e.column)}r=i.concat(r)}return t.position.indent=r||[],t}}function b(e,t){var r=t?t.children:l,n=r[r.length-1];return n&&e.type===n.type&&("text"===e.type||"blockquote"===e.type)&&Ut(n)&&Ut(e)&&(e=("text"===e.type?$t:Mt).call(c,n,e)),e!==n&&r.push(e),c.atStart&&0!==l.length&&c.exitStart(),e}function C(e){var r=g(),n=v(),u=m();return y(e),i.reset=o,o.test=a,i.test=a,t=t.slice(e.length),d(e),r=r(),i;function i(e,t){return n(b(n(e),t),r)}function o(){var r=i.apply(null,arguments);return p=u.line,h=u.column,t=e+t,r}function a(){var r=n({});return p=u.line,h=u.column,t=e+t,r.position}}}};function Ut(e){var t,r;return"text"!==e.type||!e.position||(t=e.position.start,r=e.position.end,t.line!==r.line||r.column-t.column===e.value.length)}function $t(e,t){return e.value+=t.value,e}function Mt(e,t){return this.options.commonmark||this.options.gfm?t:(e.children=e.children.concat(t.children),e)}var zt=Ht,Gt=["\\","`","*","{","}","[","]","(",")","#","+","-",".","!","_",">"],Vt=Gt.concat(["~","|"]),Yt=Vt.concat(["\n",'"',"$","%","&","'",",","/",":",";","<","=","?","@","^"]);function Ht(e){var t=e||{};return t.commonmark?Yt:t.gfm?Vt:Gt}Ht.default=Gt,Ht.gfm=Vt,Ht.commonmark=Yt;var Xt={position:!0,gfm:!0,commonmark:!1,pedantic:!1,blocks:["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","iframe","legend","li","link","main","menu","menuitem","meta","nav","noframes","ol","optgroup","option","p","param","pre","section","source","title","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]},Wt=function(t){var r,n,u=this,i=u.options;if(null==t)t={};else{if("object"!=typeof t)throw new Error("Invalid value `"+t+"` for setting `options`");t=e(t)}for(r in Xt){if(null==(n=t[r])&&(n=i[r]),"blocks"!==r&&"boolean"!=typeof n||"blocks"===r&&"object"!=typeof n)throw new Error("Invalid value `"+n+"` for setting `options."+r+"`");t[r]=n}return u.options=t,u.escape=zt(t),u};var Zt=Jt;function Jt(e){if(null==e)return Kt;if("string"==typeof e)return function(e){return t;function t(t){return Boolean(t&&t.type===e)}}(e);if("object"==typeof e)return"length"in e?function(e){var t=[],r=-1;for(;++r<e.length;)t[r]=Jt(e[r]);return n;function n(){for(var e=-1;++e<t.length;)if(t[e].apply(this,arguments))return!0;return!1}}(e):function(e){return t;function t(t){var r;for(r in e)if(t[r]!==e[r])return!1;return!0}}(e);if("function"==typeof e)return e;throw new Error("Expected function, string, or object as test")}function Kt(){return!0}var Qt=function(e){return"\x1b[33m"+e+"\x1b[39m"};var er=tr;function tr(e,t,r,n){var u,i;"function"==typeof t&&"function"!=typeof r&&(n=r,r=t,t=null),i=Zt(t),u=n?-1:1,function e(o,a,c){var s,l="object"==typeof o&&null!==o?o:{};"string"==typeof l.type&&(s="string"==typeof l.tagName?l.tagName:"string"==typeof l.name?l.name:void 0,f.displayName="node ("+Qt(l.type+(s?"<"+s+">":""))+")");return f;function f(){var s,l,f=c.concat(o),D=[];if((!t||i(o,a,c[c.length-1]||null))&&false===(D=function(e){if(null!==e&&"object"==typeof e&&"length"in e)return e;if("number"==typeof e)return[true,e];return[e]}(r(o,c)))[0])return D;if(o.children&&"skip"!==D[0])for(l=(n?o.children.length:-1)+u;l>-1&&l<o.children.length;){if(false===(s=e(o.children[l],l,f)())[0])return s;l="number"==typeof s[1]?s[1]:l+u}return D}}(e,null,[])()}tr.CONTINUE=true,tr.SKIP="skip",tr.EXIT=false;var rr=or,nr=er.CONTINUE,ur=er.SKIP,ir=er.EXIT;function or(e,t,r,n){"function"==typeof t&&"function"!=typeof r&&(n=r,r=t,t=null),er(e,t,(function(e,t){var n=t[t.length-1],u=n?n.children.indexOf(e):null;return r(e,u,n)}),n)}or.CONTINUE=nr,or.SKIP=ur,or.EXIT=ir;var ar=function(e,t){return rr(e,t?cr:sr),e};function cr(e){delete e.position}function sr(e){e.position=void 0}var lr=function(){var t,r=this,n=String(r.file),u={line:1,column:1,offset:0},i=e(u);65279===(n=n.replace(fr,"\n")).charCodeAt(0)&&(n=n.slice(1),i.column++,i.offset++);t={type:"root",children:r.tokenizeBlock(n,i),position:{start:u,end:r.eof||e(u)}},r.options.position||ar(t,!0);return t},fr=/\r\n|\r/g;var Dr=/^[ \t]*(\n|$)/,pr=function(e,t,r){var n,u="",i=0,o=t.length;for(;i<o&&null!=(n=Dr.exec(t.slice(i)));)i+=n[0].length,u+=n[0];if(""===u)return;if(r)return!0;e(u)}
/*!
 * repeat-string <https://github.com/jonschlinkert/repeat-string>
 *
 * Copyright (c) 2014-2015, Jon Schlinkert.
 * Licensed under the MIT License.
 */;var hr,dr="",gr=function(e,t){if("string"!=typeof e)throw new TypeError("expected a string");if(1===t)return e;if(2===t)return e+e;var r=e.length*t;if(hr!==e||void 0===hr)hr=e,dr="";else if(dr.length>=r)return dr.substr(0,r);for(;r>dr.length&&t>1;)1&t&&(dr+=e),t>>=1,e+=e;return dr=(dr+=e).substr(0,r)};var mr=function(e){return String(e).replace(/\n+$/,"")};var Er=function(e,t,r){var n,u,i,o=-1,a=t.length,c="",s="",l="",f="";for(;++o<a;)if(n=t.charAt(o),i)if(i=!1,c+=l,s+=f,l="",f="","\n"===n)l=n,f=n;else for(c+=n,s+=n;++o<a;){if(!(n=t.charAt(o))||"\n"===n){f=n,l=n;break}c+=n,s+=n}else if(" "===n&&t.charAt(o+1)===n&&t.charAt(o+2)===n&&t.charAt(o+3)===n)l+=yr,o+=3,i=!0;else if("\t"===n)l+=n,i=!0;else{for(u="";"\t"===n||" "===n;)u+=n,n=t.charAt(++o);if("\n"!==n)break;l+=u+n,f+=n}if(s)return!!r||e(c)({type:"code",lang:null,meta:null,value:mr(s)})},yr=gr(" ",4);var vr=function(e,t,r){var n,u,i,o,a,c,s,l,f,D,p,h,d,g=this,m=g.options.gfm,E=t.length+1,y=0,v="";if(!m)return;for(;y<E&&(" "===(i=t.charAt(y))||"\t"===i);)v+=i,y++;if(h=y,"~"!==(i=t.charAt(y))&&"`"!==i)return;y++,u=i,n=1,v+=i;for(;y<E&&(i=t.charAt(y))===u;)v+=i,n++,y++;if(n<3)return;for(;y<E&&(" "===(i=t.charAt(y))||"\t"===i);)v+=i,y++;o="",s="";for(;y<E&&"\n"!==(i=t.charAt(y))&&("`"!==u||i!==u);)" "===i||"\t"===i?s+=i:(o+=s+i,s=""),y++;if((i=t.charAt(y))&&"\n"!==i)return;if(r)return!0;(d=e.now()).column+=v.length,d.offset+=v.length,v+=o,o=g.decode.raw(g.unescape(o),d),s&&(v+=s);s="",D="",p="",l="",f="";var b=!0;for(;y<E;)if(i=t.charAt(y),l+=D,f+=p,D="",p="","\n"===i){for(b?(v+=i,b=!1):(D+=i,p+=i),s="",y++;y<E&&" "===(i=t.charAt(y));)s+=i,y++;if(D+=s,p+=s.slice(h),!(s.length>=4)){for(s="";y<E&&(i=t.charAt(y))===u;)s+=i,y++;if(D+=s,p+=s,!(s.length<n)){for(s="";y<E&&(" "===(i=t.charAt(y))||"\t"===i);)D+=i,p+=i,y++;if(!i||"\n"===i)break}}}else l+=i,p+=i,y++;v+=l+D,y=-1,E=o.length;for(;++y<E;)if(" "===(i=o.charAt(y))||"\t"===i)a||(a=o.slice(0,y));else if(a){c=o.slice(y);break}return e(v)({type:"code",lang:a||o||null,meta:c||null,value:f})};var br=u((function(e,t){(t=e.exports=function(e){return e.replace(/^\s*|\s*$/g,"")}).left=function(e){return e.replace(/^\s*/,"")},t.right=function(e){return e.replace(/\s*$/,"")}})),Cr=function(e,t,r,n){var u,i,o=e.length,a=-1;for(;++a<o;)if(u=e[a],(void 0===(i=u[1]||{}).pedantic||i.pedantic===r.options.pedantic)&&(void 0===i.commonmark||i.commonmark===r.options.commonmark)&&t[u[0]].apply(r,n))return!0;return!1};var Ar=function(e,t,r){var n,u,i,o,a,c,s,l,f,D=this,p=D.offset,h=D.blockTokenizers,d=D.interruptBlockquote,g=e.now(),m=g.line,E=t.length,y=[],v=[],b=[],C=0;for(;C<E&&(" "===(u=t.charAt(C))||"\t"===u);)C++;if(">"!==t.charAt(C))return;if(r)return!0;C=0;for(;C<E;){for(o=t.indexOf("\n",C),s=C,l=!1,-1===o&&(o=E);C<E&&(" "===(u=t.charAt(C))||"\t"===u);)C++;if(">"===t.charAt(C)?(C++,l=!0," "===t.charAt(C)&&C++):C=s,a=t.slice(C,o),!l&&!br(a)){C=s;break}if(!l&&(i=t.slice(C),Cr(d,h,D,[e,i,!0])))break;c=s===C?a:t.slice(s,o),b.push(C-s),y.push(c),v.push(a),C=o+1}C=-1,E=b.length,n=e(y.join("\n"));for(;++C<E;)p[m]=(p[m]||0)+b[C],m++;return f=D.enterBlock(),v=D.tokenizeBlock(v.join("\n"),g),f(),n({type:"blockquote",children:v})};var wr=function(e,t,r){var n,u,i,o=this.options.pedantic,a=t.length+1,c=-1,s=e.now(),l="",f="";for(;++c<a;){if(" "!==(n=t.charAt(c))&&"\t"!==n){c--;break}l+=n}i=0;for(;++c<=a;){if("#"!==(n=t.charAt(c))){c--;break}l+=n,i++}if(i>6)return;if(!i||!o&&"#"===t.charAt(c+1))return;a=t.length+1,u="";for(;++c<a;){if(" "!==(n=t.charAt(c))&&"\t"!==n){c--;break}u+=n}if(!o&&0===u.length&&n&&"\n"!==n)return;if(r)return!0;l+=u,u="",f="";for(;++c<a&&(n=t.charAt(c))&&"\n"!==n;)if(" "===n||"\t"===n||"#"===n){for(;" "===n||"\t"===n;)u+=n,n=t.charAt(++c);if(o||!f||u||"#"!==n){for(;"#"===n;)u+=n,n=t.charAt(++c);for(;" "===n||"\t"===n;)u+=n,n=t.charAt(++c);c--}else f+=n}else f+=u+n,u="";return s.column+=l.length,s.offset+=l.length,e(l+=f+u)({type:"heading",depth:i,children:this.tokenizeInline(f,s)})};var Fr=function(e,t,r){var n,u,i,o,a=-1,c=t.length+1,s="";for(;++a<c&&("\t"===(n=t.charAt(a))||" "===n);)s+=n;if("*"!==n&&"-"!==n&&"_"!==n)return;u=n,s+=n,i=1,o="";for(;++a<c;)if((n=t.charAt(a))===u)i++,s+=o+u,o="";else{if(" "!==n)return i>=3&&(!n||"\n"===n)?(s+=o,!!r||e(s)({type:"thematicBreak"})):void 0;o+=n}};var kr=function(e){var t,r=0,n=0,u=e.charAt(r),i={},o=0;for(;"\t"===u||" "===u;){for(n+=t="\t"===u?4:1,t>1&&(n=Math.floor(n/t)*t);o<n;)i[++o]=r;u=e.charAt(++r)}return{indent:n,stops:i}};var Or=function(e,t){var r,n,u,i=e.split("\n"),o=i.length+1,a=1/0,c=[];i.unshift(gr(" ",t)+"!");for(;o--;)if(n=kr(i[o]),c[o]=n.stops,0!==br(i[o]).length){if(!n.indent){a=1/0;break}n.indent>0&&n.indent<a&&(a=n.indent)}if(a!==1/0)for(o=i.length;o--;){for(u=c[o],r=a;r&&!(r in u);)r--;i[o]=i[o].slice(u[r]+1)}return i.shift(),i.join("\n")};var Tr=function(e,t,r){var n,u,i,o,a,c,s,l,f,D,p,h,d,g,m,E,y,v,b,C,A,w,F,k=this,O=k.options.commonmark,T=k.options.pedantic,S=k.blockTokenizers,I=k.interruptList,R=0,x=t.length,B=null,N=!1;for(;R<x&&("\t"===(o=t.charAt(R))||" "===o);)R++;if("*"===(o=t.charAt(R))||"+"===o||"-"===o)a=o,i=!1;else{for(i=!0,u="";R<x&&(o=t.charAt(R),yt(o));)u+=o,R++;if(o=t.charAt(R),!u||!("."===o||O&&")"===o))return;if(r&&"1"!==u)return;B=parseInt(u,10),a=o}if(" "!==(o=t.charAt(++R))&&"\t"!==o&&(T||"\n"!==o&&""!==o))return;if(r)return!0;R=0,g=[],m=[],E=[];for(;R<x;){for(c=t.indexOf("\n",R),s=R,l=!1,F=!1,-1===c&&(c=x),n=0;R<x;){if("\t"===(o=t.charAt(R)))n+=4-n%4;else{if(" "!==o)break;n++}R++}if(y&&n>=y.indent&&(F=!0),o=t.charAt(R),f=null,!F){if("*"===o||"+"===o||"-"===o)f=o,R++,n++;else{for(u="";R<x&&(o=t.charAt(R),yt(o));)u+=o,R++;o=t.charAt(R),R++,u&&("."===o||O&&")"===o)&&(f=o,n+=u.length+1)}if(f)if("\t"===(o=t.charAt(R)))n+=4-n%4,R++;else if(" "===o){for(w=R+4;R<w&&" "===t.charAt(R);)R++,n++;R===w&&" "===t.charAt(R)&&(R-=3,n-=3)}else"\n"!==o&&""!==o&&(f=null)}if(f){if(!T&&a!==f)break;l=!0}else O||F||" "!==t.charAt(s)?O&&y&&(F=n>=y.indent||n>4):F=!0,l=!1,R=s;if(p=t.slice(s,c),D=s===R?p:t.slice(R,c),("*"===f||"_"===f||"-"===f)&&S.thematicBreak.call(k,e,p,!0))break;if(h=d,d=!l&&!br(D).length,F&&y)y.value=y.value.concat(E,p),m=m.concat(E,p),E=[];else if(l)0!==E.length&&(N=!0,y.value.push(""),y.trail=E.concat()),y={value:[p],indent:n,trail:[]},g.push(y),m=m.concat(E,p),E=[];else if(d){if(h&&!O)break;E.push(p)}else{if(h)break;if(Cr(I,S,k,[e,p,!0]))break;y.value=y.value.concat(E,p),m=m.concat(E,p),E=[]}R=c+1}C=e(m.join("\n")).reset({type:"list",ordered:i,start:B,spread:N,children:[]}),v=k.enterList(),b=k.enterBlock(),R=-1,x=g.length;for(;++R<x;)y=g[R].value.join("\n"),A=e.now(),e(y)(Nr(k,y,A),C),y=g[R].trail.join("\n"),R!==x-1&&(y+="\n"),e(y);return v(),b(),C},Sr=/\n\n(?!\s*$)/,Ir=/^\[([ X\tx])][ \t]/,Rr=/^([ \t]*)([*+-]|\d+[.)])( {1,4}(?! )| |\t|$|(?=\n))([^\n]*)/,xr=/^([ \t]*)([*+-]|\d+[.)])([ \t]+)/,Br=/^( {1,4}|\t)?/gm;function Nr(e,t,r){var n,u,i=e.offset,o=e.options.pedantic?Lr:Pr,a=null;return t=o.apply(null,arguments),e.options.gfm&&(n=t.match(Ir))&&(u=n[0].length,a="x"===n[1].toLowerCase(),i[r.line]+=u,t=t.slice(u)),{type:"listItem",spread:Sr.test(t),checked:a,children:e.tokenizeBlock(t,r)}}function Lr(e,t,r){var n=e.offset,u=r.line;return t=t.replace(xr,i),u=r.line,t.replace(Br,i);function i(e){return n[u]=(n[u]||0)+e.length,u++,""}}function Pr(e,t,r){var n,u,i,o,a,c,s,l=e.offset,f=r.line;for(o=(t=t.replace(Rr,(function(e,t,r,o,a){u=t+r+o,i=a,Number(r)<10&&u.length%2==1&&(r=" "+r);return(n=t+gr(" ",r.length)+o)+i}))).split("\n"),(a=Or(t,kr(n).indent).split("\n"))[0]=i,l[f]=(l[f]||0)+u.length,f++,c=0,s=o.length;++c<s;)l[f]=(l[f]||0)+o[c].length-a[c].length,f++;return a.join("\n")}var qr=function(e,t,r){var n,u,i,o,a,c=e.now(),s=t.length,l=-1,f="";for(;++l<s;){if(" "!==(i=t.charAt(l))||l>=3){l--;break}f+=i}n="",u="";for(;++l<s;){if("\n"===(i=t.charAt(l))){l--;break}" "===i||"\t"===i?u+=i:(n+=u+i,u="")}if(c.column+=f.length,c.offset+=f.length,f+=n+u,i=t.charAt(++l),o=t.charAt(++l),"\n"!==i||"="!==o&&"-"!==o)return;f+=i,u=o,a="="===o?1:2;for(;++l<s;){if((i=t.charAt(l))!==o){if("\n"!==i)return;l--;break}u+=i}if(r)return!0;return e(f+u)({type:"heading",depth:a,children:this.tokenizeInline(n,c)})};var jr="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\u0000-\\u0020]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",_r="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",Ur={openCloseTag:new RegExp("^(?:"+jr+"|"+_r+")"),tag:new RegExp("^(?:"+jr+"|"+_r+"|\x3c!----\x3e|\x3c!--(?:-?[^>-])(?:-?[^-])*--\x3e|<[?].*?[?]>|<![A-Za-z]+\\s+[^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)")},$r=function(e,t,r){var n,u,i,o,a,c,s,l=this.options.blocks.join("|"),f=new RegExp("^</?("+l+")(?=(\\s|/?>|$))","i"),D=t.length,p=0,h=[[Mr,zr,!0],[Gr,Vr,!0],[Yr,Hr,!0],[Xr,Wr,!0],[Zr,Jr,!0],[f,Kr,!0],[Qr,Kr,!1]];for(;p<D&&("\t"===(o=t.charAt(p))||" "===o);)p++;if("<"!==t.charAt(p))return;n=-1===(n=t.indexOf("\n",p+1))?D:n,u=t.slice(p,n),i=-1,a=h.length;for(;++i<a;)if(h[i][0].test(u)){c=h[i];break}if(!c)return;if(r)return c[2];if(p=n,!c[1].test(u))for(;p<D;){if(n=-1===(n=t.indexOf("\n",p+1))?D:n,u=t.slice(p+1,n),c[1].test(u)){u&&(p=n);break}p=n}return s=t.slice(0,p),e(s)({type:"html",value:s})},Mr=/^<(script|pre|style)(?=(\s|>|$))/i,zr=/<\/(script|pre|style)>/i,Gr=/^<!--/,Vr=/-->/,Yr=/^<\?/,Hr=/\?>/,Xr=/^<![A-Za-z]/,Wr=/>/,Zr=/^<!\[CDATA\[/,Jr=/]]>/,Kr=/^$/,Qr=new RegExp(Ur.openCloseTag.source+"\\s*$");var en=function(e){return rn.test("number"==typeof e?tn(e):e.charAt(0))},tn=String.fromCharCode,rn=/\s/;var nn=function(e){return String(e).replace(/\s+/g," ")};var un=function(e){return nn(e).toLowerCase()};var on=function(e,t,r){var n,u,i,o,a,c,s,l,f=this,D=f.options.commonmark,p=0,h=t.length,d="";for(;p<h&&(" "===(o=t.charAt(p))||"\t"===o);)d+=o,p++;if("["!==(o=t.charAt(p)))return;p++,d+=o,i="";for(;p<h&&"]"!==(o=t.charAt(p));)"\\"===o&&(i+=o,p++,o=t.charAt(p)),i+=o,p++;if(!i||"]"!==t.charAt(p)||":"!==t.charAt(p+1))return;c=i,p=(d+=i+"]:").length,i="";for(;p<h&&("\t"===(o=t.charAt(p))||" "===o||"\n"===o);)d+=o,p++;if(o=t.charAt(p),i="",n=d,"<"===o){for(p++;p<h&&an(o=t.charAt(p));)i+=o,p++;if((o=t.charAt(p))===an.delimiter)d+="<"+i+o,p++;else{if(D)return;p-=i.length+1,i=""}}if(!i){for(;p<h&&cn(o=t.charAt(p));)i+=o,p++;d+=i}if(!i)return;s=i,i="";for(;p<h&&("\t"===(o=t.charAt(p))||" "===o||"\n"===o);)i+=o,p++;o=t.charAt(p),a=null,'"'===o?a='"':"'"===o?a="'":"("===o&&(a=")");if(a){if(!i)return;for(p=(d+=i+o).length,i="";p<h&&(o=t.charAt(p))!==a;){if("\n"===o){if(p++,"\n"===(o=t.charAt(p))||o===a)return;i+="\n"}i+=o,p++}if((o=t.charAt(p))!==a)return;u=d,d+=i+o,p++,l=i,i=""}else i="",p=d.length;for(;p<h&&("\t"===(o=t.charAt(p))||" "===o);)d+=o,p++;if(!(o=t.charAt(p))||"\n"===o)return!!r||(n=e(n).test().end,s=f.decode.raw(f.unescape(s),n,{nonTerminated:!1}),l&&(u=e(u).test().end,l=f.decode.raw(f.unescape(l),u)),e(d)({type:"definition",identifier:un(c),label:c,title:l||null,url:s}))};function an(e){return">"!==e&&"["!==e&&"]"!==e}function cn(e){return"["!==e&&"]"!==e&&!en(e)}an.delimiter=">";var sn=function(e,t,r){var n,u,i,o,a,c,s,l,f,D,p,h,d,g,m,E,y,v,b,C,A,w;if(!this.options.gfm)return;n=0,E=0,c=t.length+1,s=[];for(;n<c;){if(C=t.indexOf("\n",n),A=t.indexOf("|",n+1),-1===C&&(C=t.length),-1===A||A>C){if(E<2)return;break}s.push(t.slice(n,C)),E++,n=C+1}o=s.join("\n"),u=s.splice(1,1)[0]||[],n=0,c=u.length,E--,i=!1,p=[];for(;n<c;){if("|"===(f=u.charAt(n))){if(D=null,!1===i){if(!1===w)return}else p.push(i),i=!1;w=!1}else if("-"===f)D=!0,i=i||null;else if(":"===f)i="left"===i?"center":D&&null===i?"right":"left";else if(!en(f))return;n++}!1!==i&&p.push(i);if(p.length<1)return;if(r)return!0;m=-1,v=[],b=e(o).reset({type:"table",align:p,children:v});for(;++m<E;){for(y=s[m],a={type:"tableRow",children:[]},m&&e("\n"),e(y).reset(a,b),c=y.length+1,n=0,l="",h="",d=!0;n<c;)"\t"!==(f=y.charAt(n))&&" "!==f?(""===f||"|"===f?d?e(f):(!h&&!f||d||(o=h,l.length>1&&(f?(o+=l.slice(0,-1),l=l.charAt(l.length-1)):(o+=l,l="")),g=e.now(),e(o)({type:"tableCell",children:this.tokenizeInline(h,g)},a)),e(l+f),l="",h=""):(l&&(h+=l,l=""),h+=f,"\\"===f&&n!==c-2&&(h+=y.charAt(n+1),n++)),d=!1,n++):(h?l+=f:e(f),n++);m||e("\n"+u)}return b};var ln=function(e,t,r){var n,u,i,o,a,c=this,s=c.options.commonmark,l=c.blockTokenizers,f=c.interruptParagraph,D=t.indexOf("\n"),p=t.length;for(;D<p;){if(-1===D){D=p;break}if("\n"===t.charAt(D+1))break;if(s){for(o=0,n=D+1;n<p;){if("\t"===(i=t.charAt(n))){o=4;break}if(" "!==i)break;o++,n++}if(o>=4&&"\n"!==i){D=t.indexOf("\n",D+1);continue}}if(u=t.slice(D+1),Cr(f,l,c,[e,u,!0]))break;if(n=D,-1!==(D=t.indexOf("\n",D+1))&&""===br(t.slice(n,D))){D=n;break}}if(u=t.slice(0,D),r)return!0;return a=e.now(),u=mr(u),e(u)({type:"paragraph",children:c.tokenizeInline(u,a)})};var fn=function(e,t){return e.indexOf("\\",t)};var Dn=pn;pn.locator=fn;function pn(e,t,r){var n,u;if("\\"===t.charAt(0)&&(n=t.charAt(1),-1!==this.escape.indexOf(n)))return!!r||(u="\n"===n?{type:"break"}:{type:"text",value:n},e("\\"+n)(u))}var hn=function(e,t){return e.indexOf("<",t)};var dn=mn;mn.locator=hn,mn.notInLink=!0;var gn="mailto:".length;function mn(e,t,r){var n,u,i,o,a,c=this,s="",l=t.length,f=0,D="",p=!1,h="";if("<"===t.charAt(0)){for(f++,s="<";f<l&&(n=t.charAt(f),!(en(n)||">"===n||"@"===n||":"===n&&"/"===t.charAt(f+1)));)D+=n,f++;if(D){if(h+=D,D="",h+=n=t.charAt(f),f++,"@"===n)p=!0;else{if(":"!==n||"/"!==t.charAt(f+1))return;h+="/",f++}for(;f<l&&(n=t.charAt(f),!en(n)&&">"!==n);)D+=n,f++;if(n=t.charAt(f),D&&">"===n)return!!r||(i=h+=D,s+=h+n,(u=e.now()).column++,u.offset++,p&&("mailto:"===h.slice(0,gn).toLowerCase()?(i=i.slice(gn),u.column+=gn,u.offset+=gn):h="mailto:"+h),o=c.inlineTokenizers,c.inlineTokenizers={text:o.text},a=c.enterLink(),i=c.tokenizeInline(i,u),c.inlineTokenizers=o,a(),e(s)({type:"link",title:null,url:kt(h,{nonTerminated:!1}),children:i}))}}}var En=function(e,t){var r,n=String(e),u=0;if("string"!=typeof t)throw new Error("Expected character");r=n.indexOf(t);for(;-1!==r;)u++,r=n.indexOf(t,r+t.length);return u};var yn=function(e,t){var r,n,u,i=-1;if(!this.options.gfm)return i;n=vn.length,r=-1;for(;++r<n;)-1!==(u=e.indexOf(vn[r],t))&&(-1===i||u<i)&&(i=u);return i},vn=["www.","http://","https://"];var bn=Cn;Cn.locator=yn,Cn.notInLink=!0;function Cn(e,t,r){var n,u,i,o,a,c,s,l,f,D,p,h,d,g,m=this,E=m.options.gfm,y=m.inlineTokenizers,v=t.length,b=-1,C=!1;if(E){if("www."===t.slice(0,4))C=!0,o=4;else if("http://"===t.slice(0,7).toLowerCase())o=7;else{if("https://"!==t.slice(0,8).toLowerCase())return;o=8}for(b=o-1,i=o,n=[];o<v;)if(46!==(s=t.charCodeAt(o))){if(!yt(s)&&!bt(s)&&45!==s&&95!==s)break;o++}else{if(b===o-1)break;n.push(o),b=o,o++}if(46===s&&(n.pop(),o--),void 0!==n[0]&&(u=n.length<2?i:n[n.length-2]+1,-1===t.slice(u,o).indexOf("_"))){if(r)return!0;for(l=o,a=o;o<v&&(s=t.charCodeAt(o),!en(s)&&60!==s);)o++,33===s||42===s||44===s||46===s||58===s||63===s||95===s||126===s||(l=o);if(o=l,41===t.charCodeAt(o-1))for(c=t.slice(a,o),f=En(c,"("),D=En(c,")");D>f;)o=a+c.lastIndexOf(")"),c=t.slice(a,o),D--;if(59===t.charCodeAt(o-1)&&(o--,bt(t.charCodeAt(o-1)))){for(l=o-2;bt(t.charCodeAt(l));)l--;38===t.charCodeAt(l)&&(o=l)}return p=t.slice(0,o),d=kt(p,{nonTerminated:!1}),C&&(d="http://"+d),g=m.enterLink(),m.inlineTokenizers={text:y.text},h=m.tokenizeInline(p,e.now()),m.inlineTokenizers=y,g(),e(p)({type:"link",title:null,url:d,children:h})}}}var An=function e(t,r){var n,u;if(!this.options.gfm)return-1;if(-1===(n=t.indexOf("@",r)))return-1;if((u=n)===r||!wn(t.charCodeAt(u-1)))return e.call(this,t,n+1);for(;u>r&&wn(t.charCodeAt(u-1));)u--;return u};function wn(e){return yt(e)||bt(e)||43===e||45===e||46===e||95===e}var Fn=kn;kn.locator=An,kn.notInLink=!0;function kn(e,t,r){var n,u,i,o,a=this,c=a.options.gfm,s=a.inlineTokenizers,l=0,f=t.length,D=-1;if(c){for(n=t.charCodeAt(l);yt(n)||bt(n)||43===n||45===n||46===n||95===n;)n=t.charCodeAt(++l);if(0!==l&&64===n){for(l++;l<f&&(n=t.charCodeAt(l),yt(n)||bt(n)||45===n||46===n||95===n);)l++,-1===D&&46===n&&(D=l);if(-1!==D&&D!==l&&45!==n&&95!==n)return 46===n&&l--,u=t.slice(0,l),!!r||(o=a.enterLink(),a.inlineTokenizers={text:s.text},i=a.tokenizeInline(u,e.now()),a.inlineTokenizers=s,o(),e(u)({type:"link",title:null,url:"mailto:"+kt(u,{nonTerminated:!1}),children:i}))}}}var On=Ur.tag,Tn=Rn;Rn.locator=hn;var Sn=/^<a /i,In=/^<\/a>/i;function Rn(e,t,r){var n,u,i=this,o=t.length;if(!("<"!==t.charAt(0)||o<3)&&(n=t.charAt(1),(bt(n)||"?"===n||"!"===n||"/"===n)&&(u=t.match(On))))return!!r||(u=u[0],!i.inLink&&Sn.test(u)?i.inLink=!0:i.inLink&&In.test(u)&&(i.inLink=!1),e(u)({type:"html",value:u}))}var xn=function(e,t){var r=e.indexOf("[",t),n=e.indexOf("![",t);if(-1===n)return r;return r<n?r:n};var Bn=Nn;Nn.locator=xn;function Nn(e,t,r){var n,u,i,o,a,c,s,l,f,D,p,h,d,g,m,E,y,v,b=this,C="",A=0,w=t.charAt(0),F=b.options.pedantic,k=b.options.commonmark,O=b.options.gfm;if("!"===w&&(l=!0,C=w,w=t.charAt(++A)),"["===w&&(l||!b.inLink)){for(C+=w,g="",A++,p=t.length,d=0,(E=e.now()).column+=A,E.offset+=A;A<p;){if(c=w=t.charAt(A),"`"===w){for(u=1;"`"===t.charAt(A+1);)c+=w,A++,u++;i?u>=i&&(i=0):i=u}else if("\\"===w)A++,c+=t.charAt(A);else if(i&&!O||"["!==w){if((!i||O)&&"]"===w){if(!d){if("("!==t.charAt(A+1))return;c+="(",n=!0,A++;break}d--}}else d++;g+=c,c="",A++}if(n){for(f=g,C+=g+c,A++;A<p&&(w=t.charAt(A),en(w));)C+=w,A++;if(g="",o=C,"<"===(w=t.charAt(A))){for(A++,o+="<";A<p&&">"!==(w=t.charAt(A));){if(k&&"\n"===w)return;g+=w,A++}if(">"!==t.charAt(A))return;C+="<"+g+">",m=g,A++}else{for(w=null,c="";A<p&&(w=t.charAt(A),!c||!('"'===w||"'"===w||k&&"("===w));){if(en(w)){if(!F)break;c+=w}else{if("("===w)d++;else if(")"===w){if(0===d)break;d--}g+=c,c="","\\"===w&&(g+="\\",w=t.charAt(++A)),g+=w}A++}m=g,A=(C+=g).length}for(g="";A<p&&(w=t.charAt(A),en(w));)g+=w,A++;if(w=t.charAt(A),C+=g,g&&('"'===w||"'"===w||k&&"("===w))if(A++,g="",D="("===w?")":w,a=C+=w,k){for(;A<p&&(w=t.charAt(A))!==D;)"\\"===w&&(g+="\\",w=t.charAt(++A)),A++,g+=w;if((w=t.charAt(A))!==D)return;for(h=g,C+=g+w,A++;A<p&&(w=t.charAt(A),en(w));)C+=w,A++}else for(c="";A<p;){if((w=t.charAt(A))===D)s&&(g+=D+c,c=""),s=!0;else if(s){if(")"===w){C+=g+D+c,h=g;break}en(w)?c+=w:(g+=D+c+w,c="",s=!1)}else g+=w;A++}if(")"===t.charAt(A))return!!r||(C+=")",m=b.decode.raw(b.unescape(m),e(o).test().end,{nonTerminated:!1}),h&&(a=e(a).test().end,h=b.decode.raw(b.unescape(h),a)),v={type:l?"image":"link",title:h||null,url:m},l?v.alt=b.decode.raw(b.unescape(f),E)||null:(y=b.enterLink(),v.children=b.tokenizeInline(f,E),y()),e(C)(v))}}}var Ln=Pn;Pn.locator=xn;function Pn(e,t,r){var n,u,i,o,a,c,s,l,f=this,D=f.options.commonmark,p=t.charAt(0),h=0,d=t.length,g="",m="",E="link",y="shortcut";if("!"===p&&(E="image",m=p,p=t.charAt(++h)),"["===p){for(h++,m+=p,c="",l=0;h<d;){if("["===(p=t.charAt(h)))s=!0,l++;else if("]"===p){if(!l)break;l--}"\\"===p&&(c+="\\",p=t.charAt(++h)),c+=p,h++}if(g=c,n=c,"]"===(p=t.charAt(h))){if(h++,g+=p,c="",!D)for(;h<d&&(p=t.charAt(h),en(p));)c+=p,h++;if("["===(p=t.charAt(h))){for(u="",c+=p,h++;h<d&&"["!==(p=t.charAt(h))&&"]"!==p;)"\\"===p&&(u+="\\",p=t.charAt(++h)),u+=p,h++;"]"===(p=t.charAt(h))?(y=u?"full":"collapsed",c+=u+p,h++):u="",g+=c,c=""}else{if(!n)return;u=n}if("full"===y||!s)return g=m+g,"link"===E&&f.inLink?null:!!r||((i=e.now()).column+=m.length,i.offset+=m.length,o={type:E+"Reference",identifier:un(u="full"===y?u:n),label:u,referenceType:y},"link"===E?(a=f.enterLink(),o.children=f.tokenizeInline(n,i),a()):o.alt=f.decode.raw(f.unescape(n),i)||null,e(g)(o))}}}var qn=function(e,t){var r=e.indexOf("**",t),n=e.indexOf("__",t);if(-1===n)return r;if(-1===r)return n;return n<r?n:r};var jn=_n;_n.locator=qn;function _n(e,t,r){var n,u,i,o,a,c,s,l=0,f=t.charAt(l);if(!("*"!==f&&"_"!==f||t.charAt(++l)!==f||(u=this.options.pedantic,a=(i=f)+i,c=t.length,l++,o="",f="",u&&en(t.charAt(l)))))for(;l<c;){if(s=f,!((f=t.charAt(l))!==i||t.charAt(l+1)!==i||u&&en(s))&&(f=t.charAt(l+2))!==i){if(!br(o))return;return!!r||((n=e.now()).column+=2,n.offset+=2,e(a+o+a)({type:"strong",children:this.tokenizeInline(o,n)}))}u||"\\"!==f||(o+=f,f=t.charAt(++l)),o+=f,l++}}var Un=function(e){return Mn.test("number"==typeof e?$n(e):e.charAt(0))},$n=String.fromCharCode,Mn=/\w/;var zn=function(e,t){var r=e.indexOf("*",t),n=e.indexOf("_",t);if(-1===n)return r;if(-1===r)return n;return n<r?n:r};var Gn=Vn;Vn.locator=zn;function Vn(e,t,r){var n,u,i,o,a,c,s,l=0,f=t.charAt(l);if(!("*"!==f&&"_"!==f||(u=this.options.pedantic,a=f,i=f,c=t.length,l++,o="",f="",u&&en(t.charAt(l)))))for(;l<c;){if(s=f,!((f=t.charAt(l))!==i||u&&en(s))){if((f=t.charAt(++l))!==i){if(!br(o)||s===i)return;if(!u&&"_"===i&&Un(f)){o+=i;continue}return!!r||((n=e.now()).column++,n.offset++,e(a+o+i)({type:"emphasis",children:this.tokenizeInline(o,n)}))}o+=i}u||"\\"!==f||(o+=f,f=t.charAt(++l)),o+=f,l++}}var Yn=function(e,t){return e.indexOf("~~",t)};var Hn=Xn;Xn.locator=Yn;function Xn(e,t,r){var n,u,i,o="",a="",c="",s="";if(this.options.gfm&&"~"===t.charAt(0)&&"~"===t.charAt(1)&&!en(t.charAt(2)))for(n=1,u=t.length,(i=e.now()).column+=2,i.offset+=2;++n<u;){if(!("~"!==(o=t.charAt(n))||"~"!==a||c&&en(c)))return!!r||e("~~"+s+"~~")({type:"delete",children:this.tokenizeInline(s,i)});s+=a,c=a,a=o}}var Wn=function(e,t){return e.indexOf("`",t)};var Zn=Jn;Jn.locator=Wn;function Jn(e,t,r){for(var n,u,i,o,a,c,s=t.length,l=0;l<s&&96===t.charCodeAt(l);)l++;if(0!==l&&l!==s){for(n=l,a=t.charCodeAt(l);l<s;){if(o=a,a=t.charCodeAt(l+1),96===o){if(void 0===u&&(u=l),i=l+1,96!==a&&i-u===n){c=!0;break}}else void 0!==u&&(u=void 0,i=void 0);l++}if(c){if(r)return!0;if(l=n,s=u,o=t.charCodeAt(l),a=t.charCodeAt(s-1),c=!1,s-l>2&&(32===o||10===o)&&(32===a||10===a)){for(l++,s--;l<s;){if(32!==(o=t.charCodeAt(l))&&10!==o){c=!0;break}l++}!0===c&&(n++,u--)}return e(t.slice(0,i))({type:"inlineCode",value:t.slice(n,u)})}}}var Kn=function(e,t){var r=e.indexOf("\n",t);for(;r>t&&" "===e.charAt(r-1);)r--;return r};var Qn=eu;eu.locator=Kn;function eu(e,t,r){for(var n,u=t.length,i=-1,o="";++i<u;){if("\n"===(n=t.charAt(i))){if(i<2)return;return!!r||e(o+=n)({type:"break"})}if(" "!==n)return;o+=n}}var tu=function(e,t,r){var n,u,i,o,a,c,s,l,f,D,p=this;if(r)return!0;n=p.inlineMethods,o=n.length,u=p.inlineTokenizers,i=-1,f=t.length;for(;++i<o;)"text"!==(l=n[i])&&u[l]&&((s=u[l].locator)||e.file.fail("Missing locator: `"+l+"`"),-1!==(c=s.call(p,t,1))&&c<f&&(f=c));a=t.slice(0,f),D=e.now(),p.decode(a,D,(function(t,r,n){e(n||t)({type:"text",value:t})}))};var ru=nu;function nu(t,r){this.file=r,this.offset={},this.options=e(this.options),this.setOptions({}),this.inList=!1,this.inBlock=!1,this.inLink=!1,this.atStart=!0,this.toOffset=dt(r).toOffset,this.unescape=gt(this,"escape"),this.decode=jt(this)}var uu=nu.prototype;function iu(e){var t,r=[];for(t in e)r.push(t);return r}uu.setOptions=Wt,uu.parse=lr,uu.options=Xt,uu.exitStart=ht("atStart",!0),uu.enterList=ht("inList",!1),uu.enterLink=ht("inLink",!1),uu.enterBlock=ht("inBlock",!1),uu.interruptParagraph=[["thematicBreak"],["list"],["atxHeading"],["fencedCode"],["blockquote"],["html"],["setextHeading",{commonmark:!1}],["definition",{commonmark:!1}]],uu.interruptList=[["atxHeading",{pedantic:!1}],["fencedCode",{pedantic:!1}],["thematicBreak",{pedantic:!1}],["definition",{commonmark:!1}]],uu.interruptBlockquote=[["indentedCode",{commonmark:!0}],["fencedCode",{commonmark:!0}],["atxHeading",{commonmark:!0}],["setextHeading",{commonmark:!0}],["thematicBreak",{commonmark:!0}],["html",{commonmark:!0}],["list",{commonmark:!0}],["definition",{commonmark:!1}]],uu.blockTokenizers={blankLine:pr,indentedCode:Er,fencedCode:vr,blockquote:Ar,atxHeading:wr,thematicBreak:Fr,list:Tr,setextHeading:qr,html:$r,definition:on,table:sn,paragraph:ln},uu.inlineTokenizers={escape:Dn,autoLink:dn,url:bn,email:Fn,html:Tn,link:Bn,reference:Ln,strong:jn,emphasis:Gn,deletion:Hn,code:Zn,break:Qn,text:tu},uu.blockMethods=iu(uu.blockTokenizers),uu.inlineMethods=iu(uu.inlineTokenizers),uu.tokenizeBlock=_t("block"),uu.tokenizeInline=_t("inline"),uu.tokenizeFactory=_t;var ou=au;function au(t){var r=this.data("settings"),n=pt(ru);n.prototype.options=e(n.prototype.options,r,t),this.Parser=n}au.Parser=ru;var cu=function(e){if(e)throw e}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */;var su=function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},lu=Object.prototype.hasOwnProperty,fu=Object.prototype.toString,Du=Object.defineProperty,pu=Object.getOwnPropertyDescriptor,hu=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===fu.call(e)},du=function(e){if(!e||"[object Object]"!==fu.call(e))return!1;var t,r=lu.call(e,"constructor"),n=e.constructor&&e.constructor.prototype&&lu.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!r&&!n)return!1;for(t in e);return void 0===t||lu.call(e,t)},gu=function(e,t){Du&&"__proto__"===t.name?Du(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},mu=function(e,t){if("__proto__"===t){if(!lu.call(e,t))return;if(pu)return pu(e,t).value}return e[t]},Eu=function e(){var t,r,n,u,i,o,a=arguments[0],c=1,s=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[1]||{},c=2),(null==a||"object"!=typeof a&&"function"!=typeof a)&&(a={});c<s;++c)if(null!=(t=arguments[c]))for(r in t)n=mu(a,r),a!==(u=mu(t,r))&&(l&&u&&(du(u)||(i=hu(u)))?(i?(i=!1,o=n&&hu(n)?n:[]):o=n&&du(n)?n:{},gu(a,{name:r,newValue:e(l,o,u)})):void 0!==u&&gu(a,{name:r,newValue:u}));return a},yu=e=>{if("[object Object]"!==Object.prototype.toString.call(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t===Object.prototype},vu=[].slice,bu=function(e,t){var r;return function(){var t,i=vu.call(arguments,0),o=e.length>i.length;o&&i.push(n);try{t=e.apply(null,i)}catch(e){if(o&&r)throw e;return n(e)}o||(t&&"function"==typeof t.then?t.then(u,n):t instanceof Error?n(t):u(t))};function n(){r||(r=!0,t.apply(null,arguments))}function u(e){n(null,e)}};var Cu=wu;wu.wrap=bu;var Au=[].slice;function wu(){var e=[],t={run:function(){var t=-1,r=Au.call(arguments,0,-1),n=arguments[arguments.length-1];if("function"!=typeof n)throw new Error("Expected function as last argument, not "+n);function u(i){var o=e[++t],a=Au.call(arguments,0),c=a.slice(1),s=r.length,l=-1;if(i)n(i);else{for(;++l<s;)null!==c[l]&&void 0!==c[l]||(c[l]=r[l]);r=c,o?bu(o,u).apply(null,r):n.apply(null,[null].concat(r))}}u.apply(null,[null].concat(r))},use:function(r){if("function"!=typeof r)throw new Error("Expected `fn` to be a function, not "+r);return e.push(r),t}};return t}var Fu={}.hasOwnProperty,ku=function(e){if(!e||"object"!=typeof e)return"";if(Fu.call(e,"position")||Fu.call(e,"type"))return Tu(e.position);if(Fu.call(e,"start")||Fu.call(e,"end"))return Tu(e);if(Fu.call(e,"line")||Fu.call(e,"column"))return Ou(e);return""};function Ou(e){return e&&"object"==typeof e||(e={}),Su(e.line)+":"+Su(e.column)}function Tu(e){return e&&"object"==typeof e||(e={}),Ou(e.start)+"-"+Ou(e.end)}function Su(e){return e&&"number"==typeof e?e:1}var Iu=Bu;function Ru(){}Ru.prototype=Error.prototype,Bu.prototype=new Ru;var xu=Bu.prototype;function Bu(e,t,r){var n,u,i;"string"==typeof t&&(r=t,t=null),n=function(e){var t,r=[null,null];"string"==typeof e&&(-1===(t=e.indexOf(":"))?r[1]=e:(r[0]=e.slice(0,t),r[1]=e.slice(t+1)));return r}(r),u=ku(t)||"1:1",i={start:{line:null,column:null},end:{line:null,column:null}},t&&t.position&&(t=t.position),t&&(t.start?(i=t,t=t.start):i.start=t),e.stack&&(this.stack=e.stack,e=e.message),this.message=e,this.name=u,this.reason=e,this.line=t?t.line:null,this.column=t?t.column:null,this.location=i,this.source=n[0],this.ruleId=n[1]}function Nu(e,t){for(var r=0,n=e.length-1;n>=0;n--){var u=e[n];"."===u?e.splice(n,1):".."===u?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r--;r)e.unshift("..");return e}xu.file="",xu.name="",xu.reason="",xu.message="",xu.stack="",xu.fatal=null,xu.column=null,xu.line=null;var Lu=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,Pu=function(e){return Lu.exec(e).slice(1)};function qu(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var n=r>=0?arguments[r]:"/";if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");n&&(e=n+"/"+e,t="/"===n.charAt(0))}return(t?"/":"")+(e=Nu(Yu(e.split("/"),(function(e){return!!e})),!t).join("/"))||"."}function ju(e){var t=_u(e),r="/"===Hu(e,-1);return(e=Nu(Yu(e.split("/"),(function(e){return!!e})),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e}function _u(e){return"/"===e.charAt(0)}function Uu(){var e=Array.prototype.slice.call(arguments,0);return ju(Yu(e,(function(e,t){if("string"!=typeof e)throw new TypeError("Arguments to path.join must be strings");return e})).join("/"))}function $u(e,t){function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=qu(e).substr(1),t=qu(t).substr(1);for(var n=r(e.split("/")),u=r(t.split("/")),i=Math.min(n.length,u.length),o=i,a=0;a<i;a++)if(n[a]!==u[a]){o=a;break}var c=[];for(a=o;a<n.length;a++)c.push("..");return(c=c.concat(u.slice(o))).join("/")}function Mu(e){var t=Pu(e),r=t[0],n=t[1];return r||n?(n&&(n=n.substr(0,n.length-1)),r+n):"."}function zu(e,t){var r=Pu(e)[2];return t&&r.substr(-1*t.length)===t&&(r=r.substr(0,r.length-t.length)),r}function Gu(e){return Pu(e)[3]}var Vu={extname:Gu,basename:zu,dirname:Mu,sep:"/",delimiter:":",relative:$u,join:Uu,isAbsolute:_u,normalize:ju,resolve:qu};function Yu(e,t){if(e.filter)return e.filter(t);for(var r=[],n=0;n<e.length;n++)t(e[n],n,e)&&r.push(e[n]);return r}var Hu="b"==="ab".substr(-1)?function(e,t,r){return e.substr(t,r)}:function(e,t,r){return t<0&&(t=e.length+t),e.substr(t,r)},Xu=n(Object.freeze({__proto__:null,resolve:qu,normalize:ju,isAbsolute:_u,join:Uu,relative:$u,sep:"/",delimiter:":",dirname:Mu,basename:zu,extname:Gu,default:Vu})),Wu=ke,Zu=Qu,Ju={}.hasOwnProperty,Ku=["history","path","basename","stem","extname","dirname"];function Qu(e){var t,r;if(e){if("string"==typeof e||su(e))e={contents:e};else if("message"in e&&"messages"in e)return e}else e={};if(!(this instanceof Qu))return new Qu(e);for(this.data={},this.messages=[],this.history=[],this.cwd=Wu.cwd(),r=-1;++r<Ku.length;)t=Ku[r],Ju.call(e,t)&&(this[t]=e[t]);for(t in e)Ku.indexOf(t)<0&&(this[t]=e[t])}function ei(e,t){if(e&&e.indexOf(Xu.sep)>-1)throw new Error("`"+t+"` cannot be a path: did not expect `"+Xu.sep+"`")}function ti(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function ri(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}Qu.prototype.toString=function(e){return(this.contents||"").toString(e)},Object.defineProperty(Qu.prototype,"path",{get:function(){return this.history[this.history.length-1]},set:function(e){ti(e,"path"),this.path!==e&&this.history.push(e)}}),Object.defineProperty(Qu.prototype,"dirname",{get:function(){return"string"==typeof this.path?Xu.dirname(this.path):void 0},set:function(e){ri(this.path,"dirname"),this.path=Xu.join(e||"",this.basename)}}),Object.defineProperty(Qu.prototype,"basename",{get:function(){return"string"==typeof this.path?Xu.basename(this.path):void 0},set:function(e){ti(e,"basename"),ei(e,"basename"),this.path=Xu.join(this.dirname||"",e)}}),Object.defineProperty(Qu.prototype,"extname",{get:function(){return"string"==typeof this.path?Xu.extname(this.path):void 0},set:function(e){if(ei(e,"extname"),ri(this.path,"extname"),e){if(46!==e.charCodeAt(0))throw new Error("`extname` must start with `.`");if(e.indexOf(".",1)>-1)throw new Error("`extname` cannot contain multiple dots")}this.path=Xu.join(this.dirname,this.stem+(e||""))}}),Object.defineProperty(Qu.prototype,"stem",{get:function(){return"string"==typeof this.path?Xu.basename(this.path,this.extname):void 0},set:function(e){ti(e,"stem"),ei(e,"stem"),this.path=Xu.join(this.dirname||"",e+(this.extname||""))}});var ni=Zu;Zu.prototype.message=function(e,t,r){var n=new Iu(e,t,r);this.path&&(n.name=this.path+":"+n.name,n.file=this.path);return n.fatal=!1,this.messages.push(n),n},Zu.prototype.info=function(){var e=this.message.apply(this,arguments);return e.fatal=null,e},Zu.prototype.fail=function(){var e=this.message.apply(this,arguments);throw e.fatal=!0,e};var ui=ni,ii=function e(){var t,r=[],n=Cu(),u={},i=-1;return o.data=function(e,r){if("string"==typeof e)return 2===arguments.length?(Di("data",t),u[e]=r,o):ai.call(u,e)&&u[e]||null;if(e)return Di("data",t),u=e,o;return u},o.freeze=a,o.attachers=r,o.use=function(e){var n;if(Di("use",t),null==e);else if("function"==typeof e)l.apply(null,arguments);else{if("object"!=typeof e)throw new Error("Expected usable value, not `"+e+"`");"length"in e?s(e):i(e)}n&&(u.settings=Eu(u.settings||{},n));return o;function i(e){s(e.plugins),e.settings&&(n=Eu(n||{},e.settings))}function a(e){if("function"==typeof e)l(e);else{if("object"!=typeof e)throw new Error("Expected usable value, not `"+e+"`");"length"in e?l.apply(null,e):i(e)}}function s(e){var t=-1;if(null==e);else{if("object"!=typeof e||!("length"in e))throw new Error("Expected a list of plugins, not `"+e+"`");for(;++t<e.length;)a(e[t])}}function l(e,t){var n=c(e);n?(yu(n[1])&&yu(t)&&(t=Eu(!0,n[1],t)),n[1]=t):r.push(oi.call(arguments))}},o.parse=function(e){var t,r=ui(e);if(a(),li("parse",t=o.Parser),si(t,"parse"))return new t(String(r),r).parse();return t(String(r),r)},o.stringify=function(e,t){var r,n=ui(t);if(a(),fi("stringify",r=o.Compiler),pi(e),si(r,"compile"))return new r(e,n).compile();return r(e,n)},o.run=s,o.runSync=function(e,t){var r,n;return s(e,t,u),hi("runSync","run",n),r;function u(e,t){n=!0,r=t,cu(e)}},o.process=l,o.processSync=f,o;function o(){for(var t=e(),n=-1;++n<r.length;)t.use.apply(null,r[n]);return t.data(Eu(!0,{},u)),t}function a(){var e,u;if(t)return o;for(;++i<r.length;)!1!==(e=r[i])[1]&&(!0===e[1]&&(e[1]=void 0),"function"==typeof(u=e[0].apply(o,e.slice(1)))&&n.use(u));return t=!0,i=1/0,o}function c(e){for(var t=-1;++t<r.length;)if(r[t][0]===e)return r[t]}function s(e,t,r){if(pi(e),a(),r||"function"!=typeof t||(r=t,t=null),!r)return new Promise(u);function u(u,i){n.run(e,ui(t),(function(t,n,o){n=n||e,t?i(t):u?u(n):r(null,n,o)}))}u(null,r)}function l(e,t){if(a(),li("process",o.Parser),fi("process",o.Compiler),!t)return new Promise(r);function r(r,n){var u=ui(e);ci.run(o,{file:u},(function(e){e?n(e):r?r(u):t(null,u)}))}r(null,t)}function f(e){var t,r;return a(),li("processSync",o.Parser),fi("processSync",o.Compiler),l(t=ui(e),(function(e){r=!0,cu(e)})),hi("processSync","process",r),t}}().freeze(),oi=[].slice,ai={}.hasOwnProperty,ci=Cu().use((function(e,t){t.tree=e.parse(t.file)})).use((function(e,t,r){e.run(t.tree,t.file,(function(e,n,u){e?r(e):(t.tree=n,t.file=u,r())}))})).use((function(e,t){var r=e.stringify(t.tree,t.file);null==r||("string"==typeof r||su(r)?t.file.contents=r:t.file.result=r)}));function si(e,t){return"function"==typeof e&&e.prototype&&(function(e){var t;for(t in e)return!0;return!1}(e.prototype)||t in e.prototype)}function li(e,t){if("function"!=typeof t)throw new Error("Cannot `"+e+"` without `Parser`")}function fi(e,t){if("function"!=typeof t)throw new Error("Cannot `"+e+"` without `Compiler`")}function Di(e,t){if(t)throw new Error("Cannot invoke `"+e+"` on a frozen processor.\nCreate a new processor first, by invoking it: use `processor()` instead of `processor`.")}function pi(e){if(!e||"string"!=typeof e.type)throw new Error("Expected node, got `"+e+"`")}function hi(e,t,r){if(!r)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}var di={isRemarkParser:function(e){return Boolean(e&&e.prototype&&e.prototype.blockTokenizers)},isRemarkCompiler:function(e){return Boolean(e&&e.prototype&&e.prototype.visitors)}},gi=function(e){const t=this.Parser,r=this.Compiler;di.isRemarkParser(t)&&function(e,t){const r=e.prototype,n=r.inlineMethods;function u(e,t){return e.indexOf("$",t)}function i(e,r,n){const u=r.length;let i,o,a,c,s,l,f,D=!1,p=!1,h=0;if(92===r.charCodeAt(h)&&(p=!0,h++),36===r.charCodeAt(h)){if(h++,p)return!!n||e(r.slice(0,h))({type:"text",value:"$"});if(36===r.charCodeAt(h)&&(D=!0,h++),a=r.charCodeAt(h),32!==a&&9!==a){for(c=h;h<u;){if(o=a,a=r.charCodeAt(h+1),36===o){if(i=r.charCodeAt(h-1),32!==i&&9!==i&&(a!=a||a<48||a>57)&&(!D||36===a)){s=h-1,h++,D&&h++,l=h;break}}else 92===o&&(h++,a=r.charCodeAt(h+1));h++}if(void 0!==l)return!!n||(f=r.slice(c,s+1),e(r.slice(0,l))({type:"inlineMath",value:f,data:{hName:"span",hProperties:{className:mi.concat(D&&t.inlineMathDouble?["math-display"]:[])},hChildren:[{type:"text",value:f}]}}))}}}i.locator=u,r.inlineTokenizers.math=i,n.splice(n.indexOf("text"),0,"math")}(t,e);di.isRemarkCompiler(r)&&function(e){function t(e){let t="$";return(e.data&&e.data.hProperties&&e.data.hProperties.className||[]).includes("math-display")&&(t="$$"),t+e.value+t}e.prototype.visitors.inlineMath=t}(r)};const mi=["math","math-inline"];var Ei=function(){const e=this.Parser,t=this.Compiler;di.isRemarkParser(e)&&function(e){const t=e.prototype,r=t.blockMethods,n=t.interruptParagraph,u=t.interruptList,i=t.interruptBlockquote;function o(e,t,r){var n=t.length,u=0;let i,o,a,c,s,l,f,D,p,h,d;for(;u<n&&32===t.charCodeAt(u);)u++;for(s=u;u<n&&36===t.charCodeAt(u);)u++;if(l=u-s,!(l<2)){for(;u<n&&32===t.charCodeAt(u);)u++;for(f=u;u<n;){if(i=t.charCodeAt(u),36===i)return;if(10===i)break;u++}if(10===t.charCodeAt(u)){if(r)return!0;for(o=[],f!==u&&o.push(t.slice(f,u)),u++,a=t.indexOf("\n",u+1),a=-1===a?n:a;u<n;){for(D=!1,h=u,d=a,c=a,p=0;c>h&&32===t.charCodeAt(c-1);)c--;for(;c>h&&36===t.charCodeAt(c-1);)p++,c--;for(l<=p&&t.indexOf("$",h)===c&&(D=!0,d=c);h<=d&&h-u<s&&32===t.charCodeAt(h);)h++;if(D)for(;d>h&&32===t.charCodeAt(d-1);)d--;if(D&&h===d||o.push(t.slice(h,d)),D)break;u=a+1,a=t.indexOf("\n",u+1),a=-1===a?n:a}return o=o.join("\n"),e(t.slice(0,a))({type:"math",value:o,data:{hName:"div",hProperties:{className:yi.concat()},hChildren:[{type:"text",value:o}]}})}}}t.blockTokenizers.math=o,r.splice(r.indexOf("fencedCode")+1,0,"math"),n.splice(n.indexOf("fencedCode")+1,0,["math"]),u.splice(u.indexOf("fencedCode")+1,0,["math"]),i.splice(i.indexOf("fencedCode")+1,0,["math"])}(e);di.isRemarkCompiler(t)&&function(e){function t(e){return"$$\n"+e.value+"\n$$"}e.prototype.visitors.math=t}(t)};const yi=["math","math-display"];var vi=function(e){var t=e||{};Ei.call(this,t),gi.call(this,t)};var bi=function(e){var t=this.Parser,r=this.Compiler;(function(e){return Boolean(e&&e.prototype&&e.prototype.blockTokenizers)})(t)&&function(e,t){var r,n=t||{},u=e.prototype,i=u.blockTokenizers,o=u.inlineTokenizers,a=u.blockMethods,c=u.inlineMethods,s=i.definition,l=o.reference,f=[],D=-1,p=a.length;for(;++D<p;)"newline"!==(r=a[D])&&"indentedCode"!==r&&"paragraph"!==r&&"footnoteDefinition"!==r&&f.push([r]);f.push(["footnoteDefinition"]),n.inlineNotes&&(Ci(c,"reference","inlineNote"),o.inlineNote=g);function h(e,t,r){for(var n,u,o,a,c,s,l,f,D,p,h,d,g,m=this,E=m.interruptFootnoteDefinition,y=m.offset,v=t.length+1,b=0,C=[];b<v&&(9===(a=t.charCodeAt(b))||32===a);)b++;if(91===t.charCodeAt(b++)&&94===t.charCodeAt(b++)){for(u=b;b<v;){if((a=t.charCodeAt(b))!=a||10===a||9===a||32===a)return;if(93===a){o=b,b++;break}b++}if(void 0!==o&&u!==o&&58===t.charCodeAt(b++)){if(r)return!0;for(n=t.slice(u,o),c=e.now(),D=0,p=0,h=b,d=[];b<v;){if((a=t.charCodeAt(b))!=a||10===a)g={start:D,contentStart:h||b,contentEnd:b,end:b},d.push(g),10===a&&(D=b+1,p=0,h=void 0,g.end=D);else if(void 0!==p)if(32===a||9===a)(p+=32===a?1:4-p%4)>4&&(p=void 0,h=b);else{if(p<4&&g&&(g.contentStart===g.contentEnd||Ai(E,i,m,[e,t.slice(b,1024),!0])))break;p=void 0,h=b}b++}for(b=-1,v=d.length;v>0&&(g=d[v-1]).contentStart===g.contentEnd;)v--;for(s=e(t.slice(0,g.contentEnd));++b<v;)g=d[b],y[c.line+b]=(y[c.line+b]||0)+(g.contentStart-g.start),C.push(t.slice(g.contentStart,g.end));return l=m.enterBlock(),f=m.tokenizeBlock(C.join(""),c),l(),s({type:"footnoteDefinition",identifier:n.toLowerCase(),label:n,children:f})}}}function d(e,t,r){var n,u,i,o,a=t.length+1,c=0;if(91===t.charCodeAt(c++)&&94===t.charCodeAt(c++)){for(u=c;c<a;){if((o=t.charCodeAt(c))!=o||10===o||9===o||32===o)return;if(93===o){i=c,c++;break}c++}if(void 0!==i&&u!==i)return!!r||(n=t.slice(u,i),e(t.slice(0,c))({type:"footnoteReference",identifier:n.toLowerCase(),label:n}))}}function g(e,t,r){var n,u,i,o,a,c,s,l=this,f=t.length+1,D=0,p=0;if(94===t.charCodeAt(D++)&&91===t.charCodeAt(D++)){for(i=D;D<f;){if((u=t.charCodeAt(D))!=u)return;if(void 0===c)if(92===u)D+=2;else if(91===u)p++,D++;else if(93===u){if(0===p){o=D,D++;break}p--,D++}else if(96===u){for(a=D,c=1;96===t.charCodeAt(a+c);)c++;D+=c}else D++;else if(96===u){for(a=D,s=1;96===t.charCodeAt(a+s);)s++;D+=s,c===s&&(c=void 0),s=void 0}else D++}if(void 0!==o)return!!r||((n=e.now()).column+=2,n.offset+=2,e(t.slice(0,D))({type:"footnote",children:l.tokenizeInline(t.slice(i,o),n)}))}}function m(e,t,r){var n=0;if(33===t.charCodeAt(n)&&n++,91===t.charCodeAt(n)&&94!==t.charCodeAt(n+1))return l.call(this,e,t,r)}function E(e,t,r){for(var n=0,u=t.charCodeAt(n);32===u||9===u;)u=t.charCodeAt(++n);if(91===u&&94!==t.charCodeAt(n+1))return s.call(this,e,t,r)}function y(e,t){return e.indexOf("[",t)}function v(e,t){return e.indexOf("^[",t)}Ci(a,"definition","footnoteDefinition"),Ci(c,"reference","footnoteCall"),i.definition=E,i.footnoteDefinition=h,o.footnoteCall=d,o.reference=m,u.interruptFootnoteDefinition=f,m.locator=l.locator,d.locator=y,g.locator=v}(t,e);(function(e){return Boolean(e&&e.prototype&&e.prototype.visitors)})(r)&&function(e){var t=e.prototype.visitors,r="    ";function n(e){return"^["+this.all(e).join("")+"]"}function u(e){return"[^"+(e.label||e.identifier)+"]"}function i(e){for(var t,n=this.all(e).join("\n\n").split("\n"),u=0,i=n.length;++u<i;)""!==(t=n[u])&&(n[u]=r+t);return"[^"+(e.label||e.identifier)+"]: "+n.join("\n")}t.footnote=n,t.footnoteReference=u,t.footnoteDefinition=i}(r)};function Ci(e,t,r){e.splice(e.indexOf(t),0,r)}function Ai(e,t,r,n){for(var u=e.length,i=-1;++i<u;)if(t[e[i][0]].apply(r,n))return!0;return!1}const wi=new RegExp("^(?<startDelimiter>-{3}|\\+{3})(?<language>[^\\n]*)\\n(?:|(?<value>.*?)\\n)(?<endDelimiter>\\k<startDelimiter>|\\.{3})[^\\S\\n]*(?:\\n|$)","s");var Fi=function(e){const t=e.match(wi);if(!t)return{content:e};const{startDelimiter:r,language:n,value:u="",endDelimiter:i}=t.groups;let o=n.trim()||"yaml";if("+++"===r&&(o="toml"),"yaml"!==o&&r!==i)return{content:e};const[a]=t;return{frontMatter:{type:"front-matter",lang:o,value:u,startDelimiter:r,endDelimiter:i,raw:a.replace(/\n$/,"")},content:a.replace(/[^\n]/g," ")+e.slice(a.length)}};const ki=["format","prettier"];function Oi(e){const t=`@(${ki.join("|")})`,r=new RegExp([`\x3c!--\\s*${t}\\s*--\x3e`,`\x3c!--.*\r?\n[\\s\\S]*(^|\n)[^\\S\n]*${t}[^\\S\n]*($|\n)[\\s\\S]*\n.*--\x3e`].join("|"),"m"),n=e.match(r);return n&&0===n.index}var Ti={startWithPragma:Oi,hasPragma:e=>Oi(Fi(e).content.trimStart()),insertPragma:e=>{const t=Fi(e),r=`\x3c!-- @${ki[0]} --\x3e`;return t.frontMatter?`${t.frontMatter.raw}\n\n${r}\n\n${t.content}`:`${r}\n\n${t.content}`}};var Si={locStart:function(e){return e.position.start.offset},locEnd:function(e){return e.position.end.offset}},Ii=e=>"string"==typeof e?e.replace((({onlyFirst:e=!1}={})=>{const t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")})(),""):e;const Ri=e=>!Number.isNaN(e)&&(e>=4352&&(e<=4447||9001===e||9002===e||11904<=e&&e<=12871&&12351!==e||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141));var xi=Ri,Bi=Ri;xi.default=Bi;const Ni=e=>{if("string"!=typeof e||0===e.length)return 0;if(0===(e=Ii(e)).length)return 0;e=e.replace(/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g,"  ");let t=0;for(let r=0;r<e.length;r++){const n=e.codePointAt(r);n<=31||n>=127&&n<=159||(n>=768&&n<=879||(n>65535&&r++,t+=xi(n)?2:1))}return t};var Li=Ni,Pi=Ni;Li.default=Pi;var qi=e=>{if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")};var ji=e=>e[e.length-1];function _i(e,t){if(null==e)return{};var r,n,u=function(e,t){if(null==e)return{};var r,n,u={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(u[r]=e[r]);return u}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(u[r]=e[r])}return u}var Ui=function(e){return e&&e.Math==Math&&e},$i=Ui("object"==typeof globalThis&&globalThis)||Ui("object"==typeof window&&window)||Ui("object"==typeof self&&self)||Ui("object"==typeof r&&r)||function(){return this}()||Function("return this")(),Mi=function(e){try{return!!e()}catch(e){return!0}},zi=!Mi((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),Gi={}.propertyIsEnumerable,Vi=Object.getOwnPropertyDescriptor,Yi={f:Vi&&!Gi.call({1:2},1)?function(e){var t=Vi(this,e);return!!t&&t.enumerable}:Gi},Hi=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},Xi={}.toString,Wi=function(e){return Xi.call(e).slice(8,-1)},Zi="".split,Ji=Mi((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==Wi(e)?Zi.call(e,""):Object(e)}:Object,Ki=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},Qi=function(e){return Ji(Ki(e))},eo=function(e){return"object"==typeof e?null!==e:"function"==typeof e},to=function(e,t){if(!eo(e))return e;var r,n;if(t&&"function"==typeof(r=e.toString)&&!eo(n=r.call(e)))return n;if("function"==typeof(r=e.valueOf)&&!eo(n=r.call(e)))return n;if(!t&&"function"==typeof(r=e.toString)&&!eo(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")},ro=function(e){return Object(Ki(e))},no={}.hasOwnProperty,uo=Object.hasOwn||function(e,t){return no.call(ro(e),t)},io=$i.document,oo=eo(io)&&eo(io.createElement),ao=!zi&&!Mi((function(){return 7!=Object.defineProperty(function(e){return oo?io.createElement(e):{}}("div"),"a",{get:function(){return 7}}).a})),co=Object.getOwnPropertyDescriptor,so={f:zi?co:function(e,t){if(e=Qi(e),t=to(t,!0),ao)try{return co(e,t)}catch(e){}if(uo(e,t))return Hi(!Yi.f.call(e,t),e[t])}},lo=function(e){if(!eo(e))throw TypeError(String(e)+" is not an object");return e},fo=Object.defineProperty,Do={f:zi?fo:function(e,t,r){if(lo(e),t=to(t,!0),lo(r),ao)try{return fo(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},po=zi?function(e,t,r){return Do.f(e,t,Hi(1,r))}:function(e,t,r){return e[t]=r,e},ho=function(e,t){try{po($i,e,t)}catch(r){$i[e]=t}return t},go=$i["__core-js_shared__"]||ho("__core-js_shared__",{}),mo=Function.toString;"function"!=typeof go.inspectSource&&(go.inspectSource=function(e){return mo.call(e)});var Eo,yo,vo,bo,Co=go.inspectSource,Ao=$i.WeakMap,wo="function"==typeof Ao&&/native code/.test(Co(Ao)),Fo=u((function(e){(e.exports=function(e,t){return go[e]||(go[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.14.0",mode:"global",copyright:"\xa9 2021 Denis Pushkarev (zloirock.ru)"})})),ko=0,Oo=Math.random(),To=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++ko+Oo).toString(36)},So=Fo("keys"),Io={},Ro=$i.WeakMap;if(wo||go.state){var xo=go.state||(go.state=new Ro),Bo=xo.get,No=xo.has,Lo=xo.set;Eo=function(e,t){if(No.call(xo,e))throw new TypeError("Object already initialized");return t.facade=e,Lo.call(xo,e,t),t},yo=function(e){return Bo.call(xo,e)||{}},vo=function(e){return No.call(xo,e)}}else{var Po=So[bo="state"]||(So[bo]=To(bo));Io[Po]=!0,Eo=function(e,t){if(uo(e,Po))throw new TypeError("Object already initialized");return t.facade=e,po(e,Po,t),t},yo=function(e){return uo(e,Po)?e[Po]:{}},vo=function(e){return uo(e,Po)}}var qo,jo,_o={set:Eo,get:yo,has:vo,enforce:function(e){return vo(e)?yo(e):Eo(e,{})},getterFor:function(e){return function(t){var r;if(!eo(t)||(r=yo(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return r}}},Uo=u((function(e){var t=_o.get,r=_o.enforce,n=String(String).split("String");(e.exports=function(e,t,u,i){var o,a=!!i&&!!i.unsafe,c=!!i&&!!i.enumerable,s=!!i&&!!i.noTargetGet;"function"==typeof u&&("string"!=typeof t||uo(u,"name")||po(u,"name",t),(o=r(u)).source||(o.source=n.join("string"==typeof t?t:""))),e!==$i?(a?!s&&e[t]&&(c=!0):delete e[t],c?e[t]=u:po(e,t,u)):c?e[t]=u:ho(t,u)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||Co(this)}))})),$o=$i,Mo=function(e){return"function"==typeof e?e:void 0},zo=function(e,t){return arguments.length<2?Mo($o[e])||Mo($i[e]):$o[e]&&$o[e][t]||$i[e]&&$i[e][t]},Go=Math.ceil,Vo=Math.floor,Yo=function(e){return isNaN(e=+e)?0:(e>0?Vo:Go)(e)},Ho=Math.min,Xo=function(e){return e>0?Ho(Yo(e),9007199254740991):0},Wo=Math.max,Zo=Math.min,Jo=function(e){return function(t,r,n){var u,i=Qi(t),o=Xo(i.length),a=function(e,t){var r=Yo(e);return r<0?Wo(r+t,0):Zo(r,t)}(n,o);if(e&&r!=r){for(;o>a;)if((u=i[a++])!=u)return!0}else for(;o>a;a++)if((e||a in i)&&i[a]===r)return e||a||0;return!e&&-1}},Ko={includes:Jo(!0),indexOf:Jo(!1)}.indexOf,Qo=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),ea={f:Object.getOwnPropertyNames||function(e){return function(e,t){var r,n=Qi(e),u=0,i=[];for(r in n)!uo(Io,r)&&uo(n,r)&&i.push(r);for(;t.length>u;)uo(n,r=t[u++])&&(~Ko(i,r)||i.push(r));return i}(e,Qo)}},ta={f:Object.getOwnPropertySymbols},ra=zo("Reflect","ownKeys")||function(e){var t=ea.f(lo(e)),r=ta.f;return r?t.concat(r(e)):t},na=function(e,t){for(var r=ra(t),n=Do.f,u=so.f,i=0;i<r.length;i++){var o=r[i];uo(e,o)||n(e,o,u(t,o))}},ua=/#|\.prototype\./,ia=function(e,t){var r=aa[oa(e)];return r==sa||r!=ca&&("function"==typeof t?Mi(t):!!t)},oa=ia.normalize=function(e){return String(e).replace(ua,".").toLowerCase()},aa=ia.data={},ca=ia.NATIVE="N",sa=ia.POLYFILL="P",la=ia,fa=so.f,Da=function(e,t){var r,n,u,i,o,a=e.target,c=e.global,s=e.stat;if(r=c?$i:s?$i[a]||ho(a,{}):($i[a]||{}).prototype)for(n in t){if(i=t[n],u=e.noTargetGet?(o=fa(r,n))&&o.value:r[n],!la(c?n:a+(s?".":"#")+n,e.forced)&&void 0!==u){if(typeof i==typeof u)continue;na(i,u)}(e.sham||u&&u.sham)&&po(i,"sham",!0),Uo(r,n,i,e)}},pa=Array.isArray||function(e){return"Array"==Wi(e)},ha=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},da=function(e,t,r){if(ha(e),void 0===t)return e;switch(r){case 0:return function(){return e.call(t)};case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,u){return e.call(t,r,n,u)}}return function(){return e.apply(t,arguments)}},ga=function(e,t,r,n,u,i,o,a){for(var c,s=u,l=0,f=!!o&&da(o,a,3);l<n;){if(l in r){if(c=f?f(r[l],l,t):r[l],i>0&&pa(c))s=ga(e,t,c,Xo(c.length),s,i-1)-1;else{if(s>=9007199254740991)throw TypeError("Exceed the acceptable array length");e[s]=c}s++}l++}return s},ma=ga,Ea=zo("navigator","userAgent")||"",ya=$i.process,va=ya&&ya.versions,ba=va&&va.v8;ba?jo=(qo=ba.split("."))[0]<4?1:qo[0]+qo[1]:Ea&&(!(qo=Ea.match(/Edge\/(\d+)/))||qo[1]>=74)&&(qo=Ea.match(/Chrome\/(\d+)/))&&(jo=qo[1]);var Ca=jo&&+jo,Aa=!!Object.getOwnPropertySymbols&&!Mi((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&Ca&&Ca<41})),wa=Aa&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Fa=Fo("wks"),ka=$i.Symbol,Oa=wa?ka:ka&&ka.withoutSetter||To,Ta=function(e){return uo(Fa,e)&&(Aa||"string"==typeof Fa[e])||(Aa&&uo(ka,e)?Fa[e]=ka[e]:Fa[e]=Oa("Symbol."+e)),Fa[e]},Sa=Ta("species"),Ia=function(e,t){var r;return pa(e)&&("function"!=typeof(r=e.constructor)||r!==Array&&!pa(r.prototype)?eo(r)&&null===(r=r[Sa])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===t?0:t)};Da({target:"Array",proto:!0},{flatMap:function(e){var t,r=ro(this),n=Xo(r.length);return ha(e),(t=Ia(r,0)).length=ma(t,r,r,n,0,1,e,arguments.length>1?arguments[1]:void 0),t}});var Ra,xa,Ba=Math.floor,Na=function(e,t){var r=e.length,n=Ba(r/2);return r<8?La(e,t):Pa(Na(e.slice(0,n),t),Na(e.slice(n),t),t)},La=function(e,t){for(var r,n,u=e.length,i=1;i<u;){for(n=i,r=e[i];n&&t(e[n-1],r)>0;)e[n]=e[--n];n!==i++&&(e[n]=r)}return e},Pa=function(e,t,r){for(var n=e.length,u=t.length,i=0,o=0,a=[];i<n||o<u;)i<n&&o<u?a.push(r(e[i],t[o])<=0?e[i++]:t[o++]):a.push(i<n?e[i++]:t[o++]);return a},qa=Na,ja=Ea.match(/firefox\/(\d+)/i),_a=!!ja&&+ja[1],Ua=/MSIE|Trident/.test(Ea),$a=Ea.match(/AppleWebKit\/(\d+)\./),Ma=!!$a&&+$a[1],za=[],Ga=za.sort,Va=Mi((function(){za.sort(void 0)})),Ya=Mi((function(){za.sort(null)})),Ha=!!(xa=[]["sort"])&&Mi((function(){xa.call(null,Ra||function(){throw 1},1)})),Xa=!Mi((function(){if(Ca)return Ca<70;if(!(_a&&_a>3)){if(Ua)return!0;if(Ma)return Ma<603;var e,t,r,n,u="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)za.push({k:t+n,v:r})}for(za.sort((function(e,t){return t.v-e.v})),n=0;n<za.length;n++)t=za[n].k.charAt(0),u.charAt(u.length-1)!==t&&(u+=t);return"DGBEFHACIJK"!==u}}));Da({target:"Array",proto:!0,forced:Va||!Ya||!Ha||!Xa},{sort:function(e){void 0!==e&&ha(e);var t=ro(this);if(Xa)return void 0===e?Ga.call(t):Ga.call(t,e);var r,n,u=[],i=Xo(t.length);for(n=0;n<i;n++)n in t&&u.push(t[n]);for(r=(u=qa(u,function(e){return function(t,r){return void 0===r?-1:void 0===t?1:void 0!==e?+e(t,r)||0:String(t)>String(r)?1:-1}}(e))).length,n=0;n<r;)t[n]=u[n++];for(;n<i;)delete t[n++];return t}});var Wa={},Za=Ta("iterator"),Ja=Array.prototype,Ka={};Ka[Ta("toStringTag")]="z";var Qa="[object z]"===String(Ka),ec=Ta("toStringTag"),tc="Arguments"==Wi(function(){return arguments}()),rc=Qa?Wi:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),ec))?r:tc?Wi(t):"Object"==(n=Wi(t))&&"function"==typeof t.callee?"Arguments":n},nc=Ta("iterator"),uc=function(e){var t=e.return;if(void 0!==t)return lo(t.call(e)).value},ic=function(e,t){this.stopped=e,this.result=t},oc=function(e,t,r){var n,u,i,o,a,c,s,l=r&&r.that,f=!(!r||!r.AS_ENTRIES),D=!(!r||!r.IS_ITERATOR),p=!(!r||!r.INTERRUPTED),h=da(t,l,1+f+p),d=function(e){return n&&uc(n),new ic(!0,e)},g=function(e){return f?(lo(e),p?h(e[0],e[1],d):h(e[0],e[1])):p?h(e,d):h(e)};if(D)n=e;else{if("function"!=typeof(u=function(e){if(null!=e)return e[nc]||e["@@iterator"]||Wa[rc(e)]}(e)))throw TypeError("Target is not iterable");if(function(e){return void 0!==e&&(Wa.Array===e||Ja[Za]===e)}(u)){for(i=0,o=Xo(e.length);o>i;i++)if((a=g(e[i]))&&a instanceof ic)return a;return new ic(!1)}n=u.call(e)}for(c=n.next;!(s=c.call(n)).done;){try{a=g(s.value)}catch(e){throw uc(n),e}if("object"==typeof a&&a&&a instanceof ic)return a}return new ic(!1)};Da({target:"Object",stat:!0},{fromEntries:function(e){var t={};return oc(e,(function(e,r){!function(e,t,r){var n=to(t);n in e?Do.f(e,n,Hi(0,r)):e[n]=r}(t,e,r)}),{AS_ENTRIES:!0}),t}});var ac="object"==typeof ke&&ke.env&&ke.env.NODE_DEBUG&&/\bsemver\b/i.test(ke.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var cc={SEMVER_SPEC_VERSION:"2.0.0",MAX_LENGTH:256,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||9007199254740991,MAX_SAFE_COMPONENT_LENGTH:16},sc=u((function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:r}=cc,n=(t=e.exports={}).re=[],u=t.src=[],i=t.t={};let o=0;const a=(e,t,r)=>{const a=o++;ac(a,t),i[e]=a,u[a]=t,n[a]=new RegExp(t,r?"g":void 0)};a("NUMERICIDENTIFIER","0|[1-9]\\d*"),a("NUMERICIDENTIFIERLOOSE","[0-9]+"),a("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),a("MAINVERSION",`(${u[i.NUMERICIDENTIFIER]})\\.(${u[i.NUMERICIDENTIFIER]})\\.(${u[i.NUMERICIDENTIFIER]})`),a("MAINVERSIONLOOSE",`(${u[i.NUMERICIDENTIFIERLOOSE]})\\.(${u[i.NUMERICIDENTIFIERLOOSE]})\\.(${u[i.NUMERICIDENTIFIERLOOSE]})`),a("PRERELEASEIDENTIFIER",`(?:${u[i.NUMERICIDENTIFIER]}|${u[i.NONNUMERICIDENTIFIER]})`),a("PRERELEASEIDENTIFIERLOOSE",`(?:${u[i.NUMERICIDENTIFIERLOOSE]}|${u[i.NONNUMERICIDENTIFIER]})`),a("PRERELEASE",`(?:-(${u[i.PRERELEASEIDENTIFIER]}(?:\\.${u[i.PRERELEASEIDENTIFIER]})*))`),a("PRERELEASELOOSE",`(?:-?(${u[i.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[i.PRERELEASEIDENTIFIERLOOSE]})*))`),a("BUILDIDENTIFIER","[0-9A-Za-z-]+"),a("BUILD",`(?:\\+(${u[i.BUILDIDENTIFIER]}(?:\\.${u[i.BUILDIDENTIFIER]})*))`),a("FULLPLAIN",`v?${u[i.MAINVERSION]}${u[i.PRERELEASE]}?${u[i.BUILD]}?`),a("FULL",`^${u[i.FULLPLAIN]}$`),a("LOOSEPLAIN",`[v=\\s]*${u[i.MAINVERSIONLOOSE]}${u[i.PRERELEASELOOSE]}?${u[i.BUILD]}?`),a("LOOSE",`^${u[i.LOOSEPLAIN]}$`),a("GTLT","((?:<|>)?=?)"),a("XRANGEIDENTIFIERLOOSE",`${u[i.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),a("XRANGEIDENTIFIER",`${u[i.NUMERICIDENTIFIER]}|x|X|\\*`),a("XRANGEPLAIN",`[v=\\s]*(${u[i.XRANGEIDENTIFIER]})(?:\\.(${u[i.XRANGEIDENTIFIER]})(?:\\.(${u[i.XRANGEIDENTIFIER]})(?:${u[i.PRERELEASE]})?${u[i.BUILD]}?)?)?`),a("XRANGEPLAINLOOSE",`[v=\\s]*(${u[i.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[i.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[i.XRANGEIDENTIFIERLOOSE]})(?:${u[i.PRERELEASELOOSE]})?${u[i.BUILD]}?)?)?`),a("XRANGE",`^${u[i.GTLT]}\\s*${u[i.XRANGEPLAIN]}$`),a("XRANGELOOSE",`^${u[i.GTLT]}\\s*${u[i.XRANGEPLAINLOOSE]}$`),a("COERCE",`(^|[^\\d])(\\d{1,${r}})(?:\\.(\\d{1,${r}}))?(?:\\.(\\d{1,${r}}))?(?:$|[^\\d])`),a("COERCERTL",u[i.COERCE],!0),a("LONETILDE","(?:~>?)"),a("TILDETRIM",`(\\s*)${u[i.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",a("TILDE",`^${u[i.LONETILDE]}${u[i.XRANGEPLAIN]}$`),a("TILDELOOSE",`^${u[i.LONETILDE]}${u[i.XRANGEPLAINLOOSE]}$`),a("LONECARET","(?:\\^)"),a("CARETTRIM",`(\\s*)${u[i.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",a("CARET",`^${u[i.LONECARET]}${u[i.XRANGEPLAIN]}$`),a("CARETLOOSE",`^${u[i.LONECARET]}${u[i.XRANGEPLAINLOOSE]}$`),a("COMPARATORLOOSE",`^${u[i.GTLT]}\\s*(${u[i.LOOSEPLAIN]})$|^$`),a("COMPARATOR",`^${u[i.GTLT]}\\s*(${u[i.FULLPLAIN]})$|^$`),a("COMPARATORTRIM",`(\\s*)${u[i.GTLT]}\\s*(${u[i.LOOSEPLAIN]}|${u[i.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",a("HYPHENRANGE",`^\\s*(${u[i.XRANGEPLAIN]})\\s+-\\s+(${u[i.XRANGEPLAIN]})\\s*$`),a("HYPHENRANGELOOSE",`^\\s*(${u[i.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[i.XRANGEPLAINLOOSE]})\\s*$`),a("STAR","(<|>)?=?\\s*\\*"),a("GTE0","^\\s*>=\\s*0.0.0\\s*$"),a("GTE0PRE","^\\s*>=\\s*0.0.0-0\\s*$")}));const lc=["includePrerelease","loose","rtl"];var fc=e=>e?"object"!=typeof e?{loose:!0}:lc.filter((t=>e[t])).reduce(((e,t)=>(e[t]=!0,e)),{}):{};const Dc=/^[0-9]+$/,pc=(e,t)=>{const r=Dc.test(e),n=Dc.test(t);return r&&n&&(e=+e,t=+t),e===t?0:r&&!n?-1:n&&!r?1:e<t?-1:1};var hc={compareIdentifiers:pc,rcompareIdentifiers:(e,t)=>pc(t,e)};const{MAX_LENGTH:dc,MAX_SAFE_INTEGER:gc}=cc,{re:mc,t:Ec}=sc,{compareIdentifiers:yc}=hc;class vc{constructor(e,t){if(t=fc(t),e instanceof vc){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid Version: ${e}`);if(e.length>dc)throw new TypeError(`version is longer than ${dc} characters`);ac("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const r=e.trim().match(t.loose?mc[Ec.LOOSE]:mc[Ec.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>gc||this.major<0)throw new TypeError("Invalid major version");if(this.minor>gc||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>gc||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<gc)return t}return e})):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(ac("SemVer.compare",this.version,this.options,e),!(e instanceof vc)){if("string"==typeof e&&e===this.version)return 0;e=new vc(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof vc||(e=new vc(e,this.options)),yc(this.major,e.major)||yc(this.minor,e.minor)||yc(this.patch,e.patch)}comparePre(e){if(e instanceof vc||(e=new vc(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const r=this.prerelease[t],n=e.prerelease[t];if(ac("prerelease compare",t,r,n),void 0===r&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===r)return-1;if(r!==n)return yc(r,n)}while(++t)}compareBuild(e){e instanceof vc||(e=new vc(e,this.options));let t=0;do{const r=this.build[t],n=e.build[t];if(ac("prerelease compare",t,r,n),void 0===r&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===r)return-1;if(r!==n)return yc(r,n)}while(++t)}inc(e,t){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t),this.inc("pre",t);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t),this.inc("pre",t);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{let e=this.prerelease.length;for(;--e>=0;)"number"==typeof this.prerelease[e]&&(this.prerelease[e]++,e=-2);-1===e&&this.prerelease.push(0)}t&&(this.prerelease[0]===t?isNaN(this.prerelease[1])&&(this.prerelease=[t,0]):this.prerelease=[t,0]);break;default:throw new Error(`invalid increment argument: ${e}`)}return this.format(),this.raw=this.version,this}}var bc=vc;var Cc=(e,t,r)=>new bc(e,r).compare(new bc(t,r));var Ac=(e,t,r)=>Cc(e,t,r)<0;var wc=(e,t,r)=>Cc(e,t,r)>=0,Fc="2.3.2",kc=u((function(e,t){function r(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]}function n(){return"undefined"!=typeof WeakMap?new WeakMap:{add:r,delete:r,get:r,set:r,has:function(e){return!1}}}Object.defineProperty(t,"__esModule",{value:!0}),t.outdent=void 0;var u=Object.prototype.hasOwnProperty,i=function(e,t){return u.call(e,t)};function o(e,t){for(var r in t)i(t,r)&&(e[r]=t[r]);return e}var a=/^[ \t]*(?:\r\n|\r|\n)/,c=/(?:\r\n|\r|\n)[ \t]*$/,s=/^(?:[\r\n]|$)/,l=/(?:\r\n|\r|\n)([ \t]*)(?:[^ \t\r\n]|$)/,f=/^[ \t]*[\r\n][ \t\r\n]*$/;function D(e,t,r){var n=0,u=e[0].match(l);u&&(n=u[1].length);var i=new RegExp("(\\r\\n|\\r|\\n).{0,"+n+"}","g");t&&(e=e.slice(1));var o=r.newline,s=r.trimLeadingNewline,f=r.trimTrailingNewline,D="string"==typeof o,p=e.length;return e.map((function(e,t){return e=e.replace(i,"$1"),0===t&&s&&(e=e.replace(a,"")),t===p-1&&f&&(e=e.replace(c,"")),D&&(e=e.replace(/\r\n|\n|\r/g,(function(e){return o}))),e}))}function p(e,t){for(var r="",n=0,u=e.length;n<u;n++)r+=e[n],n<u-1&&(r+=t[n]);return r}function h(e){return i(e,"raw")&&i(e,"length")}var d=function e(t){var r=n(),u=n();return o((function n(i){for(var a=[],c=1;c<arguments.length;c++)a[c-1]=arguments[c];if(h(i)){var l=i,g=(a[0]===n||a[0]===d)&&f.test(l[0])&&s.test(l[1]),m=g?u:r,E=m.get(l);if(E||(E=D(l,g,t),m.set(l,E)),0===a.length)return E[0];var y=p(E,g?a.slice(1):a);return y}return e(o(o({},t),i||{}))}),{string:function(e){return D([e],!1,t)[0]}})}({trimLeadingNewline:!0,trimTrailingNewline:!0});t.outdent=d,t.default=d;try{e.exports=d,Object.defineProperty(d,"__esModule",{value:!0}),d.default=d,d.outdent=d}catch(e){}}));const{outdent:Oc}=kc,Tc={cursorOffset:{since:"1.4.0",category:"Special",type:"int",default:-1,range:{start:-1,end:Number.POSITIVE_INFINITY,step:1},description:Oc`
      Print (to stderr) where a cursor at the given position would move to after formatting.
      This option cannot be used with --range-start and --range-end.
    `,cliCategory:"Editor"},endOfLine:{since:"1.15.0",category:"Global",type:"choice",default:[{since:"1.15.0",value:"auto"},{since:"2.0.0",value:"lf"}],description:"Which end of line characters to apply.",choices:[{value:"lf",description:"Line Feed only (\\n), common on Linux and macOS as well as inside git repos"},{value:"crlf",description:"Carriage Return + Line Feed characters (\\r\\n), common on Windows"},{value:"cr",description:"Carriage Return character only (\\r), used very rarely"},{value:"auto",description:Oc`
          Maintain existing
          (mixed values within one file are normalised by looking at what's used after the first line)
        `}]},filepath:{since:"1.4.0",category:"Special",type:"path",description:"Specify the input filepath. This will be used to do parser inference.",cliName:"stdin-filepath",cliCategory:"Other",cliDescription:"Path to the file to pretend that stdin comes from."},insertPragma:{since:"1.8.0",category:"Special",type:"boolean",default:!1,description:"Insert @format pragma into file's first docblock comment.",cliCategory:"Other"},parser:{since:"0.0.10",category:"Global",type:"choice",default:[{since:"0.0.10",value:"babylon"},{since:"1.13.0",value:void 0}],description:"Which parser to use.",exception:e=>"string"==typeof e||"function"==typeof e,choices:[{value:"flow",description:"Flow"},{value:"babel",since:"1.16.0",description:"JavaScript"},{value:"babel-flow",since:"1.16.0",description:"Flow"},{value:"babel-ts",since:"2.0.0",description:"TypeScript"},{value:"typescript",since:"1.4.0",description:"TypeScript"},{value:"espree",since:"2.2.0",description:"JavaScript"},{value:"meriyah",since:"2.2.0",description:"JavaScript"},{value:"css",since:"1.7.1",description:"CSS"},{value:"less",since:"1.7.1",description:"Less"},{value:"scss",since:"1.7.1",description:"SCSS"},{value:"json",since:"1.5.0",description:"JSON"},{value:"json5",since:"1.13.0",description:"JSON5"},{value:"json-stringify",since:"1.13.0",description:"JSON.stringify"},{value:"graphql",since:"1.5.0",description:"GraphQL"},{value:"markdown",since:"1.8.0",description:"Markdown"},{value:"mdx",since:"1.15.0",description:"MDX"},{value:"vue",since:"1.10.0",description:"Vue"},{value:"yaml",since:"1.14.0",description:"YAML"},{value:"glimmer",since:"2.3.0",description:"Ember / Handlebars"},{value:"html",since:"1.15.0",description:"HTML"},{value:"angular",since:"1.15.0",description:"Angular"},{value:"lwc",since:"1.17.0",description:"Lightning Web Components"}]},plugins:{since:"1.10.0",type:"path",array:!0,default:[{value:[]}],category:"Global",description:"Add a plugin. Multiple plugins can be passed as separate `--plugin`s.",exception:e=>"string"==typeof e||"object"==typeof e,cliName:"plugin",cliCategory:"Config"},pluginSearchDirs:{since:"1.13.0",type:"path",array:!0,default:[{value:[]}],category:"Global",description:Oc`
      Custom directory that contains prettier plugins in node_modules subdirectory.
      Overrides default behavior when plugins are searched relatively to the location of Prettier.
      Multiple values are accepted.
    `,exception:e=>"string"==typeof e||"object"==typeof e,cliName:"plugin-search-dir",cliCategory:"Config"},printWidth:{since:"0.0.0",category:"Global",type:"int",default:80,description:"The line length where Prettier will try wrap.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},rangeEnd:{since:"1.4.0",category:"Special",type:"int",default:Number.POSITIVE_INFINITY,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:Oc`
      Format code ending at a given character offset (exclusive).
      The range will extend forwards to the end of the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:"Editor"},rangeStart:{since:"1.4.0",category:"Special",type:"int",default:0,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:Oc`
      Format code starting at a given character offset.
      The range will extend backwards to the start of the first line containing the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:"Editor"},requirePragma:{since:"1.7.0",category:"Special",type:"boolean",default:!1,description:Oc`
      Require either '@prettier' or '@format' to be present in the file's first docblock comment
      in order for it to be formatted.
    `,cliCategory:"Other"},tabWidth:{type:"int",category:"Global",default:2,description:"Number of spaces per indentation level.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},useTabs:{since:"1.0.0",category:"Global",type:"boolean",default:!1,description:"Indent with tabs instead of spaces."},embeddedLanguageFormatting:{since:"2.1.0",category:"Global",type:"choice",default:[{since:"2.1.0",value:"auto"}],description:"Control how Prettier formats quoted code embedded in the file.",choices:[{value:"auto",description:"Format embedded code if Prettier can automatically identify it."},{value:"off",description:"Never automatically format embedded code."}]}};const Sc=["cliName","cliCategory","cliDescription"],Ic={compare:Cc,lt:Ac,gte:wc},Rc=Fc,xc={CATEGORY_CONFIG:"Config",CATEGORY_EDITOR:"Editor",CATEGORY_FORMAT:"Format",CATEGORY_OTHER:"Other",CATEGORY_OUTPUT:"Output",CATEGORY_GLOBAL:"Global",CATEGORY_SPECIAL:"Special",options:Tc}.options;var Bc={getSupportInfo:function({plugins:e=[],showUnreleased:t=!1,showDeprecated:r=!1,showInternal:n=!1}={}){const u=Rc.split("-",1)[0],i=e.flatMap((e=>e.languages||[])).filter(s),o=(a=Object.assign({},...e.map((({options:e})=>e)),xc),c="name",Object.entries(a).map((([e,t])=>Object.assign({[c]:e},t)))).filter((e=>s(e)&&l(e))).sort(((e,t)=>e.name===t.name?0:e.name<t.name?-1:1)).map((function(e){if(n)return e;return _i(e,Sc)})).map((t=>{t=Object.assign({},t),Array.isArray(t.default)&&(t.default=1===t.default.length?t.default[0].value:t.default.filter(s).sort(((e,t)=>Ic.compare(t.since,e.since)))[0].value),Array.isArray(t.choices)&&(t.choices=t.choices.filter((e=>s(e)&&l(e))),"parser"===t.name&&function(e,t,r){const n=new Set(e.choices.map((e=>e.value)));for(const u of t)if(u.parsers)for(const t of u.parsers)if(!n.has(t)){n.add(t);const i=r.find((e=>e.parsers&&e.parsers[t]));let o=u.name;i&&i.name&&(o+=` (plugin: ${i.name})`),e.choices.push({value:t,description:o})}}(t,i,e));const r=Object.fromEntries(e.filter((e=>e.defaultOptions&&void 0!==e.defaultOptions[t.name])).map((e=>[e.name,e.defaultOptions[t.name]])));return Object.assign(Object.assign({},t),{},{pluginDefaults:r})}));var a,c;return{languages:i,options:o};function s(e){return t||!("since"in e)||e.since&&Ic.gte(u,e.since)}function l(e){return r||!("deprecated"in e)||e.deprecated&&Ic.lt(u,e.deprecated)}}};const{getSupportInfo:Nc}=Bc,Lc=/[^\x20-\x7F]/;function Pc(e){return(t,r,n)=>{const u=n&&n.backwards;if(!1===r)return!1;const{length:i}=t;let o=r;for(;o>=0&&o<i;){const r=t.charAt(o);if(e instanceof RegExp){if(!e.test(r))return o}else if(!e.includes(r))return o;u?o--:o++}return(-1===o||o===i)&&o}}const qc=Pc(/\s/),jc=Pc(" \t"),_c=Pc(",; \t"),Uc=Pc(/[^\n\r]/);function $c(e,t){if(!1===t)return!1;if("/"===e.charAt(t)&&"*"===e.charAt(t+1))for(let r=t+2;r<e.length;++r)if("*"===e.charAt(r)&&"/"===e.charAt(r+1))return r+2;return t}function Mc(e,t){return!1!==t&&("/"===e.charAt(t)&&"/"===e.charAt(t+1)?Uc(e,t):t)}function zc(e,t,r){const n=r&&r.backwards;if(!1===t)return!1;const u=e.charAt(t);if(n){if("\r"===e.charAt(t-1)&&"\n"===u)return t-2;if("\n"===u||"\r"===u||"\u2028"===u||"\u2029"===u)return t-1}else{if("\r"===u&&"\n"===e.charAt(t+1))return t+2;if("\n"===u||"\r"===u||"\u2028"===u||"\u2029"===u)return t+1}return t}function Gc(e,t,r={}){const n=jc(e,r.backwards?t-1:t,r);return n!==zc(e,n,r)}function Vc(e,t){let r=null,n=t;for(;n!==r;)r=n,n=_c(e,n),n=$c(e,n),n=jc(e,n);return n=Mc(e,n),n=zc(e,n),!1!==n&&Gc(e,n)}function Yc(e,t){let r=null,n=t;for(;n!==r;)r=n,n=jc(e,n),n=$c(e,n),n=Mc(e,n),n=zc(e,n);return n}function Hc(e,t,r){return Yc(e,r(t))}function Xc(e,t,r=0){let n=0;for(let u=r;u<e.length;++u)"\t"===e[u]?n=n+t-n%t:n++;return n}function Wc(e,t){const r=e.slice(1,-1),n={quote:'"',regex:/"/g},u={quote:"'",regex:/'/g},i="'"===t?u:n,o=i===u?n:u;let a=i.quote;if(r.includes(i.quote)||r.includes(o.quote)){a=(r.match(i.regex)||[]).length>(r.match(o.regex)||[]).length?o.quote:i.quote}return a}function Zc(e,t,r){const n='"'===t?"'":'"',u=e.replace(/\\(.)|(["'])/gs,((e,u,i)=>u===n?u:i===t?"\\"+i:i||(r&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/.test(u)?u:"\\"+u)));return t+u+t}function Jc(e,t){(e.comments||(e.comments=[])).push(t),t.printed=!1,t.nodeDescription=function(e){const t=e.type||e.kind||"(unknown type)";let r=String(e.name||e.id&&("object"==typeof e.id?e.id.name:e.id)||e.key&&("object"==typeof e.key?e.key.name:e.key)||e.value&&("object"==typeof e.value?"":String(e.value))||e.operator||"");r.length>20&&(r=r.slice(0,19)+"\u2026");return t+(r?" "+r:"")}(e)}var Kc={inferParserByLanguage:function(e,t){const{languages:r}=Nc({plugins:t.plugins}),n=r.find((({name:t})=>t.toLowerCase()===e))||r.find((({aliases:t})=>Array.isArray(t)&&t.includes(e)))||r.find((({extensions:t})=>Array.isArray(t)&&t.includes(`.${e}`)));return n&&n.parsers[0]},getStringWidth:function(e){return e?Lc.test(e)?Li(e):e.length:0},getMaxContinuousCount:function(e,t){const r=e.match(new RegExp(`(${qi(t)})+`,"g"));return null===r?0:r.reduce(((e,r)=>Math.max(e,r.length/t.length)),0)},getMinNotPresentContinuousCount:function(e,t){const r=e.match(new RegExp(`(${qi(t)})+`,"g"));if(null===r)return 0;const n=new Map;let u=0;for(const e of r){const r=e.length/t.length;n.set(r,!0),r>u&&(u=r)}for(let e=1;e<u;e++)if(!n.get(e))return e;return u+1},getPenultimate:e=>e[e.length-2],getLast:ji,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:Yc,getNextNonSpaceNonCommentCharacterIndex:Hc,getNextNonSpaceNonCommentCharacter:function(e,t,r){return e.charAt(Hc(e,t,r))},skip:Pc,skipWhitespace:qc,skipSpaces:jc,skipToLineEnd:_c,skipEverythingButNewLine:Uc,skipInlineComment:$c,skipTrailingComment:Mc,skipNewline:zc,isNextLineEmptyAfterIndex:Vc,isNextLineEmpty:function(e,t,r){return Vc(e,r(t))},isPreviousLineEmpty:function(e,t,r){let n=r(t)-1;return n=jc(e,n,{backwards:!0}),n=zc(e,n,{backwards:!0}),n=jc(e,n,{backwards:!0}),n!==zc(e,n,{backwards:!0})},hasNewline:Gc,hasNewlineInRange:function(e,t,r){for(let n=t;n<r;++n)if("\n"===e.charAt(n))return!0;return!1},hasSpaces:function(e,t,r={}){return jc(e,r.backwards?t-1:t,r)!==t},getAlignmentSize:Xc,getIndentSize:function(e,t){const r=e.lastIndexOf("\n");return-1===r?0:Xc(e.slice(r+1).match(/^[\t ]*/)[0],t)},getPreferredQuote:Wc,printString:function(e,t){return Zc(e.slice(1,-1),"json"===t.parser||"json5"===t.parser&&"preserve"===t.quoteProps&&!t.singleQuote?'"':t.__isInHtmlAttribute?"'":Wc(e,t.singleQuote?"'":'"'),!("css"===t.parser||"less"===t.parser||"scss"===t.parser||t.__embeddedInHtml))},printNumber:function(e){return e.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(\d)/,"$1$2$3").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")},makeString:Zc,addLeadingComment:function(e,t){t.leading=!0,t.trailing=!1,Jc(e,t)},addDanglingComment:function(e,t,r){t.leading=!1,t.trailing=!1,r&&(t.marker=r),Jc(e,t)},addTrailingComment:function(e,t){t.leading=!1,t.trailing=!0,Jc(e,t)},isFrontMatterNode:function(e){return e&&"front-matter"===e.type},getShebang:function(e){if(!e.startsWith("#!"))return"";const t=e.indexOf("\n");return-1===t?e:e.slice(0,t)},isNonEmptyArray:function(e){return Array.isArray(e)&&e.length>0},createGroupIdMapper:function(e){const t=new WeakMap;return function(r){return t.has(r)||t.set(r,Symbol(e)),t.get(r)}}};const{getLast:Qc}=Kc,{locStart:es,locEnd:ts}=Si,{cjkPattern:rs,kPattern:ns,punctuationPattern:us}={cjkPattern:"(?:[\\u02ea-\\u02eb\\u1100-\\u11ff\\u2e80-\\u2e99\\u2e9b-\\u2ef3\\u2f00-\\u2fd5\\u2ff0-\\u303f\\u3041-\\u3096\\u3099-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u3190-\\u3191\\u3196-\\u31ba\\u31c0-\\u31e3\\u31f0-\\u321e\\u322a-\\u3247\\u3260-\\u327e\\u328a-\\u32b0\\u32c0-\\u32cb\\u32d0-\\u3370\\u337b-\\u337f\\u33e0-\\u33fe\\u3400-\\u4db5\\u4e00-\\u9fef\\ua960-\\ua97c\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufe10-\\ufe1f\\ufe30-\\ufe6f\\uff00-\\uffef]|[\\ud840-\\ud868\\ud86a-\\ud86c\\ud86f-\\ud872\\ud874-\\ud879][\\udc00-\\udfff]|\\ud82c[\\udc00-\\udd1e\\udd50-\\udd52\\udd64-\\udd67]|\\ud83c[\\ude00\\ude50-\\ude51]|\\ud869[\\udc00-\\uded6\\udf00-\\udfff]|\\ud86d[\\udc00-\\udf34\\udf40-\\udfff]|\\ud86e[\\udc00-\\udc1d\\udc20-\\udfff]|\\ud873[\\udc00-\\udea1\\udeb0-\\udfff]|\\ud87a[\\udc00-\\udfe0]|\\ud87e[\\udc00-\\ude1d])(?:[\\ufe00-\\ufe0f]|\\udb40[\\udd00-\\uddef])?",kPattern:"[\\u1100-\\u11ff\\u3001-\\u3003\\u3008-\\u3011\\u3013-\\u301f\\u302e-\\u3030\\u3037\\u30fb\\u3131-\\u318e\\u3200-\\u321e\\u3260-\\u327e\\ua960-\\ua97c\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\ufe45-\\ufe46\\uff61-\\uff65\\uffa0-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc]",punctuationPattern:"[\\u0021-\\u002f\\u003a-\\u0040\\u005b-\\u0060\\u007b-\\u007e\\u00a1\\u00a7\\u00ab\\u00b6-\\u00b7\\u00bb\\u00bf\\u037e\\u0387\\u055a-\\u055f\\u0589-\\u058a\\u05be\\u05c0\\u05c3\\u05c6\\u05f3-\\u05f4\\u0609-\\u060a\\u060c-\\u060d\\u061b\\u061e-\\u061f\\u066a-\\u066d\\u06d4\\u0700-\\u070d\\u07f7-\\u07f9\\u0830-\\u083e\\u085e\\u0964-\\u0965\\u0970\\u09fd\\u0a76\\u0af0\\u0c77\\u0c84\\u0df4\\u0e4f\\u0e5a-\\u0e5b\\u0f04-\\u0f12\\u0f14\\u0f3a-\\u0f3d\\u0f85\\u0fd0-\\u0fd4\\u0fd9-\\u0fda\\u104a-\\u104f\\u10fb\\u1360-\\u1368\\u1400\\u166e\\u169b-\\u169c\\u16eb-\\u16ed\\u1735-\\u1736\\u17d4-\\u17d6\\u17d8-\\u17da\\u1800-\\u180a\\u1944-\\u1945\\u1a1e-\\u1a1f\\u1aa0-\\u1aa6\\u1aa8-\\u1aad\\u1b5a-\\u1b60\\u1bfc-\\u1bff\\u1c3b-\\u1c3f\\u1c7e-\\u1c7f\\u1cc0-\\u1cc7\\u1cd3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205e\\u207d-\\u207e\\u208d-\\u208e\\u2308-\\u230b\\u2329-\\u232a\\u2768-\\u2775\\u27c5-\\u27c6\\u27e6-\\u27ef\\u2983-\\u2998\\u29d8-\\u29db\\u29fc-\\u29fd\\u2cf9-\\u2cfc\\u2cfe-\\u2cff\\u2d70\\u2e00-\\u2e2e\\u2e30-\\u2e4f\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301f\\u3030\\u303d\\u30a0\\u30fb\\ua4fe-\\ua4ff\\ua60d-\\ua60f\\ua673\\ua67e\\ua6f2-\\ua6f7\\ua874-\\ua877\\ua8ce-\\ua8cf\\ua8f8-\\ua8fa\\ua8fc\\ua92e-\\ua92f\\ua95f\\ua9c1-\\ua9cd\\ua9de-\\ua9df\\uaa5c-\\uaa5f\\uaade-\\uaadf\\uaaf0-\\uaaf1\\uabeb\\ufd3e-\\ufd3f\\ufe10-\\ufe19\\ufe30-\\ufe52\\ufe54-\\ufe61\\ufe63\\ufe68\\ufe6a-\\ufe6b\\uff01-\\uff03\\uff05-\\uff0a\\uff0c-\\uff0f\\uff1a-\\uff1b\\uff1f-\\uff20\\uff3b-\\uff3d\\uff3f\\uff5b\\uff5d\\uff5f-\\uff65]|\\ud800[\\udd00-\\udd02\\udf9f\\udfd0]|\\ud801[\\udd6f]|\\ud802[\\udc57\\udd1f\\udd3f\\ude50-\\ude58\\ude7f\\udef0-\\udef6\\udf39-\\udf3f\\udf99-\\udf9c]|\\ud803[\\udf55-\\udf59]|\\ud804[\\udc47-\\udc4d\\udcbb-\\udcbc\\udcbe-\\udcc1\\udd40-\\udd43\\udd74-\\udd75\\uddc5-\\uddc8\\uddcd\\udddb\\udddd-\\udddf\\ude38-\\ude3d\\udea9]|\\ud805[\\udc4b-\\udc4f\\udc5b\\udc5d\\udcc6\\uddc1-\\uddd7\\ude41-\\ude43\\ude60-\\ude6c\\udf3c-\\udf3e]|\\ud806[\\udc3b\\udde2\\ude3f-\\ude46\\ude9a-\\ude9c\\ude9e-\\udea2]|\\ud807[\\udc41-\\udc45\\udc70-\\udc71\\udef7-\\udef8\\udfff]|\\ud809[\\udc70-\\udc74]|\\ud81a[\\ude6e-\\ude6f\\udef5\\udf37-\\udf3b\\udf44]|\\ud81b[\\ude97-\\ude9a\\udfe2]|\\ud82f[\\udc9f]|\\ud836[\\ude87-\\ude8b]|\\ud83a[\\udd5e-\\udd5f]"},is=["liquidNode","inlineCode","emphasis","strong","delete","wikiLink","link","linkReference","image","imageReference","footnote","footnoteReference","sentence","whitespace","word","break","inlineMath"],os=[...is,"tableCell","paragraph","heading"],as=new RegExp(ns),cs=new RegExp(us);function ss(e,t){const[,r,n,u]=t.slice(e.position.start.offset,e.position.end.offset).match(/^\s*(\d+)(\.|\))(\s*)/);return{numberText:r,marker:n,leadingSpaces:u}}var ls={mapAst:function(e,t){return function e(r,n,u){const i=Object.assign({},t(r,n,u));return i.children&&(i.children=i.children.map(((t,r)=>e(t,r,[i,...u])))),i}(e,null,[])},splitText:function(e,t){const r="non-cjk",n="cj-letter",u="cjk-punctuation",i=[],o=("preserve"===t.proseWrap?e:e.replace(new RegExp(`(${rs})\n(${rs})`,"g"),"$1$2")).split(/([\t\n ]+)/);for(const[e,t]of o.entries()){if(e%2==1){i.push({type:"whitespace",value:/\n/.test(t)?"\n":" "});continue}if((0===e||e===o.length-1)&&""===t)continue;const c=t.split(new RegExp(`(${rs})`));for(const[e,t]of c.entries())(0!==e&&e!==c.length-1||""!==t)&&(e%2!=0?a(cs.test(t)?{type:"word",value:t,kind:u,hasLeadingPunctuation:!0,hasTrailingPunctuation:!0}:{type:"word",value:t,kind:as.test(t)?"k-letter":n,hasLeadingPunctuation:!1,hasTrailingPunctuation:!1}):""!==t&&a({type:"word",value:t,kind:r,hasLeadingPunctuation:cs.test(t[0]),hasTrailingPunctuation:cs.test(Qc(t))}))}return i;function a(e){const t=Qc(i);var o,a;t&&"word"===t.type&&(t.kind===r&&e.kind===n&&!t.hasTrailingPunctuation||t.kind===n&&e.kind===r&&!e.hasLeadingPunctuation?i.push({type:"whitespace",value:" "}):(o=r,a=u,t.kind===o&&e.kind===a||t.kind===a&&e.kind===o||[t.value,e.value].some((e=>/\u3000/.test(e)))||i.push({type:"whitespace",value:""}))),i.push(e)}},punctuationPattern:us,getFencedCodeBlockValue:function(e,t){const{value:r}=e;return e.position.end.offset===t.length&&r.endsWith("\n")&&t.endsWith("\n")?r.slice(0,-1):r},getOrderedListItemInfo:ss,hasGitDiffFriendlyOrderedList:function(e,t){if(!e.ordered)return!1;if(e.children.length<2)return!1;const r=Number(ss(e.children[0],t.originalText).numberText),n=Number(ss(e.children[1],t.originalText).numberText);if(0===r&&e.children.length>2){const r=Number(ss(e.children[2],t.originalText).numberText);return 1===n&&1===r}return 1===n},INLINE_NODE_TYPES:is,INLINE_NODE_WRAPPER_TYPES:os,isAutolink:function(e){if(!e||"link"!==e.type||1!==e.children.length)return!1;const t=e.children[0];return t&&es(e)===es(t)&&ts(e)===ts(t)}};const fs=/^import\s/,Ds=/^export\s/,ps=e=>fs.test(e),hs=e=>Ds.test(e),ds=(e,t)=>{const r=t.indexOf("\n\n"),n=t.slice(0,r);if(hs(n)||ps(n))return e(n)({type:hs(n)?"export":"import",value:n})};ds.locator=e=>hs(e)||ps(e)?-1:1;var gs={esSyntax:function(){const{Parser:e}=this,t=e.prototype.blockTokenizers,r=e.prototype.blockMethods;t.esSyntax=ds,r.splice(r.indexOf("paragraph"),0,"esSyntax")},BLOCKS_REGEX:"[a-z][a-z0-9]*(\\.[a-z][a-z0-9]*)*|",COMMENT_REGEX:/<!---->|<!---?[^>-](?:-?[^-])*-->/};const{locStart:ms,locEnd:Es}=Si,{mapAst:ys,INLINE_NODE_WRAPPER_TYPES:vs}=ls;function bs({isMDX:e}){return t=>{const r=ii().use(ou,Object.assign({commonmark:!0},e&&{blocks:[gs.BLOCKS_REGEX]})).use(bi).use(ws).use(vi).use(e?gs.esSyntax:Cs).use(Fs).use(e?As:Cs).use(ks).use(Os);return r.runSync(r.parse(t))}}function Cs(e){return e}function As(){return e=>ys(e,((e,t,[r])=>"html"!==e.type||gs.COMMENT_REGEX.test(e.value)||vs.includes(r.type)?e:Object.assign(Object.assign({},e),{},{type:"jsx"})))}function ws(){const e=this.Parser.prototype;function t(e,t){const r=Fi(t);if(r.frontMatter)return e(r.frontMatter.raw)(r.frontMatter)}e.blockMethods=["frontMatter",...e.blockMethods],e.blockTokenizers.frontMatter=t,t.onlyAtStart=!0}function Fs(){const e=this.Parser.prototype,t=e.inlineMethods;function r(e,t){const r=t.match(/^({%.*?%}|{{.*?}})/s);if(r)return e(r[0])({type:"liquidNode",value:r[0]})}t.splice(t.indexOf("text"),0,"liquid"),e.inlineTokenizers.liquid=r,r.locator=function(e,t){return e.indexOf("{",t)}}function ks(){const e="wikiLink",t=/^\[\[(?<linkContents>.+?)]]/s,r=this.Parser.prototype,n=r.inlineMethods;function u(r,n){const u=t.exec(n);if(u){const t=u.groups.linkContents.trim();return r(u[0])({type:e,value:t})}}n.splice(n.indexOf("link"),0,e),r.inlineTokenizers.wikiLink=u,u.locator=function(e,t){return e.indexOf("[",t)}}function Os(){const e=this.Parser.prototype,t=e.blockTokenizers.list;function r(e,t,r){return"listItem"===t.type&&(t.loose=t.spread||"\n"===e.charAt(e.length-1),t.loose&&(r.loose=!0)),t}e.blockTokenizers.list=function(e,n,u){function i(t){const n=e(t);function u(e,u){return n(r(t,e,u),u)}return u.reset=function(e,u){return n.reset(r(t,e,u),u)},u}return i.now=e.now,t.call(this,i,n,u)}}const Ts={astFormat:"mdast",hasPragma:Ti.hasPragma,locStart:ms,locEnd:Es},Ss=Object.assign(Object.assign({},Ts),{},{parse:bs({isMDX:!1})});var Is={parsers:{remark:Ss,markdown:Ss,mdx:Object.assign(Object.assign({},Ts),{},{parse:bs({isMDX:!0})})}};export default Is;
