!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Which Cat Breed Should I Choose?</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f9f9f9;
            text-align: center;
            margin: 0;
            padding: 0;
        }
        h1 {
            color: #333;
        }
        .container {
            margin: 50px auto;
            width: 50%;
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        label {
            display: block;
            margin: 20px 0 10px;
        }
        select {
            padding: 10px;
            width: 100%;
            border-radius: 5px;
            border: 1px solid #ccc;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <h1>Which Cat Breed Should I Choose?</h1>
    <div class="container">
        <form id="catQuizForm">
            <label for="personality">What's your personality like?</label>
            <select id="personality">
                <option value="">Select...</option>
                <option value="active">Active</option>
                <option value="laidback">Laid-back</option>
                <option value="introverted">Introverted</option>
                <option value="extroverted">Extroverted</option>
            </select>
            <label for="location">Where do you live?</label>
            <select id="location">
                <option value="">Select...</option>
                <option value="apartment">Apartment</option>
                <option value="suburb">Suburb</option>
                <option value="rural">Rural</option>
            </select>
            <button type="button" onclick="getCatBreed()">Find My Cat Breed!</button>
        </form>
        <div class="result" id="result"></div>
    </div>
    <script>
        function getCatBreed() {
            const personality = document.getElementById('personality').value;
            const location = document.getElementById('location').value;
            let breed = "";

            if (personality === "active" && location === "apartment") {
                breed = "Bengal";
            } else if (personality === "laidback" && location === "suburb") {
                breed = "British Shorthair";
            } else if (personality === "introverted" && location === "rural") {
                breed = "Maine Coon";
            } else if (personality === "extroverted" && location === "apartment") {
                breed = "Siamese";
            } else if (personality === "extroverted" && location === "suburb") {
                breed = "Sphynx";
            } else if (personality === "introverted" && location === "apartment") {
                breed = "Scottish Fold";
            } else {
                breed = "Any friendly breed!";
            }
            document.getElementById('result').innerHTML = `You should choose a: <strong>${breed}</strong>`;
        }
    </script>
</body>
</html>
