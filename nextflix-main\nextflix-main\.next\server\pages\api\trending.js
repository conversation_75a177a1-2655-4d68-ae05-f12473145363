(function() {
var exports = {};
exports.id = "pages/api/trending";
exports.ids = ["pages/api/trending"];
exports.modules = {

/***/ "./config/genres.ts":
/*!**************************!*\
  !*** ./config/genres.ts ***!
  \**************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "genres": function() { return /* binding */ genres; }
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./types/index.ts");

const genres = {
  [_types__WEBPACK_IMPORTED_MODULE_0__.MediaType.MOVIE]: [{
    id: 28,
    name: 'Action'
  }, {
    id: 12,
    name: 'Adventure'
  }, {
    id: 16,
    name: 'Animation'
  }, {
    id: 35,
    name: 'Comedy'
  }, {
    id: 80,
    name: 'Crime'
  }, {
    id: 99,
    name: 'Documentary'
  }, {
    id: 18,
    name: 'Drama'
  }, {
    id: 10751,
    name: 'Family'
  }, {
    id: 14,
    name: 'Fantasy'
  }, {
    id: 36,
    name: 'History'
  }, {
    id: 27,
    name: 'Horror'
  }, {
    id: 10402,
    name: 'Music'
  }, {
    id: 9648,
    name: 'Mystery'
  }, {
    id: 10749,
    name: 'Romance'
  }, {
    id: 878,
    name: 'Science Fiction'
  }, {
    id: 10770,
    name: 'TV Movie'
  }, {
    id: 53,
    name: 'Thriller'
  }, {
    id: 10752,
    name: 'War'
  }, {
    id: 37,
    name: 'Western'
  }],
  [_types__WEBPACK_IMPORTED_MODULE_0__.MediaType.TV]: [{
    id: 10759,
    name: 'Adventure'
  }, {
    id: 16,
    name: 'Animation'
  }, {
    id: 35,
    name: 'Comedy'
  }, {
    id: 80,
    name: 'Crime'
  }, {
    id: 99,
    name: 'Documentary'
  }, {
    id: 18,
    name: 'Drama'
  }, {
    id: 10751,
    name: 'Family'
  }, {
    id: 10762,
    name: 'Kids'
  }, {
    id: 9648,
    name: 'Mystery'
  }, {
    id: 10763,
    name: 'News'
  }, {
    id: 10764,
    name: 'Reality'
  }, {
    id: 10765,
    name: 'Sci-Fi & Fantasy'
  }, {
    id: 10766,
    name: 'Soap'
  }, {
    id: 10767,
    name: 'Talk'
  }, {
    id: 10768,
    name: 'War & Politics'
  }, {
    id: 37,
    name: 'Western'
  }]
};

/***/ }),

/***/ "./pages/api/trending.ts":
/*!*******************************!*\
  !*** ./pages/api/trending.ts ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ handler; }
/* harmony export */ });
/* harmony import */ var _utils_axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/axios */ "./utils/axios.ts");
/* harmony import */ var _utils_apiResolvers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/apiResolvers */ "./utils/apiResolvers.ts");


const apiKey = process.env.TMDB_KEY;
async function handler(request, response) {
  const {
    type,
    time
  } = request.query;

  try {
    const result = await (0,_utils_axios__WEBPACK_IMPORTED_MODULE_0__.default)().get(`/trending/${type}/${time}`, {
      params: {
        api_key: apiKey,
        watch_region: 'US'
      }
    });
    const data = (0,_utils_apiResolvers__WEBPACK_IMPORTED_MODULE_1__.parse)(result.data.results, type);
    response.status(200).json({
      type: 'Success',
      data
    });
  } catch (error) {
    console.log(error.data);
    response.status(500).json({
      type: 'Error',
      data: error.data
    });
  }
}

/***/ }),

/***/ "./types/index.ts":
/*!************************!*\
  !*** ./types/index.ts ***!
  \************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "MediaType": function() { return /* binding */ MediaType; }
/* harmony export */ });
let MediaType;

(function (MediaType) {
  MediaType["MOVIE"] = "movie";
  MediaType["TV"] = "tv";
})(MediaType || (MediaType = {}));

/***/ }),

/***/ "./utils/apiResolvers.ts":
/*!*******************************!*\
  !*** ./utils/apiResolvers.ts ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "parse": function() { return /* binding */ parse; }
/* harmony export */ });
/* harmony import */ var _config_genres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/genres */ "./config/genres.ts");

function parse(array, type) {
  const parsedResponse = [];
  array.forEach(element => {
    const resolved = {
      id: element.id,
      title: element.name || element.title,
      rating: element.vote_average,
      overview: element.overview,
      poster: getImageUrl(element.poster_path, 'poster'),
      banner: getImageUrl(element.backdrop_path, 'original'),
      genre: getGenre(element.genre_ids, type)
    };
    parsedResponse.push(resolved);
  });
  return parsedResponse;
}

function getImageUrl(path, type) {
  const dimension = type === 'poster' ? 'w500' : 'original';
  return `https://image.tmdb.org/t/p/${dimension}${path}`;
}

function getGenre(genreIds, type) {
  const result = _config_genres__WEBPACK_IMPORTED_MODULE_0__.genres[type].filter(item => genreIds.includes(item.id));

  if (result.length > 3) {
    return result.slice(0, 3);
  }

  return result;
}

/***/ }),

/***/ "./utils/axios.ts":
/*!************************!*\
  !*** ./utils/axios.ts ***!
  \************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ getInstance; }
/* harmony export */ });
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ "axios");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(axios__WEBPACK_IMPORTED_MODULE_0__);

function getInstance() {
  return axios__WEBPACK_IMPORTED_MODULE_0___default().create({
    baseURL: 'https://api.themoviedb.org/3'
  });
}

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ (function(module) {

"use strict";
module.exports = require("axios");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
var __webpack_exports__ = (__webpack_exec__("./pages/api/trending.ts"));
module.exports = __webpack_exports__;

})();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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