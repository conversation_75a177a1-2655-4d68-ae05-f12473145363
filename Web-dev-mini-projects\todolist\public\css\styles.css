html {
  background-color: #E4E9FD;
  min-height: 1000px;
  font-family: 'helvetica neue';
}

h1 {
  color: #fff;
  padding: 10px;
}

.box {
  max-width: 400px;
  margin: 50px auto;
  background: white;
  border-radius: 5px;
  box-shadow: 5px 5px 15px -5px rgba(0, 0, 0, 0.3);
}

#heading {
  background-color: rgb(72, 156, 193);
  text-align: center;
}

.item {
  min-height: 70px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #F1F1F1;
}

.item:last-child {
  border-bottom: 0;
}

input:checked+p {
  text-decoration: line-through;
  text-decoration-color: rgb(72, 156, 193);
}

input[type="checkbox"] {
  margin: 20px;
}

p {
  margin: 0;
  padding: 20px;
  font-size: 20px;
  font-weight: 200;
  color: #00204a;
}

form {
  text-align: center;
  margin-left: 20px;
}

button {
  min-height: 50px;
  width: 50px;
  border-radius: 50%;
  border-color: transparent;
  background-color: rgb(72, 156, 193);
  color: #fff;
  font-size: 30px;
  padding-bottom: 6px;
  border-width: 0;
}

input[type="text"] {
  text-align: center;
  height: 60px;
  top: 10px;
  border: none;
  background: transparent;
  font-size: 20px;
  font-weight: 200;
  width: 313px;
}

input[type="text"]:focus {
  outline: none;
  box-shadow: inset 0 -3px 0 0 rgb(72, 156, 193);
}

::placeholder {
  color: grey;
  opacity: 1;
}

footer {
  color: white;
  color: rgba(0, 0, 0, 0.5);
  text-align: center;
}
