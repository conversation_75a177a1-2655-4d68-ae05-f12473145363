exports.id = "components_Dialog_tsx";
exports.ids = ["components_Dialog_tsx"];
exports.modules = {

/***/ "./components/Dialog.tsx":
/*!*******************************!*\
  !*** ./components/Dialog.tsx ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Dialog; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ "framer-motion");
/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(framer_motion__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useExternalClick */ "./hooks/useExternalClick.ts");


var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\Dialog.tsx";


function Dialog(props) {
  const {
    visible,
    classname,
    onClose,
    dialogRef,
    children
  } = props;
  (0,_hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_2__.default)(dialogRef, () => {
    onClose === null || onClose === void 0 ? void 0 : onClose();
  });
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
    children: visible && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {
      className: classname,
      initial: {
        opacity: 0,
        y: 12
      },
      animate: {
        opacity: 1,
        y: 0
      },
      exit: {
        opacity: 0
      },
      children: children
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 23,
      columnNumber: 9
    }, this)
  }, void 0, false);
}

/***/ }),

/***/ "./hooks/useExternalClick.ts":
/*!***********************************!*\
  !*** ./hooks/useExternalClick.ts ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useExternalClick; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

function useExternalClick(ref, callback) {
  const onClick = event => {
    var _ref$current;

    if (!(ref !== null && ref !== void 0 && (_ref$current = ref.current) !== null && _ref$current !== void 0 && _ref$current.contains(event.target))) {
      callback();
    }
  };

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    document.addEventListener('click', onClick);
    return () => document.removeEventListener('click', onClick);
  });
}

/***/ })

};
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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