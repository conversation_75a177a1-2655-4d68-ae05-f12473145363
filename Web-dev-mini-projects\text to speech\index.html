<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="index.css">
    <title>Text to speech</title>
</head>
<body>
    <script type="text/javascript" src="https://code.responsivevoice.org/responsivevoice.js"></script>
    <h1>
        Text to speech converter 
    </h1>
    <textarea id="txt" name="text"></textarea>
    <br>
    <p>*Takes few second to process. Have patience</p>
    <input class="btn" type="button" onclick="textSpeak()" value="submit"> 
    <script>
        function textSpeak(){
            var text = document.getElementById("txt").value;
            responsiveVoice.speak(text);
        }
    </script>
</body>
</html>