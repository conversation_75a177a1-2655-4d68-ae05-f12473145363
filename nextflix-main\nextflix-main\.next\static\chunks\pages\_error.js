(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/_error"],{

/***/ "./node_modules/next/dist/pages/_error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/pages/_error.js ***!
  \************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* module decorator */ module = __webpack_require__.nmd(module);


var _classCallCheck = __webpack_require__(/*! ./node_modules/next/node_modules/@babel/runtime/helpers/classCallCheck */ "./node_modules/next/node_modules/@babel/runtime/helpers/classCallCheck.js");

var _createClass = __webpack_require__(/*! ./node_modules/next/node_modules/@babel/runtime/helpers/createClass */ "./node_modules/next/node_modules/@babel/runtime/helpers/createClass.js");

var _inherits = __webpack_require__(/*! ./node_modules/next/node_modules/@babel/runtime/helpers/inherits */ "./node_modules/next/node_modules/@babel/runtime/helpers/inherits.js");

var _possibleConstructorReturn = __webpack_require__(/*! ./node_modules/next/node_modules/@babel/runtime/helpers/possibleConstructorReturn */ "./node_modules/next/node_modules/@babel/runtime/helpers/possibleConstructorReturn.js");

var _getPrototypeOf = __webpack_require__(/*! ./node_modules/next/node_modules/@babel/runtime/helpers/getPrototypeOf */ "./node_modules/next/node_modules/@babel/runtime/helpers/getPrototypeOf.js");

function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }

function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "./node_modules/next/node_modules/@babel/runtime/helpers/interopRequireDefault.js");

exports.__esModule = true;
exports.default = void 0;

var _react = _interopRequireDefault(__webpack_require__(/*! react */ "./node_modules/react/index.js"));

var _head = _interopRequireDefault(__webpack_require__(/*! ../next-server/lib/head */ "./node_modules/next/dist/next-server/lib/head.js"));

var statusCodes = {
  400: 'Bad Request',
  404: 'This page could not be found',
  405: 'Method Not Allowed',
  500: 'Internal Server Error'
};

function _getInitialProps(_ref) {
  var res = _ref.res,
      err = _ref.err;
  var statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;
  return {
    statusCode: statusCode
  };
}
/**
* `Error` component used for handling errors.
*/


var Error = /*#__PURE__*/function (_react$default$Compon) {
  _inherits(Error, _react$default$Compon);

  var _super = _createSuper(Error);

  function Error() {
    _classCallCheck(this, Error);

    return _super.apply(this, arguments);
  }

  _createClass(Error, [{
    key: "render",
    value: function render() {
      var statusCode = this.props.statusCode;
      var title = this.props.title || statusCodes[statusCode] || 'An unexpected error has occurred';
      return /*#__PURE__*/_react["default"].createElement("div", {
        style: styles.error
      }, /*#__PURE__*/_react["default"].createElement(_head["default"], null, /*#__PURE__*/_react["default"].createElement("title", null, statusCode ? "".concat(statusCode, ": ").concat(title) : 'Application error: a client-side exception has occurred')), /*#__PURE__*/_react["default"].createElement("div", null, /*#__PURE__*/_react["default"].createElement("style", {
        dangerouslySetInnerHTML: {
          __html: 'body { margin: 0 }'
        }
      }), statusCode ? /*#__PURE__*/_react["default"].createElement("h1", {
        style: styles.h1
      }, statusCode) : null, /*#__PURE__*/_react["default"].createElement("div", {
        style: styles.desc
      }, /*#__PURE__*/_react["default"].createElement("h2", {
        style: styles.h2
      }, this.props.title || statusCode ? title : /*#__PURE__*/_react["default"].createElement(_react["default"].Fragment, null, "Application error: a client-side exception has occurred (", /*#__PURE__*/_react["default"].createElement("a", {
        href: "https://nextjs.org/docs/messages/client-side-exception-occurred"
      }, "developer guidance"), ")"), "."))));
    }
  }]);

  return Error;
}(_react["default"].Component);

exports.default = Error;
Error.displayName = 'ErrorPage';
Error.getInitialProps = _getInitialProps;
Error.origGetInitialProps = _getInitialProps;
var styles = {
  error: {
    color: '#000',
    background: '#fff',
    fontFamily: '-apple-system, BlinkMacSystemFont, Roboto, "Segoe UI", "Fira Sans", Avenir, "Helvetica Neue", "Lucida Grande", sans-serif',
    height: '100vh',
    textAlign: 'center',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center'
  },
  desc: {
    display: 'inline-block',
    textAlign: 'left',
    lineHeight: '49px',
    height: '49px',
    verticalAlign: 'middle'
  },
  h1: {
    display: 'inline-block',
    borderRight: '1px solid rgba(0, 0, 0,.3)',
    margin: 0,
    marginRight: '20px',
    padding: '10px 23px 10px 0',
    fontSize: '24px',
    fontWeight: 500,
    verticalAlign: 'top'
  },
  h2: {
    fontSize: '14px',
    fontWeight: 'normal',
    lineHeight: 'inherit',
    margin: 0,
    padding: 0
  }
};

;
    var _a, _b;
    // Legacy CSS implementations will `eval` browser code in a Node.js context
    // to extract CSS. For backwards compatibility, we need to check we're in a
    // browser context before continuing.
    if (typeof self !== 'undefined' &&
        // AMP / No-JS mode does not inject these helpers:
        '$RefreshHelpers$' in self) {
        var currentExports = module.__proto__.exports;
        var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
        // This cannot happen in MainTemplate because the exports mismatch between
        // templating and execution.
        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
        // A module can be accepted automatically based on its exports, e.g. when
        // it is a Refresh Boundary.
        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
            // Save the previous exports on update so we can compare the boundary
            // signatures.
            module.hot.dispose(function (data) {
                data.prevExports = currentExports;
            });
            // Unconditionally accept an update to this module, we'll check if it's
            // still a Refresh Boundary later.
            module.hot.accept();
            // This field is set when the previous version of this module was a
            // Refresh Boundary, letting us know we need to check for invalidation or
            // enqueue an update.
            if (prevExports !== null) {
                // A boundary can become ineligible if its exports are incompatible
                // with the previous exports.
                //
                // For example, if you add/remove/change exports, we'll want to
                // re-execute the importing modules, and force those components to
                // re-render. Similarly, if you convert a class component to a
                // function, we want to invalidate the boundary.
                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                    module.hot.invalidate();
                }
                else {
                    self.$RefreshHelpers$.scheduleUpdate();
                }
            }
        }
        else {
            // Since we just executed the code for the module, it's possible that the
            // new exports made it ineligible for being a boundary.
            // We only care about the case when we were _previously_ a boundary,
            // because we already accepted this update (accidental side effect).
            var isNoLongerABoundary = prevExports !== null;
            if (isNoLongerABoundary) {
                module.hot.invalidate();
            }
        }
    }


/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?page=%2F_error&absolutePagePath=next%2Fdist%2Fpages%2F_error!":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?page=%2F_error&absolutePagePath=next%2Fdist%2Fpages%2F_error! ***!
  \************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {


    (window.__NEXT_P = window.__NEXT_P || []).push([
      "/_error",
      function () {
        return __webpack_require__(/*! next/dist/pages/_error */ "./node_modules/next/dist/pages/_error.js");
      }
    ]);
  

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ // runtime can't be in strict mode because a global variable is assign and maybe created.
/******/ 
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?page=%2F_error&absolutePagePath=next%2Fdist%2Fpages%2F_error!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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