const questions = [
    {
        question: 'What is the capital of France?',
        answers: [
            { text: 'Berlin', isCorrect: false },
            { text: 'Madrid', isCorrect: false },
            { text: 'Paris', isCorrect: true },
            { text: 'Rome', isCorrect: false },
        ],
    },
    {
        question: 'Who developed the theory of relativity?',
        answers: [
            { text: '<PERSON> Newton', isCorrect: false },
            { text: 'Albert Einstein', isCorrect: true },
            { text: '<PERSON>', isCorrect: false },
            { text: 'Nikola Tesla', isCorrect: false },
        ],
    },

    {
        question: 'What is the largest planet in our solar system?',
        answers: [
            { text: 'Earth', isCorrect: false },
            { text: 'Jupiter', isCorrect: true },
            { text: 'Mars', isCorrect: false },
            { text: 'Saturn', isCorrect: false },
        ],
    },
    {
        question: 'What is the chemical symbol for water?',
        answers: [
            { text: 'O2', isCorrect: false },
            { text: 'H2O', isCorrect: true },
            { text: 'CO2', isCorrect: false },
            { text: 'HO', isCorrect: false },
        ],
    },
    {
        question: 'Who wrote "To Kill a Mockingbird"?',
        answers: [
            { text: '<PERSON>', isCorrect: true },
            { text: 'Mark Twain', isCorrect: false },
            { text: 'Ernest Hemingway', isCorrect: false },
            { text: 'F. Scott Fitzgerald', isCorrect: false },
        ],
    },
    {
        question: 'What is the capital of Japan?',
        answers: [
            { text: 'Seoul', isCorrect: false },
            { text: 'Beijing', isCorrect: false },
            { text: 'Tokyo', isCorrect: true },
            { text: 'Bangkok', isCorrect: false },
        ],
    },
    {
        question: 'Who painted the Mona Lisa?',
        answers: [
            { text: 'Vincent van Gogh', isCorrect: false },
            { text: 'Pablo Picasso', isCorrect: false },
            { text: 'Leonardo da Vinci', isCorrect: true },
            { text: 'Claude Monet', isCorrect: false },
        ],
    },
    {
        question: 'What is the hardest natural substance on Earth?',
        answers: [
            { text: 'Gold', isCorrect: false },
            { text: 'Iron', isCorrect: false },
            { text: 'Diamond', isCorrect: true },
            { text: 'Platinum', isCorrect: false },
        ],
    },
    {
        question: 'What is the smallest prime number?',
        answers: [
            { text: '0', isCorrect: false },
            { text: '1', isCorrect: false },
            { text: '2', isCorrect: true },
            { text: '3', isCorrect: false },
        ],
    },
    {
        question: 'Who is known as the "Father of Computers"?',
        answers: [
            { text: 'Alan Turing', isCorrect: false },
            { text: 'Charles Babbage', isCorrect: true },
            { text: 'Bill Gates', isCorrect: false },
            { text: 'Steve Jobs', isCorrect: false },
        ],
    },
    {
        question: 'What is the main ingredient in guacamole?',
        answers: [
            { text: 'Tomato', isCorrect: false },
            { text: 'Onion', isCorrect: false },
            { text: 'Avocado', isCorrect: true },
            { text: 'Pepper', isCorrect: false },
        ],
    },
    {
        question: 'What is the speed of light?',
        answers: [
            { text: '300,000 km/s', isCorrect: true },
            { text: '150,000 km/s', isCorrect: false },
            { text: '450,000 km/s', isCorrect: false },
            { text: '600,000 km/s', isCorrect: false },
        ],
    },
   
];

export default questions;