var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e){var t={exports:{}};return e(t,t.exports),t.exports}var r=t((function(e,t){var r="\n",n=function(){function e(e){this.string=e;for(var t=[0],n=0;n<e.length;)switch(e[n]){case r:n+=r.length,t.push(n);break;case"\r":e[n+="\r".length]===r&&(n+=r.length),t.push(n);break;default:n++}this.offsets=t}return e.prototype.locationForIndex=function(e){if(e<0||e>this.string.length)return null;for(var t=0,r=this.offsets;r[t+1]<=e;)t++;return{line:t,column:e-r[t]}},e.prototype.indexForLocation=function(e){var t=e.line,r=e.column;return t<0||t>=this.offsets.length||r<0||r>this.lengthOfLine(t)?null:this.offsets[t]+r},e.prototype.lengthOfLine=function(e){var t=this.offsets[e];return(e===this.offsets.length-1?this.string.length:this.offsets[e+1])-t},e}();t.__esModule=!0,t.default=n}));var n=function(e,t){const r=new SyntaxError(e+" ("+t.start.line+":"+t.start.column+")");return r.loc=t,r};var o={locStart:function(e){return e.loc.start.offset},locEnd:function(e){return e.loc.end.offset}};var i=Object.freeze({__proto__:null,DEBUG:!1,CI:!1});const a=Object.freeze([]);function s(){return a}const l=s(),c=s();var u;const{keys:h}=Object;let p=null!==(u=Object.assign)&&void 0!==u?u:function(e){for(let t=1;t<arguments.length;t++){let r=arguments[t];if(null===r||"object"!=typeof r)continue;let n=h(r);for(let t=0;t<n.length;t++){let o=n[t];e[o]=r[o]}}return e};function d(e){let t={};t[e]=1;for(let r in t)if(r===e)return r;return e}const f="function"==typeof Proxy,m="function"==typeof Symbol&&"symbol"==typeof Symbol();function g(e="unreachable"){return new Error(e)}function b(e){return d(`__${e}${Math.floor(Math.random()*Date.now())}__`)}const y=m?Symbol:b;function v(e){return-*********&e}function S(e){return *********|e}function k(e){return~e}function w(e){return~e}function E(e){return(e|=0)<0?v(e):k(e)}function P(e){return(e|=0)>-*********?w(e):S(e)}[1,-1].forEach((e=>P(E(e))));var T="function"==typeof WeakSet?WeakSet:class{constructor(){this._map=new WeakMap}add(e){return this._map.set(e,!0),this}delete(e){return this._map.delete(e)}has(e){return this._map.has(e)}};function x(e){return 9===e.nodeType}function N(e,t){let r=!1;if(null!==e)if("string"==typeof t)r=O(e,t);else{if(!Array.isArray(t))throw g();r=t.some((t=>O(e,t)))}if(r)return e;throw function(e,t){return new Error(`cannot cast a ${e} into ${t}`)}(`SimpleElement(${e})`,t)}function O(e,t){switch(t){case"NODE":return!0;case"HTML":return e instanceof HTMLElement;case"SVG":return e instanceof SVGElement;case"ELEMENT":return e instanceof Element;default:if(t.toUpperCase()===t)throw new Error("BUG: this code is missing handling for a generic node type");return e instanceof Element&&e.tagName.toLowerCase()===t}}function A(e){return e.length>0}const C=console,L=console;var _=Object.freeze({__proto__:null,LOCAL_LOGGER:C,LOGGER:L,assertNever:function(e,t="unexpected unreachable branch"){throw L.log("unreachable",e),L.log(`${t} :: ${JSON.stringify(e)} (${e})`),new Error("code reached unreachable")},assert:function(e,t){if(!e)throw new Error(t||"assertion failure")},deprecate:function(e){C.warn(`DEPRECATION: ${e}`)},dict:function(){return Object.create(null)},isDict:function(e){return null!=e},isObject:function(e){return"function"==typeof e||"object"==typeof e&&null!==e},Stack:class{constructor(e=[]){this.current=null,this.stack=e}get size(){return this.stack.length}push(e){this.current=e,this.stack.push(e)}pop(){let e=this.stack.pop(),t=this.stack.length;return this.current=0===t?null:this.stack[t-1],void 0===e?null:e}nth(e){let t=this.stack.length;return t<e?null:this.stack[t-e]}isEmpty(){return 0===this.stack.length}toArray(){return this.stack}},isSerializationFirstNode:function(e){return"%+b:0%"===e.nodeValue},SERIALIZATION_FIRST_NODE_STRING:"%+b:0%",assign:p,fillNulls:function(e){let t=new Array(e);for(let r=0;r<e;r++)t[r]=null;return t},values:function(e){const t=[];for(const r in e)t.push(e[r]);return t},_WeakSet:T,castToSimple:function(e){return x(e)||function(e){e.nodeType}(e),e},castToBrowser:function(e,t){if(null==e)return null;if(void 0===typeof document)throw new Error("Attempted to cast to a browser node in a non-browser context");if(x(e))return e;if(e.ownerDocument!==document)throw new Error("Attempted to cast to a browser node with a node that was not created from this document");return N(e,t)},checkNode:N,intern:d,buildUntouchableThis:function(e){return null},debugToString:undefined,beginTestSteps:undefined,endTestSteps:undefined,logStep:undefined,verifySteps:undefined,EMPTY_ARRAY:a,emptyArray:s,EMPTY_STRING_ARRAY:l,EMPTY_NUMBER_ARRAY:c,isEmptyArray:function(e){return e===a},clearElement:function(e){let t=e.firstChild;for(;t;){let r=t.nextSibling;e.removeChild(t),t=r}},HAS_NATIVE_PROXY:f,HAS_NATIVE_SYMBOL:m,keys:function(e){return Object.keys(e)},unwrap:function(e){if(null==e)throw new Error("Expected value to be present");return e},expect:function(e,t){if(null==e)throw new Error(t);return e},unreachable:g,exhausted:function(e){throw new Error(`Exhausted ${e}`)},tuple:(...e)=>e,enumerableSymbol:b,symbol:y,strip:function(e,...t){let r="";for(let n=0;n<e.length;n++){r+=`${e[n]}${void 0!==t[n]?String(t[n]):""}`}let n=r.split("\n");for(;n.length&&n[0].match(/^\s*$/);)n.shift();for(;n.length&&n[n.length-1].match(/^\s*$/);)n.pop();let o=1/0;for(let e of n){let t=e.match(/^\s*/)[0].length;o=Math.min(o,t)}let i=[];for(let e of n)i.push(e.slice(o));return i.join("\n")},isHandle:function(e){return e>=0},isNonPrimitiveHandle:function(e){return e>3},constants:function(...e){return[!1,!0,null,void 0,...e]},isSmallInt:function(e){return e%1==0&&e<=536870911&&e>=-*********},encodeNegative:v,decodeNegative:S,encodePositive:k,decodePositive:w,encodeHandle:function(e){return e},decodeHandle:function(e){return e},encodeImmediate:E,decodeImmediate:P,unwrapHandle:function(e){if("number"==typeof e)return e;{let t=e.errors[0];throw new Error(`Compile Error: ${t.problem} @ ${t.span.start}..${t.span.end}`)}},unwrapTemplate:function(e){if("error"===e.result)throw new Error(`Compile Error: ${e.problem} @ ${e.span.start}..${e.span.end}`);return e},extractHandle:function(e){return"number"==typeof e?e:e.handle},isOkHandle:function(e){return"number"==typeof e},isErrHandle:function(e){return"number"==typeof e},isPresent:A,ifPresent:function(e,t,r){return A(e)?t(e):r()},toPresentOption:function(e){return A(e)?e:null},assertPresent:function(e,t="unexpected empty list"){if(!A(e))throw new Error(t)},mapPresent:function(e,t){if(null===e)return null;let r=[];for(let n of e)r.push(t(n));return r}}),B=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isLocatedWithPositionsArray=function(e){return(0,_.isPresent)(e)&&e.every(l)},t.isLocatedWithPositions=l,t.BROKEN_LOCATION=t.NON_EXISTENT_LOCATION=t.TEMPORARY_LOCATION=t.SYNTHETIC=t.SYNTHETIC_LOCATION=t.UNKNOWN_POSITION=void 0;const r=Object.freeze({line:1,column:0});t.UNKNOWN_POSITION=r;const n=Object.freeze({source:"(synthetic)",start:r,end:r});t.SYNTHETIC_LOCATION=n;const o=n;t.SYNTHETIC=o;const i=Object.freeze({source:"(temporary)",start:r,end:r});t.TEMPORARY_LOCATION=i;const a=Object.freeze({source:"(nonexistent)",start:r,end:r});t.NON_EXISTENT_LOCATION=a;const s=Object.freeze({source:"(broken)",start:r,end:r});function l(e){return void 0!==e.loc}t.BROKEN_LOCATION=s})),D=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.SourceSlice=void 0;class r{constructor(e){this.loc=e.loc,this.chars=e.chars}static synthetic(e){let t=q.SourceSpan.synthetic(e);return new r({loc:t,chars:e})}static load(e,t){return new r({loc:q.SourceSpan.load(e,t[1]),chars:t[0]})}getString(){return this.chars}serialize(){return[this.chars,this.loc.serialize()]}}t.SourceSlice=r})),I=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.match=function(e){return e(new u).check()},t.IsInvisible=t.MatchAny=void 0;var r,n,o,i=function(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)};const a="MATCH_ANY";t.MatchAny=a;const s="IS_INVISIBLE";t.IsInvisible=s;class l{constructor(e){r.set(this,void 0),function(e,t,r){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");t.set(e,r)}(this,r,e)}first(e){for(let t of i(this,r)){let r=t.match(e);if((0,_.isPresent)(r))return r[0]}return null}}r=new WeakMap;class c{constructor(){n.set(this,new Map)}get(e,t){let r=i(this,n).get(e);return r||(r=t(),i(this,n).set(e,r),r)}add(e,t){i(this,n).set(e,t)}match(e){let t=function(e){switch(e){case"Broken":case"InternalsSynthetic":case"NonExistent":return s;default:return e}}(e),r=[],o=i(this,n).get(t),l=i(this,n).get(a);return o&&r.push(o),l&&r.push(l),r}}n=new WeakMap;class u{constructor(){o.set(this,new c)}check(){return(e,t)=>this.matchFor(e.kind,t.kind)(e,t)}matchFor(e,t){let r=i(this,o).match(e);return new l(r).first(t)}when(e,t,r){return i(this,o).get(e,(()=>new c)).add(t,r),this}}o=new WeakMap})),M=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.InvisiblePosition=t.HbsPosition=t.CharPosition=t.SourceOffset=t.BROKEN=void 0;var r,n,o=function(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)},i=function(e,t,r){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");return t.set(e,r),r};const a="BROKEN";t.BROKEN=a;class s{constructor(e){this.data=e}static forHbsPos(e,t){return new c(e,t,null).wrap()}static broken(e=B.UNKNOWN_POSITION){return new u("Broken",e).wrap()}get offset(){let e=this.data.toCharPos();return null===e?null:e.offset}eql(e){return h(this.data,e.data)}until(e){return(0,R.span)(this.data,e.data)}move(e){let t=this.data.toCharPos();if(null===t)return s.broken();{let r=t.offset+e;return t.source.check(r)?new l(t.source,r).wrap():s.broken()}}collapsed(){return(0,R.span)(this.data,this.data)}toJSON(){return this.data.toJSON()}}t.SourceOffset=s;class l{constructor(e,t){this.source=e,this.charPos=t,this.kind="CharPosition",r.set(this,null)}toCharPos(){return this}toJSON(){let e=this.toHbsPos();return null===e?B.UNKNOWN_POSITION:e.toJSON()}wrap(){return new s(this)}get offset(){return this.charPos}toHbsPos(){let e=o(this,r);if(null===e){let t=this.source.hbsPosFor(this.charPos);i(this,r,e=null===t?a:new c(this.source,t,this.charPos))}return e===a?null:e}}t.CharPosition=l,r=new WeakMap;class c{constructor(e,t,r=null){this.source=e,this.hbsPos=t,this.kind="HbsPosition",n.set(this,void 0),i(this,n,null===r?null:new l(e,r))}toCharPos(){let e=o(this,n);if(null===e){let t=this.source.charPosFor(this.hbsPos);i(this,n,e=null===t?a:new l(this.source,t))}return e===a?null:e}toJSON(){return this.hbsPos}wrap(){return new s(this)}toHbsPos(){return this}}t.HbsPosition=c,n=new WeakMap;class u{constructor(e,t){this.kind=e,this.pos=t}toCharPos(){return null}toJSON(){return this.pos}wrap(){return new s(this)}get offset(){return null}}t.InvisiblePosition=u;const h=(0,I.match)((e=>e.when("HbsPosition","HbsPosition",(({hbsPos:e},{hbsPos:t})=>e.column===t.column&&e.line===t.line)).when("CharPosition","CharPosition",(({charPos:e},{charPos:t})=>e===t)).when("CharPosition","HbsPosition",(({offset:e},t)=>{var r;return e===(null===(r=t.toCharPos())||void 0===r?void 0:r.offset)})).when("HbsPosition","CharPosition",((e,{offset:t})=>{var r;return(null===(r=e.toCharPos())||void 0===r?void 0:r.offset)===t})).when(I.MatchAny,I.MatchAny,(()=>!1))))})),R=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.span=t.HbsSpan=t.SourceSpan=void 0;var r,n,o,a=function(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)},s=function(e,t,r){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");return t.set(e,r),r};class l{constructor(e){this.data=e,this.isInvisible="CharPosition"!==e.kind&&"HbsPosition"!==e.kind}static get NON_EXISTENT(){return new h("NonExistent",B.NON_EXISTENT_LOCATION).wrap()}static load(e,t){return"number"==typeof t?l.forCharPositions(e,t,t):"string"==typeof t?l.synthetic(t):Array.isArray(t)?l.forCharPositions(e,t[0],t[1]):"NonExistent"===t?l.NON_EXISTENT:"Broken"===t?l.broken(B.BROKEN_LOCATION):void(0,_.assertNever)(t)}static forHbsLoc(e,t){let r=new M.HbsPosition(e,t.start),n=new M.HbsPosition(e,t.end);return new u(e,{start:r,end:n},t).wrap()}static forCharPositions(e,t,r){let n=new M.CharPosition(e,t),o=new M.CharPosition(e,r);return new c(e,{start:n,end:o}).wrap()}static synthetic(e){return new h("InternalsSynthetic",B.NON_EXISTENT_LOCATION,e).wrap()}static broken(e=B.BROKEN_LOCATION){return new h("Broken",e).wrap()}getStart(){return this.data.getStart().wrap()}getEnd(){return this.data.getEnd().wrap()}get loc(){let e=this.data.toHbsSpan();return null===e?B.BROKEN_LOCATION:e.toHbsLoc()}get module(){return this.data.getModule()}get startPosition(){return this.loc.start}get endPosition(){return this.loc.end}toJSON(){return this.loc}withStart(e){return p(e.data,this.data.getEnd())}withEnd(e){return p(this.data.getStart(),e.data)}asString(){return this.data.asString()}toSlice(e){let t=this.data.asString();return i.DEBUG&&void 0!==e&&t!==e&&console.warn(`unexpectedly found ${JSON.stringify(t)} when slicing source, but expected ${JSON.stringify(e)}`),new D.SourceSlice({loc:this,chars:e||t})}get start(){return this.loc.start}set start(e){this.data.locDidUpdate({start:e})}get end(){return this.loc.end}set end(e){this.data.locDidUpdate({end:e})}get source(){return this.module}collapse(e){switch(e){case"start":return this.getStart().collapsed();case"end":return this.getEnd().collapsed()}}extend(e){return p(this.data.getStart(),e.data.getEnd())}serialize(){return this.data.serialize()}slice({skipStart:e=0,skipEnd:t=0}){return p(this.getStart().move(e).data,this.getEnd().move(-t).data)}sliceStartChars({skipStart:e=0,chars:t}){return p(this.getStart().move(e).data,this.getStart().move(e+t).data)}sliceEndChars({skipEnd:e=0,chars:t}){return p(this.getEnd().move(e-t).data,this.getStart().move(-e).data)}}t.SourceSpan=l;class c{constructor(e,t){this.source=e,this.charPositions=t,this.kind="CharPosition",r.set(this,null)}wrap(){return new l(this)}asString(){return this.source.slice(this.charPositions.start.charPos,this.charPositions.end.charPos)}getModule(){return this.source.module}getStart(){return this.charPositions.start}getEnd(){return this.charPositions.end}locDidUpdate(){}toHbsSpan(){let e=a(this,r);if(null===e){let t=this.charPositions.start.toHbsPos(),n=this.charPositions.end.toHbsPos();e=s(this,r,null===t||null===n?M.BROKEN:new u(this.source,{start:t,end:n}))}return e===M.BROKEN?null:e}serialize(){let{start:{charPos:e},end:{charPos:t}}=this.charPositions;return e===t?e:[e,t]}toCharPosSpan(){return this}}r=new WeakMap;class u{constructor(e,t,r=null){this.source=e,this.hbsPositions=t,this.kind="HbsPosition",n.set(this,null),o.set(this,void 0),s(this,o,r)}serialize(){let e=this.toCharPosSpan();return null===e?"Broken":e.wrap().serialize()}wrap(){return new l(this)}updateProvided(e,t){a(this,o)&&(a(this,o)[t]=e),s(this,n,null),s(this,o,{start:e,end:e})}locDidUpdate({start:e,end:t}){void 0!==e&&(this.updateProvided(e,"start"),this.hbsPositions.start=new M.HbsPosition(this.source,e,null)),void 0!==t&&(this.updateProvided(t,"end"),this.hbsPositions.end=new M.HbsPosition(this.source,t,null))}asString(){let e=this.toCharPosSpan();return null===e?"":e.asString()}getModule(){return this.source.module}getStart(){return this.hbsPositions.start}getEnd(){return this.hbsPositions.end}toHbsLoc(){return{start:this.hbsPositions.start.hbsPos,end:this.hbsPositions.end.hbsPos}}toHbsSpan(){return this}toCharPosSpan(){let e=a(this,n);if(null===e){let t=this.hbsPositions.start.toCharPos(),r=this.hbsPositions.end.toCharPos();if(!t||!r)return e=s(this,n,M.BROKEN),null;e=s(this,n,new c(this.source,{start:t,end:r}))}return e===M.BROKEN?null:e}}t.HbsSpan=u,n=new WeakMap,o=new WeakMap;class h{constructor(e,t,r=null){this.kind=e,this.loc=t,this.string=r}serialize(){switch(this.kind){case"Broken":case"NonExistent":return this.kind;case"InternalsSynthetic":return this.string||""}}wrap(){return new l(this)}asString(){return this.string||""}locDidUpdate({start:e,end:t}){void 0!==e&&(this.loc.start=e),void 0!==t&&(this.loc.end=t)}getModule(){return"an unknown module"}getStart(){return new M.InvisiblePosition(this.kind,this.loc.start)}getEnd(){return new M.InvisiblePosition(this.kind,this.loc.end)}toCharPosSpan(){return this}toHbsSpan(){return null}toHbsLoc(){return B.BROKEN_LOCATION}}const p=(0,I.match)((e=>e.when("HbsPosition","HbsPosition",((e,t)=>new u(e.source,{start:e,end:t}).wrap())).when("CharPosition","CharPosition",((e,t)=>new c(e.source,{start:e,end:t}).wrap())).when("CharPosition","HbsPosition",((e,t)=>{let r=t.toCharPos();return null===r?new h("Broken",B.BROKEN_LOCATION).wrap():p(e,r)})).when("HbsPosition","CharPosition",((e,t)=>{let r=e.toCharPos();return null===r?new h("Broken",B.BROKEN_LOCATION).wrap():p(r,t)})).when(I.IsInvisible,I.MatchAny,(e=>new h(e.kind,B.BROKEN_LOCATION).wrap())).when(I.MatchAny,I.IsInvisible,((e,t)=>new h(t.kind,B.BROKEN_LOCATION).wrap()))));t.span=p})),q=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"SourceSpan",{enumerable:!0,get:function(){return R.SourceSpan}}),Object.defineProperty(t,"SourceOffset",{enumerable:!0,get:function(){return M.SourceOffset}})})),j=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Source=void 0;t.Source=class{constructor(e,t="an unknown module"){this.source=e,this.module=t}check(e){return e>=0&&e<=this.source.length}slice(e,t){return this.source.slice(e,t)}offsetFor(e,t){return q.SourceOffset.forHbsPos(this,{line:e,column:t})}spanFor({start:e,end:t}){return q.SourceSpan.forHbsLoc(this,{start:{line:e.line,column:e.column},end:{line:t.line,column:t.column}})}hbsPosFor(e){let t=0,r=0;if(e>this.source.length)return null;for(;;){let n=this.source.indexOf("\n",r);if(e<=n||-1===n)return{line:t+1,column:e-r};t+=1,r=n+1}}charPosFor(e){let{line:t,column:r}=e,n=this.source.length,o=0,a=0;for(;;){if(a>=n)return n;let e=this.source.indexOf("\n",a);if(-1===e&&(e=this.source.length),o===t-1)return a+r>e?e:(i.DEBUG&&this.hbsPosFor(a+r),a+r);if(-1===e)return 0;o+=1,a=e+1}}}})),H=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.PathExpressionImplV1=void 0;var r,n=(r=V)&&r.__esModule?r:{default:r};t.PathExpressionImplV1=class{constructor(e,t,r,n){this.original=e,this.loc=n,this.type="PathExpression",this.this=!1,this.data=!1;let o=r.slice();"ThisHead"===t.type?this.this=!0:"AtHead"===t.type?(this.data=!0,o.unshift(t.name.slice(1))):o.unshift(t.name),this.parts=o}get head(){let e;e=this.this?"this":this.data?`@${this.parts[0]}`:this.parts[0];let t=this.loc.collapse("start").sliceStartChars({chars:e.length}).loc;return n.default.head(e,t)}get tail(){return this.this?this.parts:this.parts.slice(1)}}})),V=t((function(e,t){let r;function n(){return r||(r=new j.Source("","(synthetic)")),r}function o(e){switch(e.type){case"AtHead":return{original:e.name,parts:[e.name]};case"ThisHead":return{original:"this",parts:[]};case"VarHead":return{original:e.name,parts:[e.name]}}}function i(e,t){let r,[n,...o]=e.split(".");return r="this"===n?{type:"ThisHead",loc:p(t||null)}:"@"===n[0]?{type:"AtHead",name:n,loc:p(t||null)}:{type:"VarHead",name:n,loc:p(t||null)},{head:r,tail:o}}function a(e){return{type:"ThisHead",loc:p(e||null)}}function s(e,t){return{type:"AtHead",name:e,loc:p(t||null)}}function l(e,t){return{type:"VarHead",name:e,loc:p(t||null)}}function c(e,t){if("string"!=typeof e){if("type"in e)return e;{let{head:r,tail:n}=i(e.head,q.SourceSpan.broken()),{original:a}=o(r);return new H.PathExpressionImplV1([a,...n].join("."),r,n,p(t||null))}}let{head:r,tail:n}=i(e,q.SourceSpan.broken());return new H.PathExpressionImplV1(e,r,n,p(t||null))}function u(e,t,r){return{type:e,value:t,original:t,loc:p(r||null)}}function h(e,t){return{type:"Hash",pairs:e||[],loc:p(t||null)}}function p(...e){if(1===e.length){let t=e[0];return t&&"object"==typeof t?q.SourceSpan.forHbsLoc(n(),t):q.SourceSpan.forHbsLoc(n(),B.SYNTHETIC_LOCATION)}{let[t,r,o,i,a]=e,s=a?new j.Source("",a):n();return q.SourceSpan.forHbsLoc(s,{start:{line:t,column:r},end:{line:o,column:i}})}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d={mustache:function(e,t,r,n,o,i){return"string"==typeof e&&(e=c(e)),{type:"MustacheStatement",path:e,params:t||[],hash:r||h([]),escaped:!n,trusting:!!n,loc:p(o||null),strip:i||{open:!1,close:!1}}},block:function(e,t,r,n,o,i,a,s,l){let u,d;return u="Template"===n.type?(0,_.assign)({},n,{type:"Block"}):n,d=null!=o&&"Template"===o.type?(0,_.assign)({},o,{type:"Block"}):o,{type:"BlockStatement",path:c(e),params:t||[],hash:r||h([]),program:u||null,inverse:d||null,loc:p(i||null),openStrip:a||{open:!1,close:!1},inverseStrip:s||{open:!1,close:!1},closeStrip:l||{open:!1,close:!1}}},partial:function(e,t,r,n,o){return{type:"PartialStatement",name:e,params:t||[],hash:r||h([]),indent:n||"",strip:{open:!1,close:!1},loc:p(o||null)}},comment:function(e,t){return{type:"CommentStatement",value:e,loc:p(t||null)}},mustacheComment:function(e,t){return{type:"MustacheCommentStatement",value:e,loc:p(t||null)}},element:function(e,t){let r,{attrs:n,blockParams:o,modifiers:i,comments:a,children:s,loc:l}=t,c=!1;return"object"==typeof e?(c=e.selfClosing,r=e.name):"/"===e.slice(-1)?(r=e.slice(0,-1),c=!0):r=e,{type:"ElementNode",tag:r,selfClosing:c,attributes:n||[],blockParams:o||[],modifiers:i||[],comments:a||[],children:s||[],loc:p(l||null)}},elementModifier:function(e,t,r,n){return{type:"ElementModifierStatement",path:c(e),params:t||[],hash:r||h([]),loc:p(n||null)}},attr:function(e,t,r){return{type:"AttrNode",name:e,value:t,loc:p(r||null)}},text:function(e,t){return{type:"TextNode",chars:e||"",loc:p(t||null)}},sexpr:function(e,t,r,n){return{type:"SubExpression",path:c(e),params:t||[],hash:r||h([]),loc:p(n||null)}},concat:function(e,t){if(!(0,_.isPresent)(e))throw new Error("b.concat requires at least one part");return{type:"ConcatStatement",parts:e||[],loc:p(t||null)}},hash:h,pair:function(e,t,r){return{type:"HashPair",key:e,value:t,loc:p(r||null)}},literal:u,program:function(e,t,r){return{type:"Template",body:e||[],blockParams:t||[],loc:p(r||null)}},blockItself:function(e,t,r=!1,n){return{type:"Block",body:e||[],blockParams:t||[],chained:r,loc:p(n||null)}},template:function(e,t,r){return{type:"Template",body:e||[],blockParams:t||[],loc:p(r||null)}},loc:p,pos:function(e,t){return{line:e,column:t}},path:c,fullPath:function(e,t,r){let{original:n,parts:i}=o(e),a=[...n,...[...i,...t]].join(".");return new H.PathExpressionImplV1(a,e,t,p(r||null))},head:function(e,t){return"@"===e[0]?s(e,t):"this"===e?a(t):l(e,t)},at:s,var:l,this:a,blockName:function(e,t){return{type:"NamedBlockName",name:e,loc:p(t||null)}},string:f("StringLiteral"),boolean:f("BooleanLiteral"),number:f("NumberLiteral"),undefined:()=>u("UndefinedLiteral",void 0),null:()=>u("NullLiteral",null)};function f(e){return function(t,r){return u(e,t,r)}}t.default=d})),U=Object.defineProperty({},"__esModule",{value:!0}),$=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(U).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return U[e]}})}))})),z=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.loadResolution=function(e){if("string"==typeof e)switch(e){case"Loose":return o.fallback();case"Strict":return n}switch(e[0]){case"ambiguous":switch(e[1]){case"Append":return o.append({invoke:!1});case"Attr":return o.attr();case"Invoke":return o.append({invoke:!0})}case"ns":return o.namespaced(e[1])}},t.ARGUMENT_RESOLUTION=t.LooseModeResolution=t.STRICT_RESOLUTION=t.StrictResolution=void 0;class r{constructor(){this.isAngleBracket=!1}resolution(){return 31}serialize(){return"Strict"}}t.StrictResolution=r;const n=new r;t.STRICT_RESOLUTION=n;class o{constructor(e,t=!1){this.ambiguity=e,this.isAngleBracket=t}static namespaced(e,t=!1){return new o({namespaces:[e],fallback:!1},t)}static fallback(){return new o({namespaces:[],fallback:!0})}static append({invoke:e}){return new o({namespaces:["Component","Helper"],fallback:!e})}static trustingAppend({invoke:e}){return new o({namespaces:["Helper"],fallback:!e})}static attr(){return new o({namespaces:["Helper"],fallback:!0})}resolution(){if(0===this.ambiguity.namespaces.length)return 33;if(1!==this.ambiguity.namespaces.length)return this.ambiguity.fallback?34:35;if(this.ambiguity.fallback)return 36;switch(this.ambiguity.namespaces[0]){case"Helper":return 37;case"Modifier":return 38;case"Component":return 39}}serialize(){return 0===this.ambiguity.namespaces.length?"Loose":1===this.ambiguity.namespaces.length?this.ambiguity.fallback?["ambiguous","Attr"]:["ns",this.ambiguity.namespaces[0]]:this.ambiguity.fallback?["ambiguous","Append"]:["ambiguous","Invoke"]}}t.LooseModeResolution=o;const i=o.fallback();t.ARGUMENT_RESOLUTION=i})),F=function(e){if(void 0!==e){const t=e;return{fields:()=>class{constructor(e){this.type=t,this.loc=e.loc,G(e,this)}}}}return{fields:()=>class{constructor(e){this.loc=e.loc,G(e,this)}}}};function G(e,t){for(let n of(r=e,Object.keys(r)))t[n]=e[n];var r}var K=Object.defineProperty({node:F},"__esModule",{value:!0}),W=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.NamedArgument=t.NamedArguments=t.PositionalArguments=t.Args=void 0;class r extends((0,K.node)().fields()){static empty(e){return new r({loc:e,positional:n.empty(e),named:o.empty(e)})}static named(e){return new r({loc:e.loc,positional:n.empty(e.loc.collapse("end")),named:e})}nth(e){return this.positional.nth(e)}get(e){return this.named.get(e)}isEmpty(){return this.positional.isEmpty()&&this.named.isEmpty()}}t.Args=r;class n extends((0,K.node)().fields()){static empty(e){return new n({loc:e,exprs:[]})}get size(){return this.exprs.length}nth(e){return this.exprs[e]||null}isEmpty(){return 0===this.exprs.length}}t.PositionalArguments=n;class o extends((0,K.node)().fields()){static empty(e){return new o({loc:e,entries:[]})}get size(){return this.entries.length}get(e){let t=this.entries.filter((t=>t.name.chars===e))[0];return t?t.value:null}isEmpty(){return 0===this.entries.length}}t.NamedArguments=o;t.NamedArgument=class{constructor(e){this.loc=e.name.loc.extend(e.value.loc),this.name=e.name,this.value=e.value}}})),Y=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.ElementModifier=t.ComponentArg=t.SplatAttr=t.HtmlAttr=void 0;class r extends((0,K.node)("HtmlAttr").fields()){}t.HtmlAttr=r;class n extends((0,K.node)("SplatAttr").fields()){}t.SplatAttr=n;class o extends((0,K.node)().fields()){toNamedArgument(){return new W.NamedArgument({name:this.name,value:this.value})}}t.ComponentArg=o;class i extends((0,K.node)("ElementModifier").fields()){}t.ElementModifier=i})),Q=Object.defineProperty({},"__esModule",{value:!0}),J=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.loc=i,t.hasSpan=a,t.maybeLoc=function(e,t){return a(e)?i(e):t},t.SpanList=void 0;var r,n=function(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)};class o{constructor(e=[]){r.set(this,void 0),function(e,t,r){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");t.set(e,r)}(this,r,e)}static range(e,t=q.SourceSpan.NON_EXISTENT){return new o(e.map(i)).getRangeOffset(t)}add(e){n(this,r).push(e)}getRangeOffset(e){if(0===n(this,r).length)return e;{let e=n(this,r)[0],t=n(this,r)[n(this,r).length-1];return e.extend(t)}}}function i(e){if(Array.isArray(e)){let t=e[0],r=e[e.length-1];return i(t).extend(i(r))}return e instanceof q.SourceSpan?e:e.loc}function a(e){return!Array.isArray(e)||0!==e.length}t.SpanList=o,r=new WeakMap})),X=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleElement=t.InvokeComponent=t.InvokeBlock=t.AppendContent=t.HtmlComment=t.HtmlText=t.GlimmerComment=void 0;class r extends((0,K.node)("GlimmerComment").fields()){}t.GlimmerComment=r;class n extends((0,K.node)("HtmlText").fields()){}t.HtmlText=n;class o extends((0,K.node)("HtmlComment").fields()){}t.HtmlComment=o;class i extends((0,K.node)("AppendContent").fields()){get callee(){return"Call"===this.value.type?this.value.callee:this.value}get args(){return"Call"===this.value.type?this.value.args:W.Args.empty(this.value.loc.collapse("end"))}}t.AppendContent=i;class a extends((0,K.node)("InvokeBlock").fields()){}t.InvokeBlock=a;class s extends((0,K.node)("InvokeComponent").fields()){get args(){let e=this.componentArgs.map((e=>e.toNamedArgument()));return W.Args.named(new W.NamedArguments({loc:J.SpanList.range(e,this.callee.loc.collapse("end")),entries:e}))}}t.InvokeComponent=s;class l extends((0,K.node)("SimpleElement").fields()){get args(){let e=this.componentArgs.map((e=>e.toNamedArgument()));return W.Args.named(new W.NamedArguments({loc:J.SpanList.range(e,this.tag.loc.collapse("end")),entries:e}))}}t.SimpleElement=l})),Z=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isLiteral=function(e,t){return"Literal"===e.type&&(void 0===t||("null"===t?null===e.value:typeof e.value===t))},t.InterpolateExpression=t.DeprecatedCallExpression=t.CallExpression=t.PathExpression=t.LiteralExpression=void 0;class r extends((0,K.node)("Literal").fields()){toSlice(){return new D.SourceSlice({loc:this.loc,chars:this.value})}}t.LiteralExpression=r;class n extends((0,K.node)("Path").fields()){}t.PathExpression=n;class o extends((0,K.node)("Call").fields()){}t.CallExpression=o;class i extends((0,K.node)("DeprecatedCall").fields()){}t.DeprecatedCallExpression=i;class a extends((0,K.node)("Interpolate").fields()){}t.InterpolateExpression=a})),ee=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.FreeVarReference=t.LocalVarReference=t.ArgReference=t.ThisReference=void 0;class r extends((0,K.node)("This").fields()){}t.ThisReference=r;class n extends((0,K.node)("Arg").fields()){}t.ArgReference=n;class o extends((0,K.node)("Local").fields()){}t.LocalVarReference=o;class i extends((0,K.node)("Free").fields()){}t.FreeVarReference=i})),te=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.NamedBlock=t.NamedBlocks=t.Block=t.Template=void 0;class r extends((0,K.node)().fields()){}t.Template=r;class n extends((0,K.node)().fields()){}t.Block=n;class o extends((0,K.node)().fields()){get(e){return this.blocks.filter((t=>t.name.chars===e))[0]||null}}t.NamedBlocks=o;class i extends((0,K.node)().fields()){get args(){let e=this.componentArgs.map((e=>e.toNamedArgument()));return W.Args.named(new W.NamedArguments({loc:J.SpanList.range(e,this.name.loc.collapse("end")),entries:e}))}}t.NamedBlock=i})),re=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(z).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return z[e]}})})),Object.keys(K).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return K[e]}})})),Object.keys(W).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return W[e]}})})),Object.keys(Y).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return Y[e]}})})),Object.keys(Q).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return Q[e]}})})),Object.keys(X).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return X[e]}})})),Object.keys(Z).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return Z[e]}})})),Object.keys(ee).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return ee[e]}})})),Object.keys(te).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return te[e]}})}))})),ne=function(e){return e&&e.Math==Math&&e},oe=ne("object"==typeof globalThis&&globalThis)||ne("object"==typeof window&&window)||ne("object"==typeof self&&self)||ne("object"==typeof e&&e)||function(){return this}()||Function("return this")(),ie=function(e){try{return!!e()}catch(e){return!0}},ae=!ie((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),se={}.propertyIsEnumerable,le=Object.getOwnPropertyDescriptor,ce={f:le&&!se.call({1:2},1)?function(e){var t=le(this,e);return!!t&&t.enumerable}:se},ue=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},he={}.toString,pe="".split,de=ie((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==function(e){return he.call(e).slice(8,-1)}(e)?pe.call(e,""):Object(e)}:Object,fe=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},me=function(e){return de(fe(e))},ge=function(e){return"object"==typeof e?null!==e:"function"==typeof e},be=function(e,t){if(!ge(e))return e;var r,n;if(t&&"function"==typeof(r=e.toString)&&!ge(n=r.call(e)))return n;if("function"==typeof(r=e.valueOf)&&!ge(n=r.call(e)))return n;if(!t&&"function"==typeof(r=e.toString)&&!ge(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")},ye=function(e){return Object(fe(e))},ve={}.hasOwnProperty,Se=Object.hasOwn||function(e,t){return ve.call(ye(e),t)},ke=oe.document,we=ge(ke)&&ge(ke.createElement),Ee=!ae&&!ie((function(){return 7!=Object.defineProperty((e="div",we?ke.createElement(e):{}),"a",{get:function(){return 7}}).a;var e})),Pe=Object.getOwnPropertyDescriptor,Te={f:ae?Pe:function(e,t){if(e=me(e),t=be(t,!0),Ee)try{return Pe(e,t)}catch(e){}if(Se(e,t))return ue(!ce.f.call(e,t),e[t])}},xe=function(e){if(!ge(e))throw TypeError(String(e)+" is not an object");return e},Ne=Object.defineProperty,Oe={f:ae?Ne:function(e,t,r){if(xe(e),t=be(t,!0),xe(r),Ee)try{return Ne(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},Ae=ae?function(e,t,r){return Oe.f(e,t,ue(1,r))}:function(e,t,r){return e[t]=r,e},Ce=function(e,t){try{Ae(oe,e,t)}catch(r){oe[e]=t}return t},Le=oe["__core-js_shared__"]||Ce("__core-js_shared__",{}),_e=Function.toString;"function"!=typeof Le.inspectSource&&(Le.inspectSource=function(e){return _e.call(e)});var Be,De,Ie,Me,Re=Le.inspectSource,qe=oe.WeakMap,je="function"==typeof qe&&/native code/.test(Re(qe)),He=t((function(e){(e.exports=function(e,t){return Le[e]||(Le[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.14.0",mode:"global",copyright:"\xa9 2021 Denis Pushkarev (zloirock.ru)"})})),Ve=0,Ue=Math.random(),$e=He("keys"),ze={},Fe=oe.WeakMap;if(je||Le.state){var Ge=Le.state||(Le.state=new Fe),Ke=Ge.get,We=Ge.has,Ye=Ge.set;Be=function(e,t){if(We.call(Ge,e))throw new TypeError("Object already initialized");return t.facade=e,Ye.call(Ge,e,t),t},De=function(e){return Ke.call(Ge,e)||{}},Ie=function(e){return We.call(Ge,e)}}else{var Qe=$e[Me="state"]||($e[Me]=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++Ve+Ue).toString(36)}(Me));ze[Qe]=!0,Be=function(e,t){if(Se(e,Qe))throw new TypeError("Object already initialized");return t.facade=e,Ae(e,Qe,t),t},De=function(e){return Se(e,Qe)?e[Qe]:{}},Ie=function(e){return Se(e,Qe)}}var Je,Xe,Ze={set:Be,get:De,has:Ie,enforce:function(e){return Ie(e)?De(e):Be(e,{})},getterFor:function(e){return function(t){var r;if(!ge(t)||(r=De(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return r}}},et=t((function(e){var t=Ze.get,r=Ze.enforce,n=String(String).split("String");(e.exports=function(e,t,o,i){var a,s=!!i&&!!i.unsafe,l=!!i&&!!i.enumerable,c=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof t||Se(o,"name")||Ae(o,"name",t),(a=r(o)).source||(a.source=n.join("string"==typeof t?t:""))),e!==oe?(s?!c&&e[t]&&(l=!0):delete e[t],l?e[t]=o:Ae(e,t,o)):l?e[t]=o:Ce(t,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||Re(this)}))})),tt=oe,rt=function(e){return"function"==typeof e?e:void 0},nt=function(e,t){return arguments.length<2?rt(tt[e])||rt(oe[e]):tt[e]&&tt[e][t]||oe[e]&&oe[e][t]},ot=Math.ceil,it=Math.floor,at=function(e){return isNaN(e=+e)?0:(e>0?it:ot)(e)},st=Math.min,lt=function(e){return e>0?st(at(e),9007199254740991):0},ct=Math.max,ut=Math.min,ht=function(e){return function(t,r,n){var o,i=me(t),a=lt(i.length),s=function(e,t){var r=at(e);return r<0?ct(r+t,0):ut(r,t)}(n,a);if(e&&r!=r){for(;a>s;)if((o=i[s++])!=o)return!0}else for(;a>s;s++)if((e||s in i)&&i[s]===r)return e||s||0;return!e&&-1}},pt={includes:ht(!0),indexOf:ht(!1)}.indexOf,dt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),ft={f:Object.getOwnPropertyNames||function(e){return function(e,t){var r,n=me(e),o=0,i=[];for(r in n)!Se(ze,r)&&Se(n,r)&&i.push(r);for(;t.length>o;)Se(n,r=t[o++])&&(~pt(i,r)||i.push(r));return i}(e,dt)}},mt={f:Object.getOwnPropertySymbols},gt=nt("Reflect","ownKeys")||function(e){var t=ft.f(xe(e)),r=mt.f;return r?t.concat(r(e)):t},bt=function(e,t){for(var r=gt(t),n=Oe.f,o=Te.f,i=0;i<r.length;i++){var a=r[i];Se(e,a)||n(e,a,o(t,a))}},yt=/#|\.prototype\./,vt=function(e,t){var r=kt[St(e)];return r==Et||r!=wt&&("function"==typeof t?ie(t):!!t)},St=vt.normalize=function(e){return String(e).replace(yt,".").toLowerCase()},kt=vt.data={},wt=vt.NATIVE="N",Et=vt.POLYFILL="P",Pt=vt,Tt=Te.f,xt=Math.floor,Nt=function(e,t){var r=e.length,n=xt(r/2);return r<8?Ot(e,t):At(Nt(e.slice(0,n),t),Nt(e.slice(n),t),t)},Ot=function(e,t){for(var r,n,o=e.length,i=1;i<o;){for(n=i,r=e[i];n&&t(e[n-1],r)>0;)e[n]=e[--n];n!==i++&&(e[n]=r)}return e},At=function(e,t,r){for(var n=e.length,o=t.length,i=0,a=0,s=[];i<n||a<o;)i<n&&a<o?s.push(r(e[i],t[a])<=0?e[i++]:t[a++]):s.push(i<n?e[i++]:t[a++]);return s},Ct=Nt,Lt=nt("navigator","userAgent")||"",_t=Lt.match(/firefox\/(\d+)/i),Bt=!!_t&&+_t[1],Dt=/MSIE|Trident/.test(Lt),It=oe.process,Mt=It&&It.versions,Rt=Mt&&Mt.v8;Rt?Xe=(Je=Rt.split("."))[0]<4?1:Je[0]+Je[1]:Lt&&(!(Je=Lt.match(/Edge\/(\d+)/))||Je[1]>=74)&&(Je=Lt.match(/Chrome\/(\d+)/))&&(Xe=Je[1]);var qt,jt,Ht=Xe&&+Xe,Vt=Lt.match(/AppleWebKit\/(\d+)\./),Ut=!!Vt&&+Vt[1],$t=[],zt=$t.sort,Ft=ie((function(){$t.sort(void 0)})),Gt=ie((function(){$t.sort(null)})),Kt=!!(jt=[]["sort"])&&ie((function(){jt.call(null,qt||function(){throw 1},1)})),Wt=!ie((function(){if(Ht)return Ht<70;if(!(Bt&&Bt>3)){if(Dt)return!0;if(Ut)return Ut<603;var e,t,r,n,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)$t.push({k:t+n,v:r})}for($t.sort((function(e,t){return t.v-e.v})),n=0;n<$t.length;n++)t=$t[n].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}}));!function(e,t){var r,n,o,i,a,s=e.target,l=e.global,c=e.stat;if(r=l?oe:c?oe[s]||Ce(s,{}):(oe[s]||{}).prototype)for(n in t){if(i=t[n],o=e.noTargetGet?(a=Tt(r,n))&&a.value:r[n],!Pt(l?n:s+(c?".":"#")+n,e.forced)&&void 0!==o){if(typeof i==typeof o)continue;bt(i,o)}(e.sham||o&&o.sham)&&Ae(i,"sham",!0),et(r,n,i,e)}}({target:"Array",proto:!0,forced:Ft||!Gt||!Kt||!Wt},{sort:function(e){void 0!==e&&function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function")}(e);var t=ye(this);if(Wt)return void 0===e?zt.call(t):zt.call(t,e);var r,n,o=[],i=lt(t.length);for(n=0;n<i;n++)n in t&&o.push(t[n]);for(r=(o=Ct(o,function(e){return function(t,r){return void 0===r?-1:void 0===t?1:void 0!==e?+e(t,r)||0:String(t)>String(r)?1:-1}}(e))).length,n=0;n<r;)t[n]=o[n++];for(;n<i;)delete t[n++];return t}});var Yt=function(e){if(Xt.test(e))return e.replace(Zt,rr);return e},Qt=function(e){if(er.test(e))return e.replace(tr,nr);return e},Jt=function(e,t){if(e.loc.isInvisible||t.loc.isInvisible)return 0;if(e.loc.startPosition.line<t.loc.startPosition.line)return-1;if(e.loc.startPosition.line===t.loc.startPosition.line&&e.loc.startPosition.column<t.loc.startPosition.column)return-1;if(e.loc.startPosition.line===t.loc.startPosition.line&&e.loc.startPosition.column===t.loc.startPosition.column)return 0;return 1};const Xt=/[\xA0"&]/,Zt=new RegExp(Xt.source,"g"),er=/[\xA0&<>]/,tr=new RegExp(er.source,"g");function rr(e){switch(e.charCodeAt(0)){case 160:return"&nbsp;";case 34:return"&quot;";case 38:return"&amp;";default:return e}}function nr(e){switch(e.charCodeAt(0)){case 160:return"&nbsp;";case 38:return"&amp;";case 60:return"&lt;";case 62:return"&gt;";default:return e}}var or=Object.defineProperty({escapeAttrValue:Yt,escapeText:Qt,sortByLoc:Jt},"__esModule",{value:!0}),ir=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.voidMap=void 0;const r=Object.create(null);t.voidMap=r;"area base br col command embed hr img input keygen link meta param source track wbr".split(" ").forEach((e=>{r[e]=!0}));const n=/\S/;t.default=class{constructor(e){this.buffer="",this.options=e}handledByOverride(e,t=!1){if(void 0!==this.options.override){let r=this.options.override(e,this.options);if("string"==typeof r)return t&&""!==r&&n.test(r[0])&&(r=` ${r}`),this.buffer+=r,!0}return!1}Node(e){switch(e.type){case"MustacheStatement":case"BlockStatement":case"PartialStatement":case"MustacheCommentStatement":case"CommentStatement":case"TextNode":case"ElementNode":case"AttrNode":case"Block":case"Template":return this.TopLevelStatement(e);case"StringLiteral":case"BooleanLiteral":case"NumberLiteral":case"UndefinedLiteral":case"NullLiteral":case"PathExpression":case"SubExpression":return this.Expression(e);case"Program":return this.Block(e);case"ConcatStatement":return this.ConcatStatement(e);case"Hash":return this.Hash(e);case"HashPair":return this.HashPair(e);case"ElementModifierStatement":return this.ElementModifierStatement(e)}}Expression(e){switch(e.type){case"StringLiteral":case"BooleanLiteral":case"NumberLiteral":case"UndefinedLiteral":case"NullLiteral":return this.Literal(e);case"PathExpression":return this.PathExpression(e);case"SubExpression":return this.SubExpression(e)}}Literal(e){switch(e.type){case"StringLiteral":return this.StringLiteral(e);case"BooleanLiteral":return this.BooleanLiteral(e);case"NumberLiteral":return this.NumberLiteral(e);case"UndefinedLiteral":return this.UndefinedLiteral(e);case"NullLiteral":return this.NullLiteral(e)}}TopLevelStatement(e){switch(e.type){case"MustacheStatement":return this.MustacheStatement(e);case"BlockStatement":return this.BlockStatement(e);case"PartialStatement":return this.PartialStatement(e);case"MustacheCommentStatement":return this.MustacheCommentStatement(e);case"CommentStatement":return this.CommentStatement(e);case"TextNode":return this.TextNode(e);case"ElementNode":return this.ElementNode(e);case"Block":case"Template":return this.Block(e);case"AttrNode":return this.AttrNode(e)}}Block(e){if(e.chained){e.body[0].chained=!0}this.handledByOverride(e)||this.TopLevelStatements(e.body)}TopLevelStatements(e){e.forEach((e=>this.TopLevelStatement(e)))}ElementNode(e){this.handledByOverride(e)||(this.OpenElementNode(e),this.TopLevelStatements(e.children),this.CloseElementNode(e))}OpenElementNode(e){this.buffer+=`<${e.tag}`;const t=[...e.attributes,...e.modifiers,...e.comments].sort(or.sortByLoc);for(const e of t)switch(this.buffer+=" ",e.type){case"AttrNode":this.AttrNode(e);break;case"ElementModifierStatement":this.ElementModifierStatement(e);break;case"MustacheCommentStatement":this.MustacheCommentStatement(e)}e.blockParams.length&&this.BlockParams(e.blockParams),e.selfClosing&&(this.buffer+=" /"),this.buffer+=">"}CloseElementNode(e){e.selfClosing||r[e.tag.toLowerCase()]||(this.buffer+=`</${e.tag}>`)}AttrNode(e){if(this.handledByOverride(e))return;let{name:t,value:r}=e;this.buffer+=t,("TextNode"!==r.type||r.chars.length>0)&&(this.buffer+="=",this.AttrNodeValue(r))}AttrNodeValue(e){"TextNode"===e.type?(this.buffer+='"',this.TextNode(e,!0),this.buffer+='"'):this.Node(e)}TextNode(e,t){this.handledByOverride(e)||("raw"===this.options.entityEncoding?this.buffer+=e.chars:this.buffer+=t?(0,or.escapeAttrValue)(e.chars):(0,or.escapeText)(e.chars))}MustacheStatement(e){this.handledByOverride(e)||(this.buffer+=e.escaped?"{{":"{{{",e.strip.open&&(this.buffer+="~"),this.Expression(e.path),this.Params(e.params),this.Hash(e.hash),e.strip.close&&(this.buffer+="~"),this.buffer+=e.escaped?"}}":"}}}")}BlockStatement(e){this.handledByOverride(e)||(e.chained?(this.buffer+=e.inverseStrip.open?"{{~":"{{",this.buffer+="else "):this.buffer+=e.openStrip.open?"{{~#":"{{#",this.Expression(e.path),this.Params(e.params),this.Hash(e.hash),e.program.blockParams.length&&this.BlockParams(e.program.blockParams),e.chained?this.buffer+=e.inverseStrip.close?"~}}":"}}":this.buffer+=e.openStrip.close?"~}}":"}}",this.Block(e.program),e.inverse&&(e.inverse.chained||(this.buffer+=e.inverseStrip.open?"{{~":"{{",this.buffer+="else",this.buffer+=e.inverseStrip.close?"~}}":"}}"),this.Block(e.inverse)),e.chained||(this.buffer+=e.closeStrip.open?"{{~/":"{{/",this.Expression(e.path),this.buffer+=e.closeStrip.close?"~}}":"}}"))}BlockParams(e){this.buffer+=` as |${e.join(" ")}|`}PartialStatement(e){this.handledByOverride(e)||(this.buffer+="{{>",this.Expression(e.name),this.Params(e.params),this.Hash(e.hash),this.buffer+="}}")}ConcatStatement(e){this.handledByOverride(e)||(this.buffer+='"',e.parts.forEach((e=>{"TextNode"===e.type?this.TextNode(e,!0):this.Node(e)})),this.buffer+='"')}MustacheCommentStatement(e){this.handledByOverride(e)||(this.buffer+=`{{!--${e.value}--}}`)}ElementModifierStatement(e){this.handledByOverride(e)||(this.buffer+="{{",this.Expression(e.path),this.Params(e.params),this.Hash(e.hash),this.buffer+="}}")}CommentStatement(e){this.handledByOverride(e)||(this.buffer+=`\x3c!--${e.value}--\x3e`)}PathExpression(e){this.handledByOverride(e)||(this.buffer+=e.original)}SubExpression(e){this.handledByOverride(e)||(this.buffer+="(",this.Expression(e.path),this.Params(e.params),this.Hash(e.hash),this.buffer+=")")}Params(e){e.length&&e.forEach((e=>{this.buffer+=" ",this.Expression(e)}))}Hash(e){this.handledByOverride(e,!0)||e.pairs.forEach((e=>{this.buffer+=" ",this.HashPair(e)}))}HashPair(e){this.handledByOverride(e)||(this.buffer+=e.key,this.buffer+="=",this.Node(e.value))}StringLiteral(e){this.handledByOverride(e)||(this.buffer+=JSON.stringify(e.value))}BooleanLiteral(e){this.handledByOverride(e)||(this.buffer+=e.value)}NumberLiteral(e){this.handledByOverride(e)||(this.buffer+=e.value)}UndefinedLiteral(e){this.handledByOverride(e)||(this.buffer+="undefined")}NullLiteral(e){this.handledByOverride(e)||(this.buffer+="null")}print(e){let{options:t}=this;if(t.override){let r=t.override(e,t);if(void 0!==r)return r}return this.buffer="",this.Node(e),this.buffer}}})),ar=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];function sr(e,t){var r,n,o,i,a=t&&t.loc;a&&(r=a.start.line,n=a.end.line,o=a.start.column,i=a.end.column,e+=" - "+r+":"+o);for(var s=Error.prototype.constructor.call(this,e),l=0;l<ar.length;l++)this[ar[l]]=s[ar[l]];Error.captureStackTrace&&Error.captureStackTrace(this,sr);try{a&&(this.lineNumber=r,this.endLineNumber=n,Object.defineProperty?(Object.defineProperty(this,"column",{value:o,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:i,enumerable:!0})):(this.column=o,this.endColumn=i))}catch(e){}}function lr(){this.parents=[]}function cr(e){this.acceptRequired(e,"path"),this.acceptArray(e.params),this.acceptKey(e,"hash")}function ur(e){cr.call(this,e),this.acceptKey(e,"program"),this.acceptKey(e,"inverse")}function hr(e){this.acceptRequired(e,"name"),this.acceptArray(e.params),this.acceptKey(e,"hash")}function pr(e){void 0===e&&(e={}),this.options=e}function dr(e,t,r){void 0===t&&(t=e.length);var n=e[t-1],o=e[t-2];return n?"ContentStatement"===n.type?(o||!r?/\r?\n\s*?$/:/(^|\r?\n)\s*?$/).test(n.original):void 0:r}function fr(e,t,r){void 0===t&&(t=-1);var n=e[t+1],o=e[t+2];return n?"ContentStatement"===n.type?(o||!r?/^\s*?\r?\n/:/^\s*?(\r?\n|$)/).test(n.original):void 0:r}function mr(e,t,r){var n=e[null==t?0:t+1];if(n&&"ContentStatement"===n.type&&(r||!n.rightStripped)){var o=n.value;n.value=n.value.replace(r?/^\s+/:/^[ \t]*\r?\n?/,""),n.rightStripped=n.value!==o}}function gr(e,t,r){var n=e[null==t?e.length-1:t-1];if(n&&"ContentStatement"===n.type&&(r||!n.leftStripped)){var o=n.value;return n.value=n.value.replace(r?/\s+$/:/[ \t]+$/,""),n.leftStripped=n.value!==o,n.leftStripped}}sr.prototype=new Error,lr.prototype={constructor:lr,mutating:!1,acceptKey:function(e,t){var r=this.accept(e[t]);if(this.mutating){if(r&&!lr.prototype[r.type])throw new sr('Unexpected node type "'+r.type+'" found when accepting '+t+" on "+e.type);e[t]=r}},acceptRequired:function(e,t){if(this.acceptKey(e,t),!e[t])throw new sr(e.type+" requires "+t)},acceptArray:function(e){for(var t=0,r=e.length;t<r;t++)this.acceptKey(e,t),e[t]||(e.splice(t,1),t--,r--)},accept:function(e){if(e){if(!this[e.type])throw new sr("Unknown type: "+e.type,e);this.current&&this.parents.unshift(this.current),this.current=e;var t=this[e.type](e);return this.current=this.parents.shift(),!this.mutating||t?t:!1!==t?e:void 0}},Program:function(e){this.acceptArray(e.body)},MustacheStatement:cr,Decorator:cr,BlockStatement:ur,DecoratorBlock:ur,PartialStatement:hr,PartialBlockStatement:function(e){hr.call(this,e),this.acceptKey(e,"program")},ContentStatement:function(){},CommentStatement:function(){},SubExpression:cr,PathExpression:function(){},StringLiteral:function(){},NumberLiteral:function(){},BooleanLiteral:function(){},UndefinedLiteral:function(){},NullLiteral:function(){},Hash:function(e){this.acceptArray(e.pairs)},HashPair:function(e){this.acceptRequired(e,"value")}},pr.prototype=new lr,pr.prototype.Program=function(e){var t=!this.options.ignoreStandalone,r=!this.isRootSeen;this.isRootSeen=!0;for(var n=e.body,o=0,i=n.length;o<i;o++){var a=n[o],s=this.accept(a);if(s){var l=dr(n,o,r),c=fr(n,o,r),u=s.openStandalone&&l,h=s.closeStandalone&&c,p=s.inlineStandalone&&l&&c;s.close&&mr(n,o,!0),s.open&&gr(n,o,!0),t&&p&&(mr(n,o),gr(n,o)&&"PartialStatement"===a.type&&(a.indent=/([ \t]+$)/.exec(n[o-1].original)[1])),t&&u&&(mr((a.program||a.inverse).body),gr(n,o)),t&&h&&(mr(n,o),gr((a.inverse||a.program).body))}}return e},pr.prototype.BlockStatement=pr.prototype.DecoratorBlock=pr.prototype.PartialBlockStatement=function(e){this.accept(e.program),this.accept(e.inverse);var t=e.program||e.inverse,r=e.program&&e.inverse,n=r,o=r;if(r&&r.chained)for(n=r.body[0].program;o.chained;)o=o.body[o.body.length-1].program;var i={open:e.openStrip.open,close:e.closeStrip.close,openStandalone:fr(t.body),closeStandalone:dr((n||t).body)};if(e.openStrip.close&&mr(t.body,null,!0),r){var a=e.inverseStrip;a.open&&gr(t.body,null,!0),a.close&&mr(n.body,null,!0),e.closeStrip.open&&gr(o.body,null,!0),!this.options.ignoreStandalone&&dr(t.body)&&fr(n.body)&&(gr(t.body),mr(n.body))}else e.closeStrip.open&&gr(t.body,null,!0);return i},pr.prototype.Decorator=pr.prototype.MustacheStatement=function(e){return e.strip},pr.prototype.PartialStatement=pr.prototype.CommentStatement=function(e){var t=e.strip||{};return{inlineStandalone:!0,open:t.open,close:t.close}};var br=function(){var e=function(e,t,r,n){for(r=r||{},n=e.length;n--;r[e[n]]=t);return r},t=[2,44],r=[1,20],n=[5,14,15,19,29,34,39,44,47,48,52,56,60],o=[1,35],i=[1,38],a=[1,30],s=[1,31],l=[1,32],c=[1,33],u=[1,34],h=[1,37],p=[14,15,19,29,34,39,44,47,48,52,56,60],d=[14,15,19,29,34,44,47,48,52,56,60],f=[15,18],m=[14,15,19,29,34,47,48,52,56,60],g=[33,64,71,79,80,81,82,83,84],b=[23,33,55,64,67,71,74,79,80,81,82,83,84],y=[1,51],v=[23,33,55,64,67,71,74,79,80,81,82,83,84,86],S=[2,43],k=[55,64,71,79,80,81,82,83,84],w=[1,58],E=[1,59],P=[1,66],T=[33,64,71,74,79,80,81,82,83,84],x=[23,64,71,79,80,81,82,83,84],N=[1,76],O=[64,67,71,79,80,81,82,83,84],A=[33,74],C=[23,33,55,67,71,74],L=[1,106],_=[1,118],B=[71,76],D={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,program_repetition0:6,statement:7,mustache:8,block:9,rawBlock:10,partial:11,partialBlock:12,content:13,COMMENT:14,CONTENT:15,openRawBlock:16,rawBlock_repetition0:17,END_RAW_BLOCK:18,OPEN_RAW_BLOCK:19,helperName:20,openRawBlock_repetition0:21,openRawBlock_option0:22,CLOSE_RAW_BLOCK:23,openBlock:24,block_option0:25,closeBlock:26,openInverse:27,block_option1:28,OPEN_BLOCK:29,openBlock_repetition0:30,openBlock_option0:31,openBlock_option1:32,CLOSE:33,OPEN_INVERSE:34,openInverse_repetition0:35,openInverse_option0:36,openInverse_option1:37,openInverseChain:38,OPEN_INVERSE_CHAIN:39,openInverseChain_repetition0:40,openInverseChain_option0:41,openInverseChain_option1:42,inverseAndProgram:43,INVERSE:44,inverseChain:45,inverseChain_option0:46,OPEN_ENDBLOCK:47,OPEN:48,expr:49,mustache_repetition0:50,mustache_option0:51,OPEN_UNESCAPED:52,mustache_repetition1:53,mustache_option1:54,CLOSE_UNESCAPED:55,OPEN_PARTIAL:56,partial_repetition0:57,partial_option0:58,openPartialBlock:59,OPEN_PARTIAL_BLOCK:60,openPartialBlock_repetition0:61,openPartialBlock_option0:62,sexpr:63,OPEN_SEXPR:64,sexpr_repetition0:65,sexpr_option0:66,CLOSE_SEXPR:67,hash:68,hash_repetition_plus0:69,hashSegment:70,ID:71,EQUALS:72,blockParams:73,OPEN_BLOCK_PARAMS:74,blockParams_repetition_plus0:75,CLOSE_BLOCK_PARAMS:76,path:77,dataName:78,STRING:79,NUMBER:80,BOOLEAN:81,UNDEFINED:82,NULL:83,DATA:84,pathSegments:85,SEP:86,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"COMMENT",15:"CONTENT",18:"END_RAW_BLOCK",19:"OPEN_RAW_BLOCK",23:"CLOSE_RAW_BLOCK",29:"OPEN_BLOCK",33:"CLOSE",34:"OPEN_INVERSE",39:"OPEN_INVERSE_CHAIN",44:"INVERSE",47:"OPEN_ENDBLOCK",48:"OPEN",52:"OPEN_UNESCAPED",55:"CLOSE_UNESCAPED",56:"OPEN_PARTIAL",60:"OPEN_PARTIAL_BLOCK",64:"OPEN_SEXPR",67:"CLOSE_SEXPR",71:"ID",72:"EQUALS",74:"OPEN_BLOCK_PARAMS",76:"CLOSE_BLOCK_PARAMS",79:"STRING",80:"NUMBER",81:"BOOLEAN",82:"UNDEFINED",83:"NULL",84:"DATA",86:"SEP"},productions_:[0,[3,2],[4,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[13,1],[10,3],[16,5],[9,4],[9,4],[24,6],[27,6],[38,6],[43,2],[45,3],[45,1],[26,3],[8,5],[8,5],[11,5],[12,3],[59,5],[49,1],[49,1],[63,5],[68,1],[70,3],[73,3],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[78,2],[77,1],[85,3],[85,1],[6,0],[6,2],[17,0],[17,2],[21,0],[21,2],[22,0],[22,1],[25,0],[25,1],[28,0],[28,1],[30,0],[30,2],[31,0],[31,1],[32,0],[32,1],[35,0],[35,2],[36,0],[36,1],[37,0],[37,1],[40,0],[40,2],[41,0],[41,1],[42,0],[42,1],[46,0],[46,1],[50,0],[50,2],[51,0],[51,1],[53,0],[53,2],[54,0],[54,1],[57,0],[57,2],[58,0],[58,1],[61,0],[61,2],[62,0],[62,1],[65,0],[65,2],[66,0],[66,1],[69,1],[69,2],[75,1],[75,2]],performAction:function(e,t,r,n,o,i,a){var s=i.length-1;switch(o){case 1:return i[s-1];case 2:this.$=n.prepareProgram(i[s]);break;case 3:case 4:case 5:case 6:case 7:case 8:case 20:case 27:case 28:case 33:case 34:this.$=i[s];break;case 9:this.$={type:"CommentStatement",value:n.stripComment(i[s]),strip:n.stripFlags(i[s],i[s]),loc:n.locInfo(this._$)};break;case 10:this.$={type:"ContentStatement",original:i[s],value:i[s],loc:n.locInfo(this._$)};break;case 11:this.$=n.prepareRawBlock(i[s-2],i[s-1],i[s],this._$);break;case 12:this.$={path:i[s-3],params:i[s-2],hash:i[s-1]};break;case 13:this.$=n.prepareBlock(i[s-3],i[s-2],i[s-1],i[s],!1,this._$);break;case 14:this.$=n.prepareBlock(i[s-3],i[s-2],i[s-1],i[s],!0,this._$);break;case 15:this.$={open:i[s-5],path:i[s-4],params:i[s-3],hash:i[s-2],blockParams:i[s-1],strip:n.stripFlags(i[s-5],i[s])};break;case 16:case 17:this.$={path:i[s-4],params:i[s-3],hash:i[s-2],blockParams:i[s-1],strip:n.stripFlags(i[s-5],i[s])};break;case 18:this.$={strip:n.stripFlags(i[s-1],i[s-1]),program:i[s]};break;case 19:var l=n.prepareBlock(i[s-2],i[s-1],i[s],i[s],!1,this._$),c=n.prepareProgram([l],i[s-1].loc);c.chained=!0,this.$={strip:i[s-2].strip,program:c,chain:!0};break;case 21:this.$={path:i[s-1],strip:n.stripFlags(i[s-2],i[s])};break;case 22:case 23:this.$=n.prepareMustache(i[s-3],i[s-2],i[s-1],i[s-4],n.stripFlags(i[s-4],i[s]),this._$);break;case 24:this.$={type:"PartialStatement",name:i[s-3],params:i[s-2],hash:i[s-1],indent:"",strip:n.stripFlags(i[s-4],i[s]),loc:n.locInfo(this._$)};break;case 25:this.$=n.preparePartialBlock(i[s-2],i[s-1],i[s],this._$);break;case 26:this.$={path:i[s-3],params:i[s-2],hash:i[s-1],strip:n.stripFlags(i[s-4],i[s])};break;case 29:this.$={type:"SubExpression",path:i[s-3],params:i[s-2],hash:i[s-1],loc:n.locInfo(this._$)};break;case 30:this.$={type:"Hash",pairs:i[s],loc:n.locInfo(this._$)};break;case 31:this.$={type:"HashPair",key:n.id(i[s-2]),value:i[s],loc:n.locInfo(this._$)};break;case 32:this.$=n.id(i[s-1]);break;case 35:this.$={type:"StringLiteral",value:i[s],original:i[s],loc:n.locInfo(this._$)};break;case 36:this.$={type:"NumberLiteral",value:Number(i[s]),original:Number(i[s]),loc:n.locInfo(this._$)};break;case 37:this.$={type:"BooleanLiteral",value:"true"===i[s],original:"true"===i[s],loc:n.locInfo(this._$)};break;case 38:this.$={type:"UndefinedLiteral",original:void 0,value:void 0,loc:n.locInfo(this._$)};break;case 39:this.$={type:"NullLiteral",original:null,value:null,loc:n.locInfo(this._$)};break;case 40:this.$=n.preparePath(!0,i[s],this._$);break;case 41:this.$=n.preparePath(!1,i[s],this._$);break;case 42:i[s-2].push({part:n.id(i[s]),original:i[s],separator:i[s-1]}),this.$=i[s-2];break;case 43:this.$=[{part:n.id(i[s]),original:i[s]}];break;case 44:case 46:case 48:case 56:case 62:case 68:case 76:case 80:case 84:case 88:case 92:this.$=[];break;case 45:case 47:case 49:case 57:case 63:case 69:case 77:case 81:case 85:case 89:case 93:case 97:case 99:i[s-1].push(i[s]);break;case 96:case 98:this.$=[i[s]]}},table:[e([5,14,15,19,29,34,48,52,56,60],t,{3:1,4:2,6:3}),{1:[3]},{5:[1,4]},e([5,39,44,47],[2,2],{7:5,8:6,9:7,10:8,11:9,12:10,13:11,24:15,27:16,16:17,59:19,14:[1,12],15:r,19:[1,23],29:[1,21],34:[1,22],48:[1,13],52:[1,14],56:[1,18],60:[1,24]}),{1:[2,1]},e(n,[2,45]),e(n,[2,3]),e(n,[2,4]),e(n,[2,5]),e(n,[2,6]),e(n,[2,7]),e(n,[2,8]),e(n,[2,9]),{20:26,49:25,63:27,64:o,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},{20:26,49:39,63:27,64:o,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},e(p,t,{6:3,4:40}),e(d,t,{6:3,4:41}),e(f,[2,46],{17:42}),{20:26,49:43,63:27,64:o,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},e(m,t,{6:3,4:44}),e([5,14,15,18,19,29,34,39,44,47,48,52,56,60],[2,10]),{20:45,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},{20:46,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},{20:47,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},{20:26,49:48,63:27,64:o,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},e(g,[2,76],{50:49}),e(b,[2,27]),e(b,[2,28]),e(b,[2,33]),e(b,[2,34]),e(b,[2,35]),e(b,[2,36]),e(b,[2,37]),e(b,[2,38]),e(b,[2,39]),{20:26,49:50,63:27,64:o,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},e(b,[2,41],{86:y}),{71:i,85:52},e(v,S),e(k,[2,80],{53:53}),{25:54,38:56,39:w,43:57,44:E,45:55,47:[2,52]},{28:60,43:61,44:E,47:[2,54]},{13:63,15:r,18:[1,62]},e(g,[2,84],{57:64}),{26:65,47:P},e(T,[2,56],{30:67}),e(T,[2,62],{35:68}),e(x,[2,48],{21:69}),e(g,[2,88],{61:70}),{20:26,33:[2,78],49:72,51:71,63:27,64:o,68:73,69:74,70:75,71:N,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},e(O,[2,92],{65:77}),{71:[1,78]},e(b,[2,40],{86:y}),{20:26,49:80,54:79,55:[2,82],63:27,64:o,68:81,69:74,70:75,71:N,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},{26:82,47:P},{47:[2,53]},e(p,t,{6:3,4:83}),{47:[2,20]},{20:84,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},e(m,t,{6:3,4:85}),{26:86,47:P},{47:[2,55]},e(n,[2,11]),e(f,[2,47]),{20:26,33:[2,86],49:88,58:87,63:27,64:o,68:89,69:74,70:75,71:N,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},e(n,[2,25]),{20:90,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},e(A,[2,58],{20:26,63:27,77:28,78:29,85:36,69:74,70:75,31:91,49:92,68:93,64:o,71:N,79:a,80:s,81:l,82:c,83:u,84:h}),e(A,[2,64],{20:26,63:27,77:28,78:29,85:36,69:74,70:75,36:94,49:95,68:96,64:o,71:N,79:a,80:s,81:l,82:c,83:u,84:h}),{20:26,22:97,23:[2,50],49:98,63:27,64:o,68:99,69:74,70:75,71:N,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},{20:26,33:[2,90],49:101,62:100,63:27,64:o,68:102,69:74,70:75,71:N,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},{33:[1,103]},e(g,[2,77]),{33:[2,79]},e([23,33,55,67,74],[2,30],{70:104,71:[1,105]}),e(C,[2,96]),e(v,S,{72:L}),{20:26,49:108,63:27,64:o,66:107,67:[2,94],68:109,69:74,70:75,71:N,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},e(v,[2,42]),{55:[1,110]},e(k,[2,81]),{55:[2,83]},e(n,[2,13]),{38:56,39:w,43:57,44:E,45:112,46:111,47:[2,74]},e(T,[2,68],{40:113}),{47:[2,18]},e(n,[2,14]),{33:[1,114]},e(g,[2,85]),{33:[2,87]},{33:[1,115]},{32:116,33:[2,60],73:117,74:_},e(T,[2,57]),e(A,[2,59]),{33:[2,66],37:119,73:120,74:_},e(T,[2,63]),e(A,[2,65]),{23:[1,121]},e(x,[2,49]),{23:[2,51]},{33:[1,122]},e(g,[2,89]),{33:[2,91]},e(n,[2,22]),e(C,[2,97]),{72:L},{20:26,49:123,63:27,64:o,71:i,77:28,78:29,79:a,80:s,81:l,82:c,83:u,84:h,85:36},{67:[1,124]},e(O,[2,93]),{67:[2,95]},e(n,[2,23]),{47:[2,19]},{47:[2,75]},e(A,[2,70],{20:26,63:27,77:28,78:29,85:36,69:74,70:75,41:125,49:126,68:127,64:o,71:N,79:a,80:s,81:l,82:c,83:u,84:h}),e(n,[2,24]),e(n,[2,21]),{33:[1,128]},{33:[2,61]},{71:[1,130],75:129},{33:[1,131]},{33:[2,67]},e(f,[2,12]),e(m,[2,26]),e(C,[2,31]),e(b,[2,29]),{33:[2,72],42:132,73:133,74:_},e(T,[2,69]),e(A,[2,71]),e(p,[2,15]),{71:[1,135],76:[1,134]},e(B,[2,98]),e(d,[2,16]),{33:[1,136]},{33:[2,73]},{33:[2,32]},e(B,[2,99]),e(p,[2,17])],defaultActions:{4:[2,1],55:[2,53],57:[2,20],61:[2,55],73:[2,79],81:[2,83],85:[2,18],89:[2,87],99:[2,51],102:[2,91],109:[2,95],111:[2,19],112:[2,75],117:[2,61],120:[2,67],133:[2,73],134:[2,32]},parseError:function(e,t){if(!t.recoverable){var r=new Error(e);throw r.hash=t,r}this.trace(e)},parse:function(e){var t=this,r=[0],n=[null],o=[],i=this.table,a="",s=0,l=0,c=2,u=1,h=o.slice.call(arguments,1),p=Object.create(this.lexer),d={yy:{}};for(var f in this.yy)Object.prototype.hasOwnProperty.call(this.yy,f)&&(d.yy[f]=this.yy[f]);p.setInput(e,d.yy),d.yy.lexer=p,d.yy.parser=this,void 0===p.yylloc&&(p.yylloc={});var m=p.yylloc;o.push(m);var g=p.options&&p.options.ranges;"function"==typeof d.yy.parseError?this.parseError=d.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;for(var b,y,v,S,k,w,E,P,T=function(){var e;return"number"!=typeof(e=p.lex()||u)&&(e=t.symbols_[e]||e),e},x={};;){if(y=r[r.length-1],this.defaultActions[y]?v=this.defaultActions[y]:(null==b&&(b=T()),v=i[y]&&i[y][b]),void 0===v||!v.length||!v[0]){var N="";for(k in P=[],i[y])this.terminals_[k]&&k>c&&P.push("'"+this.terminals_[k]+"'");N=p.showPosition?"Parse error on line "+(s+1)+":\n"+p.showPosition()+"\nExpecting "+P.join(", ")+", got '"+(this.terminals_[b]||b)+"'":"Parse error on line "+(s+1)+": Unexpected "+(b==u?"end of input":"'"+(this.terminals_[b]||b)+"'"),this.parseError(N,{text:p.match,token:this.terminals_[b]||b,line:p.yylineno,loc:m,expected:P})}if(v[0]instanceof Array&&v.length>1)throw new Error("Parse Error: multiple actions possible at state: "+y+", token: "+b);switch(v[0]){case 1:r.push(b),n.push(p.yytext),o.push(p.yylloc),r.push(v[1]),b=null,l=p.yyleng,a=p.yytext,s=p.yylineno,m=p.yylloc;break;case 2:if(w=this.productions_[v[1]][1],x.$=n[n.length-w],x._$={first_line:o[o.length-(w||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(w||1)].first_column,last_column:o[o.length-1].last_column},g&&(x._$.range=[o[o.length-(w||1)].range[0],o[o.length-1].range[1]]),void 0!==(S=this.performAction.apply(x,[a,l,s,d.yy,v[1],n,o].concat(h))))return S;w&&(r=r.slice(0,-1*w*2),n=n.slice(0,-1*w),o=o.slice(0,-1*w)),r.push(this.productions_[v[1]][0]),n.push(x.$),o.push(x._$),E=i[r[r.length-2]][r[r.length-1]],r.push(E);break;case 3:return!0}}return!0}},I={EOF:1,parseError:function(e,t){if(!this.yy.parser)throw new Error(e);this.yy.parser.parseError(e,t)},setInput:function(e,t){return this.yy=t||this.yy||{},this._input=e,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var e=this._input[0];return this.yytext+=e,this.yyleng++,this.offset++,this.match+=e,this.matched+=e,e.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),e},unput:function(e){var t=e.length,r=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-t),this.offset-=t;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),r.length-1&&(this.yylineno-=r.length-1);var o=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:r?(r.length===n.length?this.yylloc.first_column:0)+n[n.length-r.length].length-r[0].length:this.yylloc.first_column-t},this.options.ranges&&(this.yylloc.range=[o[0],o[0]+this.yyleng-t]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},less:function(e){this.unput(this.match.slice(e))},pastInput:function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var e=this.match;return e.length<20&&(e+=this._input.substr(0,20-e.length)),(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var e=this.pastInput(),t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"},test_match:function(e,t){var r,n,o;if(this.options.backtrack_lexer&&(o={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(o.yylloc.range=this.yylloc.range.slice(0))),(n=e[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=n.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length},this.yytext+=e[0],this.match+=e[0],this.matches=e,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(e[0].length),this.matched+=e[0],r=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),r)return r;if(this._backtrack){for(var i in o)this[i]=o[i];return!1}return!1},next:function(){if(this.done)return this.EOF;var e,t,r,n;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var o=this._currentRules(),i=0;i<o.length;i++)if((r=this._input.match(this.rules[o[i]]))&&(!t||r[0].length>t[0].length)){if(t=r,n=i,this.options.backtrack_lexer){if(!1!==(e=this.test_match(r,o[i])))return e;if(this._backtrack){t=!1;continue}return!1}if(!this.options.flex)break}return t?!1!==(e=this.test_match(t,o[n]))&&e:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var e=this.next();return e||this.lex()},begin:function(e){this.conditionStack.push(e)},popState:function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(e){return(e=this.conditionStack.length-1-Math.abs(e||0))>=0?this.conditionStack[e]:"INITIAL"},pushState:function(e){this.begin(e)},stateStackSize:function(){return this.conditionStack.length},options:{},performAction:function(e,t,r,n){function o(e,r){return t.yytext=t.yytext.substring(e,t.yyleng-r+e)}switch(r){case 0:if("\\\\"===t.yytext.slice(-2)?(o(0,1),this.begin("mu")):"\\"===t.yytext.slice(-1)?(o(0,1),this.begin("emu")):this.begin("mu"),t.yytext)return 15;break;case 1:return 15;case 2:return this.popState(),15;case 3:return this.begin("raw"),15;case 4:return this.popState(),"raw"===this.conditionStack[this.conditionStack.length-1]?15:(o(5,9),18);case 5:return 15;case 6:return this.popState(),14;case 7:return 64;case 8:return 67;case 9:return 19;case 10:return this.popState(),this.begin("raw"),23;case 11:return 56;case 12:return 60;case 13:return 29;case 14:return 47;case 15:case 16:return this.popState(),44;case 17:return 34;case 18:return 39;case 19:return 52;case 20:return 48;case 21:this.unput(t.yytext),this.popState(),this.begin("com");break;case 22:return this.popState(),14;case 23:return 48;case 24:return 72;case 25:case 26:return 71;case 27:return 86;case 28:break;case 29:return this.popState(),55;case 30:return this.popState(),33;case 31:return t.yytext=o(1,2).replace(/\\"/g,'"'),79;case 32:return t.yytext=o(1,2).replace(/\\'/g,"'"),79;case 33:return 84;case 34:case 35:return 81;case 36:return 82;case 37:return 83;case 38:return 80;case 39:return 74;case 40:return 76;case 41:return 71;case 42:return t.yytext=t.yytext.replace(/\\([\\\]])/g,"$1"),71;case 43:return"INVALID";case 44:return 5}},rules:[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:\{\{\{\{(?=[^/]))/,/^(?:\{\{\{\{\/[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.])\}\}\}\})/,/^(?:[^\x00]+?(?=(\{\{\{\{)))/,/^(?:[\s\S]*?--(~)?\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{\{\{)/,/^(?:\}\}\}\})/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#>)/,/^(?:\{\{(~)?#\*?)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^\s*(~)?\}\})/,/^(?:\{\{(~)?\s*else\s*(~)?\}\})/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{(~)?!--)/,/^(?:\{\{(~)?![\s\S]*?\}\})/,/^(?:\{\{(~)?\*?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)|])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:undefined(?=([~}\s)])))/,/^(?:null(?=([~}\s)])))/,/^(?:-?[0-9]+(?:\.[0-9]+)?(?=([~}\s)])))/,/^(?:as\s+\|)/,/^(?:\|)/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)|]))))/,/^(?:\[(\\\]|[^\]])*\])/,/^(?:.)/,/^(?:$)/],conditions:{mu:{rules:[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[6],inclusive:!1},raw:{rules:[3,4,5],inclusive:!1},INITIAL:{rules:[0,1,44],inclusive:!0}}};function M(){this.yy={}}return D.lexer=I,M.prototype=D,D.Parser=M,new M}();function yr(){this.padding=0}function vr(e,t){if(t=t.path?t.path.original:t,e.path.original!==t){var r={loc:e.path.loc};throw new sr(e.path.original+" doesn't match "+t,r)}}function Sr(e,t){this.source=e,this.start={line:t.first_line,column:t.first_column},this.end={line:t.last_line,column:t.last_column}}yr.prototype=new lr,yr.prototype.pad=function(e){for(var t="",r=0,n=this.padding;r<n;r++)t+="  ";return t+=e+"\n"},yr.prototype.Program=function(e){var t,r,n="",o=e.body;if(e.blockParams){var i="BLOCK PARAMS: [";for(t=0,r=e.blockParams.length;t<r;t++)i+=" "+e.blockParams[t];i+=" ]",n+=this.pad(i)}for(t=0,r=o.length;t<r;t++)n+=this.accept(o[t]);return this.padding--,n},yr.prototype.MustacheStatement=function(e){return this.pad("{{ "+this.SubExpression(e)+" }}")},yr.prototype.Decorator=function(e){return this.pad("{{ DIRECTIVE "+this.SubExpression(e)+" }}")},yr.prototype.BlockStatement=yr.prototype.DecoratorBlock=function(e){var t="";return t+=this.pad(("DecoratorBlock"===e.type?"DIRECTIVE ":"")+"BLOCK:"),this.padding++,t+=this.pad(this.SubExpression(e)),e.program&&(t+=this.pad("PROGRAM:"),this.padding++,t+=this.accept(e.program),this.padding--),e.inverse&&(e.program&&this.padding++,t+=this.pad("{{^}}"),this.padding++,t+=this.accept(e.inverse),this.padding--,e.program&&this.padding--),this.padding--,t},yr.prototype.PartialStatement=function(e){var t="PARTIAL:"+e.name.original;return e.params[0]&&(t+=" "+this.accept(e.params[0])),e.hash&&(t+=" "+this.accept(e.hash)),this.pad("{{> "+t+" }}")},yr.prototype.PartialBlockStatement=function(e){var t="PARTIAL BLOCK:"+e.name.original;return e.params[0]&&(t+=" "+this.accept(e.params[0])),e.hash&&(t+=" "+this.accept(e.hash)),t+=" "+this.pad("PROGRAM:"),this.padding++,t+=this.accept(e.program),this.padding--,this.pad("{{> "+t+" }}")},yr.prototype.ContentStatement=function(e){return this.pad("CONTENT[ '"+e.value+"' ]")},yr.prototype.CommentStatement=function(e){return this.pad("{{! '"+e.value+"' }}")},yr.prototype.SubExpression=function(e){for(var t,r=e.params,n=[],o=0,i=r.length;o<i;o++)n.push(this.accept(r[o]));return r="["+n.join(", ")+"]",t=e.hash?" "+this.accept(e.hash):"",this.accept(e.path)+" "+r+t},yr.prototype.PathExpression=function(e){var t=e.parts.join("/");return(e.data?"@":"")+"PATH:"+t},yr.prototype.StringLiteral=function(e){return'"'+e.value+'"'},yr.prototype.NumberLiteral=function(e){return"NUMBER{"+e.value+"}"},yr.prototype.BooleanLiteral=function(e){return"BOOLEAN{"+e.value+"}"},yr.prototype.UndefinedLiteral=function(){return"UNDEFINED"},yr.prototype.NullLiteral=function(){return"NULL"},yr.prototype.Hash=function(e){for(var t=e.pairs,r=[],n=0,o=t.length;n<o;n++)r.push(this.accept(t[n]));return"HASH{"+r.join(", ")+"}"},yr.prototype.HashPair=function(e){return e.key+"="+this.accept(e.value)};var kr=Object.freeze({__proto__:null,SourceLocation:Sr,id:function(e){return/^\[.*\]$/.test(e)?e.substring(1,e.length-1):e},stripFlags:function(e,t){return{open:"~"===e.charAt(2),close:"~"===t.charAt(t.length-3)}},stripComment:function(e){return e.replace(/^\{\{~?!-?-?/,"").replace(/-?-?~?\}\}$/,"")},preparePath:function(e,t,r){r=this.locInfo(r);for(var n=e?"@":"",o=[],i=0,a=0,s=t.length;a<s;a++){var l=t[a].part,c=t[a].original!==l;if(n+=(t[a].separator||"")+l,c||".."!==l&&"."!==l&&"this"!==l)o.push(l);else{if(o.length>0)throw new sr("Invalid path: "+n,{loc:r});".."===l&&i++}}return{type:"PathExpression",data:e,depth:i,parts:o,original:n,loc:r}},prepareMustache:function(e,t,r,n,o,i){var a=n.charAt(3)||n.charAt(2),s="{"!==a&&"&"!==a;return{type:/\*/.test(n)?"Decorator":"MustacheStatement",path:e,params:t,hash:r,escaped:s,strip:o,loc:this.locInfo(i)}},prepareRawBlock:function(e,t,r,n){vr(e,r);var o={type:"Program",body:t,strip:{},loc:n=this.locInfo(n)};return{type:"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:o,openStrip:{},inverseStrip:{},closeStrip:{},loc:n}},prepareBlock:function(e,t,r,n,o,i){n&&n.path&&vr(e,n);var a,s,l=/\*/.test(e.open);if(t.blockParams=e.blockParams,r){if(l)throw new sr("Unexpected inverse block on decorator",r);r.chain&&(r.program.body[0].closeStrip=n.strip),s=r.strip,a=r.program}return o&&(o=a,a=t,t=o),{type:l?"DecoratorBlock":"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:t,inverse:a,openStrip:e.strip,inverseStrip:s,closeStrip:n&&n.strip,loc:this.locInfo(i)}},prepareProgram:function(e,t){if(!t&&e.length){var r=e[0].loc,n=e[e.length-1].loc;r&&n&&(t={source:r.source,start:{line:r.start.line,column:r.start.column},end:{line:n.end.line,column:n.end.column}})}return{type:"Program",body:e,strip:{},loc:t}},preparePartialBlock:function(e,t,r,n){return vr(e,r),{type:"PartialBlockStatement",name:e.path,params:e.params,hash:e.hash,program:t,openStrip:e.strip,closeStrip:r&&r.strip,loc:this.locInfo(n)}}}),wr={};for(var Er in kr)Object.prototype.hasOwnProperty.call(kr,Er)&&(wr[Er]=kr[Er]);function Pr(e,t){return"Program"===e.type?e:(br.yy=wr,br.yy.locInfo=function(e){return new Sr(t&&t.srcName,e)},br.parse(e))}var Tr=Object.freeze({__proto__:null,Visitor:lr,WhitespaceControl:pr,parser:br,Exception:sr,print:function(e){return(new yr).accept(e)},PrintVisitor:yr,parse:function(e,t){var r=Pr(e,t);return new pr(t).accept(r)},parseWithoutProcessing:Pr}),xr={Aacute:"\xc1",aacute:"\xe1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223e",acd:"\u223f",acE:"\u223e\u0333",Acirc:"\xc2",acirc:"\xe2",acute:"\xb4",Acy:"\u0410",acy:"\u0430",AElig:"\xc6",aelig:"\xe6",af:"\u2061",Afr:"\ud835\udd04",afr:"\ud835\udd1e",Agrave:"\xc0",agrave:"\xe0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03b1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2a3f",amp:"&",AMP:"&",andand:"\u2a55",And:"\u2a53",and:"\u2227",andd:"\u2a5c",andslope:"\u2a58",andv:"\u2a5a",ang:"\u2220",ange:"\u29a4",angle:"\u2220",angmsdaa:"\u29a8",angmsdab:"\u29a9",angmsdac:"\u29aa",angmsdad:"\u29ab",angmsdae:"\u29ac",angmsdaf:"\u29ad",angmsdag:"\u29ae",angmsdah:"\u29af",angmsd:"\u2221",angrt:"\u221f",angrtvb:"\u22be",angrtvbd:"\u299d",angsph:"\u2222",angst:"\xc5",angzarr:"\u237c",Aogon:"\u0104",aogon:"\u0105",Aopf:"\ud835\udd38",aopf:"\ud835\udd52",apacir:"\u2a6f",ap:"\u2248",apE:"\u2a70",ape:"\u224a",apid:"\u224b",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224a",Aring:"\xc5",aring:"\xe5",Ascr:"\ud835\udc9c",ascr:"\ud835\udcb6",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224d",Atilde:"\xc3",atilde:"\xe3",Auml:"\xc4",auml:"\xe4",awconint:"\u2233",awint:"\u2a11",backcong:"\u224c",backepsilon:"\u03f6",backprime:"\u2035",backsim:"\u223d",backsimeq:"\u22cd",Backslash:"\u2216",Barv:"\u2ae7",barvee:"\u22bd",barwed:"\u2305",Barwed:"\u2306",barwedge:"\u2305",bbrk:"\u23b5",bbrktbrk:"\u23b6",bcong:"\u224c",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201e",becaus:"\u2235",because:"\u2235",Because:"\u2235",bemptyv:"\u29b0",bepsi:"\u03f6",bernou:"\u212c",Bernoullis:"\u212c",Beta:"\u0392",beta:"\u03b2",beth:"\u2136",between:"\u226c",Bfr:"\ud835\udd05",bfr:"\ud835\udd1f",bigcap:"\u22c2",bigcirc:"\u25ef",bigcup:"\u22c3",bigodot:"\u2a00",bigoplus:"\u2a01",bigotimes:"\u2a02",bigsqcup:"\u2a06",bigstar:"\u2605",bigtriangledown:"\u25bd",bigtriangleup:"\u25b3",biguplus:"\u2a04",bigvee:"\u22c1",bigwedge:"\u22c0",bkarow:"\u290d",blacklozenge:"\u29eb",blacksquare:"\u25aa",blacktriangle:"\u25b4",blacktriangledown:"\u25be",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20e5",bnequiv:"\u2261\u20e5",bNot:"\u2aed",bnot:"\u2310",Bopf:"\ud835\udd39",bopf:"\ud835\udd53",bot:"\u22a5",bottom:"\u22a5",bowtie:"\u22c8",boxbox:"\u29c9",boxdl:"\u2510",boxdL:"\u2555",boxDl:"\u2556",boxDL:"\u2557",boxdr:"\u250c",boxdR:"\u2552",boxDr:"\u2553",boxDR:"\u2554",boxh:"\u2500",boxH:"\u2550",boxhd:"\u252c",boxHd:"\u2564",boxhD:"\u2565",boxHD:"\u2566",boxhu:"\u2534",boxHu:"\u2567",boxhU:"\u2568",boxHU:"\u2569",boxminus:"\u229f",boxplus:"\u229e",boxtimes:"\u22a0",boxul:"\u2518",boxuL:"\u255b",boxUl:"\u255c",boxUL:"\u255d",boxur:"\u2514",boxuR:"\u2558",boxUr:"\u2559",boxUR:"\u255a",boxv:"\u2502",boxV:"\u2551",boxvh:"\u253c",boxvH:"\u256a",boxVh:"\u256b",boxVH:"\u256c",boxvl:"\u2524",boxvL:"\u2561",boxVl:"\u2562",boxVL:"\u2563",boxvr:"\u251c",boxvR:"\u255e",boxVr:"\u255f",boxVR:"\u2560",bprime:"\u2035",breve:"\u02d8",Breve:"\u02d8",brvbar:"\xa6",bscr:"\ud835\udcb7",Bscr:"\u212c",bsemi:"\u204f",bsim:"\u223d",bsime:"\u22cd",bsolb:"\u29c5",bsol:"\\",bsolhsub:"\u27c8",bull:"\u2022",bullet:"\u2022",bump:"\u224e",bumpE:"\u2aae",bumpe:"\u224f",Bumpeq:"\u224e",bumpeq:"\u224f",Cacute:"\u0106",cacute:"\u0107",capand:"\u2a44",capbrcup:"\u2a49",capcap:"\u2a4b",cap:"\u2229",Cap:"\u22d2",capcup:"\u2a47",capdot:"\u2a40",CapitalDifferentialD:"\u2145",caps:"\u2229\ufe00",caret:"\u2041",caron:"\u02c7",Cayleys:"\u212d",ccaps:"\u2a4d",Ccaron:"\u010c",ccaron:"\u010d",Ccedil:"\xc7",ccedil:"\xe7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2a4c",ccupssm:"\u2a50",Cdot:"\u010a",cdot:"\u010b",cedil:"\xb8",Cedilla:"\xb8",cemptyv:"\u29b2",cent:"\xa2",centerdot:"\xb7",CenterDot:"\xb7",cfr:"\ud835\udd20",Cfr:"\u212d",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03a7",chi:"\u03c7",circ:"\u02c6",circeq:"\u2257",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",circledast:"\u229b",circledcirc:"\u229a",circleddash:"\u229d",CircleDot:"\u2299",circledR:"\xae",circledS:"\u24c8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cir:"\u25cb",cirE:"\u29c3",cire:"\u2257",cirfnint:"\u2a10",cirmid:"\u2aef",cirscir:"\u29c2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201d",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",colon:":",Colon:"\u2237",Colone:"\u2a74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2a6d",Congruent:"\u2261",conint:"\u222e",Conint:"\u222f",ContourIntegral:"\u222e",copf:"\ud835\udd54",Copf:"\u2102",coprod:"\u2210",Coproduct:"\u2210",copy:"\xa9",COPY:"\xa9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21b5",cross:"\u2717",Cross:"\u2a2f",Cscr:"\ud835\udc9e",cscr:"\ud835\udcb8",csub:"\u2acf",csube:"\u2ad1",csup:"\u2ad0",csupe:"\u2ad2",ctdot:"\u22ef",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22de",cuesc:"\u22df",cularr:"\u21b6",cularrp:"\u293d",cupbrcap:"\u2a48",cupcap:"\u2a46",CupCap:"\u224d",cup:"\u222a",Cup:"\u22d3",cupcup:"\u2a4a",cupdot:"\u228d",cupor:"\u2a45",cups:"\u222a\ufe00",curarr:"\u21b7",curarrm:"\u293c",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",curlyvee:"\u22ce",curlywedge:"\u22cf",curren:"\xa4",curvearrowleft:"\u21b6",curvearrowright:"\u21b7",cuvee:"\u22ce",cuwed:"\u22cf",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232d",dagger:"\u2020",Dagger:"\u2021",daleth:"\u2138",darr:"\u2193",Darr:"\u21a1",dArr:"\u21d3",dash:"\u2010",Dashv:"\u2ae4",dashv:"\u22a3",dbkarow:"\u290f",dblac:"\u02dd",Dcaron:"\u010e",dcaron:"\u010f",Dcy:"\u0414",dcy:"\u0434",ddagger:"\u2021",ddarr:"\u21ca",DD:"\u2145",dd:"\u2146",DDotrahd:"\u2911",ddotseq:"\u2a77",deg:"\xb0",Del:"\u2207",Delta:"\u0394",delta:"\u03b4",demptyv:"\u29b1",dfisht:"\u297f",Dfr:"\ud835\udd07",dfr:"\ud835\udd21",dHar:"\u2965",dharl:"\u21c3",dharr:"\u21c2",DiacriticalAcute:"\xb4",DiacriticalDot:"\u02d9",DiacriticalDoubleAcute:"\u02dd",DiacriticalGrave:"`",DiacriticalTilde:"\u02dc",diam:"\u22c4",diamond:"\u22c4",Diamond:"\u22c4",diamondsuit:"\u2666",diams:"\u2666",die:"\xa8",DifferentialD:"\u2146",digamma:"\u03dd",disin:"\u22f2",div:"\xf7",divide:"\xf7",divideontimes:"\u22c7",divonx:"\u22c7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231e",dlcrop:"\u230d",dollar:"$",Dopf:"\ud835\udd3b",dopf:"\ud835\udd55",Dot:"\xa8",dot:"\u02d9",DotDot:"\u20dc",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22a1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222f",DoubleDot:"\xa8",DoubleDownArrow:"\u21d3",DoubleLeftArrow:"\u21d0",DoubleLeftRightArrow:"\u21d4",DoubleLeftTee:"\u2ae4",DoubleLongLeftArrow:"\u27f8",DoubleLongLeftRightArrow:"\u27fa",DoubleLongRightArrow:"\u27f9",DoubleRightArrow:"\u21d2",DoubleRightTee:"\u22a8",DoubleUpArrow:"\u21d1",DoubleUpDownArrow:"\u21d5",DoubleVerticalBar:"\u2225",DownArrowBar:"\u2913",downarrow:"\u2193",DownArrow:"\u2193",Downarrow:"\u21d3",DownArrowUpArrow:"\u21f5",DownBreve:"\u0311",downdownarrows:"\u21ca",downharpoonleft:"\u21c3",downharpoonright:"\u21c2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295e",DownLeftVectorBar:"\u2956",DownLeftVector:"\u21bd",DownRightTeeVector:"\u295f",DownRightVectorBar:"\u2957",DownRightVector:"\u21c1",DownTeeArrow:"\u21a7",DownTee:"\u22a4",drbkarow:"\u2910",drcorn:"\u231f",drcrop:"\u230c",Dscr:"\ud835\udc9f",dscr:"\ud835\udcb9",DScy:"\u0405",dscy:"\u0455",dsol:"\u29f6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22f1",dtri:"\u25bf",dtrif:"\u25be",duarr:"\u21f5",duhar:"\u296f",dwangle:"\u29a6",DZcy:"\u040f",dzcy:"\u045f",dzigrarr:"\u27ff",Eacute:"\xc9",eacute:"\xe9",easter:"\u2a6e",Ecaron:"\u011a",ecaron:"\u011b",Ecirc:"\xca",ecirc:"\xea",ecir:"\u2256",ecolon:"\u2255",Ecy:"\u042d",ecy:"\u044d",eDDot:"\u2a77",Edot:"\u0116",edot:"\u0117",eDot:"\u2251",ee:"\u2147",efDot:"\u2252",Efr:"\ud835\udd08",efr:"\ud835\udd22",eg:"\u2a9a",Egrave:"\xc8",egrave:"\xe8",egs:"\u2a96",egsdot:"\u2a98",el:"\u2a99",Element:"\u2208",elinters:"\u23e7",ell:"\u2113",els:"\u2a95",elsdot:"\u2a97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25fb",emptyv:"\u2205",EmptyVerySmallSquare:"\u25ab",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",ENG:"\u014a",eng:"\u014b",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\ud835\udd3c",eopf:"\ud835\udd56",epar:"\u22d5",eparsl:"\u29e3",eplus:"\u2a71",epsi:"\u03b5",Epsilon:"\u0395",epsilon:"\u03b5",epsiv:"\u03f5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2a96",eqslantless:"\u2a95",Equal:"\u2a75",equals:"=",EqualTilde:"\u2242",equest:"\u225f",Equilibrium:"\u21cc",equiv:"\u2261",equivDD:"\u2a78",eqvparsl:"\u29e5",erarr:"\u2971",erDot:"\u2253",escr:"\u212f",Escr:"\u2130",esdot:"\u2250",Esim:"\u2a73",esim:"\u2242",Eta:"\u0397",eta:"\u03b7",ETH:"\xd0",eth:"\xf0",Euml:"\xcb",euml:"\xeb",euro:"\u20ac",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",exponentiale:"\u2147",ExponentialE:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\ufb03",fflig:"\ufb00",ffllig:"\ufb04",Ffr:"\ud835\udd09",ffr:"\ud835\udd23",filig:"\ufb01",FilledSmallSquare:"\u25fc",FilledVerySmallSquare:"\u25aa",fjlig:"fj",flat:"\u266d",fllig:"\ufb02",fltns:"\u25b1",fnof:"\u0192",Fopf:"\ud835\udd3d",fopf:"\ud835\udd57",forall:"\u2200",ForAll:"\u2200",fork:"\u22d4",forkv:"\u2ad9",Fouriertrf:"\u2131",fpartint:"\u2a0d",frac12:"\xbd",frac13:"\u2153",frac14:"\xbc",frac15:"\u2155",frac16:"\u2159",frac18:"\u215b",frac23:"\u2154",frac25:"\u2156",frac34:"\xbe",frac35:"\u2157",frac38:"\u215c",frac45:"\u2158",frac56:"\u215a",frac58:"\u215d",frac78:"\u215e",frasl:"\u2044",frown:"\u2322",fscr:"\ud835\udcbb",Fscr:"\u2131",gacute:"\u01f5",Gamma:"\u0393",gamma:"\u03b3",Gammad:"\u03dc",gammad:"\u03dd",gap:"\u2a86",Gbreve:"\u011e",gbreve:"\u011f",Gcedil:"\u0122",Gcirc:"\u011c",gcirc:"\u011d",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",ge:"\u2265",gE:"\u2267",gEl:"\u2a8c",gel:"\u22db",geq:"\u2265",geqq:"\u2267",geqslant:"\u2a7e",gescc:"\u2aa9",ges:"\u2a7e",gesdot:"\u2a80",gesdoto:"\u2a82",gesdotol:"\u2a84",gesl:"\u22db\ufe00",gesles:"\u2a94",Gfr:"\ud835\udd0a",gfr:"\ud835\udd24",gg:"\u226b",Gg:"\u22d9",ggg:"\u22d9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gla:"\u2aa5",gl:"\u2277",glE:"\u2a92",glj:"\u2aa4",gnap:"\u2a8a",gnapprox:"\u2a8a",gne:"\u2a88",gnE:"\u2269",gneq:"\u2a88",gneqq:"\u2269",gnsim:"\u22e7",Gopf:"\ud835\udd3e",gopf:"\ud835\udd58",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22db",GreaterFullEqual:"\u2267",GreaterGreater:"\u2aa2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2a7e",GreaterTilde:"\u2273",Gscr:"\ud835\udca2",gscr:"\u210a",gsim:"\u2273",gsime:"\u2a8e",gsiml:"\u2a90",gtcc:"\u2aa7",gtcir:"\u2a7a",gt:">",GT:">",Gt:"\u226b",gtdot:"\u22d7",gtlPar:"\u2995",gtquest:"\u2a7c",gtrapprox:"\u2a86",gtrarr:"\u2978",gtrdot:"\u22d7",gtreqless:"\u22db",gtreqqless:"\u2a8c",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\ufe00",gvnE:"\u2269\ufe00",Hacek:"\u02c7",hairsp:"\u200a",half:"\xbd",hamilt:"\u210b",HARDcy:"\u042a",hardcy:"\u044a",harrcir:"\u2948",harr:"\u2194",hArr:"\u21d4",harrw:"\u21ad",Hat:"^",hbar:"\u210f",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22b9",hfr:"\ud835\udd25",Hfr:"\u210c",HilbertSpace:"\u210b",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21ff",homtht:"\u223b",hookleftarrow:"\u21a9",hookrightarrow:"\u21aa",hopf:"\ud835\udd59",Hopf:"\u210d",horbar:"\u2015",HorizontalLine:"\u2500",hscr:"\ud835\udcbd",Hscr:"\u210b",hslash:"\u210f",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224e",HumpEqual:"\u224f",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xcd",iacute:"\xed",ic:"\u2063",Icirc:"\xce",icirc:"\xee",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xa1",iff:"\u21d4",ifr:"\ud835\udd26",Ifr:"\u2111",Igrave:"\xcc",igrave:"\xec",ii:"\u2148",iiiint:"\u2a0c",iiint:"\u222d",iinfin:"\u29dc",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Imacr:"\u012a",imacr:"\u012b",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",Im:"\u2111",imof:"\u22b7",imped:"\u01b5",Implies:"\u21d2",incare:"\u2105",in:"\u2208",infin:"\u221e",infintie:"\u29dd",inodot:"\u0131",intcal:"\u22ba",int:"\u222b",Int:"\u222c",integers:"\u2124",Integral:"\u222b",intercal:"\u22ba",Intersection:"\u22c2",intlarhk:"\u2a17",intprod:"\u2a3c",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012e",iogon:"\u012f",Iopf:"\ud835\udd40",iopf:"\ud835\udd5a",Iota:"\u0399",iota:"\u03b9",iprod:"\u2a3c",iquest:"\xbf",iscr:"\ud835\udcbe",Iscr:"\u2110",isin:"\u2208",isindot:"\u22f5",isinE:"\u22f9",isins:"\u22f4",isinsv:"\u22f3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xcf",iuml:"\xef",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\ud835\udd0d",jfr:"\ud835\udd27",jmath:"\u0237",Jopf:"\ud835\udd41",jopf:"\ud835\udd5b",Jscr:"\ud835\udca5",jscr:"\ud835\udcbf",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039a",kappa:"\u03ba",kappav:"\u03f0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041a",kcy:"\u043a",Kfr:"\ud835\udd0e",kfr:"\ud835\udd28",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040c",kjcy:"\u045c",Kopf:"\ud835\udd42",kopf:"\ud835\udd5c",Kscr:"\ud835\udca6",kscr:"\ud835\udcc0",lAarr:"\u21da",Lacute:"\u0139",lacute:"\u013a",laemptyv:"\u29b4",lagran:"\u2112",Lambda:"\u039b",lambda:"\u03bb",lang:"\u27e8",Lang:"\u27ea",langd:"\u2991",langle:"\u27e8",lap:"\u2a85",Laplacetrf:"\u2112",laquo:"\xab",larrb:"\u21e4",larrbfs:"\u291f",larr:"\u2190",Larr:"\u219e",lArr:"\u21d0",larrfs:"\u291d",larrhk:"\u21a9",larrlp:"\u21ab",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21a2",latail:"\u2919",lAtail:"\u291b",lat:"\u2aab",late:"\u2aad",lates:"\u2aad\ufe00",lbarr:"\u290c",lBarr:"\u290e",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298b",lbrksld:"\u298f",lbrkslu:"\u298d",Lcaron:"\u013d",lcaron:"\u013e",Lcedil:"\u013b",lcedil:"\u013c",lceil:"\u2308",lcub:"{",Lcy:"\u041b",lcy:"\u043b",ldca:"\u2936",ldquo:"\u201c",ldquor:"\u201e",ldrdhar:"\u2967",ldrushar:"\u294b",ldsh:"\u21b2",le:"\u2264",lE:"\u2266",LeftAngleBracket:"\u27e8",LeftArrowBar:"\u21e4",leftarrow:"\u2190",LeftArrow:"\u2190",Leftarrow:"\u21d0",LeftArrowRightArrow:"\u21c6",leftarrowtail:"\u21a2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27e6",LeftDownTeeVector:"\u2961",LeftDownVectorBar:"\u2959",LeftDownVector:"\u21c3",LeftFloor:"\u230a",leftharpoondown:"\u21bd",leftharpoonup:"\u21bc",leftleftarrows:"\u21c7",leftrightarrow:"\u2194",LeftRightArrow:"\u2194",Leftrightarrow:"\u21d4",leftrightarrows:"\u21c6",leftrightharpoons:"\u21cb",leftrightsquigarrow:"\u21ad",LeftRightVector:"\u294e",LeftTeeArrow:"\u21a4",LeftTee:"\u22a3",LeftTeeVector:"\u295a",leftthreetimes:"\u22cb",LeftTriangleBar:"\u29cf",LeftTriangle:"\u22b2",LeftTriangleEqual:"\u22b4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVectorBar:"\u2958",LeftUpVector:"\u21bf",LeftVectorBar:"\u2952",LeftVector:"\u21bc",lEg:"\u2a8b",leg:"\u22da",leq:"\u2264",leqq:"\u2266",leqslant:"\u2a7d",lescc:"\u2aa8",les:"\u2a7d",lesdot:"\u2a7f",lesdoto:"\u2a81",lesdotor:"\u2a83",lesg:"\u22da\ufe00",lesges:"\u2a93",lessapprox:"\u2a85",lessdot:"\u22d6",lesseqgtr:"\u22da",lesseqqgtr:"\u2a8b",LessEqualGreater:"\u22da",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2aa1",lesssim:"\u2272",LessSlantEqual:"\u2a7d",LessTilde:"\u2272",lfisht:"\u297c",lfloor:"\u230a",Lfr:"\ud835\udd0f",lfr:"\ud835\udd29",lg:"\u2276",lgE:"\u2a91",lHar:"\u2962",lhard:"\u21bd",lharu:"\u21bc",lharul:"\u296a",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",llarr:"\u21c7",ll:"\u226a",Ll:"\u22d8",llcorner:"\u231e",Lleftarrow:"\u21da",llhard:"\u296b",lltri:"\u25fa",Lmidot:"\u013f",lmidot:"\u0140",lmoustache:"\u23b0",lmoust:"\u23b0",lnap:"\u2a89",lnapprox:"\u2a89",lne:"\u2a87",lnE:"\u2268",lneq:"\u2a87",lneqq:"\u2268",lnsim:"\u22e6",loang:"\u27ec",loarr:"\u21fd",lobrk:"\u27e6",longleftarrow:"\u27f5",LongLeftArrow:"\u27f5",Longleftarrow:"\u27f8",longleftrightarrow:"\u27f7",LongLeftRightArrow:"\u27f7",Longleftrightarrow:"\u27fa",longmapsto:"\u27fc",longrightarrow:"\u27f6",LongRightArrow:"\u27f6",Longrightarrow:"\u27f9",looparrowleft:"\u21ab",looparrowright:"\u21ac",lopar:"\u2985",Lopf:"\ud835\udd43",lopf:"\ud835\udd5d",loplus:"\u2a2d",lotimes:"\u2a34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25ca",lozenge:"\u25ca",lozf:"\u29eb",lpar:"(",lparlt:"\u2993",lrarr:"\u21c6",lrcorner:"\u231f",lrhar:"\u21cb",lrhard:"\u296d",lrm:"\u200e",lrtri:"\u22bf",lsaquo:"\u2039",lscr:"\ud835\udcc1",Lscr:"\u2112",lsh:"\u21b0",Lsh:"\u21b0",lsim:"\u2272",lsime:"\u2a8d",lsimg:"\u2a8f",lsqb:"[",lsquo:"\u2018",lsquor:"\u201a",Lstrok:"\u0141",lstrok:"\u0142",ltcc:"\u2aa6",ltcir:"\u2a79",lt:"<",LT:"<",Lt:"\u226a",ltdot:"\u22d6",lthree:"\u22cb",ltimes:"\u22c9",ltlarr:"\u2976",ltquest:"\u2a7b",ltri:"\u25c3",ltrie:"\u22b4",ltrif:"\u25c2",ltrPar:"\u2996",lurdshar:"\u294a",luruhar:"\u2966",lvertneqq:"\u2268\ufe00",lvnE:"\u2268\ufe00",macr:"\xaf",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21a6",mapsto:"\u21a6",mapstodown:"\u21a7",mapstoleft:"\u21a4",mapstoup:"\u21a5",marker:"\u25ae",mcomma:"\u2a29",Mcy:"\u041c",mcy:"\u043c",mdash:"\u2014",mDDot:"\u223a",measuredangle:"\u2221",MediumSpace:"\u205f",Mellintrf:"\u2133",Mfr:"\ud835\udd10",mfr:"\ud835\udd2a",mho:"\u2127",micro:"\xb5",midast:"*",midcir:"\u2af0",mid:"\u2223",middot:"\xb7",minusb:"\u229f",minus:"\u2212",minusd:"\u2238",minusdu:"\u2a2a",MinusPlus:"\u2213",mlcp:"\u2adb",mldr:"\u2026",mnplus:"\u2213",models:"\u22a7",Mopf:"\ud835\udd44",mopf:"\ud835\udd5e",mp:"\u2213",mscr:"\ud835\udcc2",Mscr:"\u2133",mstpos:"\u223e",Mu:"\u039c",mu:"\u03bc",multimap:"\u22b8",mumap:"\u22b8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20d2",nap:"\u2249",napE:"\u2a70\u0338",napid:"\u224b\u0338",napos:"\u0149",napprox:"\u2249",natural:"\u266e",naturals:"\u2115",natur:"\u266e",nbsp:"\xa0",nbump:"\u224e\u0338",nbumpe:"\u224f\u0338",ncap:"\u2a43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2a6d\u0338",ncup:"\u2a42",Ncy:"\u041d",ncy:"\u043d",ndash:"\u2013",nearhk:"\u2924",nearr:"\u2197",neArr:"\u21d7",nearrow:"\u2197",ne:"\u2260",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200b",NegativeThickSpace:"\u200b",NegativeThinSpace:"\u200b",NegativeVeryThinSpace:"\u200b",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226b",NestedLessLess:"\u226a",NewLine:"\n",nexist:"\u2204",nexists:"\u2204",Nfr:"\ud835\udd11",nfr:"\ud835\udd2b",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2a7e\u0338",nges:"\u2a7e\u0338",nGg:"\u22d9\u0338",ngsim:"\u2275",nGt:"\u226b\u20d2",ngt:"\u226f",ngtr:"\u226f",nGtv:"\u226b\u0338",nharr:"\u21ae",nhArr:"\u21ce",nhpar:"\u2af2",ni:"\u220b",nis:"\u22fc",nisd:"\u22fa",niv:"\u220b",NJcy:"\u040a",njcy:"\u045a",nlarr:"\u219a",nlArr:"\u21cd",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nleftarrow:"\u219a",nLeftarrow:"\u21cd",nleftrightarrow:"\u21ae",nLeftrightarrow:"\u21ce",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2a7d\u0338",nles:"\u2a7d\u0338",nless:"\u226e",nLl:"\u22d8\u0338",nlsim:"\u2274",nLt:"\u226a\u20d2",nlt:"\u226e",nltri:"\u22ea",nltrie:"\u22ec",nLtv:"\u226a\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xa0",nopf:"\ud835\udd5f",Nopf:"\u2115",Not:"\u2aec",not:"\xac",NotCongruent:"\u2262",NotCupCap:"\u226d",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226f",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226b\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2a7e\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224e\u0338",NotHumpEqual:"\u224f\u0338",notin:"\u2209",notindot:"\u22f5\u0338",notinE:"\u22f9\u0338",notinva:"\u2209",notinvb:"\u22f7",notinvc:"\u22f6",NotLeftTriangleBar:"\u29cf\u0338",NotLeftTriangle:"\u22ea",NotLeftTriangleEqual:"\u22ec",NotLess:"\u226e",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226a\u0338",NotLessSlantEqual:"\u2a7d\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2aa2\u0338",NotNestedLessLess:"\u2aa1\u0338",notni:"\u220c",notniva:"\u220c",notnivb:"\u22fe",notnivc:"\u22fd",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2aaf\u0338",NotPrecedesSlantEqual:"\u22e0",NotReverseElement:"\u220c",NotRightTriangleBar:"\u29d0\u0338",NotRightTriangle:"\u22eb",NotRightTriangleEqual:"\u22ed",NotSquareSubset:"\u228f\u0338",NotSquareSubsetEqual:"\u22e2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22e3",NotSubset:"\u2282\u20d2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2ab0\u0338",NotSucceedsSlantEqual:"\u22e1",NotSucceedsTilde:"\u227f\u0338",NotSuperset:"\u2283\u20d2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",nparallel:"\u2226",npar:"\u2226",nparsl:"\u2afd\u20e5",npart:"\u2202\u0338",npolint:"\u2a14",npr:"\u2280",nprcue:"\u22e0",nprec:"\u2280",npreceq:"\u2aaf\u0338",npre:"\u2aaf\u0338",nrarrc:"\u2933\u0338",nrarr:"\u219b",nrArr:"\u21cf",nrarrw:"\u219d\u0338",nrightarrow:"\u219b",nRightarrow:"\u21cf",nrtri:"\u22eb",nrtrie:"\u22ed",nsc:"\u2281",nsccue:"\u22e1",nsce:"\u2ab0\u0338",Nscr:"\ud835\udca9",nscr:"\ud835\udcc3",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22e2",nsqsupe:"\u22e3",nsub:"\u2284",nsubE:"\u2ac5\u0338",nsube:"\u2288",nsubset:"\u2282\u20d2",nsubseteq:"\u2288",nsubseteqq:"\u2ac5\u0338",nsucc:"\u2281",nsucceq:"\u2ab0\u0338",nsup:"\u2285",nsupE:"\u2ac6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20d2",nsupseteq:"\u2289",nsupseteqq:"\u2ac6\u0338",ntgl:"\u2279",Ntilde:"\xd1",ntilde:"\xf1",ntlg:"\u2278",ntriangleleft:"\u22ea",ntrianglelefteq:"\u22ec",ntriangleright:"\u22eb",ntrianglerighteq:"\u22ed",Nu:"\u039d",nu:"\u03bd",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224d\u20d2",nvdash:"\u22ac",nvDash:"\u22ad",nVdash:"\u22ae",nVDash:"\u22af",nvge:"\u2265\u20d2",nvgt:">\u20d2",nvHarr:"\u2904",nvinfin:"\u29de",nvlArr:"\u2902",nvle:"\u2264\u20d2",nvlt:"<\u20d2",nvltrie:"\u22b4\u20d2",nvrArr:"\u2903",nvrtrie:"\u22b5\u20d2",nvsim:"\u223c\u20d2",nwarhk:"\u2923",nwarr:"\u2196",nwArr:"\u21d6",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xd3",oacute:"\xf3",oast:"\u229b",Ocirc:"\xd4",ocirc:"\xf4",ocir:"\u229a",Ocy:"\u041e",ocy:"\u043e",odash:"\u229d",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2a38",odot:"\u2299",odsold:"\u29bc",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29bf",Ofr:"\ud835\udd12",ofr:"\ud835\udd2c",ogon:"\u02db",Ograve:"\xd2",ograve:"\xf2",ogt:"\u29c1",ohbar:"\u29b5",ohm:"\u03a9",oint:"\u222e",olarr:"\u21ba",olcir:"\u29be",olcross:"\u29bb",oline:"\u203e",olt:"\u29c0",Omacr:"\u014c",omacr:"\u014d",Omega:"\u03a9",omega:"\u03c9",Omicron:"\u039f",omicron:"\u03bf",omid:"\u29b6",ominus:"\u2296",Oopf:"\ud835\udd46",oopf:"\ud835\udd60",opar:"\u29b7",OpenCurlyDoubleQuote:"\u201c",OpenCurlyQuote:"\u2018",operp:"\u29b9",oplus:"\u2295",orarr:"\u21bb",Or:"\u2a54",or:"\u2228",ord:"\u2a5d",order:"\u2134",orderof:"\u2134",ordf:"\xaa",ordm:"\xba",origof:"\u22b6",oror:"\u2a56",orslope:"\u2a57",orv:"\u2a5b",oS:"\u24c8",Oscr:"\ud835\udcaa",oscr:"\u2134",Oslash:"\xd8",oslash:"\xf8",osol:"\u2298",Otilde:"\xd5",otilde:"\xf5",otimesas:"\u2a36",Otimes:"\u2a37",otimes:"\u2297",Ouml:"\xd6",ouml:"\xf6",ovbar:"\u233d",OverBar:"\u203e",OverBrace:"\u23de",OverBracket:"\u23b4",OverParenthesis:"\u23dc",para:"\xb6",parallel:"\u2225",par:"\u2225",parsim:"\u2af3",parsl:"\u2afd",part:"\u2202",PartialD:"\u2202",Pcy:"\u041f",pcy:"\u043f",percnt:"%",period:".",permil:"\u2030",perp:"\u22a5",pertenk:"\u2031",Pfr:"\ud835\udd13",pfr:"\ud835\udd2d",Phi:"\u03a6",phi:"\u03c6",phiv:"\u03d5",phmmat:"\u2133",phone:"\u260e",Pi:"\u03a0",pi:"\u03c0",pitchfork:"\u22d4",piv:"\u03d6",planck:"\u210f",planckh:"\u210e",plankv:"\u210f",plusacir:"\u2a23",plusb:"\u229e",pluscir:"\u2a22",plus:"+",plusdo:"\u2214",plusdu:"\u2a25",pluse:"\u2a72",PlusMinus:"\xb1",plusmn:"\xb1",plussim:"\u2a26",plustwo:"\u2a27",pm:"\xb1",Poincareplane:"\u210c",pointint:"\u2a15",popf:"\ud835\udd61",Popf:"\u2119",pound:"\xa3",prap:"\u2ab7",Pr:"\u2abb",pr:"\u227a",prcue:"\u227c",precapprox:"\u2ab7",prec:"\u227a",preccurlyeq:"\u227c",Precedes:"\u227a",PrecedesEqual:"\u2aaf",PrecedesSlantEqual:"\u227c",PrecedesTilde:"\u227e",preceq:"\u2aaf",precnapprox:"\u2ab9",precneqq:"\u2ab5",precnsim:"\u22e8",pre:"\u2aaf",prE:"\u2ab3",precsim:"\u227e",prime:"\u2032",Prime:"\u2033",primes:"\u2119",prnap:"\u2ab9",prnE:"\u2ab5",prnsim:"\u22e8",prod:"\u220f",Product:"\u220f",profalar:"\u232e",profline:"\u2312",profsurf:"\u2313",prop:"\u221d",Proportional:"\u221d",Proportion:"\u2237",propto:"\u221d",prsim:"\u227e",prurel:"\u22b0",Pscr:"\ud835\udcab",pscr:"\ud835\udcc5",Psi:"\u03a8",psi:"\u03c8",puncsp:"\u2008",Qfr:"\ud835\udd14",qfr:"\ud835\udd2e",qint:"\u2a0c",qopf:"\ud835\udd62",Qopf:"\u211a",qprime:"\u2057",Qscr:"\ud835\udcac",qscr:"\ud835\udcc6",quaternions:"\u210d",quatint:"\u2a16",quest:"?",questeq:"\u225f",quot:'"',QUOT:'"',rAarr:"\u21db",race:"\u223d\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221a",raemptyv:"\u29b3",rang:"\u27e9",Rang:"\u27eb",rangd:"\u2992",range:"\u29a5",rangle:"\u27e9",raquo:"\xbb",rarrap:"\u2975",rarrb:"\u21e5",rarrbfs:"\u2920",rarrc:"\u2933",rarr:"\u2192",Rarr:"\u21a0",rArr:"\u21d2",rarrfs:"\u291e",rarrhk:"\u21aa",rarrlp:"\u21ac",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21a3",rarrw:"\u219d",ratail:"\u291a",rAtail:"\u291c",ratio:"\u2236",rationals:"\u211a",rbarr:"\u290d",rBarr:"\u290f",RBarr:"\u2910",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298c",rbrksld:"\u298e",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201d",rdquor:"\u201d",rdsh:"\u21b3",real:"\u211c",realine:"\u211b",realpart:"\u211c",reals:"\u211d",Re:"\u211c",rect:"\u25ad",reg:"\xae",REG:"\xae",ReverseElement:"\u220b",ReverseEquilibrium:"\u21cb",ReverseUpEquilibrium:"\u296f",rfisht:"\u297d",rfloor:"\u230b",rfr:"\ud835\udd2f",Rfr:"\u211c",rHar:"\u2964",rhard:"\u21c1",rharu:"\u21c0",rharul:"\u296c",Rho:"\u03a1",rho:"\u03c1",rhov:"\u03f1",RightAngleBracket:"\u27e9",RightArrowBar:"\u21e5",rightarrow:"\u2192",RightArrow:"\u2192",Rightarrow:"\u21d2",RightArrowLeftArrow:"\u21c4",rightarrowtail:"\u21a3",RightCeiling:"\u2309",RightDoubleBracket:"\u27e7",RightDownTeeVector:"\u295d",RightDownVectorBar:"\u2955",RightDownVector:"\u21c2",RightFloor:"\u230b",rightharpoondown:"\u21c1",rightharpoonup:"\u21c0",rightleftarrows:"\u21c4",rightleftharpoons:"\u21cc",rightrightarrows:"\u21c9",rightsquigarrow:"\u219d",RightTeeArrow:"\u21a6",RightTee:"\u22a2",RightTeeVector:"\u295b",rightthreetimes:"\u22cc",RightTriangleBar:"\u29d0",RightTriangle:"\u22b3",RightTriangleEqual:"\u22b5",RightUpDownVector:"\u294f",RightUpTeeVector:"\u295c",RightUpVectorBar:"\u2954",RightUpVector:"\u21be",RightVectorBar:"\u2953",RightVector:"\u21c0",ring:"\u02da",risingdotseq:"\u2253",rlarr:"\u21c4",rlhar:"\u21cc",rlm:"\u200f",rmoustache:"\u23b1",rmoust:"\u23b1",rnmid:"\u2aee",roang:"\u27ed",roarr:"\u21fe",robrk:"\u27e7",ropar:"\u2986",ropf:"\ud835\udd63",Ropf:"\u211d",roplus:"\u2a2e",rotimes:"\u2a35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2a12",rrarr:"\u21c9",Rrightarrow:"\u21db",rsaquo:"\u203a",rscr:"\ud835\udcc7",Rscr:"\u211b",rsh:"\u21b1",Rsh:"\u21b1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22cc",rtimes:"\u22ca",rtri:"\u25b9",rtrie:"\u22b5",rtrif:"\u25b8",rtriltri:"\u29ce",RuleDelayed:"\u29f4",ruluhar:"\u2968",rx:"\u211e",Sacute:"\u015a",sacute:"\u015b",sbquo:"\u201a",scap:"\u2ab8",Scaron:"\u0160",scaron:"\u0161",Sc:"\u2abc",sc:"\u227b",sccue:"\u227d",sce:"\u2ab0",scE:"\u2ab4",Scedil:"\u015e",scedil:"\u015f",Scirc:"\u015c",scirc:"\u015d",scnap:"\u2aba",scnE:"\u2ab6",scnsim:"\u22e9",scpolint:"\u2a13",scsim:"\u227f",Scy:"\u0421",scy:"\u0441",sdotb:"\u22a1",sdot:"\u22c5",sdote:"\u2a66",searhk:"\u2925",searr:"\u2198",seArr:"\u21d8",searrow:"\u2198",sect:"\xa7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\ud835\udd16",sfr:"\ud835\udd30",sfrown:"\u2322",sharp:"\u266f",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xad",Sigma:"\u03a3",sigma:"\u03c3",sigmaf:"\u03c2",sigmav:"\u03c2",sim:"\u223c",simdot:"\u2a6a",sime:"\u2243",simeq:"\u2243",simg:"\u2a9e",simgE:"\u2aa0",siml:"\u2a9d",simlE:"\u2a9f",simne:"\u2246",simplus:"\u2a24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2a33",smeparsl:"\u29e4",smid:"\u2223",smile:"\u2323",smt:"\u2aaa",smte:"\u2aac",smtes:"\u2aac\ufe00",SOFTcy:"\u042c",softcy:"\u044c",solbar:"\u233f",solb:"\u29c4",sol:"/",Sopf:"\ud835\udd4a",sopf:"\ud835\udd64",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\ufe00",sqcup:"\u2294",sqcups:"\u2294\ufe00",Sqrt:"\u221a",sqsub:"\u228f",sqsube:"\u2291",sqsubset:"\u228f",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",square:"\u25a1",Square:"\u25a1",SquareIntersection:"\u2293",SquareSubset:"\u228f",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25aa",squ:"\u25a1",squf:"\u25aa",srarr:"\u2192",Sscr:"\ud835\udcae",sscr:"\ud835\udcc8",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22c6",Star:"\u22c6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03f5",straightphi:"\u03d5",strns:"\xaf",sub:"\u2282",Sub:"\u22d0",subdot:"\u2abd",subE:"\u2ac5",sube:"\u2286",subedot:"\u2ac3",submult:"\u2ac1",subnE:"\u2acb",subne:"\u228a",subplus:"\u2abf",subrarr:"\u2979",subset:"\u2282",Subset:"\u22d0",subseteq:"\u2286",subseteqq:"\u2ac5",SubsetEqual:"\u2286",subsetneq:"\u228a",subsetneqq:"\u2acb",subsim:"\u2ac7",subsub:"\u2ad5",subsup:"\u2ad3",succapprox:"\u2ab8",succ:"\u227b",succcurlyeq:"\u227d",Succeeds:"\u227b",SucceedsEqual:"\u2ab0",SucceedsSlantEqual:"\u227d",SucceedsTilde:"\u227f",succeq:"\u2ab0",succnapprox:"\u2aba",succneqq:"\u2ab6",succnsim:"\u22e9",succsim:"\u227f",SuchThat:"\u220b",sum:"\u2211",Sum:"\u2211",sung:"\u266a",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"\u2283",Sup:"\u22d1",supdot:"\u2abe",supdsub:"\u2ad8",supE:"\u2ac6",supe:"\u2287",supedot:"\u2ac4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27c9",suphsub:"\u2ad7",suplarr:"\u297b",supmult:"\u2ac2",supnE:"\u2acc",supne:"\u228b",supplus:"\u2ac0",supset:"\u2283",Supset:"\u22d1",supseteq:"\u2287",supseteqq:"\u2ac6",supsetneq:"\u228b",supsetneqq:"\u2acc",supsim:"\u2ac8",supsub:"\u2ad4",supsup:"\u2ad6",swarhk:"\u2926",swarr:"\u2199",swArr:"\u21d9",swarrow:"\u2199",swnwar:"\u292a",szlig:"\xdf",Tab:"\t",target:"\u2316",Tau:"\u03a4",tau:"\u03c4",tbrk:"\u23b4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20db",telrec:"\u2315",Tfr:"\ud835\udd17",tfr:"\ud835\udd31",there4:"\u2234",therefore:"\u2234",Therefore:"\u2234",Theta:"\u0398",theta:"\u03b8",thetasym:"\u03d1",thetav:"\u03d1",thickapprox:"\u2248",thicksim:"\u223c",ThickSpace:"\u205f\u200a",ThinSpace:"\u2009",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223c",THORN:"\xde",thorn:"\xfe",tilde:"\u02dc",Tilde:"\u223c",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",timesbar:"\u2a31",timesb:"\u22a0",times:"\xd7",timesd:"\u2a30",tint:"\u222d",toea:"\u2928",topbot:"\u2336",topcir:"\u2af1",top:"\u22a4",Topf:"\ud835\udd4b",topf:"\ud835\udd65",topfork:"\u2ada",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",TRADE:"\u2122",triangle:"\u25b5",triangledown:"\u25bf",triangleleft:"\u25c3",trianglelefteq:"\u22b4",triangleq:"\u225c",triangleright:"\u25b9",trianglerighteq:"\u22b5",tridot:"\u25ec",trie:"\u225c",triminus:"\u2a3a",TripleDot:"\u20db",triplus:"\u2a39",trisb:"\u29cd",tritime:"\u2a3b",trpezium:"\u23e2",Tscr:"\ud835\udcaf",tscr:"\ud835\udcc9",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040b",tshcy:"\u045b",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226c",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",Uacute:"\xda",uacute:"\xfa",uarr:"\u2191",Uarr:"\u219f",uArr:"\u21d1",Uarrocir:"\u2949",Ubrcy:"\u040e",ubrcy:"\u045e",Ubreve:"\u016c",ubreve:"\u016d",Ucirc:"\xdb",ucirc:"\xfb",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21c5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296e",ufisht:"\u297e",Ufr:"\ud835\udd18",ufr:"\ud835\udd32",Ugrave:"\xd9",ugrave:"\xf9",uHar:"\u2963",uharl:"\u21bf",uharr:"\u21be",uhblk:"\u2580",ulcorn:"\u231c",ulcorner:"\u231c",ulcrop:"\u230f",ultri:"\u25f8",Umacr:"\u016a",umacr:"\u016b",uml:"\xa8",UnderBar:"_",UnderBrace:"\u23df",UnderBracket:"\u23b5",UnderParenthesis:"\u23dd",Union:"\u22c3",UnionPlus:"\u228e",Uogon:"\u0172",uogon:"\u0173",Uopf:"\ud835\udd4c",uopf:"\ud835\udd66",UpArrowBar:"\u2912",uparrow:"\u2191",UpArrow:"\u2191",Uparrow:"\u21d1",UpArrowDownArrow:"\u21c5",updownarrow:"\u2195",UpDownArrow:"\u2195",Updownarrow:"\u21d5",UpEquilibrium:"\u296e",upharpoonleft:"\u21bf",upharpoonright:"\u21be",uplus:"\u228e",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",upsi:"\u03c5",Upsi:"\u03d2",upsih:"\u03d2",Upsilon:"\u03a5",upsilon:"\u03c5",UpTeeArrow:"\u21a5",UpTee:"\u22a5",upuparrows:"\u21c8",urcorn:"\u231d",urcorner:"\u231d",urcrop:"\u230e",Uring:"\u016e",uring:"\u016f",urtri:"\u25f9",Uscr:"\ud835\udcb0",uscr:"\ud835\udcca",utdot:"\u22f0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25b5",utrif:"\u25b4",uuarr:"\u21c8",Uuml:"\xdc",uuml:"\xfc",uwangle:"\u29a7",vangrt:"\u299c",varepsilon:"\u03f5",varkappa:"\u03f0",varnothing:"\u2205",varphi:"\u03d5",varpi:"\u03d6",varpropto:"\u221d",varr:"\u2195",vArr:"\u21d5",varrho:"\u03f1",varsigma:"\u03c2",varsubsetneq:"\u228a\ufe00",varsubsetneqq:"\u2acb\ufe00",varsupsetneq:"\u228b\ufe00",varsupsetneqq:"\u2acc\ufe00",vartheta:"\u03d1",vartriangleleft:"\u22b2",vartriangleright:"\u22b3",vBar:"\u2ae8",Vbar:"\u2aeb",vBarv:"\u2ae9",Vcy:"\u0412",vcy:"\u0432",vdash:"\u22a2",vDash:"\u22a8",Vdash:"\u22a9",VDash:"\u22ab",Vdashl:"\u2ae6",veebar:"\u22bb",vee:"\u2228",Vee:"\u22c1",veeeq:"\u225a",vellip:"\u22ee",verbar:"|",Verbar:"\u2016",vert:"|",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200a",Vfr:"\ud835\udd19",vfr:"\ud835\udd33",vltri:"\u22b2",vnsub:"\u2282\u20d2",vnsup:"\u2283\u20d2",Vopf:"\ud835\udd4d",vopf:"\ud835\udd67",vprop:"\u221d",vrtri:"\u22b3",Vscr:"\ud835\udcb1",vscr:"\ud835\udccb",vsubnE:"\u2acb\ufe00",vsubne:"\u228a\ufe00",vsupnE:"\u2acc\ufe00",vsupne:"\u228b\ufe00",Vvdash:"\u22aa",vzigzag:"\u299a",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2a5f",wedge:"\u2227",Wedge:"\u22c0",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\ud835\udd1a",wfr:"\ud835\udd34",Wopf:"\ud835\udd4e",wopf:"\ud835\udd68",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\ud835\udcb2",wscr:"\ud835\udccc",xcap:"\u22c2",xcirc:"\u25ef",xcup:"\u22c3",xdtri:"\u25bd",Xfr:"\ud835\udd1b",xfr:"\ud835\udd35",xharr:"\u27f7",xhArr:"\u27fa",Xi:"\u039e",xi:"\u03be",xlarr:"\u27f5",xlArr:"\u27f8",xmap:"\u27fc",xnis:"\u22fb",xodot:"\u2a00",Xopf:"\ud835\udd4f",xopf:"\ud835\udd69",xoplus:"\u2a01",xotime:"\u2a02",xrarr:"\u27f6",xrArr:"\u27f9",Xscr:"\ud835\udcb3",xscr:"\ud835\udccd",xsqcup:"\u2a06",xuplus:"\u2a04",xutri:"\u25b3",xvee:"\u22c1",xwedge:"\u22c0",Yacute:"\xdd",yacute:"\xfd",YAcy:"\u042f",yacy:"\u044f",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042b",ycy:"\u044b",yen:"\xa5",Yfr:"\ud835\udd1c",yfr:"\ud835\udd36",YIcy:"\u0407",yicy:"\u0457",Yopf:"\ud835\udd50",yopf:"\ud835\udd6a",Yscr:"\ud835\udcb4",yscr:"\ud835\udcce",YUcy:"\u042e",yucy:"\u044e",yuml:"\xff",Yuml:"\u0178",Zacute:"\u0179",zacute:"\u017a",Zcaron:"\u017d",zcaron:"\u017e",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017b",zdot:"\u017c",zeetrf:"\u2128",ZeroWidthSpace:"\u200b",Zeta:"\u0396",zeta:"\u03b6",zfr:"\ud835\udd37",Zfr:"\u2128",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21dd",zopf:"\ud835\udd6b",Zopf:"\u2124",Zscr:"\ud835\udcb5",zscr:"\ud835\udccf",zwj:"\u200d",zwnj:"\u200c"},Nr=/^#[xX]([A-Fa-f0-9]+)$/,Or=/^#([0-9]+)$/,Ar=/^([A-Za-z0-9]+)$/,Cr=function(){function e(e){this.named=e}return e.prototype.parse=function(e){if(e){var t=e.match(Nr);return t?String.fromCharCode(parseInt(t[1],16)):(t=e.match(Or))?String.fromCharCode(parseInt(t[1],10)):(t=e.match(Ar))?this.named[t[1]]:void 0}},e}(),Lr=/[\t\n\f ]/,_r=/[A-Za-z]/,Br=/\r\n?/g;function Dr(e){return Lr.test(e)}function Ir(e){return _r.test(e)}var Mr=function(){function e(e,t,r){void 0===r&&(r="precompile"),this.delegate=e,this.entityParser=t,this.mode=r,this.state="beforeData",this.line=-1,this.column=-1,this.input="",this.index=-1,this.tagNameBuffer="",this.states={beforeData:function(){var e=this.peek();if("<"!==e||this.isIgnoredEndTag()){if("precompile"===this.mode&&"\n"===e){var t=this.tagNameBuffer.toLowerCase();"pre"!==t&&"textarea"!==t||this.consume()}this.transitionTo("data"),this.delegate.beginData()}else this.transitionTo("tagOpen"),this.markTagStart(),this.consume()},data:function(){var e=this.peek(),t=this.tagNameBuffer;"<"!==e||this.isIgnoredEndTag()?"&"===e&&"script"!==t&&"style"!==t?(this.consume(),this.delegate.appendToData(this.consumeCharRef()||"&")):(this.consume(),this.delegate.appendToData(e)):(this.delegate.finishData(),this.transitionTo("tagOpen"),this.markTagStart(),this.consume())},tagOpen:function(){var e=this.consume();"!"===e?this.transitionTo("markupDeclarationOpen"):"/"===e?this.transitionTo("endTagOpen"):("@"===e||":"===e||Ir(e))&&(this.transitionTo("tagName"),this.tagNameBuffer="",this.delegate.beginStartTag(),this.appendToTagName(e))},markupDeclarationOpen:function(){var e=this.consume();"-"===e&&"-"===this.peek()?(this.consume(),this.transitionTo("commentStart"),this.delegate.beginComment()):"DOCTYPE"===e.toUpperCase()+this.input.substring(this.index,this.index+6).toUpperCase()&&(this.consume(),this.consume(),this.consume(),this.consume(),this.consume(),this.consume(),this.transitionTo("doctype"),this.delegate.beginDoctype&&this.delegate.beginDoctype())},doctype:function(){Dr(this.consume())&&this.transitionTo("beforeDoctypeName")},beforeDoctypeName:function(){var e=this.consume();Dr(e)||(this.transitionTo("doctypeName"),this.delegate.appendToDoctypeName&&this.delegate.appendToDoctypeName(e.toLowerCase()))},doctypeName:function(){var e=this.consume();Dr(e)?this.transitionTo("afterDoctypeName"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypeName&&this.delegate.appendToDoctypeName(e.toLowerCase())},afterDoctypeName:function(){var e=this.consume();if(!Dr(e))if(">"===e)this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData");else{var t=e.toUpperCase()+this.input.substring(this.index,this.index+5).toUpperCase(),r="PUBLIC"===t.toUpperCase(),n="SYSTEM"===t.toUpperCase();(r||n)&&(this.consume(),this.consume(),this.consume(),this.consume(),this.consume(),this.consume()),r?this.transitionTo("afterDoctypePublicKeyword"):n&&this.transitionTo("afterDoctypeSystemKeyword")}},afterDoctypePublicKeyword:function(){var e=this.peek();Dr(e)?(this.transitionTo("beforeDoctypePublicIdentifier"),this.consume()):'"'===e?(this.transitionTo("doctypePublicIdentifierDoubleQuoted"),this.consume()):"'"===e?(this.transitionTo("doctypePublicIdentifierSingleQuoted"),this.consume()):">"===e&&(this.consume(),this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData"))},doctypePublicIdentifierDoubleQuoted:function(){var e=this.consume();'"'===e?this.transitionTo("afterDoctypePublicIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypePublicIdentifier&&this.delegate.appendToDoctypePublicIdentifier(e)},doctypePublicIdentifierSingleQuoted:function(){var e=this.consume();"'"===e?this.transitionTo("afterDoctypePublicIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypePublicIdentifier&&this.delegate.appendToDoctypePublicIdentifier(e)},afterDoctypePublicIdentifier:function(){var e=this.consume();Dr(e)?this.transitionTo("betweenDoctypePublicAndSystemIdentifiers"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):'"'===e?this.transitionTo("doctypeSystemIdentifierDoubleQuoted"):"'"===e&&this.transitionTo("doctypeSystemIdentifierSingleQuoted")},betweenDoctypePublicAndSystemIdentifiers:function(){var e=this.consume();Dr(e)||(">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):'"'===e?this.transitionTo("doctypeSystemIdentifierDoubleQuoted"):"'"===e&&this.transitionTo("doctypeSystemIdentifierSingleQuoted"))},doctypeSystemIdentifierDoubleQuoted:function(){var e=this.consume();'"'===e?this.transitionTo("afterDoctypeSystemIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypeSystemIdentifier&&this.delegate.appendToDoctypeSystemIdentifier(e)},doctypeSystemIdentifierSingleQuoted:function(){var e=this.consume();"'"===e?this.transitionTo("afterDoctypeSystemIdentifier"):">"===e?(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData")):this.delegate.appendToDoctypeSystemIdentifier&&this.delegate.appendToDoctypeSystemIdentifier(e)},afterDoctypeSystemIdentifier:function(){var e=this.consume();Dr(e)||">"===e&&(this.delegate.endDoctype&&this.delegate.endDoctype(),this.transitionTo("beforeData"))},commentStart:function(){var e=this.consume();"-"===e?this.transitionTo("commentStartDash"):">"===e?(this.delegate.finishComment(),this.transitionTo("beforeData")):(this.delegate.appendToCommentData(e),this.transitionTo("comment"))},commentStartDash:function(){var e=this.consume();"-"===e?this.transitionTo("commentEnd"):">"===e?(this.delegate.finishComment(),this.transitionTo("beforeData")):(this.delegate.appendToCommentData("-"),this.transitionTo("comment"))},comment:function(){var e=this.consume();"-"===e?this.transitionTo("commentEndDash"):this.delegate.appendToCommentData(e)},commentEndDash:function(){var e=this.consume();"-"===e?this.transitionTo("commentEnd"):(this.delegate.appendToCommentData("-"+e),this.transitionTo("comment"))},commentEnd:function(){var e=this.consume();">"===e?(this.delegate.finishComment(),this.transitionTo("beforeData")):(this.delegate.appendToCommentData("--"+e),this.transitionTo("comment"))},tagName:function(){var e=this.consume();Dr(e)?this.transitionTo("beforeAttributeName"):"/"===e?this.transitionTo("selfClosingStartTag"):">"===e?(this.delegate.finishTag(),this.transitionTo("beforeData")):this.appendToTagName(e)},endTagName:function(){var e=this.consume();Dr(e)?(this.transitionTo("beforeAttributeName"),this.tagNameBuffer=""):"/"===e?(this.transitionTo("selfClosingStartTag"),this.tagNameBuffer=""):">"===e?(this.delegate.finishTag(),this.transitionTo("beforeData"),this.tagNameBuffer=""):this.appendToTagName(e)},beforeAttributeName:function(){var e=this.peek();Dr(e)?this.consume():"/"===e?(this.transitionTo("selfClosingStartTag"),this.consume()):">"===e?(this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):"="===e?(this.delegate.reportSyntaxError("attribute name cannot start with equals sign"),this.transitionTo("attributeName"),this.delegate.beginAttribute(),this.consume(),this.delegate.appendToAttributeName(e)):(this.transitionTo("attributeName"),this.delegate.beginAttribute())},attributeName:function(){var e=this.peek();Dr(e)?(this.transitionTo("afterAttributeName"),this.consume()):"/"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("selfClosingStartTag")):"="===e?(this.transitionTo("beforeAttributeValue"),this.consume()):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):'"'===e||"'"===e||"<"===e?(this.delegate.reportSyntaxError(e+" is not a valid character within attribute names"),this.consume(),this.delegate.appendToAttributeName(e)):(this.consume(),this.delegate.appendToAttributeName(e))},afterAttributeName:function(){var e=this.peek();Dr(e)?this.consume():"/"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("selfClosingStartTag")):"="===e?(this.consume(),this.transitionTo("beforeAttributeValue")):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.transitionTo("attributeName"),this.delegate.beginAttribute(),this.consume(),this.delegate.appendToAttributeName(e))},beforeAttributeValue:function(){var e=this.peek();Dr(e)?this.consume():'"'===e?(this.transitionTo("attributeValueDoubleQuoted"),this.delegate.beginAttributeValue(!0),this.consume()):"'"===e?(this.transitionTo("attributeValueSingleQuoted"),this.delegate.beginAttributeValue(!0),this.consume()):">"===e?(this.delegate.beginAttributeValue(!1),this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):(this.transitionTo("attributeValueUnquoted"),this.delegate.beginAttributeValue(!1),this.consume(),this.delegate.appendToAttributeValue(e))},attributeValueDoubleQuoted:function(){var e=this.consume();'"'===e?(this.delegate.finishAttributeValue(),this.transitionTo("afterAttributeValueQuoted")):"&"===e?this.delegate.appendToAttributeValue(this.consumeCharRef()||"&"):this.delegate.appendToAttributeValue(e)},attributeValueSingleQuoted:function(){var e=this.consume();"'"===e?(this.delegate.finishAttributeValue(),this.transitionTo("afterAttributeValueQuoted")):"&"===e?this.delegate.appendToAttributeValue(this.consumeCharRef()||"&"):this.delegate.appendToAttributeValue(e)},attributeValueUnquoted:function(){var e=this.peek();Dr(e)?(this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("beforeAttributeName")):"/"===e?(this.delegate.finishAttributeValue(),this.consume(),this.transitionTo("selfClosingStartTag")):"&"===e?(this.consume(),this.delegate.appendToAttributeValue(this.consumeCharRef()||"&")):">"===e?(this.delegate.finishAttributeValue(),this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):(this.consume(),this.delegate.appendToAttributeValue(e))},afterAttributeValueQuoted:function(){var e=this.peek();Dr(e)?(this.consume(),this.transitionTo("beforeAttributeName")):"/"===e?(this.consume(),this.transitionTo("selfClosingStartTag")):">"===e?(this.consume(),this.delegate.finishTag(),this.transitionTo("beforeData")):this.transitionTo("beforeAttributeName")},selfClosingStartTag:function(){">"===this.peek()?(this.consume(),this.delegate.markTagAsSelfClosing(),this.delegate.finishTag(),this.transitionTo("beforeData")):this.transitionTo("beforeAttributeName")},endTagOpen:function(){var e=this.consume();("@"===e||":"===e||Ir(e))&&(this.transitionTo("endTagName"),this.tagNameBuffer="",this.delegate.beginEndTag(),this.appendToTagName(e))}},this.reset()}return e.prototype.reset=function(){this.transitionTo("beforeData"),this.input="",this.tagNameBuffer="",this.index=0,this.line=1,this.column=0,this.delegate.reset()},e.prototype.transitionTo=function(e){this.state=e},e.prototype.tokenize=function(e){this.reset(),this.tokenizePart(e),this.tokenizeEOF()},e.prototype.tokenizePart=function(e){for(this.input+=function(e){return e.replace(Br,"\n")}(e);this.index<this.input.length;){var t=this.states[this.state];if(void 0===t)throw new Error("unhandled state "+this.state);t.call(this)}},e.prototype.tokenizeEOF=function(){this.flushData()},e.prototype.flushData=function(){"data"===this.state&&(this.delegate.finishData(),this.transitionTo("beforeData"))},e.prototype.peek=function(){return this.input.charAt(this.index)},e.prototype.consume=function(){var e=this.peek();return this.index++,"\n"===e?(this.line++,this.column=0):this.column++,e},e.prototype.consumeCharRef=function(){var e=this.input.indexOf(";",this.index);if(-1!==e){var t=this.input.slice(this.index,e),r=this.entityParser.parse(t);if(r){for(var n=t.length;n;)this.consume(),n--;return this.consume(),r}}},e.prototype.markTagStart=function(){this.delegate.tagOpen()},e.prototype.appendToTagName=function(e){this.tagNameBuffer+=e,this.delegate.appendToTagName(e)},e.prototype.isIgnoredEndTag=function(){var e=this.tagNameBuffer;return"title"===e&&"</title>"!==this.input.substring(this.index,this.index+8)||"style"===e&&"</style>"!==this.input.substring(this.index,this.index+8)||"script"===e&&"<\/script>"!==this.input.substring(this.index,this.index+9)},e}(),Rr=function(){function e(e,t){void 0===t&&(t={}),this.options=t,this.token=null,this.startLine=1,this.startColumn=0,this.tokens=[],this.tokenizer=new Mr(this,e,t.mode),this._currentAttribute=void 0}return e.prototype.tokenize=function(e){return this.tokens=[],this.tokenizer.tokenize(e),this.tokens},e.prototype.tokenizePart=function(e){return this.tokens=[],this.tokenizer.tokenizePart(e),this.tokens},e.prototype.tokenizeEOF=function(){return this.tokens=[],this.tokenizer.tokenizeEOF(),this.tokens[0]},e.prototype.reset=function(){this.token=null,this.startLine=1,this.startColumn=0},e.prototype.current=function(){var e=this.token;if(null===e)throw new Error("token was unexpectedly null");if(0===arguments.length)return e;for(var t=0;t<arguments.length;t++)if(e.type===arguments[t])return e;throw new Error("token type was unexpectedly "+e.type)},e.prototype.push=function(e){this.token=e,this.tokens.push(e)},e.prototype.currentAttribute=function(){return this._currentAttribute},e.prototype.addLocInfo=function(){this.options.loc&&(this.current().loc={start:{line:this.startLine,column:this.startColumn},end:{line:this.tokenizer.line,column:this.tokenizer.column}}),this.startLine=this.tokenizer.line,this.startColumn=this.tokenizer.column},e.prototype.beginDoctype=function(){this.push({type:"Doctype",name:""})},e.prototype.appendToDoctypeName=function(e){this.current("Doctype").name+=e},e.prototype.appendToDoctypePublicIdentifier=function(e){var t=this.current("Doctype");void 0===t.publicIdentifier?t.publicIdentifier=e:t.publicIdentifier+=e},e.prototype.appendToDoctypeSystemIdentifier=function(e){var t=this.current("Doctype");void 0===t.systemIdentifier?t.systemIdentifier=e:t.systemIdentifier+=e},e.prototype.endDoctype=function(){this.addLocInfo()},e.prototype.beginData=function(){this.push({type:"Chars",chars:""})},e.prototype.appendToData=function(e){this.current("Chars").chars+=e},e.prototype.finishData=function(){this.addLocInfo()},e.prototype.beginComment=function(){this.push({type:"Comment",chars:""})},e.prototype.appendToCommentData=function(e){this.current("Comment").chars+=e},e.prototype.finishComment=function(){this.addLocInfo()},e.prototype.tagOpen=function(){},e.prototype.beginStartTag=function(){this.push({type:"StartTag",tagName:"",attributes:[],selfClosing:!1})},e.prototype.beginEndTag=function(){this.push({type:"EndTag",tagName:""})},e.prototype.finishTag=function(){this.addLocInfo()},e.prototype.markTagAsSelfClosing=function(){this.current("StartTag").selfClosing=!0},e.prototype.appendToTagName=function(e){this.current("StartTag","EndTag").tagName+=e},e.prototype.beginAttribute=function(){this._currentAttribute=["","",!1]},e.prototype.appendToAttributeName=function(e){this.currentAttribute()[0]+=e},e.prototype.beginAttributeValue=function(e){this.currentAttribute()[2]=e},e.prototype.appendToAttributeValue=function(e){this.currentAttribute()[1]+=e},e.prototype.finishAttributeValue=function(){this.current("StartTag").attributes.push(this._currentAttribute)},e.prototype.reportSyntaxError=function(e){this.current().syntaxError=e},e}();var qr,jr=Object.freeze({__proto__:null,HTML5NamedCharRefs:xr,EntityParser:Cr,EventedTokenizer:Mr,Tokenizer:Rr,tokenize:function(e,t){return new Rr(new Cr(xr),t).tokenize(e)}}),Hr=function(e,t={entityEncoding:"transformed"}){if(!e)return"";return new Vr.default(t).print(e)},Vr=(qr=ir)&&qr.__esModule?qr:{default:qr};var Ur=Object.defineProperty({default:Hr},"__esModule",{value:!0}),$r=function(e,t){let{module:r,loc:n}=t,{line:o,column:i}=n.start,a=t.asString(),s=a?`\n\n|\n|  ${a.split("\n").join("\n|  ")}\n|\n\n`:"",l=new Error(`${e}: ${s}(error occurred in '${r}' @ line ${o} : column ${i})`);return l.name="SyntaxError",l.location=t,l.code=a,l};var zr=Object.defineProperty({generateSyntaxError:$r},"__esModule",{value:!0}),Fr=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={Program:(0,_.tuple)("body"),Template:(0,_.tuple)("body"),Block:(0,_.tuple)("body"),MustacheStatement:(0,_.tuple)("path","params","hash"),BlockStatement:(0,_.tuple)("path","params","hash","program","inverse"),ElementModifierStatement:(0,_.tuple)("path","params","hash"),PartialStatement:(0,_.tuple)("name","params","hash"),CommentStatement:(0,_.tuple)(),MustacheCommentStatement:(0,_.tuple)(),ElementNode:(0,_.tuple)("attributes","modifiers","children","comments"),AttrNode:(0,_.tuple)("value"),TextNode:(0,_.tuple)(),ConcatStatement:(0,_.tuple)("parts"),SubExpression:(0,_.tuple)("path","params","hash"),PathExpression:(0,_.tuple)(),PathHead:(0,_.tuple)(),StringLiteral:(0,_.tuple)(),BooleanLiteral:(0,_.tuple)(),NumberLiteral:(0,_.tuple)(),NullLiteral:(0,_.tuple)(),UndefinedLiteral:(0,_.tuple)(),Hash:(0,_.tuple)("pairs"),HashPair:(0,_.tuple)("value"),NamedBlock:(0,_.tuple)("attributes","modifiers","children","comments"),SimpleElement:(0,_.tuple)("attributes","modifiers","children","comments"),Component:(0,_.tuple)("head","attributes","modifiers","children","comments")};t.default=r})),Gr=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.cannotRemoveNode=function(e,t,n){return new r("Cannot remove a node unless it is part of an array",e,t,n)},t.cannotReplaceNode=function(e,t,n){return new r("Cannot replace a node with multiple nodes unless it is part of an array",e,t,n)},t.cannotReplaceOrRemoveInKeyHandlerYet=function(e,t){return new r("Replacing and removing in key handlers is not yet supported.",e,null,t)},t.default=void 0;const r=function(){function e(e,t,r,n){let o=Error.call(this,e);this.key=n,this.message=e,this.node=t,this.parent=r,this.stack=o.stack}return e.prototype=Object.create(Error.prototype),e.prototype.constructor=e,e}();var n=r;t.default=n})),Kr=t((function(e,t){function r(e){switch(e.type){case"ElementNode":return e.tag.split(".")[0];case"SubExpression":case"MustacheStatement":case"BlockStatement":return r(e.path);case"UndefinedLiteral":case"NullLiteral":case"BooleanLiteral":case"StringLiteral":case"NumberLiteral":case"TextNode":case"Template":case"Block":case"CommentStatement":case"MustacheCommentStatement":case"PartialStatement":case"ElementModifierStatement":case"AttrNode":case"ConcatStatement":case"Program":case"Hash":case"HashPair":return;case"PathExpression":default:return e.parts.length?e.parts[0]:void 0}}function n(e){switch(e.type){case"ElementNode":case"Program":case"Block":case"Template":return e.blockParams;case"BlockStatement":return e.program.blockParams;default:return}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.TransformScope=void 0;class o{constructor(e){this.locals=e,this.hasPartial=!1,this.usedLocals={};for(const t of e)this.usedLocals[t]=!1}child(e){let t=n(e);return t?new i(t,this):this}usePartial(){this.hasPartial=!0}}t.TransformScope=o;t.default=class extends o{constructor(e){var t;super(null!==(t=n(e))&&void 0!==t?t:[])}useLocal(e){let t=r(e);t&&t in this.usedLocals&&(this.usedLocals[t]=!0)}isLocal(e){return-1!==this.locals.indexOf(e)}currentUnusedLocals(){return!this.hasPartial&&this.locals.length>0&&this.locals.filter((e=>!this.usedLocals[e]))}};class i extends o{constructor(e,t){super(e),this.parent=t}useLocal(e){let t=r(e);t&&t in this.usedLocals?this.usedLocals[t]=!0:this.parent.useLocal(e)}isLocal(e){return-1!==this.locals.indexOf(e)||this.parent.isLocal(e)}currentUnusedLocals(){return!this.hasPartial&&this.locals.length>0&&!this.usedLocals[this.locals[this.locals.length-1]]&&[this.locals[this.locals.length-1]]}}})),Wr=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(Kr);t.default=class{constructor(e,t=null,n=null){this.node=e,this.parent=t,this.parentKey=n,this.scope=t?t.scope.child(e):new r.default(e),"PathExpression"===e.type&&this.scope.useLocal(e),"ElementNode"===e.type&&(this.scope.useLocal(e),e.children.forEach((e=>this.scope.useLocal(e))))}get parentNode(){return this.parent?this.parent.node:null}parents(){return{[Symbol.iterator]:()=>new n(this)}}};class n{constructor(e){this.path=e}next(){return this.path.parent?(this.path=this.path.parent,{done:!1,value:this.path}):{done:!0,value:null}}}})),Yr=function(e,t){let r=new Jr.default(e);tn(t,r)},Qr=Xr(Fr),Jr=Xr(Wr);function Xr(e){return e&&e.__esModule?e:{default:e}}function Zr(e){return"function"==typeof e?e:e.enter}function en(e){return"function"==typeof e?void 0:e.exit}function tn(e,t){let r,n,o,{node:i,parent:a,parentKey:s}=t,l=function(e,t){if(("Template"===t||"Block"===t)&&e.Program)return e.Program;let r=e[t];return void 0!==r?r:e.All}(e,i.type);if(void 0!==l&&(r=Zr(l),n=en(l)),void 0!==r&&(o=r(i,t)),null!=o){if(JSON.stringify(i)!==JSON.stringify(o)){if(Array.isArray(o))return on(e,o,a,s),o;return tn(e,new Jr.default(o,a,s))||o}o=void 0}if(void 0===o){let r=Qr.default[i.type];for(let n=0;n<r.length;n++){nn(e,l,t,r[n])}void 0!==n&&(o=n(i,t))}return o}function rn(e,t,r){e[t]=r}function nn(e,t,r,n){let o,i,{node:a}=r,s=function(e,t){return e[t]}(a,n);if(s){if(void 0!==t){let e=function(e,t){let r="function"!=typeof e?e.keys:void 0;if(void 0===r)return;let n=r[t];return void 0!==n?n:r.All}(t,n);void 0!==e&&(o=Zr(e),i=en(e))}if(void 0!==o&&void 0!==o(a,n))throw(0,Gr.cannotReplaceOrRemoveInKeyHandlerYet)(a,n);if(Array.isArray(s))on(e,s,r,n);else{let t=tn(e,new Jr.default(s,r,n));void 0!==t&&function(e,t,r,n){if(null===n)throw(0,Gr.cannotRemoveNode)(r,e,t);if(Array.isArray(n)){if(1!==n.length)throw 0===n.length?(0,Gr.cannotRemoveNode)(r,e,t):(0,Gr.cannotReplaceNode)(r,e,t);rn(e,t,n[0])}else rn(e,t,n)}(a,n,s,t)}if(void 0!==i&&void 0!==i(a,n))throw(0,Gr.cannotReplaceOrRemoveInKeyHandlerYet)(a,n)}}function on(e,t,r,n){for(let o=0;o<t.length;o++){let i=t[o],a=tn(e,new Jr.default(i,r,n));void 0!==a&&(o+=an(t,o,a)-1)}}function an(e,t,r){return null===r?(e.splice(t,1),0):Array.isArray(r)?(e.splice(t,1,...r),r.length):(e.splice(t,1,r),1)}var sn=Object.defineProperty({default:Yr},"__esModule",{value:!0}),ln=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class{constructor(e){this.order=e,this.stack=[]}visit(e,t){e&&(this.stack.push(e),"post"===this.order?(this.children(e,t),t(e,this)):(t(e,this),this.children(e,t)),this.stack.pop())}children(e,t){switch(e.type){case"Block":case"Template":return r.Program(this,e,t);case"ElementNode":return r.ElementNode(this,e,t);case"BlockStatement":return r.BlockStatement(this,e,t);default:return}}};const r={Program(e,t,r){for(let n=0;n<t.body.length;n++)e.visit(t.body[n],r)},Template(e,t,r){for(let n=0;n<t.body.length;n++)e.visit(t.body[n],r)},Block(e,t,r){for(let n=0;n<t.body.length;n++)e.visit(t.body[n],r)},ElementNode(e,t,r){for(let n=0;n<t.children.length;n++)e.visit(t.children[n],r)},BlockStatement(e,t,r){e.visit(t.program,r),e.visit(t.inverse||null,r)}}})),cn=function(e){let t=function(e){let t=e.attributes.length,r=[];for(let n=0;n<t;n++)r.push(e.attributes[n].name);let n=r.indexOf("as");if(-1===n&&r.length>0&&"|"===r[r.length-1].charAt(0))throw(0,zr.generateSyntaxError)("Block parameters must be preceded by the `as` keyword, detected block parameters without `as`",e.loc);if(-1!==n&&t>n&&"|"===r[n+1].charAt(0)){let o=r.slice(n).join(" ");if("|"!==o.charAt(o.length-1)||2!==o.match(/\|/g).length)throw(0,zr.generateSyntaxError)("Invalid block parameters syntax, '"+o+"'",e.loc);let i=[];for(let o=n+1;o<t;o++){let t=r[o].replace(/\|/g,"");if(""!==t){if(gn.test(t))throw(0,zr.generateSyntaxError)("Invalid identifier for block parameters, '"+t+"'",e.loc);i.push(t)}}if(0===i.length)throw(0,zr.generateSyntaxError)("Cannot use zero block parameters",e.loc);return e.attributes=e.attributes.slice(0,n),i}return null}(e);t&&(e.blockParams=t)},un=bn,hn=function(e,t){bn(e).push(t)},pn=function(e){return"StringLiteral"===e.type||"BooleanLiteral"===e.type||"NumberLiteral"===e.type||"NullLiteral"===e.type||"UndefinedLiteral"===e.type},dn=function(e){return"UndefinedLiteral"===e.type?"undefined":JSON.stringify(e.value)},fn=function(e){return e[0]===e[0].toUpperCase()&&e[0]!==e[0].toLowerCase()},mn=function(e){return e[0]===e[0].toLowerCase()&&e[0]!==e[0].toUpperCase()};let gn=/[!"#%-,\.\/;->@\[-\^`\{-~]/;function bn(e){switch(e.type){case"Block":case"Template":return e.body;case"ElementNode":return e.children}}var yn=Object.defineProperty({parseElementBlockParams:cn,childrenFor:un,appendChild:hn,isHBSLiteral:pn,printLiteral:dn,isUpperCase:fn,isLowerCase:mn},"__esModule",{value:!0}),vn=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;const r={close:!1,open:!1};var n=new class{pos(e,t){return{line:e,column:t}}blockItself({body:e,blockParams:t,chained:r=!1,loc:n}){return{type:"Block",body:e||[],blockParams:t||[],chained:r,loc:n}}template({body:e,blockParams:t,loc:r}){return{type:"Template",body:e||[],blockParams:t||[],loc:r}}mustache({path:e,params:t,hash:n,trusting:o,loc:i,strip:a=r}){return{type:"MustacheStatement",path:e,params:t,hash:n,escaped:!o,trusting:o,loc:i,strip:a||{open:!1,close:!1}}}block({path:e,params:t,hash:n,defaultBlock:o,elseBlock:i=null,loc:a,openStrip:s=r,inverseStrip:l=r,closeStrip:c=r}){return{type:"BlockStatement",path:e,params:t,hash:n,program:o,inverse:i,loc:a,openStrip:s,inverseStrip:l,closeStrip:c}}comment(e,t){return{type:"CommentStatement",value:e,loc:t}}mustacheComment(e,t){return{type:"MustacheCommentStatement",value:e,loc:t}}concat(e,t){return{type:"ConcatStatement",parts:e,loc:t}}element({tag:e,selfClosing:t,attrs:r,blockParams:n,modifiers:o,comments:i,children:a,loc:s}){return{type:"ElementNode",tag:e,selfClosing:t,attributes:r||[],blockParams:n||[],modifiers:o||[],comments:i||[],children:a||[],loc:s}}elementModifier({path:e,params:t,hash:r,loc:n}){return{type:"ElementModifierStatement",path:e,params:t,hash:r,loc:n}}attr({name:e,value:t,loc:r}){return{type:"AttrNode",name:e,value:t,loc:r}}text({chars:e,loc:t}){return{type:"TextNode",chars:e,loc:t}}sexpr({path:e,params:t,hash:r,loc:n}){return{type:"SubExpression",path:e,params:t,hash:r,loc:n}}path({head:e,tail:t,loc:r}){let{original:n}=function(e){switch(e.type){case"AtHead":return{original:e.name,parts:[e.name]};case"ThisHead":return{original:"this",parts:[]};case"VarHead":return{original:e.name,parts:[e.name]}}}(e),o=[...n,...t].join(".");return new H.PathExpressionImplV1(o,e,t,r)}head(e,t){return"@"===e[0]?this.atName(e,t):"this"===e?this.this(t):this.var(e,t)}this(e){return{type:"ThisHead",loc:e}}atName(e,t){return{type:"AtHead",name:e,loc:t}}var(e,t){return{type:"VarHead",name:e,loc:t}}hash(e,t){return{type:"Hash",pairs:e||[],loc:t}}pair({key:e,value:t,loc:r}){return{type:"HashPair",key:e,value:t,loc:r}}literal({type:e,value:t,loc:r}){return{type:e,value:t,original:t,loc:r}}undefined(){return this.literal({type:"UndefinedLiteral",value:void 0})}null(){return this.literal({type:"NullLiteral",value:null})}string(e,t){return this.literal({type:"StringLiteral",value:e,loc:t})}boolean(e,t){return this.literal({type:"BooleanLiteral",value:e,loc:t})}number(e,t){return this.literal({type:"NumberLiteral",value:e,loc:t})}};t.default=n})),Sn=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Parser=void 0;t.Parser=class{constructor(e,t=new jr.EntityParser(jr.HTML5NamedCharRefs),r="precompile"){this.elementStack=[],this.currentAttribute=null,this.currentNode=null,this.source=e,this.lines=e.source.split(/(?:\r\n?|\n)/g),this.tokenizer=new jr.EventedTokenizer(this,t,r)}offset(){let{line:e,column:t}=this.tokenizer;return this.source.offsetFor(e,t)}pos({line:e,column:t}){return this.source.offsetFor(e,t)}finish(e){return(0,_.assign)({},e,{loc:e.loc.until(this.offset())})}get currentAttr(){return this.currentAttribute}get currentTag(){return this.currentNode}get currentStartTag(){return this.currentNode}get currentEndTag(){return this.currentNode}get currentComment(){return this.currentNode}get currentData(){return this.currentNode}acceptTemplate(e){return this[e.type](e)}acceptNode(e){return this[e.type](e)}currentElement(){return this.elementStack[this.elementStack.length-1]}sourceForNode(e,t){let r,n,o,i=e.loc.start.line-1,a=i-1,s=e.loc.start.column,l=[];for(t?(n=t.loc.end.line-1,o=t.loc.end.column):(n=e.loc.end.line-1,o=e.loc.end.column);a<n;)a++,r=this.lines[a],a===i?i===n?l.push(r.slice(s,o)):l.push(r.slice(s)):a===n?l.push(r.slice(0,o)):l.push(r);return l.join("\n")}}})),kn=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.HandlebarsNodeVisitors=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(vn);class n extends Sn.Parser{get isTopLevel(){return 0===this.elementStack.length}Program(e){let t,n=[];t=this.isTopLevel?r.default.template({body:n,blockParams:e.blockParams,loc:this.source.spanFor(e.loc)}):r.default.blockItself({body:n,blockParams:e.blockParams,chained:e.chained,loc:this.source.spanFor(e.loc)});let o,i=e.body.length;if(this.elementStack.push(t),0===i)return this.elementStack.pop();for(o=0;o<i;o++)this.acceptNode(e.body[o]);let a=this.elementStack.pop();if(a!==t){let e=a;throw(0,zr.generateSyntaxError)(`Unclosed element \`${e.tag}\``,e.loc)}return t}BlockStatement(e){if("comment"===this.tokenizer.state)return void this.appendToCommentData(this.sourceForNode(e));if("data"!==this.tokenizer.state&&"beforeData"!==this.tokenizer.state)throw(0,zr.generateSyntaxError)("A block may only be used inside an HTML element or another block.",this.source.spanFor(e.loc));let{path:t,params:n,hash:i}=o(this,e);e.program.loc||(e.program.loc=B.NON_EXISTENT_LOCATION),e.inverse&&!e.inverse.loc&&(e.inverse.loc=B.NON_EXISTENT_LOCATION);let a=this.Program(e.program),s=e.inverse?this.Program(e.inverse):null,l=r.default.block({path:t,params:n,hash:i,defaultBlock:a,elseBlock:s,loc:this.source.spanFor(e.loc),openStrip:e.openStrip,inverseStrip:e.inverseStrip,closeStrip:e.closeStrip}),c=this.currentElement();(0,yn.appendChild)(c,l)}MustacheStatement(e){let t,{tokenizer:n}=this;if("comment"===n.state)return void this.appendToCommentData(this.sourceForNode(e));let{escaped:a,loc:s,strip:l}=e;if((0,yn.isHBSLiteral)(e.path))t=r.default.mustache({path:this.acceptNode(e.path),params:[],hash:r.default.hash([],this.source.spanFor(e.path.loc).collapse("end")),trusting:!a,loc:this.source.spanFor(s),strip:l});else{let{path:n,params:i,hash:c}=o(this,e);t=r.default.mustache({path:n,params:i,hash:c,trusting:!a,loc:this.source.spanFor(s),strip:l})}switch(n.state){case"tagOpen":case"tagName":throw(0,zr.generateSyntaxError)("Cannot use mustaches in an elements tagname",t.loc);case"beforeAttributeName":i(this.currentStartTag,t);break;case"attributeName":case"afterAttributeName":this.beginAttributeValue(!1),this.finishAttributeValue(),i(this.currentStartTag,t),n.transitionTo("beforeAttributeName");break;case"afterAttributeValueQuoted":i(this.currentStartTag,t),n.transitionTo("beforeAttributeName");break;case"beforeAttributeValue":this.beginAttributeValue(!1),this.appendDynamicAttributeValuePart(t),n.transitionTo("attributeValueUnquoted");break;case"attributeValueDoubleQuoted":case"attributeValueSingleQuoted":case"attributeValueUnquoted":this.appendDynamicAttributeValuePart(t);break;default:(0,yn.appendChild)(this.currentElement(),t)}return t}appendDynamicAttributeValuePart(e){this.finalizeTextPart();let t=this.currentAttr;t.isDynamic=!0,t.parts.push(e)}finalizeTextPart(){let e=this.currentAttr.currentPart;null!==e&&(this.currentAttr.parts.push(e),this.startTextPart())}startTextPart(){this.currentAttr.currentPart=null}ContentStatement(e){!function(e,t){let r=t.loc.start.line,n=t.loc.start.column,o=function(e,t){if(""===t)return{lines:e.split("\n").length-1,columns:0};let r=e.split(t)[0].split(/\n/),n=r.length-1;return{lines:n,columns:r[n].length}}(t.original,t.value);r+=o.lines,o.lines?n=o.columns:n+=o.columns;e.line=r,e.column=n}(this.tokenizer,e),this.tokenizer.tokenizePart(e.value),this.tokenizer.flushData()}CommentStatement(e){let{tokenizer:t}=this;if("comment"===t.state)return this.appendToCommentData(this.sourceForNode(e)),null;let{value:n,loc:o}=e,i=r.default.mustacheComment(n,this.source.spanFor(o));switch(t.state){case"beforeAttributeName":case"afterAttributeName":this.currentStartTag.comments.push(i);break;case"beforeData":case"data":(0,yn.appendChild)(this.currentElement(),i);break;default:throw(0,zr.generateSyntaxError)(`Using a Handlebars comment when in the \`${t.state}\` state is not supported`,this.source.spanFor(e.loc))}return i}PartialStatement(e){throw(0,zr.generateSyntaxError)("Handlebars partials are not supported",this.source.spanFor(e.loc))}PartialBlockStatement(e){throw(0,zr.generateSyntaxError)("Handlebars partial blocks are not supported",this.source.spanFor(e.loc))}Decorator(e){throw(0,zr.generateSyntaxError)("Handlebars decorators are not supported",this.source.spanFor(e.loc))}DecoratorBlock(e){throw(0,zr.generateSyntaxError)("Handlebars decorator blocks are not supported",this.source.spanFor(e.loc))}SubExpression(e){let{path:t,params:n,hash:i}=o(this,e);return r.default.sexpr({path:t,params:n,hash:i,loc:this.source.spanFor(e.loc)})}PathExpression(e){let t,{original:r}=e;if(-1!==r.indexOf("/")){if("./"===r.slice(0,2))throw(0,zr.generateSyntaxError)('Using "./" is not supported in Glimmer and unnecessary',this.source.spanFor(e.loc));if("../"===r.slice(0,3))throw(0,zr.generateSyntaxError)('Changing context using "../" is not supported in Glimmer',this.source.spanFor(e.loc));if(-1!==r.indexOf("."))throw(0,zr.generateSyntaxError)("Mixing '.' and '/' in paths is not supported in Glimmer; use only '.' to separate property paths",this.source.spanFor(e.loc));t=[e.parts.join("/")]}else{if("."===r)throw(0,zr.generateSyntaxError)("'.' is not a supported path in Glimmer; check for a path with a trailing '.'",this.source.spanFor(e.loc));t=e.parts}let n,o=!1;if(r.match(/^this(\..+)?$/)&&(o=!0),o)n={type:"ThisHead",loc:{start:e.loc.start,end:{line:e.loc.start.line,column:e.loc.start.column+4}}};else if(e.data){let r=t.shift();if(void 0===r)throw(0,zr.generateSyntaxError)("Attempted to parse a path expression, but it was not valid. Paths beginning with @ must start with a-z.",this.source.spanFor(e.loc));n={type:"AtHead",name:`@${r}`,loc:{start:e.loc.start,end:{line:e.loc.start.line,column:e.loc.start.column+r.length+1}}}}else{let r=t.shift();if(void 0===r)throw(0,zr.generateSyntaxError)("Attempted to parse a path expression, but it was not valid. Paths must start with a-z or A-Z.",this.source.spanFor(e.loc));n={type:"VarHead",name:r,loc:{start:e.loc.start,end:{line:e.loc.start.line,column:e.loc.start.column+r.length}}}}return new H.PathExpressionImplV1(e.original,n,t,this.source.spanFor(e.loc))}Hash(e){let t=[];for(let n=0;n<e.pairs.length;n++){let o=e.pairs[n];t.push(r.default.pair({key:o.key,value:this.acceptNode(o.value),loc:this.source.spanFor(o.loc)}))}return r.default.hash(t,this.source.spanFor(e.loc))}StringLiteral(e){return r.default.literal({type:"StringLiteral",value:e.value,loc:e.loc})}BooleanLiteral(e){return r.default.literal({type:"BooleanLiteral",value:e.value,loc:e.loc})}NumberLiteral(e){return r.default.literal({type:"NumberLiteral",value:e.value,loc:e.loc})}UndefinedLiteral(e){return r.default.literal({type:"UndefinedLiteral",value:void 0,loc:e.loc})}NullLiteral(e){return r.default.literal({type:"NullLiteral",value:null,loc:e.loc})}}function o(e,t){let r="PathExpression"===t.path.type?e.PathExpression(t.path):e.SubExpression(t.path),n=t.params?t.params.map((t=>e.acceptNode(t))):[],o=n.length>0?n[n.length-1].loc:r.loc;return{path:r,params:n,hash:t.hash?e.Hash(t.hash):{type:"Hash",pairs:[],loc:e.source.spanFor(o).collapse("end")}}}function i(e,t){let{path:n,params:o,hash:i,loc:a}=t;if((0,yn.isHBSLiteral)(n)){let r=`{{${(0,yn.printLiteral)(n)}}}`,o=`<${e.name} ... ${r} ...`;throw(0,zr.generateSyntaxError)(`In ${o}, ${r} is not a valid modifier`,t.loc)}let s=r.default.elementModifier({path:n,params:o,hash:i,loc:a});e.modifiers.push(s)}t.HandlebarsNodeVisitors=n})),wn=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.preprocess=h,t.TokenizerEventHandlers=void 0;var r=s(Ur),n=s(sn),o=s(ln),i=s(vn),a=s(V);function s(e){return e&&e.__esModule?e:{default:e}}class l extends kn.HandlebarsNodeVisitors{constructor(){super(...arguments),this.tagOpenLine=0,this.tagOpenColumn=0}reset(){this.currentNode=null}beginComment(){this.currentNode=i.default.comment("",this.source.offsetFor(this.tagOpenLine,this.tagOpenColumn))}appendToCommentData(e){this.currentComment.value+=e}finishComment(){(0,yn.appendChild)(this.currentElement(),this.finish(this.currentComment))}beginData(){this.currentNode=i.default.text({chars:"",loc:this.offset().collapsed()})}appendToData(e){this.currentData.chars+=e}finishData(){this.currentData.loc=this.currentData.loc.withEnd(this.offset()),(0,yn.appendChild)(this.currentElement(),this.currentData)}tagOpen(){this.tagOpenLine=this.tokenizer.line,this.tagOpenColumn=this.tokenizer.column}beginStartTag(){this.currentNode={type:"StartTag",name:"",attributes:[],modifiers:[],comments:[],selfClosing:!1,loc:this.source.offsetFor(this.tagOpenLine,this.tagOpenColumn)}}beginEndTag(){this.currentNode={type:"EndTag",name:"",attributes:[],modifiers:[],comments:[],selfClosing:!1,loc:this.source.offsetFor(this.tagOpenLine,this.tagOpenColumn)}}finishTag(){let e=this.finish(this.currentTag);if("StartTag"===e.type){if(this.finishStartTag(),":"===e.name)throw(0,zr.generateSyntaxError)("Invalid named block named detected, you may have created a named block without a name, or you may have began your name with a number. Named blocks must have names that are at least one character long, and begin with a lower case letter",this.source.spanFor({start:this.currentTag.loc.toJSON(),end:this.offset().toJSON()}));(ir.voidMap[e.name]||e.selfClosing)&&this.finishEndTag(!0)}else"EndTag"===e.type&&this.finishEndTag(!1)}finishStartTag(){let{name:e,attributes:t,modifiers:r,comments:n,selfClosing:o,loc:a}=this.finish(this.currentStartTag),s=i.default.element({tag:e,selfClosing:o,attrs:t,modifiers:r,comments:n,children:[],blockParams:[],loc:a});this.elementStack.push(s)}finishEndTag(e){let t=this.finish(this.currentTag),r=this.elementStack.pop(),n=this.currentElement();this.validateEndTag(t,r,e),r.loc=r.loc.withEnd(this.offset()),(0,yn.parseElementBlockParams)(r),(0,yn.appendChild)(n,r)}markTagAsSelfClosing(){this.currentTag.selfClosing=!0}appendToTagName(e){this.currentTag.name+=e}beginAttribute(){let e=this.offset();this.currentAttribute={name:"",parts:[],currentPart:null,isQuoted:!1,isDynamic:!1,start:e,valueSpan:e.collapsed()}}appendToAttributeName(e){this.currentAttr.name+=e}beginAttributeValue(e){this.currentAttr.isQuoted=e,this.startTextPart(),this.currentAttr.valueSpan=this.offset().collapsed()}appendToAttributeValue(e){let t=this.currentAttr.parts,r=t[t.length-1],n=this.currentAttr.currentPart;if(n)n.chars+=e,n.loc=n.loc.withEnd(this.offset());else{let t=this.offset();t="\n"===e?r?r.loc.getEnd():this.currentAttr.valueSpan.getStart():t.move(-1),this.currentAttr.currentPart=i.default.text({chars:e,loc:t.collapsed()})}}finishAttributeValue(){this.finalizeTextPart();let e=this.currentTag,t=this.offset();if("EndTag"===e.type)throw(0,zr.generateSyntaxError)("Invalid end tag: closing tag must not have attributes",this.source.spanFor({start:e.loc.toJSON(),end:t.toJSON()}));let{name:r,parts:n,start:o,isQuoted:a,isDynamic:s,valueSpan:l}=this.currentAttr,c=this.assembleAttributeValue(n,a,s,o.until(t));c.loc=l.withEnd(t);let u=i.default.attr({name:r,value:c,loc:o.until(t)});this.currentStartTag.attributes.push(u)}reportSyntaxError(e){throw(0,zr.generateSyntaxError)(e,this.offset().collapsed())}assembleConcatenatedValue(e){for(let t=0;t<e.length;t++){let r=e[t];if("MustacheStatement"!==r.type&&"TextNode"!==r.type)throw(0,zr.generateSyntaxError)("Unsupported node in quoted attribute value: "+r.type,r.loc)}(0,_.assertPresent)(e,"the concatenation parts of an element should not be empty");let t=e[0],r=e[e.length-1];return i.default.concat(e,this.source.spanFor(t.loc).extend(this.source.spanFor(r.loc)))}validateEndTag(e,t,r){let n;if(ir.voidMap[e.name]&&!r?n=`<${e.name}> elements do not need end tags. You should remove it`:void 0===t.tag?n=`Closing tag </${e.name}> without an open tag`:t.tag!==e.name&&(n=`Closing tag </${e.name}> did not match last open tag <${t.tag}> (on line ${t.loc.startPosition.line})`),n)throw(0,zr.generateSyntaxError)(n,e.loc)}assembleAttributeValue(e,t,r,n){if(r){if(t)return this.assembleConcatenatedValue(e);if(1===e.length||2===e.length&&"TextNode"===e[1].type&&"/"===e[1].chars)return e[0];throw(0,zr.generateSyntaxError)("An unquoted attribute value must be a string or a mustache, preceded by whitespace or a '=' character, and followed by whitespace, a '>' character, or '/>'",n)}return e.length>0?e[0]:i.default.text({chars:"",loc:n})}}t.TokenizerEventHandlers=l;const c={parse:h,builders:a.default,print:r.default,traverse:n.default,Walker:o.default};class u extends jr.EntityParser{constructor(){super({})}parse(){}}function h(e,t={}){var r,o,i;let a,s,h,p=t.mode||"precompile";"string"==typeof e?(a=new j.Source(e,null===(r=t.meta)||void 0===r?void 0:r.moduleName),s="codemod"===p?(0,Tr.parseWithoutProcessing)(e,t.parseOptions):(0,Tr.parse)(e,t.parseOptions)):e instanceof j.Source?(a=e,s="codemod"===p?(0,Tr.parseWithoutProcessing)(e.source,t.parseOptions):(0,Tr.parse)(e.source,t.parseOptions)):(a=new j.Source("",null===(o=t.meta)||void 0===o?void 0:o.moduleName),s=e),"codemod"===p&&(h=new u);let d=q.SourceSpan.forCharPositions(a,0,a.source.length);s.loc={source:"(program)",start:d.startPosition,end:d.endPosition};let f=new l(a,h,p).acceptTemplate(s);if(t.strictMode&&(f.blockParams=null!==(i=t.locals)&&void 0!==i?i:[]),t&&t.plugins&&t.plugins.ast)for(let e=0,r=t.plugins.ast.length;e<r;e++){let r=(0,t.plugins.ast[e])((0,_.assign)({},t,{syntax:c},{plugins:void 0}));(0,n.default)(f,r.visitor)}return f}})),En=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.BlockSymbolTable=t.ProgramSymbolTable=t.SymbolTable=void 0;var r;class n{static top(e,t){return new o(e,t)}child(e){let t=e.map((e=>this.allocate(e)));return new i(this,e,t)}}t.SymbolTable=n;class o extends n{constructor(e,t){super(),this.templateLocals=e,this.customizeComponentName=t,this.symbols=[],this.upvars=[],this.size=1,this.named=(0,_.dict)(),this.blocks=(0,_.dict)(),this.usedTemplateLocals=[],r.set(this,!1)}getUsedTemplateLocals(){return this.usedTemplateLocals}setHasEval(){!function(e,t,r){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");t.set(e,r)}(this,r,!0)}get hasEval(){return function(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)}(this,r)}has(e){return-1!==this.templateLocals.indexOf(e)}get(e){let t=this.usedTemplateLocals.indexOf(e);return-1!==t||(t=this.usedTemplateLocals.length,this.usedTemplateLocals.push(e)),[t,!0]}getLocalsMap(){return(0,_.dict)()}getEvalInfo(){let e=this.getLocalsMap();return Object.keys(e).map((t=>e[t]))}allocateFree(e,t){39===t.resolution()&&t.isAngleBracket&&(0,yn.isUpperCase)(e)&&(e=this.customizeComponentName(e));let r=this.upvars.indexOf(e);return-1!==r||(r=this.upvars.length,this.upvars.push(e)),r}allocateNamed(e){let t=this.named[e];return t||(t=this.named[e]=this.allocate(e)),t}allocateBlock(e){"inverse"===e&&(e="else");let t=this.blocks[e];return t||(t=this.blocks[e]=this.allocate(`&${e}`)),t}allocate(e){return this.symbols.push(e),this.size++}}t.ProgramSymbolTable=o,r=new WeakMap;class i extends n{constructor(e,t,r){super(),this.parent=e,this.symbols=t,this.slots=r}get locals(){return this.symbols}has(e){return-1!==this.symbols.indexOf(e)||this.parent.has(e)}get(e){let t=this.symbols.indexOf(e);return-1===t?this.parent.get(e):[this.slots[t],!1]}getLocalsMap(){let e=this.parent.getLocalsMap();return this.symbols.forEach((t=>e[t]=this.get(t)[0])),e}getEvalInfo(){let e=this.getLocalsMap();return Object.keys(e).map((t=>e[t]))}setHasEval(){this.parent.setHasEval()}allocateFree(e,t){return this.parent.allocateFree(e,t)}allocateNamed(e){return this.parent.allocateNamed(e)}allocateBlock(e){return this.parent.allocateBlock(e)}allocate(e){return this.parent.allocate(e)}}t.BlockSymbolTable=i})),Pn=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.BuildElement=t.Builder=void 0;var r=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n();if(t&&t.has(e))return t.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(r,i,a):r[i]=e[i]}r.default=e,t&&t.set(e,r);return r}(re);function n(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return n=function(){return e},e}class o{template(e,t,n){return new r.Template({table:e,body:t,loc:n})}block(e,t,n){return new r.Block({scope:e,body:t,loc:n})}namedBlock(e,t,n){return new r.NamedBlock({name:e,block:t,attrs:[],componentArgs:[],modifiers:[],loc:n})}simpleNamedBlock(e,t,r){return new i({selfClosing:!1,attrs:[],componentArgs:[],modifiers:[],comments:[]}).named(e,t,r)}slice(e,t){return new D.SourceSlice({loc:t,chars:e})}args(e,t,n){return new r.Args({loc:n,positional:e,named:t})}positional(e,t){return new r.PositionalArguments({loc:t,exprs:e})}namedArgument(e,t){return new r.NamedArgument({name:e,value:t})}named(e,t){return new r.NamedArguments({loc:t,entries:e})}attr({name:e,value:t,trusting:n},o){return new r.HtmlAttr({loc:o,name:e,value:t,trusting:n})}splatAttr(e,t){return new r.SplatAttr({symbol:e,loc:t})}arg({name:e,value:t,trusting:n},o){return new r.ComponentArg({name:e,value:t,trusting:n,loc:o})}path(e,t,n){return new r.PathExpression({loc:n,ref:e,tail:t})}self(e){return new r.ThisReference({loc:e})}at(e,t,n){return new r.ArgReference({loc:n,name:new D.SourceSlice({loc:n,chars:e}),symbol:t})}freeVar({name:e,context:t,symbol:n,loc:o}){return new r.FreeVarReference({name:e,resolution:t,symbol:n,loc:o})}localVar(e,t,n,o){return new r.LocalVarReference({loc:o,name:e,isTemplateLocal:n,symbol:t})}sexp(e,t){return new r.CallExpression({loc:t,callee:e.callee,args:e.args})}deprecatedCall(e,t,n){return new r.DeprecatedCallExpression({loc:n,arg:e,callee:t})}interpolate(e,t){return(0,_.assertPresent)(e),new r.InterpolateExpression({loc:t,parts:e})}literal(e,t){return new r.LiteralExpression({loc:t,value:e})}append({table:e,trusting:t,value:n},o){return new r.AppendContent({table:e,trusting:t,value:n,loc:o})}modifier({callee:e,args:t},n){return new r.ElementModifier({loc:n,callee:e,args:t})}namedBlocks(e,t){return new r.NamedBlocks({loc:t,blocks:e})}blockStatement(e,t){var{symbols:n,program:o,inverse:i=null}=e,a=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}(e,["symbols","program","inverse"]);let s=o.loc,l=[this.namedBlock(D.SourceSlice.synthetic("default"),o,o.loc)];return i&&(s=s.extend(i.loc),l.push(this.namedBlock(D.SourceSlice.synthetic("else"),i,i.loc))),new r.InvokeBlock({loc:t,blocks:this.namedBlocks(l,s),callee:a.callee,args:a.args})}element(e){return new i(e)}}t.Builder=o;class i{constructor(e){this.base=e,this.builder=new o}simple(e,t,n){return new r.SimpleElement((0,_.assign)({tag:e,body:t,componentArgs:[],loc:n},this.base))}named(e,t,n){return new r.NamedBlock((0,_.assign)({name:e,block:t,componentArgs:[],loc:n},this.base))}selfClosingComponent(e,t){return new r.InvokeComponent((0,_.assign)({loc:t,callee:e,blocks:new r.NamedBlocks({blocks:[],loc:t.sliceEndChars({skipEnd:1,chars:1})})},this.base))}componentWithDefaultBlock(e,t,n,o){let i=this.builder.block(n,t,o),a=this.builder.namedBlock(D.SourceSlice.synthetic("default"),i,o);return new r.InvokeComponent((0,_.assign)({loc:o,callee:e,blocks:this.builder.namedBlocks([a],a.loc)},this.base))}componentWithNamedBlocks(e,t,n){return new r.InvokeComponent((0,_.assign)({loc:n,callee:e,blocks:this.builder.namedBlocks(t,J.SpanList.range(t))},this.base))}}t.BuildElement=i})),Tn=function(e){return Bn(e)?Ln.LooseModeResolution.namespaced("Helper"):null},xn=function(e){return Bn(e)?Ln.LooseModeResolution.namespaced("Modifier"):null},Nn=function(e){return Bn(e)?Ln.LooseModeResolution.namespaced("Component"):Ln.LooseModeResolution.fallback()},On=function(e){return Dn(e)?Ln.LooseModeResolution.namespaced("Component",!0):null},An=function(e){let t=Bn(e),r=In(e);return t?r?Ln.LooseModeResolution.namespaced("Helper"):Ln.LooseModeResolution.attr():r?Ln.STRICT_RESOLUTION:Ln.LooseModeResolution.fallback()},Cn=function(e){let t=Bn(e),r=In(e),n=e.trusting;return t?n?Ln.LooseModeResolution.trustingAppend({invoke:r}):Ln.LooseModeResolution.append({invoke:r}):Ln.LooseModeResolution.fallback()},Ln=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_n();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}r.default=e,t&&t.set(e,r);return r}(re);function _n(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return _n=function(){return e},e}function Bn(e){return Dn(e.path)}function Dn(e){return"PathExpression"===e.type&&"VarHead"===e.head.type&&0===e.tail.length}function In(e){return e.params.length>0||e.hash.pairs.length>0}var Mn=Object.defineProperty({SexpSyntaxContext:Tn,ModifierSyntaxContext:xn,BlockSyntaxContext:Nn,ComponentSyntaxContext:On,AttrValueSyntaxContext:An,AppendSyntaxContext:Cn},"__esModule",{value:!0}),Rn=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.normalize=function(e,t={}){var r;let n=(0,wn.preprocess)(e,t),o=(0,_.assign)({strictMode:!1,locals:[]},t),i=En.SymbolTable.top(o.strictMode?o.locals:[],null!==(r=t.customizeComponentName)&&void 0!==r?r:e=>e),a=new s(e,o,i),l=new c(a),u=new p(a.loc(n.loc),n.body.map((e=>l.normalize(e))),a).assertTemplate(i),h=i.getUsedTemplateLocals();return[u,h]},t.BlockContext=void 0;var r=a(ir),n=a(vn),o=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=n?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}r.default=e,t&&t.set(e,r);return r}(re);function i(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return i=function(){return e},e}function a(e){return e&&e.__esModule?e:{default:e}}class s{constructor(e,t,r){this.source=e,this.options=t,this.table=r,this.builder=new Pn.Builder}get strict(){return this.options.strictMode||!1}loc(e){return this.source.spanFor(e)}resolutionFor(e,t){if(this.strict)return{resolution:o.STRICT_RESOLUTION};if(this.isFreeVar(e)){let r=t(e);return null===r?{resolution:"error",path:m(e),head:g(e)}:{resolution:r}}return{resolution:o.STRICT_RESOLUTION}}isFreeVar(e){return"PathExpression"===e.type?"VarHead"===e.head.type&&!this.table.has(e.head.name):"PathExpression"===e.path.type&&this.isFreeVar(e.path)}hasBinding(e){return this.table.has(e)}child(e){return new s(this.source,this.options,this.table.child(e))}customizeComponentName(e){return this.options.customizeComponentName?this.options.customizeComponentName(e):e}}t.BlockContext=s;class l{constructor(e){this.block=e}normalize(e,t){switch(e.type){case"NullLiteral":case"BooleanLiteral":case"NumberLiteral":case"StringLiteral":case"UndefinedLiteral":return this.block.builder.literal(e.value,this.block.loc(e.loc));case"PathExpression":return this.path(e,t);case"SubExpression":{let t=this.block.resolutionFor(e,Mn.SexpSyntaxContext);if("error"===t.resolution)throw(0,zr.generateSyntaxError)(`You attempted to invoke a path (\`${t.path}\`) but ${t.head} was not in scope`,e.loc);return this.block.builder.sexp(this.callParts(e,t.resolution),this.block.loc(e.loc))}}}path(e,t){let r=[],n=this.block.loc(e.head.loc);for(let t of e.tail)n=n.sliceStartChars({chars:t.length,skipStart:1}),r.push(new D.SourceSlice({loc:n,chars:t}));return this.block.builder.path(this.ref(e.head,t),r,this.block.loc(e.loc))}callParts(e,t){let{path:r,params:n,hash:i}=e,a=this.normalize(r,t),s=n.map((e=>this.normalize(e,o.ARGUMENT_RESOLUTION))),l=J.SpanList.range(s,a.loc.collapse("end")),c=this.block.loc(i.loc),u=J.SpanList.range([l,c]),h=this.block.builder.positional(n.map((e=>this.normalize(e,o.ARGUMENT_RESOLUTION))),l),p=this.block.builder.named(i.pairs.map((e=>this.namedArgument(e))),this.block.loc(i.loc));return{callee:a,args:this.block.builder.args(h,p,u)}}namedArgument(e){let t=this.block.loc(e.loc).sliceStartChars({chars:e.key.length});return this.block.builder.namedArgument(new D.SourceSlice({chars:e.key,loc:t}),this.normalize(e.value,o.ARGUMENT_RESOLUTION))}ref(e,t){let{block:r}=this,{builder:n,table:i}=r,a=r.loc(e.loc);switch(e.type){case"ThisHead":return n.self(a);case"AtHead":{let t=i.allocateNamed(e.name);return n.at(e.name,t,a)}case"VarHead":if(r.hasBinding(e.name)){let[t,n]=i.get(e.name);return r.builder.localVar(e.name,t,n,a)}{let n=r.strict?o.STRICT_RESOLUTION:t,i=r.table.allocateFree(e.name,n);return r.builder.freeVar({name:e.name,context:n,symbol:i,loc:a})}}}}class c{constructor(e){this.block=e}normalize(e){switch(e.type){case"PartialStatement":throw new Error("Handlebars partial syntax ({{> ...}}) is not allowed in Glimmer");case"BlockStatement":return this.BlockStatement(e);case"ElementNode":return new u(this.block).ElementNode(e);case"MustacheStatement":return this.MustacheStatement(e);case"MustacheCommentStatement":return this.MustacheCommentStatement(e);case"CommentStatement":{let t=this.block.loc(e.loc);return new o.HtmlComment({loc:t,text:t.slice({skipStart:4,skipEnd:3}).toSlice(e.value)})}case"TextNode":return new o.HtmlText({loc:this.block.loc(e.loc),chars:e.chars})}}MustacheCommentStatement(e){let t,r=this.block.loc(e.loc);return t="{{!--"===r.asString().slice(0,5)?r.slice({skipStart:5,skipEnd:4}):r.slice({skipStart:3,skipEnd:2}),new o.GlimmerComment({loc:r,text:t.toSlice(e.value)})}MustacheStatement(e){let{escaped:t}=e,r=this.block.loc(e.loc),n=this.expr.callParts({path:e.path,params:e.params,hash:e.hash},(0,Mn.AppendSyntaxContext)(e)),o=n.args.isEmpty()?n.callee:this.block.builder.sexp(n,r);return this.block.builder.append({table:this.block.table,trusting:!t,value:o},r)}BlockStatement(e){let{program:t,inverse:r}=e,n=this.block.loc(e.loc),o=this.block.resolutionFor(e,Mn.BlockSyntaxContext);if("error"===o.resolution)throw(0,zr.generateSyntaxError)(`You attempted to invoke a path (\`{{#${o.path}}}\`) but ${o.head} was not in scope`,n);let i=this.expr.callParts(e,o.resolution);return this.block.builder.blockStatement((0,_.assign)({symbols:this.block.table,program:this.Block(t),inverse:r?this.Block(r):null},i),n)}Block({body:e,loc:t,blockParams:r}){let n=this.block.child(r),o=new c(n);return new d(this.block.loc(t),e.map((e=>o.normalize(e))),this.block).assertBlock(n.table)}get expr(){return new l(this.block)}}class u{constructor(e){this.ctx=e}ElementNode(e){let{tag:t,selfClosing:r,comments:n}=e,o=this.ctx.loc(e.loc),[i,...a]=t.split("."),s=this.classifyTag(i,a,e.loc),l=e.attributes.filter((e=>"@"!==e.name[0])).map((e=>this.attr(e))),u=e.attributes.filter((e=>"@"===e.name[0])).map((e=>this.arg(e))),h=e.modifiers.map((e=>this.modifier(e))),p=this.ctx.child(e.blockParams),d=new c(p),m=e.children.map((e=>d.normalize(e))),g=this.ctx.builder.element({selfClosing:r,attrs:l,componentArgs:u,modifiers:h,comments:n.map((e=>new c(this.ctx).MustacheCommentStatement(e)))}),b=new f(g,o,m,this.ctx),y=this.ctx.loc(e.loc).sliceStartChars({chars:t.length,skipStart:1});if("ElementHead"===s)return":"===t[0]?b.assertNamedBlock(y.slice({skipStart:1}).toSlice(t.slice(1)),p.table):b.assertElement(y.toSlice(t),e.blockParams.length>0);if(e.selfClosing)return g.selfClosingComponent(s,o);{let r=b.assertComponent(t,p.table,e.blockParams.length>0);return g.componentWithNamedBlocks(s,r,o)}}modifier(e){let t=this.ctx.resolutionFor(e,Mn.ModifierSyntaxContext);if("error"===t.resolution)throw(0,zr.generateSyntaxError)(`You attempted to invoke a path (\`{{#${t.path}}}\`) as a modifier, but ${t.head} was not in scope. Try adding \`this\` to the beginning of the path`,e.loc);let r=this.expr.callParts(e,t.resolution);return this.ctx.builder.modifier(r,this.ctx.loc(e.loc))}mustacheAttr(e){let t=this.ctx.builder.sexp(this.expr.callParts(e,(0,Mn.AttrValueSyntaxContext)(e)),this.ctx.loc(e.loc));return t.args.isEmpty()?t.callee:t}attrPart(e){switch(e.type){case"MustacheStatement":return{expr:this.mustacheAttr(e),trusting:!e.escaped};case"TextNode":return{expr:this.ctx.builder.literal(e.chars,this.ctx.loc(e.loc)),trusting:!0}}}attrValue(e){switch(e.type){case"ConcatStatement":{let t=e.parts.map((e=>this.attrPart(e).expr));return{expr:this.ctx.builder.interpolate(t,this.ctx.loc(e.loc)),trusting:!1}}default:return this.attrPart(e)}}attr(e){if("...attributes"===e.name)return this.ctx.builder.splatAttr(this.ctx.table.allocateBlock("attrs"),this.ctx.loc(e.loc));let t=this.ctx.loc(e.loc),r=t.sliceStartChars({chars:e.name.length}).toSlice(e.name),n=this.attrValue(e.value);return this.ctx.builder.attr({name:r,value:n.expr,trusting:n.trusting},t)}maybeDeprecatedCall(e,t){if(this.ctx.strict)return null;if("MustacheStatement"!==t.type)return null;let{path:r}=t;if("PathExpression"!==r.type)return null;if("VarHead"!==r.head.type)return null;let{name:n}=r.head;if("has-block"===n||"has-block-params"===n)return null;if(this.ctx.hasBinding(n))return null;if(0!==r.tail.length)return null;if(0!==t.params.length||0!==t.hash.pairs.length)return null;let i=o.LooseModeResolution.attr(),a=this.ctx.builder.freeVar({name:n,context:i,symbol:this.ctx.table.allocateFree(n,i),loc:r.loc});return{expr:this.ctx.builder.deprecatedCall(e,a,t.loc),trusting:!1}}arg(e){let t=this.ctx.loc(e.loc),r=t.sliceStartChars({chars:e.name.length}).toSlice(e.name),n=this.maybeDeprecatedCall(r,e.value)||this.attrValue(e.value);return this.ctx.builder.arg({name:r,value:n.expr,trusting:n.trusting},t)}classifyTag(e,t,r){let o=(0,yn.isUpperCase)(e),i="@"===e[0]||"this"===e||this.ctx.hasBinding(e);if(this.ctx.strict&&!i){if(o)throw(0,zr.generateSyntaxError)(`Attempted to invoke a component that was not in scope in a strict mode template, \`<${e}>\`. If you wanted to create an element with that name, convert it to lowercase - \`<${e.toLowerCase()}>\``,r);return"ElementHead"}let a=i||o,s=r.sliceStartChars({skipStart:1,chars:e.length}),c=t.reduce(((e,t)=>e+1+t.length),0),u=s.getEnd().move(c),h=s.withEnd(u);if(a){let o=n.default.path({head:n.default.head(e,s),tail:t,loc:h}),i=this.ctx.resolutionFor(o,Mn.ComponentSyntaxContext);if("error"===i.resolution)throw(0,zr.generateSyntaxError)(`You attempted to invoke a path (\`<${i.path}>\`) but ${i.head} was not in scope`,r);return new l(this.ctx).normalize(o,i.resolution)}if(t.length>0)throw(0,zr.generateSyntaxError)(`You used ${e}.${t.join(".")} as a tag name, but ${e} is not in scope`,r);return"ElementHead"}get expr(){return new l(this.ctx)}}class h{constructor(e,t,r){this.loc=e,this.children=t,this.block=r,this.namedBlocks=t.filter((e=>e instanceof o.NamedBlock)),this.hasSemanticContent=Boolean(t.filter((e=>{if(e instanceof o.NamedBlock)return!1;switch(e.type){case"GlimmerComment":case"HtmlComment":return!1;case"HtmlText":return!/^\s*$/.exec(e.chars);default:return!0}})).length),this.nonBlockChildren=t.filter((e=>!(e instanceof o.NamedBlock)))}}class p extends h{assertTemplate(e){if((0,_.isPresent)(this.namedBlocks))throw(0,zr.generateSyntaxError)("Unexpected named block at the top-level of a template",this.loc);return this.block.builder.template(e,this.nonBlockChildren,this.block.loc(this.loc))}}class d extends h{assertBlock(e){if((0,_.isPresent)(this.namedBlocks))throw(0,zr.generateSyntaxError)("Unexpected named block nested in a normal block",this.loc);return this.block.builder.block(e,this.nonBlockChildren,this.loc)}}class f extends h{constructor(e,t,r,n){super(t,r,n),this.el=e}assertNamedBlock(e,t){if(this.el.base.selfClosing)throw(0,zr.generateSyntaxError)(`<:${e.chars}/> is not a valid named block: named blocks cannot be self-closing`,this.loc);if((0,_.isPresent)(this.namedBlocks))throw(0,zr.generateSyntaxError)(`Unexpected named block inside <:${e.chars}> named block: named blocks cannot contain nested named blocks`,this.loc);if(!(0,yn.isLowerCase)(e.chars))throw(0,zr.generateSyntaxError)(`<:${e.chars}> is not a valid named block, and named blocks must begin with a lowercase letter`,this.loc);if(this.el.base.attrs.length>0||this.el.base.componentArgs.length>0||this.el.base.modifiers.length>0)throw(0,zr.generateSyntaxError)(`named block <:${e.chars}> cannot have attributes, arguments, or modifiers`,this.loc);let r=J.SpanList.range(this.nonBlockChildren,this.loc);return this.block.builder.namedBlock(e,this.block.builder.block(t,this.nonBlockChildren,r),this.loc)}assertElement(e,t){if(t)throw(0,zr.generateSyntaxError)(`Unexpected block params in <${e}>: simple elements cannot have block params`,this.loc);if((0,_.isPresent)(this.namedBlocks)){let t=this.namedBlocks.map((e=>e.name));if(1===t.length)throw(0,zr.generateSyntaxError)(`Unexpected named block <:foo> inside <${e.chars}> HTML element`,this.loc);{let r=t.map((e=>`<:${e.chars}>`)).join(", ");throw(0,zr.generateSyntaxError)(`Unexpected named blocks inside <${e.chars}> HTML element (${r})`,this.loc)}}return this.el.simple(e,this.nonBlockChildren,this.loc)}assertComponent(e,t,r){if((0,_.isPresent)(this.namedBlocks)&&this.hasSemanticContent)throw(0,zr.generateSyntaxError)(`Unexpected content inside <${e}> component invocation: when using named blocks, the tag cannot contain other content`,this.loc);if((0,_.isPresent)(this.namedBlocks)){if(r)throw(0,zr.generateSyntaxError)(`Unexpected block params list on <${e}> component invocation: when passing named blocks, the invocation tag cannot take block params`,this.loc);let t=new Set;for(let e of this.namedBlocks){let r=e.name.chars;if(t.has(r))throw(0,zr.generateSyntaxError)(`Component had two named blocks with the same name, \`<:${r}>\`. Only one block with a given name may be passed`,this.loc);if("inverse"===r&&t.has("else")||"else"===r&&t.has("inverse"))throw(0,zr.generateSyntaxError)("Component has both <:else> and <:inverse> block. <:inverse> is an alias for <:else>",this.loc);t.add(r)}return this.namedBlocks}return[this.block.builder.namedBlock(D.SourceSlice.synthetic("default"),this.block.builder.block(t,this.nonBlockChildren,this.loc),this.loc)]}}function m(e){return"PathExpression"!==e.type&&"PathExpression"===e.path.type?m(e.path):new r.default({entityEncoding:"raw"}).print(e)}function g(e){if("PathExpression"!==e.type)return"PathExpression"===e.path.type?g(e.path):new r.default({entityEncoding:"raw"}).print(e);switch(e.head.type){case"AtHead":case"VarHead":return e.head.name;case"ThisHead":return"this"}}})),qn=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isKeyword=function(e){return e in r},t.KEYWORDS_TYPES=void 0;const r={component:["Call","Append","Block"],debugger:["Append"],"each-in":["Block"],each:["Block"],"has-block-params":["Call","Append"],"has-block":["Call","Append"],helper:["Call","Append"],if:["Call","Append","Block"],"in-element":["Block"],let:["Block"],"link-to":["Append","Block"],log:["Call","Append"],modifier:["Call"],mount:["Append"],mut:["Call","Append"],outlet:["Append"],"query-params":["Call"],readonly:["Call","Append"],unbound:["Call","Append"],unless:["Call","Append","Block"],with:["Block"],yield:["Append"]};t.KEYWORDS_TYPES=r})),jn=function(e,t={includeHtmlElements:!1,includeKeywords:!1}){const r=(0,wn.preprocess)(e),n=new Set,o=[];(0,Hn.default)(r,{Block:{enter({blockParams:e}){e.forEach((e=>{o.push(e)}))},exit({blockParams:e}){e.forEach((()=>{o.pop()}))}},ElementNode:{enter(e){e.blockParams.forEach((e=>{o.push(e)})),Vn(n,e,o,t)},exit({blockParams:e}){e.forEach((()=>{o.pop()}))}},PathExpression(e){Vn(n,e,o,t)}});let i=[];n.forEach((e=>i.push(e))),(null==t?void 0:t.includeKeywords)||(i=i.filter((e=>!(0,qn.isKeyword)(e))));return i},Hn=function(e){return e&&e.__esModule?e:{default:e}}(sn);function Vn(e,t,r,n){const o=function(e,t,r){if("PathExpression"===e.type){if("AtHead"===e.head.type||"ThisHead"===e.head.type)return;const r=e.head.name;if(-1===t.indexOf(r))return r}else if("ElementNode"===e.type){const{tag:n}=e,o=n.charAt(0);if(":"===o||"@"===o)return;if(!r.includeHtmlElements&&-1===n.indexOf(".")&&n.toLowerCase()===n)return;if("this."===n.substr(0,5))return;if(-1!==t.indexOf(n))return;return n}}(t,r,n);(Array.isArray(o)?o:[o]).forEach((t=>{void 0!==t&&"@"!==t[0]&&e.add(t.split(".")[0])}))}var Un=Object.defineProperty({getTemplateLocals:jn},"__esModule",{value:!0}),$n=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Source",{enumerable:!0,get:function(){return j.Source}}),Object.defineProperty(t,"builders",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(t,"normalize",{enumerable:!0,get:function(){return Rn.normalize}}),Object.defineProperty(t,"SymbolTable",{enumerable:!0,get:function(){return En.SymbolTable}}),Object.defineProperty(t,"BlockSymbolTable",{enumerable:!0,get:function(){return En.BlockSymbolTable}}),Object.defineProperty(t,"ProgramSymbolTable",{enumerable:!0,get:function(){return En.ProgramSymbolTable}}),Object.defineProperty(t,"generateSyntaxError",{enumerable:!0,get:function(){return zr.generateSyntaxError}}),Object.defineProperty(t,"preprocess",{enumerable:!0,get:function(){return wn.preprocess}}),Object.defineProperty(t,"print",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"sortByLoc",{enumerable:!0,get:function(){return or.sortByLoc}}),Object.defineProperty(t,"Walker",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"Path",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"traverse",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(t,"cannotRemoveNode",{enumerable:!0,get:function(){return Gr.cannotRemoveNode}}),Object.defineProperty(t,"cannotReplaceNode",{enumerable:!0,get:function(){return Gr.cannotReplaceNode}}),Object.defineProperty(t,"WalkerPath",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(t,"isKeyword",{enumerable:!0,get:function(){return qn.isKeyword}}),Object.defineProperty(t,"KEYWORDS_TYPES",{enumerable:!0,get:function(){return qn.KEYWORDS_TYPES}}),Object.defineProperty(t,"getTemplateLocals",{enumerable:!0,get:function(){return Un.getTemplateLocals}}),Object.defineProperty(t,"SourceSlice",{enumerable:!0,get:function(){return D.SourceSlice}}),Object.defineProperty(t,"SourceSpan",{enumerable:!0,get:function(){return q.SourceSpan}}),Object.defineProperty(t,"SpanList",{enumerable:!0,get:function(){return J.SpanList}}),Object.defineProperty(t,"maybeLoc",{enumerable:!0,get:function(){return J.maybeLoc}}),Object.defineProperty(t,"loc",{enumerable:!0,get:function(){return J.loc}}),Object.defineProperty(t,"hasSpan",{enumerable:!0,get:function(){return J.hasSpan}}),Object.defineProperty(t,"node",{enumerable:!0,get:function(){return K.node}}),t.ASTv2=t.AST=t.ASTv1=void 0;var r=h(V),n=u($);t.ASTv1=n,t.AST=n;var o=u(re);t.ASTv2=o;var i=h(Ur),a=h(ln),s=h(sn),l=h(Wr);function c(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return c=function(){return e},e}function u(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=c();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,t&&t.set(e,r),r}function h(e){return e&&e.__esModule?e:{default:e}}}));const zn=r.default,{locStart:Fn,locEnd:Gn}=o;function Kn(){return{name:"addBackslash",visitor:{TextNode(e){e.chars=e.chars.replace(/\\/,"\\\\")}}}}function Wn(e){const t=new zn(e),r=({line:e,column:r})=>t.indexForLocation({line:e-1,column:r});return()=>({name:"addOffset",visitor:{All(e){const{start:t,end:n}=e.loc;t.offset=r(t),n.offset=r(n)}}})}var Yn={parsers:{glimmer:{parse:function(e){const{preprocess:t}=$n;let r;try{r=t(e,{mode:"codemod",plugins:{ast:[Kn,Wn(e)]}})}catch(e){const t=function(e){const{location:t,hash:r}=e;if(t){const{start:e,end:r}=t;return"number"!=typeof r.line?{start:e}:t}if(r){const{loc:{last_line:e,last_column:t}}=r;return{start:{line:e,column:t+1}}}}(e);if(t)throw n(e.message,t);throw e}return r},astFormat:"glimmer",locStart:Fn,locEnd:Gn}}};export default Yn;
