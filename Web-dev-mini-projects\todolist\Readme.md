<h1>To Do List</h1>

<p>To Do list made using HTML, CSS,JS and Node.js.</p>

### To Do List :

<p>This app allows you to make a list of events you want to do and you can strikeout the events completed.</p>

<h3>Used Technologies</h3>
<ul>
  <li>HTML5</li>
  <li>CSS3</li>
  <li>JavaScript</li>
  <li>Node.js</li>
</ul>

#### Steps to Use:

---
* Download [Node Js and npm(Node package manager)](https://nodejs.org/en/) (when you install Node, npm also gets installed by default)
<br/>

* Clone the repository by running command
```
git clone https://github.com/Ayushparikh-code/Web-dev-mini-projects.git
```
in your git bash.
<br/>

* Run command `cd todolist`.
<br/>

* Run this command to install all dependencies for the project.
```npm install

```
*Run this command to run the file.
```
node app.js
```
* Open http://localhost:3000/ on your browser.
<br/>

<h3>ScreenShots</h3>
<br>
<img src="https://github.com/ayushseth07/Web-dev-mini-projects/blob/patch/todolist/images/main.PNG"/>
<img src="https://github.com/ayushseth07/Web-dev-mini-projects/blob/patch/todolist/images/work.PNG"/>
