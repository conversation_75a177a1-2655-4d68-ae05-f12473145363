*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body{
    font-weight: 400;
    font-size: 15px;
    line-height: 1.7;
    color: #fff;
    background-color: #1f2029;
    min-height: 100vh;
    overflow-x: none;
}
p{
    /* font-weight: 400; */
    font-weight:400;
    font-size: 16px;
    line-height: 1.7;
    color: #fff;
    margin: 0;
}
.section-fluid-main{
    position: relative;
    display: block;
    width: 100%;
    overflow-x: hidden;
}
.section{
    position: relative;
    max-width: calc(100%-40px);
    width: 860px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    padding: 100px 0;
}
.section-fluid{
    position: relative;
    width: 100%;
    display: block;
}

[type="radio"]:checked,
[type="radio"]:not(:checked){
    position: absolute;
    visibility: hidden;
}
.color-btn:checked + label,
.color-btn:not(:checked) +label{
    position: relative;
    height: 40px;
    transition: all 200ms linear;
    
    width: 40px;
    overflow: hidden;
   
    cursor: pointer;
    color: #ffeba7;
    margin-right: 10px;
    box-shadow: 0 12px 35px 0 rgba(16, 39, 112, 0.25);
    z-index: 10;
    background-position: center;
    background-size: cover;
    border: 3px solid transparent;
}

.color-btn:checked+label{
    border-color: #434343;
    transform: scale(1.1);
}

label.first-color{
    margin-left: 500px;
    background-image: url("./images/color1.jpg");
}
label.color-2{
    background-image: url("./images/color2.jpg");
}
label.color-3{
    background-image: url("./images/color3.jpg");
}
label.color-4{
    background-image: url("./images/color4.jpg");
}
label.color-5{
    background-image: url("./images/color5.jpg");
}
label.color-6{
    background-image: url("./images/color6.jpg");
}

.img-wrap{
    position: absolute;
    top: 100px;
    left: 0;
    width: 500px;
    height: 410px;
    display: inline-block;
    z-index: 9;
    transition: all 550ms linear;
    transition-delay: 100ms;
    background-position: center top;
    background-size: 100%;
    background-repeat: no-repeat;
    background-image: url("./images/chair1.png");
    opacity: 0;
}

.for-color-1:checked ~ .img-wrap.chair-1{
    opacity: 1;
    animation: shake 0.7s cubic-bezier(0.36,0.07 ,0.19, 0.97) both;
}
.img-wrap.chair-2{
    background-image: url("./images/chair2.png");
}
.for-color-2:checked ~ .img-wrap.chair-2{
    opacity: 1;
    animation: shake 0.7s cubic-bezier(0.36,0.07 ,0.19 ,0.97) both;
}
.img-wrap.chair-3{
    background-image: url("./images/chair3.png");
}
.for-color-3:checked ~ .img-wrap.chair-3{
    opacity: 1;
    animation: shake 0.7s cubic-bezier(0.36,0.07, 0.19 ,0.97) both;
}
.img-wrap.chair-4{
    background-image: url("./images/chair4.png");
}
.for-color-4:checked ~ .img-wrap.chair-4{
    opacity: 1;
    animation: shake 0.7s cubic-bezier(0.36,0.07, 0.19 ,0.97) both;
}
.img-wrap.chair-5{
    background-image: url("./images/chair5.png");
}
.for-color-5:checked ~ .img-wrap.chair-5{
    opacity: 1;
    animation: shake 0.7s cubic-bezier(0.36,0.07 ,0.19 ,0.97) both;
}
.img-wrap.chair-6{
    background-image: url("./images/chair6.png");
}
.for-color-6:checked ~ .img-wrap.chair-6{
    opacity: 1;
    animation: shake 0.7s cubic-bezier(0.36,0.07,0.19,0.97) both;
}
@keyframes shake {
    10%,
    90%{
        transform: translate3d(-1px,0,0) rotate(-1deg);
    }
    20%,
    80%{
        transform: translate3d(2px,0,0) rotate(2deg);
    }
    30%,
    50%,
    70%{
        transform: translate3d(-3px,0,0) rotate(-3deg);
    }
    40%,
    60%{
        transform: translate3d(3px,0,0) rotate(3deg);
    }
}

.back-color{
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: block;
    z-index: 1;
    background-image: linear-gradient(196deg,#f1a9a9,#e66767);
    transition: all 250ms linear;
    transition-delay: 300ms;
}

.back-color.chair-2{
    background-image: linear-gradient(196deg,#4c4c4c,#262626);
    opacity: 0;
}
.for-color-2:checked ~ .back-color.chair-2{
    opacity: 1;
}
.back-color.chair-3{
    background-image: linear-gradient(196deg,#8a9fb2,#5f7991);
    opacity: 0;
}
.for-color-3:checked ~ .back-color.chair-3{
    opacity: 1;
}
.back-color.chair-4{
    background-image: linear-gradient(196deg,#97afc3,#6789a7);
    opacity: 0;
}
.for-color-4:checked ~ .back-color.chair-4{
    opacity: 1;
}
.back-color.chair-5{
    background-image: linear-gradient(196deg,#afa6a0,#8c7f76);
    opacity: 0;
}
.for-color-5:checked ~ .back-color.chair-5{
    opacity: 1;
}
.back-color.chair-6{
    background-image: linear-gradient(196deg,#aaadac,#838786);
    opacity: 0;
}
.for-color-6:checked ~ .back-color.chair-6{
    opacity: 1;
}

.info-wrap{
    position: relative;
    margin-left: 500px;
    z-index: 10;
    display: block;
    text-align: left;
}
.title-up{
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 13px;
    line-height: 1.2;
    color: #fff;
    margin-top: 0;
    margin-bottom: 10px;
}
h2{
    font-weight: 800;
    font-size: 34px;
    line-height: 1.2;
    color: #fff;
    margin-top: 0;
    margin-bottom: 10px;
}
h4{
    font-weight: 500;
    font-size: 26px;
    line-height: 1.2;
    color: #fff;
    margin-top: 0;
    margin-bottom: 30px;
}
h4 span{
    text-decoration: line-through;
    font-size: 20px;
    opacity: 0.6;
    padding-left: 15px;
}
h5{
    font-weight: 600;
    font-size: 18px;
    line-height: 1.2;
    color: #fff;
    margin-top: 0;
    margin-bottom: 20px;
}

.desc-btn:checked + label,
.desc-btn:not(:checked) + label{
    position: relative;
    transition: all 200ms linear;
    display: inline-block;
    border: none;
    cursor: pointer;
    
    font-weight: 600;
    font-size: 18px;
    line-height: 1.2;
    color: #fff;
    margin-right: 25px;
    opacity: 0.5;
}
.desc-btn:checked +label{
    opacity: 1;
}
.desc-btn:checked:not(:checked){
    opacity: 0.8;
}
.desc-sec{
    padding-top: 20px;
    padding-bottom: 30px;
    transition: all 250ms linear;
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
    overflow: hidden;
}
.desc-sec.accor-2{
    position: absolute;
    top: 25px;
    left: 0;
    width: 100%;
    z-index: 2;
}
#desc-1:checked ~ .desc-sec.accor-1{
    opacity: 1;
    pointer-events: auto;
    transform: translateY(0);
}
#desc-2:checked ~ .desc-sec.accor-2{
    opacity: 1;
    pointer-events: auto;
    transform: translateY(0);
}
.section-inline{
    position: relative;
    display: inline-block;
    margin-right: 20px;
}
.section-inline p span{
    font-size: 30px;
    line-height: 1.1;
}
.btn{
    position: relative;
    font-weight: 500;
    font-size: 14px;
    line-height: 2;
    border-radius: 4px;
    height: 48px;
    width: 210px;
    letter-spacing: 1px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    overflow: hidden;
    background-color: transparent;
    color: #fff;
    transition: all 250ms linear;
    box-shadow: 0 6px 15px 0 rgba(16, 39, 112, 0.25);
    text-decoration: none;
    margin-top: 50px;
}
.btn::before{
    position: absolute;
    top: 0;
    left: 0;width: 100%;
    height: 100%;
    content: "";
    z-index: -1;
    background-color: #944852;
    transition: background-color 250ms 300ms ease;
}
.for-color-2:checked ~ .info-wrap .btn:before{
    background-color: #1a1a1a;
}
.for-color-3:checked ~ .info-wrap .btn:before{
    background-color: #40566e;
}
.for-color-4:checked ~ .info-wrap .btn:before{
    background-color: #5e89b2;
}
.for-color-5:checked ~ .info-wrap .btn:before{
    background-color: #8c7f76;
}
.for-color-6:checked ~ .info-wrap .btn:before{
    background-color: #5d6160;
}
.btn:hover{
    box-shadow: 0 12px 35px 0 rgba(16, 39, 112, 0.25);
    background-color: #333;
}
@media  screen and (max-width:991px) {
    .section{
        margin: 0 auto;
        text-align: center;
        
        max-width: calc(100%-40px);
        width: 370px;
    }
    label.first-color{
        margin-left: 0;
    }
    .info-wrap{
        
        width: 370px;
        margin: 0 auto;
        text-align: center;
    }
    .img-wrap{
        width: 375px;
        height: 308px;
        left: 50%;
        margin-left: -190px;
    }
    .mob-margin{
        margin-top: 320px;
    }
    .desc-btn:checked+label,
    .desc-btn:checked:not(:checked)+label{
        margin-right: 15px;
        margin-left: 15px;
    }
    .color-btn:checked+label,
    .desc-btn:checked:not(:checked)+label{
        height: 40px;
        width: 40px;
        margin: 5px auto;
        text-align: center;
    }
    .section-inline{
        margin: 0 5px;
    }
}

@media  screen and (max-width:575px) {
    .section{
        width: 280px;
    }
    .info-wrap{
        width: 280px;
    }
    .img-wrap{
        width: 375px;
        height: 308px;
        left: 50%;
        margin-left: -190px;
    }
    .color-btn:checked+label,
    .desc-btn:checked:not(:checked)+label{
        height: 30px;
        width: 30px;
    }
    .section-inline p span{
        font-size: 24px;
        line-height: 1.1;
    }
    .section-inline p{
        font-size: 14px;
    }
}
