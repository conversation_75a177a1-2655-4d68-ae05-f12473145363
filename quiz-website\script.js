// Quiz Data with High-Quality Questions
const quizData = [
    {
        question: "Which programming paradigm does JavaScript primarily support?",
        options: ["Object-oriented only", "Functional only", "Multi-paradigm", "Procedural only"],
        correct: 2,
        category: "Technology"
    },
    {
        question: "What is the time complexity of binary search algorithm?",
        options: ["O(n)", "O(log n)", "O(n²)", "O(1)"],
        correct: 1,
        category: "Computer Science"
    },
    {
        question: "Which planet has the most moons in our solar system?",
        options: ["Jupiter", "Saturn", "Neptune", "Uranus"],
        correct: 1,
        category: "Science"
    },
    {
        question: "What does 'HTTP' stand for in web development?",
        options: ["Hypertext Transfer Protocol", "High Tech Transfer Process", "Hyperlink Text Transfer Protocol", "Home Tool Transfer Protocol"],
        correct: 0,
        category: "Technology"
    },
    {
        question: "Which ancient wonder of the world was located in Alexandria?",
        options: ["Hanging Gardens", "Lighthouse of Alexandria", "Colossus of Rhodes", "Temple of Artemis"],
        correct: 1,
        category: "History"
    },
    {
        question: "What is the chemical symbol for gold?",
        options: ["Go", "Gd", "Au", "Ag"],
        correct: 2,
        category: "Science"
    },
    {
        question: "Which design pattern ensures a class has only one instance?",
        options: ["Factory", "Observer", "Singleton", "Strategy"],
        correct: 2,
        category: "Computer Science"
    },
    {
        question: "Who painted 'The Starry Night'?",
        options: ["Pablo Picasso", "<PERSON> van Gogh", "Claude Monet", "Leonardo da Vinci"],
        correct: 1,
        category: "Arts"
    },
    {
        question: "What is the largest organ in the human body?",
        options: ["Brain", "Liver", "Lungs", "Skin"],
        correct: 3,
        category: "Science"
    },
    {
        question: "Which country invented the World Wide Web?",
        options: ["United States", "United Kingdom", "Germany", "Switzerland"],
        correct: 3,
        category: "Technology"
    },
    {
        question: "What is the capital of Australia?",
        options: ["Sydney", "Melbourne", "Canberra", "Perth"],
        correct: 2,
        category: "Geography"
    },
    {
        question: "Which data structure uses LIFO (Last In, First Out) principle?",
        options: ["Queue", "Stack", "Array", "Linked List"],
        correct: 1,
        category: "Computer Science"
    },
    {
        question: "Who wrote the novel '1984'?",
        options: ["Aldous Huxley", "Ray Bradbury", "George Orwell", "H.G. Wells"],
        correct: 2,
        category: "Literature"
    },
    {
        question: "What is the speed of light in vacuum?",
        options: ["299,792,458 m/s", "300,000,000 m/s", "299,000,000 m/s", "298,792,458 m/s"],
        correct: 0,
        category: "Science"
    },
    {
        question: "Which programming language was created by Guido van Rossum?",
        options: ["Java", "Python", "Ruby", "JavaScript"],
        correct: 1,
        category: "Technology"
    }
];

// Quiz State
let currentQuestion = 0;
let score = 0;
let userAnswers = [];
let userName = '';
let categoryStats = {};

// DOM Elements
const welcomeScreen = document.getElementById('welcome-screen');
const quizScreen = document.getElementById('quiz-screen');
const resultsScreen = document.getElementById('results-screen');
const usernameInput = document.getElementById('username');
const startBtn = document.getElementById('start-btn');
const questionText = document.getElementById('question-text');
const questionCategory = document.getElementById('question-category');
const optionsContainer = document.getElementById('options-container');
const nextBtn = document.getElementById('next-btn');
const progressFill = document.getElementById('progress-fill');
const questionCounter = document.getElementById('question-counter');
const currentScoreDisplay = document.getElementById('current-score');
const finalScore = document.getElementById('final-score');
const percentageScore = document.getElementById('percentage-score');
const resultsTitle = document.getElementById('results-title');
const resultsMessage = document.getElementById('results-message');
const resultsEmoji = document.getElementById('results-emoji');
const categoryStatsContainer = document.getElementById('category-stats');
const restartBtn = document.getElementById('restart-btn');
const shareBtn = document.getElementById('share-btn');

// Initialize particles background
particlesJS('particles-js', {
    particles: {
        number: { value: 80, density: { enable: true, value_area: 800 } },
        color: { value: '#ffffff' },
        shape: { type: 'circle' },
        opacity: { value: 0.5, random: false },
        size: { value: 3, random: true },
        line_linked: { enable: true, distance: 150, color: '#ffffff', opacity: 0.4, width: 1 },
        move: { enable: true, speed: 6, direction: 'none', random: false, straight: false, out_mode: 'out', bounce: false }
    },
    interactivity: {
        detect_on: 'canvas',
        events: { onhover: { enable: true, mode: 'repulse' }, onclick: { enable: true, mode: 'push' }, resize: true },
        modes: { grab: { distance: 400, line_linked: { opacity: 1 } }, bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 3 }, repulse: { distance: 200, duration: 0.4 }, push: { particles_nb: 4 }, remove: { particles_nb: 2 } }
    },
    retina_detect: true
});

// Event Listeners
startBtn.addEventListener('click', startQuiz);
nextBtn.addEventListener('click', nextQuestion);
restartBtn.addEventListener('click', restartQuiz);
shareBtn.addEventListener('click', shareResults);

usernameInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        startQuiz();
    }
});

// Start Quiz Function
function startQuiz() {
    userName = usernameInput.value.trim();
    if (!userName) {
        usernameInput.style.borderColor = '#e74c3c';
        usernameInput.placeholder = 'Please enter your name';
        return;
    }
    
    // Reset quiz state
    currentQuestion = 0;
    score = 0;
    userAnswers = [];
    categoryStats = {};
    
    // Initialize category stats
    const categories = [...new Set(quizData.map(q => q.category))];
    categories.forEach(category => {
        categoryStats[category] = { correct: 0, total: 0 };
    });
    
    showScreen(quizScreen);
    loadQuestion();
}

// Show Screen Function
function showScreen(screen) {
    document.querySelectorAll('.screen').forEach(s => s.classList.remove('active'));
    screen.classList.add('active');
}

// Load Question Function
function loadQuestion() {
    const question = quizData[currentQuestion];
    
    questionText.textContent = question.question;
    questionCategory.textContent = question.category;
    
    // Update progress
    const progress = ((currentQuestion + 1) / quizData.length) * 100;
    progressFill.style.width = `${progress}%`;
    questionCounter.textContent = `${currentQuestion + 1} / ${quizData.length}`;
    currentScoreDisplay.textContent = `Score: ${score}`;
    
    // Load options
    optionsContainer.innerHTML = '';
    question.options.forEach((option, index) => {
        const optionElement = document.createElement('div');
        optionElement.className = 'option';
        optionElement.textContent = option;
        optionElement.addEventListener('click', () => selectOption(index));
        optionsContainer.appendChild(optionElement);
    });
    
    nextBtn.disabled = true;
}

// Select Option Function
function selectOption(selectedIndex) {
    const question = quizData[currentQuestion];
    const options = document.querySelectorAll('.option');
    
    // Remove previous selections
    options.forEach(option => {
        option.classList.remove('selected', 'correct', 'incorrect');
    });
    
    // Mark selected option
    options[selectedIndex].classList.add('selected');
    
    // Store answer
    userAnswers[currentQuestion] = selectedIndex;
    
    // Show correct answer after a delay
    setTimeout(() => {
        options[question.correct].classList.add('correct');
        if (selectedIndex !== question.correct) {
            options[selectedIndex].classList.add('incorrect');
        } else {
            score++;
            currentScoreDisplay.textContent = `Score: ${score}`;
        }
        
        // Update category stats
        categoryStats[question.category].total++;
        if (selectedIndex === question.correct) {
            categoryStats[question.category].correct++;
        }
        
        nextBtn.disabled = false;
        
        // Disable further clicking
        options.forEach(option => {
            option.style.pointerEvents = 'none';
        });
    }, 500);
}

// Next Question Function
function nextQuestion() {
    currentQuestion++;
    
    if (currentQuestion < quizData.length) {
        loadQuestion();
    } else {
        showResults();
    }
}

// Show Results Function
function showResults() {
    const percentage = Math.round((score / quizData.length) * 100);
    
    finalScore.textContent = score;
    percentageScore.textContent = `${percentage}%`;
    
    // Set results message and emoji based on performance
    let message, emoji, title;
    if (percentage >= 90) {
        title = `Outstanding, ${userName}!`;
        message = "You're a true quiz master! Your knowledge is exceptional.";
        emoji = "fas fa-crown";
        resultsEmoji.style.color = "#ffd700";
    } else if (percentage >= 75) {
        title = `Excellent work, ${userName}!`;
        message = "Great job! You have impressive knowledge across multiple domains.";
        emoji = "fas fa-trophy";
        resultsEmoji.style.color = "#ff6b6b";
    } else if (percentage >= 60) {
        title = `Good effort, ${userName}!`;
        message = "Well done! You have solid knowledge with room for improvement.";
        emoji = "fas fa-medal";
        resultsEmoji.style.color = "#4ecdc4";
    } else {
        title = `Keep learning, ${userName}!`;
        message = "Every expert was once a beginner. Keep exploring and learning!";
        emoji = "fas fa-book";
        resultsEmoji.style.color = "#45b7d1";
    }
    
    resultsTitle.textContent = title;
    resultsMessage.textContent = message;
    resultsEmoji.className = emoji;
    
    // Show category breakdown
    displayCategoryStats();
    
    showScreen(resultsScreen);
}

// Display Category Stats Function
function displayCategoryStats() {
    categoryStatsContainer.innerHTML = '';
    
    Object.entries(categoryStats).forEach(([category, stats]) => {
        const percentage = stats.total > 0 ? Math.round((stats.correct / stats.total) * 100) : 0;
        
        const statElement = document.createElement('div');
        statElement.className = 'category-stat';
        statElement.innerHTML = `
            <span>${category}</span>
            <span>${stats.correct}/${stats.total} (${percentage}%)</span>
        `;
        categoryStatsContainer.appendChild(statElement);
    });
}

// Restart Quiz Function
function restartQuiz() {
    showScreen(welcomeScreen);
    usernameInput.value = '';
    usernameInput.style.borderColor = '#e0e0e0';
    usernameInput.placeholder = 'Enter your name';
}

// Share Results Function
function shareResults() {
    const percentage = Math.round((score / quizData.length) * 100);
    const text = `I just scored ${score}/${quizData.length} (${percentage}%) on QuizMaster Pro! 🧠✨ Test your knowledge too!`;
    
    if (navigator.share) {
        navigator.share({
            title: 'QuizMaster Pro Results',
            text: text,
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(text).then(() => {
            shareBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            setTimeout(() => {
                shareBtn.innerHTML = '<i class="fas fa-share"></i> Share Results';
            }, 2000);
        });
    }
}

// Add some interactive animations
document.addEventListener('DOMContentLoaded', () => {
    // Add hover effects to buttons
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', () => {
            btn.style.transform = 'translateY(-2px)';
        });
        
        btn.addEventListener('mouseleave', () => {
            if (!btn.disabled) {
                btn.style.transform = 'translateY(0)';
            }
        });
    });
    
    // Add typing effect to tagline
    const tagline = document.querySelector('.tagline');
    const text = tagline.textContent;
    tagline.textContent = '';
    
    let i = 0;
    const typeWriter = () => {
        if (i < text.length) {
            tagline.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, 50);
        }
    };
    
    setTimeout(typeWriter, 1000);
});
