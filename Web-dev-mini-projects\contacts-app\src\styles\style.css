* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: "Inter", sans-serif;
    background-color: whitesmoke;
}

img {
    width: 100%;
}

.contact-app {
    max-width: 800px;
    margin: 0 auto;
    padding: 16px;
}

.contact-app__header {
    display: flex;
    align-items: center;
    padding: 32px 0;
}

.contact-app__header h1 {
    font-weight: normal;
    font-size: 32px;
    flex: 1;
}

.navigation ul {
    display: flex;
    align-items: center;
}

.navigation ul li {
    display: inline-block;
    padding: 16px;
}

.navigation ul li a {
    font-size: 28px;
    color: black;
}

.navigation ul li button {
    background-color: transparent;
    font-family: "Inter", sans-serif;
    border: 0;
    font-size: 28px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-item {
    display: flex;
    align-items: center;
    margin: 24px 0;
    border: 2px dashed black;
    padding: 16px;
    border-radius: 8px;
}

.contact-item__image img {
    width: 64px;
    border-radius: 50%;
}

.contact-item__body {
    margin-left: 8px;
    padding-left: 8px;
    border-left: 1px solid #aaa;
    flex: 1;
}

.contact-item__title {
    padding: 4px 0;
}

.contact-item__username {
    font-weight: lighter;
}

.contact-item__delete {
    padding: 8px;
    font-size: 28px;
    color: red;
    background-color: transparent;
    border: 0;
    border-radius: 4px;
    cursor: pointer;
}

.search-bar {
    display: block;
    width: 100%;
    margin: 16px 0;
    padding: 12px;
    background-color: transparent;
    font-family: "Inter", sans-serif;
    border: 2px dashed black;
    border-radius: 4px;
}

.login-page h2,
.register-page h2 {
    font-weight: normal;
}

.login-input,
.register-input,
.contact-input {
    margin-top: 16px;
}

.login-input input,
.register-input input,
.contact-input input {
    display: block;
    padding: 16px;
    width: 100%;
    margin: 8px 0;
    font-size: 24px;
    background-color: transparent;
    border: 2px dashed black;
    border-radius: 8px;
}

.login-input button,
.register-input button,
.contact-input button {
    display: block;
    width: 100%;
    padding: 16px;
    font-size: 24px;
    background-color: tan;
    border: 2px dashed black;
    border-radius: 8px;
    margin: 16px 0;
}
