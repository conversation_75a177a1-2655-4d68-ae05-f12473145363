/* underscore in name -> watch for changes */
export const paramsList = [
    'init',
    '_direction',
    'touchEventsTarget',
    'initialSlide',
    '_speed',
    'cssMode',
    'updateOnWindowResize',
    'resizeObserver',
    'nested',
    'focusableElements',
    '_width',
    '_height',
    'preventInteractionOnTransition',
    'userAgent',
    'url',
    '_edgeSwipeDetection',
    '_edgeSwipeThreshold',
    '_freeMode',
    '_freeModeMomentum',
    '_freeModeMomentumRatio',
    '_freeModeMomentumBounce',
    '_freeModeMomentumBounceRatio',
    '_freeModeMomentumVelocityRatio',
    '_freeModeSticky',
    '_freeModeMinimumVelocity',
    '_autoHeight',
    'setWrapperSize',
    'virtualTranslate',
    '_effect',
    'breakpoints',
    '_spaceBetween',
    '_slidesPerView',
    '_slidesPerColumn',
    '_slidesPerColumnFill',
    '_slidesPerGroup',
    '_slidesPerGroupSkip',
    '_centeredSlides',
    '_centeredSlidesBounds',
    '_slidesOffsetBefore',
    '_slidesOffsetAfter',
    'normalizeSlideIndex',
    '_centerInsufficientSlides',
    '_watchOverflow',
    'roundLengths',
    'touchRatio',
    'touchAngle',
    'simulateTouch',
    '_shortSwipes',
    '_longSwipes',
    'longSwipesRatio',
    'longSwipesMs',
    '_followFinger',
    'allowTouchMove',
    '_threshold',
    'touchMoveStopPropagation',
    'touchStartPreventDefault',
    'touchStartForcePreventDefault',
    'touchReleaseOnEdges',
    'uniqueNavElements',
    '_resistance',
    '_resistanceRatio',
    '_watchSlidesProgress',
    '_watchSlidesVisibility',
    '_grabCursor',
    'preventClicks',
    'preventClicksPropagation',
    '_slideToClickedSlide',
    '_preloadImages',
    'updateOnImagesReady',
    '_loop',
    '_loopAdditionalSlides',
    '_loopedSlides',
    '_loopFillGroupWithBlank',
    'loopPreventsSlide',
    '_allowSlidePrev',
    '_allowSlideNext',
    '_swipeHandler',
    '_noSwiping',
    'noSwipingClass',
    'noSwipingSelector',
    'passiveListeners',
    'containerModifierClass',
    'slideClass',
    'slideBlankClass',
    'slideActiveClass',
    'slideDuplicateActiveClass',
    'slideVisibleClass',
    'slideDuplicateClass',
    'slideNextClass',
    'slideDuplicateNextClass',
    'slidePrevClass',
    'slideDuplicatePrevClass',
    'wrapperClass',
    'runCallbacksOnInit',
    'observer',
    'observeParents',
    'observeSlideChildren',
    // modules
    'a11y',
    'autoplay',
    '_controller',
    'coverflowEffect',
    'cubeEffect',
    'fadeEffect',
    'flipEffect',
    'hashNavigation',
    'history',
    'keyboard',
    'lazy',
    'mousewheel',
    '_navigation',
    '_pagination',
    'parallax',
    '_scrollbar',
    '_thumbs',
    'virtual',
    'zoom',
];
//# sourceMappingURL=data:application/json;base64,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