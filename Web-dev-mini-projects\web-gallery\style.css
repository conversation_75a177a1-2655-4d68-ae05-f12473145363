/* Global Reset */
html {
    box-sizing: border-box;
    background: yellow;
    font-family: <PERSON>, <PERSON><PERSON><PERSON>weiler, "Arial Narrow Bold", sans-serif;
    font-size: 20px;
    font-weight: 200;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

/* Body Reset */
body {
    margin: 0;
}

/* Container for Photos */
.photos {
    min-height: 100vh;
    overflow: hidden;
    display: flex;
}

/* Individual Photo Styling */
.photo {
    color: rgb(8, 3, 75);
    background: #6b0f9c;
    box-shadow: inset 0 0 0 5px rgba(12, 21, 105, 0.1);
    flex: 1;
    text-align: center;
    align-items: center;
    font-size: 20px;
    background-size: cover;
    background-position: center;
    justify-content: center;
    display: flex;
    flex-direction
