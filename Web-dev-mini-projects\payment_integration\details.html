<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Personal Details</title>
    <link rel="stylesheet" href="detail.css" />
  </head>
  <body>
    <div class="heading">
      <h2>FILL THE DETAILS</h2>
    </div>
    <div id="smart-button-container">
      <div style="text-align: center">
        <label for="description">Pay to </label><input type="text" name="descriptionInput" id="description" maxlength="127" value="" />
      </div>
      <p id="descriptionError" style="visibility: hidden; color:red; text-align: center;"> Please enter a description </p>
      <div style="text-align: center">
        <label for="amount">Amount </label><input name="amountInput" type="number" id="amount" value="" /><span> USD</span>
      </div>
      <p id="priceLabelError" style="visibility: hidden; color:red; text-align: center;"> Please enter a price </p>
      <div id="invoiceidDiv" style="text-align: center; display: none;">
        <label for="invoiceid"> </label><input name="invoiceid" maxlength="127" type="text" id="invoiceid" value="" />
      </div>
      <p id="invoiceidError" style="visibility: hidden; color:red; text-align: center;"> Please enter an Invoice ID </p>
      <div style="text-align: center; margin-top: 0.625rem;" id="paypal-button-container"></div>
    </div>
    <script src="https://www.paypal.com/sdk/js?client-id=AZL8xbEw0LMzsy0E9tBt3LIfYcL3y-1v35KBvvLcKexPocsuRElvdeTYRLJJCzDS02Vor0fx_3TjsMQD&currency=USD" data-sdk-integration-source="button-factory"></script>
    <script src="payment.js"></script>
  </body>
</html>

