<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
        }
        #qrcode {
            margin-top: 20px;
        }
        input[type="text"] {
            width: 80%;
            padding: 10px;
            font-size: 16px;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            margin-top: 20px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>QR Code Generator</h1>
    <input type="text" id="textInput" placeholder="Enter text or URL" />
    <br/>
    <button onclick="generateQRCode()">Generate QR Code</button>
    <div id="qrcode"></div>

    <!-- Include the QRCode.js library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs/qrcode.min.js"></script>

    <script>
        // Function to generate the QR code
        function generateQRCode() {
            // Get the input value
            const text = document.getElementById('textInput').value;

            // Clear any previous QR codes
            document.getElementById('qrcode').innerHTML = '';

            // Generate a new QR code
            if (text) {
                new QRCode(document.getElementById('qrcode'), text);
            } else {
                alert("Please enter some text or URL!");
            }
        }
    </script>
</body>
</html>
