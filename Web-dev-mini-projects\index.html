<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Draggable Navigation Bar</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            overflow: hidden; /* Prevent scrollbars */
        }

        .nav-bar {
            width: 200px;
            background: #007BFF;
            color: white;
            padding: 15px;
            border-radius: 8px;
            position: absolute; /* Position for dragging */
            cursor: move; /* Cursor style for dragging */
        }

        .nav-bar h2 {
            margin: 0 0 15px; /* Updated margin */
            font-size: 18px;
        }

        .nav-bar ul {
            list-style-type: none;
            padding: 0;
        }

        .nav-bar ul li {
            margin: 10px 0;
        }

        .nav-bar ul li a {
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="nav-bar" id="navBar">
        <h2>Navigation</h2>
        <ul>
            <li><a href="#">Home</a></li>
            <li><a href="#">About</a></li>
            <li><a href="#">Services</a></li>
            <li><a href="#">Contact</a></li>
        </ul>
    </div>

    <script>
        const navBar = document.getElementById('navBar');

        // Variables to store the current mouse position
        let offsetX, offsetY;

        navBar.addEventListener('mousedown', function(e) {
            // Get the current mouse position relative to the nav bar
            offsetX = e.clientX - navBar.getBoundingClientRect().left;
            offsetY = e.clientY - navBar.getBoundingClientRect().top;

            // Add mousemove event to the document
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });

        function onMouseMove(e) {
            // Update the position of the nav bar
            navBar.style.left = (e.clientX - offsetX) + 'px';
            navBar.style.top = (e.clientY - offsetY) + 'px';
        }

        function onMouseUp() {
            // Remove mousemove and mouseup events
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        }
    </script>
</body>
</html>
