(function() {
var exports = {};
exports.id = "pages/api/popular";
exports.ids = ["pages/api/popular"];
exports.modules = {

/***/ "./config/genres.ts":
/*!**************************!*\
  !*** ./config/genres.ts ***!
  \**************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "genres": function() { return /* binding */ genres; }
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./types/index.ts");

const genres = {
  [_types__WEBPACK_IMPORTED_MODULE_0__.MediaType.MOVIE]: [{
    id: 28,
    name: 'Action'
  }, {
    id: 12,
    name: 'Adventure'
  }, {
    id: 16,
    name: 'Animation'
  }, {
    id: 35,
    name: 'Comedy'
  }, {
    id: 80,
    name: 'Crime'
  }, {
    id: 99,
    name: 'Documentary'
  }, {
    id: 18,
    name: 'Drama'
  }, {
    id: 10751,
    name: 'Family'
  }, {
    id: 14,
    name: 'Fantasy'
  }, {
    id: 36,
    name: 'History'
  }, {
    id: 27,
    name: 'Horror'
  }, {
    id: 10402,
    name: 'Music'
  }, {
    id: 9648,
    name: 'Mystery'
  }, {
    id: 10749,
    name: 'Romance'
  }, {
    id: 878,
    name: 'Science Fiction'
  }, {
    id: 10770,
    name: 'TV Movie'
  }, {
    id: 53,
    name: 'Thriller'
  }, {
    id: 10752,
    name: 'War'
  }, {
    id: 37,
    name: 'Western'
  }],
  [_types__WEBPACK_IMPORTED_MODULE_0__.MediaType.TV]: [{
    id: 10759,
    name: 'Adventure'
  }, {
    id: 16,
    name: 'Animation'
  }, {
    id: 35,
    name: 'Comedy'
  }, {
    id: 80,
    name: 'Crime'
  }, {
    id: 99,
    name: 'Documentary'
  }, {
    id: 18,
    name: 'Drama'
  }, {
    id: 10751,
    name: 'Family'
  }, {
    id: 10762,
    name: 'Kids'
  }, {
    id: 9648,
    name: 'Mystery'
  }, {
    id: 10763,
    name: 'News'
  }, {
    id: 10764,
    name: 'Reality'
  }, {
    id: 10765,
    name: 'Sci-Fi & Fantasy'
  }, {
    id: 10766,
    name: 'Soap'
  }, {
    id: 10767,
    name: 'Talk'
  }, {
    id: 10768,
    name: 'War & Politics'
  }, {
    id: 37,
    name: 'Western'
  }]
};

/***/ }),

/***/ "./pages/api/popular.ts":
/*!******************************!*\
  !*** ./pages/api/popular.ts ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ handler; }
/* harmony export */ });
/* harmony import */ var _utils_axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/axios */ "./utils/axios.ts");
/* harmony import */ var _utils_apiResolvers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/apiResolvers */ "./utils/apiResolvers.ts");


const apiKey = process.env.TMDB_KEY;
async function handler(request, response) {
  const {
    type
  } = request.query;

  try {
    const result = await (0,_utils_axios__WEBPACK_IMPORTED_MODULE_0__.default)().get(`/${type}/popular`, {
      params: {
        api_key: apiKey,
        watch_region: 'US',
        language: 'en-US'
      }
    });
    const data = (0,_utils_apiResolvers__WEBPACK_IMPORTED_MODULE_1__.parse)(result.data.results, type);
    response.status(200).json({
      type: 'Success',
      data
    });
  } catch (error) {
    console.log(error.data);
    response.status(500).json({
      type: 'Error',
      data: error.data
    });
  }
}

/***/ }),

/***/ "./types/index.ts":
/*!************************!*\
  !*** ./types/index.ts ***!
  \************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "MediaType": function() { return /* binding */ MediaType; }
/* harmony export */ });
let MediaType;

(function (MediaType) {
  MediaType["MOVIE"] = "movie";
  MediaType["TV"] = "tv";
})(MediaType || (MediaType = {}));

/***/ }),

/***/ "./utils/apiResolvers.ts":
/*!*******************************!*\
  !*** ./utils/apiResolvers.ts ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "parse": function() { return /* binding */ parse; }
/* harmony export */ });
/* harmony import */ var _config_genres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/genres */ "./config/genres.ts");

function parse(array, type) {
  const parsedResponse = [];
  array.forEach(element => {
    const resolved = {
      id: element.id,
      title: element.name || element.title,
      rating: element.vote_average,
      overview: element.overview,
      poster: getImageUrl(element.poster_path, 'poster'),
      banner: getImageUrl(element.backdrop_path, 'original'),
      genre: getGenre(element.genre_ids, type)
    };
    parsedResponse.push(resolved);
  });
  return parsedResponse;
}

function getImageUrl(path, type) {
  const dimension = type === 'poster' ? 'w500' : 'original';
  return `https://image.tmdb.org/t/p/${dimension}${path}`;
}

function getGenre(genreIds, type) {
  const result = _config_genres__WEBPACK_IMPORTED_MODULE_0__.genres[type].filter(item => genreIds.includes(item.id));

  if (result.length > 3) {
    return result.slice(0, 3);
  }

  return result;
}

/***/ }),

/***/ "./utils/axios.ts":
/*!************************!*\
  !*** ./utils/axios.ts ***!
  \************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ getInstance; }
/* harmony export */ });
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ "axios");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(axios__WEBPACK_IMPORTED_MODULE_0__);

function getInstance() {
  return axios__WEBPACK_IMPORTED_MODULE_0___default().create({
    baseURL: 'https://api.themoviedb.org/3'
  });
}

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ (function(module) {

"use strict";
module.exports = require("axios");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
var __webpack_exports__ = (__webpack_exec__("./pages/api/popular.ts"));
module.exports = __webpack_exports__;

})();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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