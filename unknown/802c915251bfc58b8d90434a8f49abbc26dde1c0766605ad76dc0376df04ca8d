module.exports={A:{A:{"1":"A B","2":"J D E F hB"},B:{"1":"C K L G M N O R S T U V W X Y Z P a H"},C:{"1":"0 1 2 3 4 5 6 7 8 9 K L G M N O c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB ZB GB aB Q HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB bB R S T jB U V W X Y Z P a H cB","2":"iB YB I b kB lB","36":"J D E F A B C"},D:{"1":"0 1 2 3 4 5 6 7 8 9 d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB ZB GB aB Q HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB bB R S T U V W X Y Z P a H cB mB nB oB","2":"I b J D","36":"E F A B C K L G M N O c"},E:{"1":"J D E F A B C K L G rB sB tB eB WB XB uB vB wB","2":"I b pB dB qB"},F:{"1":"0 1 2 3 4 5 6 7 8 9 G M N O c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB Q HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB XB","2":"F B C xB yB zB 0B WB fB 1B"},G:{"1":"E 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC KC","2":"dB 2B gB 3B"},H:{"2":"LC"},I:{"1":"H","2":"MC NC OC","36":"YB I PC gB QC RC"},J:{"1":"A","2":"D"},K:{"1":"Q XB","2":"A B C WB fB"},L:{"1":"H"},M:{"1":"P"},N:{"1":"A B"},O:{"1":"SC"},P:{"1":"I TC UC VC WC XC eB YC ZC aC bC"},Q:{"1":"cC"},R:{"1":"dC"},S:{"1":"eC"}},B:5,C:"Blob constructing"};
