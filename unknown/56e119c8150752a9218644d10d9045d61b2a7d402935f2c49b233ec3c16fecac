module.exports={A:{A:{"1":"A B","2":"J D hB","132":"E F"},B:{"1":"C K L G M N O R S T U V W X Y Z P a H"},C:{"1":"0 1 2 3 4 5 6 7 8 9 I b J D E F A B C K L G M N O c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB ZB GB aB Q HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB bB R S T jB U V W X Y Z P a H cB","2":"iB YB kB lB"},D:{"1":"0 1 2 3 4 5 6 7 8 9 I b J D E F A B C K L G M N O c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB ZB GB aB Q HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB bB R S T U V W X Y Z P a H cB mB nB oB"},E:{"1":"I b J D E F A B C K L G pB dB qB rB sB tB eB WB XB uB vB wB"},F:{"1":"0 1 2 3 4 5 6 7 8 9 B C G M N O c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB Q HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB fB 1B XB","2":"F xB yB zB 0B"},G:{"1":"dB 2B gB 3B","513":"E 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC KC"},H:{"4097":"LC"},I:{"1025":"YB I H MC NC OC PC gB QC RC"},J:{"258":"D A"},K:{"2":"A","258":"B C Q WB fB XB"},L:{"1025":"H"},M:{"2049":"P"},N:{"258":"A B"},O:{"258":"SC"},P:{"1025":"I TC UC VC WC XC eB YC ZC aC bC"},Q:{"1":"cC"},R:{"1025":"dC"},S:{"1":"eC"}},B:1,C:"Basic console logging functions"};
