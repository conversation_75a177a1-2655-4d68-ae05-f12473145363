var e=function(e,u){const t=new SyntaxError(e+" ("+u.start.line+":"+u.start.column+")");return t.loc=u,t};var u=function(...e){let u;for(const[t,n]of e.entries())try{return{result:n()}}catch(e){0===t&&(u=e)}return{error:u}},t=e=>"string"==typeof e?e.replace((({onlyFirst:e=!1}={})=>{const u=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(u,e?void 0:"g")})(),""):e;const n=e=>!Number.isNaN(e)&&(e>=4352&&(e<=4447||9001===e||9002===e||11904<=e&&e<=12871&&12351!==e||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141));var r=n,o=n;r.default=o;const i=e=>{if("string"!=typeof e||0===e.length)return 0;if(0===(e=t(e)).length)return 0;e=e.replace(/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g,"  ");let u=0;for(let t=0;t<e.length;t++){const n=e.codePointAt(t);n<=31||n>=127&&n<=159||(n>=768&&n<=879||(n>65535&&t++,u+=r(n)?2:1))}return u};var s=i,a=i;s.default=a;var D=e=>{if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")};var c=e=>e[e.length-1];function l(e,u){if(null==e)return{};var t,n,r=function(e,u){if(null==e)return{};var t,n,r={},o=Object.keys(e);for(n=0;n<o.length;n++)t=o[n],u.indexOf(t)>=0||(r[t]=e[t]);return r}(e,u);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)t=o[n],u.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(e,t)&&(r[t]=e[t])}return r}var p="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function f(e){return e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function d(e){var u={exports:{}};return e(u,u.exports),u.exports}var F=function(e){return e&&e.Math==Math&&e},E=F("object"==typeof globalThis&&globalThis)||F("object"==typeof window&&window)||F("object"==typeof self&&self)||F("object"==typeof p&&p)||function(){return this}()||Function("return this")(),C=function(e){try{return!!e()}catch(e){return!0}},A=!C((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),g={}.propertyIsEnumerable,m=Object.getOwnPropertyDescriptor,h={f:m&&!g.call({1:2},1)?function(e){var u=m(this,e);return!!u&&u.enumerable}:g},y=function(e,u){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:u}},B={}.toString,k=function(e){return B.call(e).slice(8,-1)},b="".split,P=C((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==k(e)?b.call(e,""):Object(e)}:Object,x=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},v=function(e){return P(x(e))},w=function(e){return"object"==typeof e?null!==e:"function"==typeof e},S=function(e,u){if(!w(e))return e;var t,n;if(u&&"function"==typeof(t=e.toString)&&!w(n=t.call(e)))return n;if("function"==typeof(t=e.valueOf)&&!w(n=t.call(e)))return n;if(!u&&"function"==typeof(t=e.toString)&&!w(n=t.call(e)))return n;throw TypeError("Can't convert object to primitive value")},T=function(e){return Object(x(e))},N={}.hasOwnProperty,I=Object.hasOwn||function(e,u){return N.call(T(e),u)},L=E.document,O=w(L)&&w(L.createElement),R=!A&&!C((function(){return 7!=Object.defineProperty((e="div",O?L.createElement(e):{}),"a",{get:function(){return 7}}).a;var e})),q=Object.getOwnPropertyDescriptor,j={f:A?q:function(e,u){if(e=v(e),u=S(u,!0),R)try{return q(e,u)}catch(e){}if(I(e,u))return y(!h.f.call(e,u),e[u])}},V=function(e){if(!w(e))throw TypeError(String(e)+" is not an object");return e},M=Object.defineProperty,$={f:A?M:function(e,u,t){if(V(e),u=S(u,!0),V(t),R)try{return M(e,u,t)}catch(e){}if("get"in t||"set"in t)throw TypeError("Accessors not supported");return"value"in t&&(e[u]=t.value),e}},U=A?function(e,u,t){return $.f(e,u,y(1,t))}:function(e,u,t){return e[u]=t,e},G=function(e,u){try{U(E,e,u)}catch(t){E[e]=u}return u},_=E["__core-js_shared__"]||G("__core-js_shared__",{}),X=Function.toString;"function"!=typeof _.inspectSource&&(_.inspectSource=function(e){return X.call(e)});var H,z,J,W,Y=_.inspectSource,K=E.WeakMap,Z="function"==typeof K&&/native code/.test(Y(K)),Q=d((function(e){(e.exports=function(e,u){return _[e]||(_[e]=void 0!==u?u:{})})("versions",[]).push({version:"3.14.0",mode:"global",copyright:"\xa9 2021 Denis Pushkarev (zloirock.ru)"})})),ee=0,ue=Math.random(),te=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++ee+ue).toString(36)},ne=Q("keys"),re={},oe=E.WeakMap;if(Z||_.state){var ie=_.state||(_.state=new oe),se=ie.get,ae=ie.has,De=ie.set;H=function(e,u){if(ae.call(ie,e))throw new TypeError("Object already initialized");return u.facade=e,De.call(ie,e,u),u},z=function(e){return se.call(ie,e)||{}},J=function(e){return ae.call(ie,e)}}else{var ce=ne[W="state"]||(ne[W]=te(W));re[ce]=!0,H=function(e,u){if(I(e,ce))throw new TypeError("Object already initialized");return u.facade=e,U(e,ce,u),u},z=function(e){return I(e,ce)?e[ce]:{}},J=function(e){return I(e,ce)}}var le,pe,fe={set:H,get:z,has:J,enforce:function(e){return J(e)?z(e):H(e,{})},getterFor:function(e){return function(u){var t;if(!w(u)||(t=z(u)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return t}}},de=d((function(e){var u=fe.get,t=fe.enforce,n=String(String).split("String");(e.exports=function(e,u,r,o){var i,s=!!o&&!!o.unsafe,a=!!o&&!!o.enumerable,D=!!o&&!!o.noTargetGet;"function"==typeof r&&("string"!=typeof u||I(r,"name")||U(r,"name",u),(i=t(r)).source||(i.source=n.join("string"==typeof u?u:""))),e!==E?(s?!D&&e[u]&&(a=!0):delete e[u],a?e[u]=r:U(e,u,r)):a?e[u]=r:G(u,r)})(Function.prototype,"toString",(function(){return"function"==typeof this&&u(this).source||Y(this)}))})),Fe=E,Ee=function(e){return"function"==typeof e?e:void 0},Ce=function(e,u){return arguments.length<2?Ee(Fe[e])||Ee(E[e]):Fe[e]&&Fe[e][u]||E[e]&&E[e][u]},Ae=Math.ceil,ge=Math.floor,me=function(e){return isNaN(e=+e)?0:(e>0?ge:Ae)(e)},he=Math.min,ye=function(e){return e>0?he(me(e),9007199254740991):0},Be=Math.max,ke=Math.min,be=function(e){return function(u,t,n){var r,o=v(u),i=ye(o.length),s=function(e,u){var t=me(e);return t<0?Be(t+u,0):ke(t,u)}(n,i);if(e&&t!=t){for(;i>s;)if((r=o[s++])!=r)return!0}else for(;i>s;s++)if((e||s in o)&&o[s]===t)return e||s||0;return!e&&-1}},Pe={includes:be(!0),indexOf:be(!1)}.indexOf,xe=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),ve={f:Object.getOwnPropertyNames||function(e){return function(e,u){var t,n=v(e),r=0,o=[];for(t in n)!I(re,t)&&I(n,t)&&o.push(t);for(;u.length>r;)I(n,t=u[r++])&&(~Pe(o,t)||o.push(t));return o}(e,xe)}},we={f:Object.getOwnPropertySymbols},Se=Ce("Reflect","ownKeys")||function(e){var u=ve.f(V(e)),t=we.f;return t?u.concat(t(e)):u},Te=function(e,u){for(var t=Se(u),n=$.f,r=j.f,o=0;o<t.length;o++){var i=t[o];I(e,i)||n(e,i,r(u,i))}},Ne=/#|\.prototype\./,Ie=function(e,u){var t=Oe[Le(e)];return t==qe||t!=Re&&("function"==typeof u?C(u):!!u)},Le=Ie.normalize=function(e){return String(e).replace(Ne,".").toLowerCase()},Oe=Ie.data={},Re=Ie.NATIVE="N",qe=Ie.POLYFILL="P",je=Ie,Ve=j.f,Me=function(e,u){var t,n,r,o,i,s=e.target,a=e.global,D=e.stat;if(t=a?E:D?E[s]||G(s,{}):(E[s]||{}).prototype)for(n in u){if(o=u[n],r=e.noTargetGet?(i=Ve(t,n))&&i.value:t[n],!je(a?n:s+(D?".":"#")+n,e.forced)&&void 0!==r){if(typeof o==typeof r)continue;Te(o,r)}(e.sham||r&&r.sham)&&U(o,"sham",!0),de(t,n,o,e)}},$e=Array.isArray||function(e){return"Array"==k(e)},Ue=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},Ge=function(e,u,t){if(Ue(e),void 0===u)return e;switch(t){case 0:return function(){return e.call(u)};case 1:return function(t){return e.call(u,t)};case 2:return function(t,n){return e.call(u,t,n)};case 3:return function(t,n,r){return e.call(u,t,n,r)}}return function(){return e.apply(u,arguments)}},_e=function(e,u,t,n,r,o,i,s){for(var a,D=r,c=0,l=!!i&&Ge(i,s,3);c<n;){if(c in t){if(a=l?l(t[c],c,u):t[c],o>0&&$e(a))D=_e(e,u,a,ye(a.length),D,o-1)-1;else{if(D>=9007199254740991)throw TypeError("Exceed the acceptable array length");e[D]=a}D++}c++}return D},Xe=_e,He=Ce("navigator","userAgent")||"",ze=E.process,Je=ze&&ze.versions,We=Je&&Je.v8;We?pe=(le=We.split("."))[0]<4?1:le[0]+le[1]:He&&(!(le=He.match(/Edge\/(\d+)/))||le[1]>=74)&&(le=He.match(/Chrome\/(\d+)/))&&(pe=le[1]);var Ye=pe&&+pe,Ke=!!Object.getOwnPropertySymbols&&!C((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&Ye&&Ye<41})),Ze=Ke&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Qe=Q("wks"),eu=E.Symbol,uu=Ze?eu:eu&&eu.withoutSetter||te,tu=function(e){return I(Qe,e)&&(Ke||"string"==typeof Qe[e])||(Ke&&I(eu,e)?Qe[e]=eu[e]:Qe[e]=uu("Symbol."+e)),Qe[e]},nu=tu("species"),ru=function(e,u){var t;return $e(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!$e(t.prototype)?w(t)&&null===(t=t[nu])&&(t=void 0):t=void 0),new(void 0===t?Array:t)(0===u?0:u)};Me({target:"Array",proto:!0},{flatMap:function(e){var u,t=T(this),n=ye(t.length);return Ue(e),(u=ru(t,0)).length=Xe(u,t,t,n,0,1,e,arguments.length>1?arguments[1]:void 0),u}});var ou,iu,su=Math.floor,au=function(e,u){var t=e.length,n=su(t/2);return t<8?Du(e,u):cu(au(e.slice(0,n),u),au(e.slice(n),u),u)},Du=function(e,u){for(var t,n,r=e.length,o=1;o<r;){for(n=o,t=e[o];n&&u(e[n-1],t)>0;)e[n]=e[--n];n!==o++&&(e[n]=t)}return e},cu=function(e,u,t){for(var n=e.length,r=u.length,o=0,i=0,s=[];o<n||i<r;)o<n&&i<r?s.push(t(e[o],u[i])<=0?e[o++]:u[i++]):s.push(o<n?e[o++]:u[i++]);return s},lu=au,pu=He.match(/firefox\/(\d+)/i),fu=!!pu&&+pu[1],du=/MSIE|Trident/.test(He),Fu=He.match(/AppleWebKit\/(\d+)\./),Eu=!!Fu&&+Fu[1],Cu=[],Au=Cu.sort,gu=C((function(){Cu.sort(void 0)})),mu=C((function(){Cu.sort(null)})),hu=!!(iu=[]["sort"])&&C((function(){iu.call(null,ou||function(){throw 1},1)})),yu=!C((function(){if(Ye)return Ye<70;if(!(fu&&fu>3)){if(du)return!0;if(Eu)return Eu<603;var e,u,t,n,r="";for(e=65;e<76;e++){switch(u=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:t=3;break;case 68:case 71:t=4;break;default:t=2}for(n=0;n<47;n++)Cu.push({k:u+n,v:t})}for(Cu.sort((function(e,u){return u.v-e.v})),n=0;n<Cu.length;n++)u=Cu[n].k.charAt(0),r.charAt(r.length-1)!==u&&(r+=u);return"DGBEFHACIJK"!==r}}));Me({target:"Array",proto:!0,forced:gu||!mu||!hu||!yu},{sort:function(e){void 0!==e&&Ue(e);var u=T(this);if(yu)return void 0===e?Au.call(u):Au.call(u,e);var t,n,r=[],o=ye(u.length);for(n=0;n<o;n++)n in u&&r.push(u[n]);for(t=(r=lu(r,function(e){return function(u,t){return void 0===t?-1:void 0===u?1:void 0!==e?+e(u,t)||0:String(u)>String(t)?1:-1}}(e))).length,n=0;n<t;)u[n]=r[n++];for(;n<o;)delete u[n++];return u}});var Bu={},ku=tu("iterator"),bu=Array.prototype,Pu={};Pu[tu("toStringTag")]="z";var xu="[object z]"===String(Pu),vu=tu("toStringTag"),wu="Arguments"==k(function(){return arguments}()),Su=xu?k:function(e){var u,t,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(t=function(e,u){try{return e[u]}catch(e){}}(u=Object(e),vu))?t:wu?k(u):"Object"==(n=k(u))&&"function"==typeof u.callee?"Arguments":n},Tu=tu("iterator"),Nu=function(e){var u=e.return;if(void 0!==u)return V(u.call(e)).value},Iu=function(e,u){this.stopped=e,this.result=u},Lu=function(e,u,t){var n,r,o,i,s,a,D,c,l=t&&t.that,p=!(!t||!t.AS_ENTRIES),f=!(!t||!t.IS_ITERATOR),d=!(!t||!t.INTERRUPTED),F=Ge(u,l,1+p+d),E=function(e){return n&&Nu(n),new Iu(!0,e)},C=function(e){return p?(V(e),d?F(e[0],e[1],E):F(e[0],e[1])):d?F(e,E):F(e)};if(f)n=e;else{if("function"!=typeof(r=function(e){if(null!=e)return e[Tu]||e["@@iterator"]||Bu[Su(e)]}(e)))throw TypeError("Target is not iterable");if(void 0!==(c=r)&&(Bu.Array===c||bu[ku]===c)){for(o=0,i=ye(e.length);i>o;o++)if((s=C(e[o]))&&s instanceof Iu)return s;return new Iu(!1)}n=r.call(e)}for(a=n.next;!(D=a.call(n)).done;){try{s=C(D.value)}catch(e){throw Nu(n),e}if("object"==typeof s&&s&&s instanceof Iu)return s}return new Iu(!1)};Me({target:"Object",stat:!0},{fromEntries:function(e){var u={};return Lu(e,(function(e,t){!function(e,u,t){var n=S(u);n in e?$.f(e,n,y(0,t)):e[n]=t}(u,e,t)}),{AS_ENTRIES:!0}),u}});var Ou=void 0!==Ou?Ou:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function Ru(){throw new Error("setTimeout has not been defined")}function qu(){throw new Error("clearTimeout has not been defined")}var ju=Ru,Vu=qu;function Mu(e){if(ju===setTimeout)return setTimeout(e,0);if((ju===Ru||!ju)&&setTimeout)return ju=setTimeout,setTimeout(e,0);try{return ju(e,0)}catch(u){try{return ju.call(null,e,0)}catch(u){return ju.call(this,e,0)}}}"function"==typeof Ou.setTimeout&&(ju=setTimeout),"function"==typeof Ou.clearTimeout&&(Vu=clearTimeout);var $u,Uu=[],Gu=!1,_u=-1;function Xu(){Gu&&$u&&(Gu=!1,$u.length?Uu=$u.concat(Uu):_u=-1,Uu.length&&Hu())}function Hu(){if(!Gu){var e=Mu(Xu);Gu=!0;for(var u=Uu.length;u;){for($u=Uu,Uu=[];++_u<u;)$u&&$u[_u].run();_u=-1,u=Uu.length}$u=null,Gu=!1,function(e){if(Vu===clearTimeout)return clearTimeout(e);if((Vu===qu||!Vu)&&clearTimeout)return Vu=clearTimeout,clearTimeout(e);try{Vu(e)}catch(u){try{return Vu.call(null,e)}catch(u){return Vu.call(this,e)}}}(e)}}function zu(e,u){this.fun=e,this.array=u}zu.prototype.run=function(){this.fun.apply(null,this.array)};function Ju(){}var Wu=Ju,Yu=Ju,Ku=Ju,Zu=Ju,Qu=Ju,et=Ju,ut=Ju;var tt=Ou.performance||{},nt=tt.now||tt.mozNow||tt.msNow||tt.oNow||tt.webkitNow||function(){return(new Date).getTime()};var rt=new Date;var ot={nextTick:function(e){var u=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)u[t-1]=arguments[t];Uu.push(new zu(e,u)),1!==Uu.length||Gu||Mu(Hu)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:Wu,addListener:Yu,once:Ku,off:Zu,removeListener:Qu,removeAllListeners:et,emit:ut,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var u=.001*nt.call(tt),t=Math.floor(u),n=Math.floor(u%1*1e9);return e&&(t-=e[0],(n-=e[1])<0&&(t--,n+=1e9)),[t,n]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-rt)/1e3}};var it="object"==typeof ot&&ot.env&&ot.env.NODE_DEBUG&&/\bsemver\b/i.test(ot.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var st={SEMVER_SPEC_VERSION:"2.0.0",MAX_LENGTH:256,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||9007199254740991,MAX_SAFE_COMPONENT_LENGTH:16},at=d((function(e,u){const{MAX_SAFE_COMPONENT_LENGTH:t}=st,n=(u=e.exports={}).re=[],r=u.src=[],o=u.t={};let i=0;const s=(e,u,t)=>{const s=i++;it(s,u),o[e]=s,r[s]=u,n[s]=new RegExp(u,t?"g":void 0)};s("NUMERICIDENTIFIER","0|[1-9]\\d*"),s("NUMERICIDENTIFIERLOOSE","[0-9]+"),s("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),s("MAINVERSION",`(${r[o.NUMERICIDENTIFIER]})\\.(${r[o.NUMERICIDENTIFIER]})\\.(${r[o.NUMERICIDENTIFIER]})`),s("MAINVERSIONLOOSE",`(${r[o.NUMERICIDENTIFIERLOOSE]})\\.(${r[o.NUMERICIDENTIFIERLOOSE]})\\.(${r[o.NUMERICIDENTIFIERLOOSE]})`),s("PRERELEASEIDENTIFIER",`(?:${r[o.NUMERICIDENTIFIER]}|${r[o.NONNUMERICIDENTIFIER]})`),s("PRERELEASEIDENTIFIERLOOSE",`(?:${r[o.NUMERICIDENTIFIERLOOSE]}|${r[o.NONNUMERICIDENTIFIER]})`),s("PRERELEASE",`(?:-(${r[o.PRERELEASEIDENTIFIER]}(?:\\.${r[o.PRERELEASEIDENTIFIER]})*))`),s("PRERELEASELOOSE",`(?:-?(${r[o.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${r[o.PRERELEASEIDENTIFIERLOOSE]})*))`),s("BUILDIDENTIFIER","[0-9A-Za-z-]+"),s("BUILD",`(?:\\+(${r[o.BUILDIDENTIFIER]}(?:\\.${r[o.BUILDIDENTIFIER]})*))`),s("FULLPLAIN",`v?${r[o.MAINVERSION]}${r[o.PRERELEASE]}?${r[o.BUILD]}?`),s("FULL",`^${r[o.FULLPLAIN]}$`),s("LOOSEPLAIN",`[v=\\s]*${r[o.MAINVERSIONLOOSE]}${r[o.PRERELEASELOOSE]}?${r[o.BUILD]}?`),s("LOOSE",`^${r[o.LOOSEPLAIN]}$`),s("GTLT","((?:<|>)?=?)"),s("XRANGEIDENTIFIERLOOSE",`${r[o.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),s("XRANGEIDENTIFIER",`${r[o.NUMERICIDENTIFIER]}|x|X|\\*`),s("XRANGEPLAIN",`[v=\\s]*(${r[o.XRANGEIDENTIFIER]})(?:\\.(${r[o.XRANGEIDENTIFIER]})(?:\\.(${r[o.XRANGEIDENTIFIER]})(?:${r[o.PRERELEASE]})?${r[o.BUILD]}?)?)?`),s("XRANGEPLAINLOOSE",`[v=\\s]*(${r[o.XRANGEIDENTIFIERLOOSE]})(?:\\.(${r[o.XRANGEIDENTIFIERLOOSE]})(?:\\.(${r[o.XRANGEIDENTIFIERLOOSE]})(?:${r[o.PRERELEASELOOSE]})?${r[o.BUILD]}?)?)?`),s("XRANGE",`^${r[o.GTLT]}\\s*${r[o.XRANGEPLAIN]}$`),s("XRANGELOOSE",`^${r[o.GTLT]}\\s*${r[o.XRANGEPLAINLOOSE]}$`),s("COERCE",`(^|[^\\d])(\\d{1,${t}})(?:\\.(\\d{1,${t}}))?(?:\\.(\\d{1,${t}}))?(?:$|[^\\d])`),s("COERCERTL",r[o.COERCE],!0),s("LONETILDE","(?:~>?)"),s("TILDETRIM",`(\\s*)${r[o.LONETILDE]}\\s+`,!0),u.tildeTrimReplace="$1~",s("TILDE",`^${r[o.LONETILDE]}${r[o.XRANGEPLAIN]}$`),s("TILDELOOSE",`^${r[o.LONETILDE]}${r[o.XRANGEPLAINLOOSE]}$`),s("LONECARET","(?:\\^)"),s("CARETTRIM",`(\\s*)${r[o.LONECARET]}\\s+`,!0),u.caretTrimReplace="$1^",s("CARET",`^${r[o.LONECARET]}${r[o.XRANGEPLAIN]}$`),s("CARETLOOSE",`^${r[o.LONECARET]}${r[o.XRANGEPLAINLOOSE]}$`),s("COMPARATORLOOSE",`^${r[o.GTLT]}\\s*(${r[o.LOOSEPLAIN]})$|^$`),s("COMPARATOR",`^${r[o.GTLT]}\\s*(${r[o.FULLPLAIN]})$|^$`),s("COMPARATORTRIM",`(\\s*)${r[o.GTLT]}\\s*(${r[o.LOOSEPLAIN]}|${r[o.XRANGEPLAIN]})`,!0),u.comparatorTrimReplace="$1$2$3",s("HYPHENRANGE",`^\\s*(${r[o.XRANGEPLAIN]})\\s+-\\s+(${r[o.XRANGEPLAIN]})\\s*$`),s("HYPHENRANGELOOSE",`^\\s*(${r[o.XRANGEPLAINLOOSE]})\\s+-\\s+(${r[o.XRANGEPLAINLOOSE]})\\s*$`),s("STAR","(<|>)?=?\\s*\\*"),s("GTE0","^\\s*>=\\s*0.0.0\\s*$"),s("GTE0PRE","^\\s*>=\\s*0.0.0-0\\s*$")}));const Dt=["includePrerelease","loose","rtl"];var ct=e=>e?"object"!=typeof e?{loose:!0}:Dt.filter((u=>e[u])).reduce(((e,u)=>(e[u]=!0,e)),{}):{};const lt=/^[0-9]+$/,pt=(e,u)=>{const t=lt.test(e),n=lt.test(u);return t&&n&&(e=+e,u=+u),e===u?0:t&&!n?-1:n&&!t?1:e<u?-1:1};var ft={compareIdentifiers:pt,rcompareIdentifiers:(e,u)=>pt(u,e)};const{MAX_LENGTH:dt,MAX_SAFE_INTEGER:Ft}=st,{re:Et,t:Ct}=at,{compareIdentifiers:At}=ft;class gt{constructor(e,u){if(u=ct(u),e instanceof gt){if(e.loose===!!u.loose&&e.includePrerelease===!!u.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid Version: ${e}`);if(e.length>dt)throw new TypeError(`version is longer than ${dt} characters`);it("SemVer",e,u),this.options=u,this.loose=!!u.loose,this.includePrerelease=!!u.includePrerelease;const t=e.trim().match(u.loose?Et[Ct.LOOSE]:Et[Ct.FULL]);if(!t)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+t[1],this.minor=+t[2],this.patch=+t[3],this.major>Ft||this.major<0)throw new TypeError("Invalid major version");if(this.minor>Ft||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>Ft||this.patch<0)throw new TypeError("Invalid patch version");t[4]?this.prerelease=t[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const u=+e;if(u>=0&&u<Ft)return u}return e})):this.prerelease=[],this.build=t[5]?t[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(it("SemVer.compare",this.version,this.options,e),!(e instanceof gt)){if("string"==typeof e&&e===this.version)return 0;e=new gt(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof gt||(e=new gt(e,this.options)),At(this.major,e.major)||At(this.minor,e.minor)||At(this.patch,e.patch)}comparePre(e){if(e instanceof gt||(e=new gt(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let u=0;do{const t=this.prerelease[u],n=e.prerelease[u];if(it("prerelease compare",u,t,n),void 0===t&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===t)return-1;if(t!==n)return At(t,n)}while(++u)}compareBuild(e){e instanceof gt||(e=new gt(e,this.options));let u=0;do{const t=this.build[u],n=e.build[u];if(it("prerelease compare",u,t,n),void 0===t&&void 0===n)return 0;if(void 0===n)return 1;if(void 0===t)return-1;if(t!==n)return At(t,n)}while(++u)}inc(e,u){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",u);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",u);break;case"prepatch":this.prerelease.length=0,this.inc("patch",u),this.inc("pre",u);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",u),this.inc("pre",u);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{let e=this.prerelease.length;for(;--e>=0;)"number"==typeof this.prerelease[e]&&(this.prerelease[e]++,e=-2);-1===e&&this.prerelease.push(0)}u&&(this.prerelease[0]===u?isNaN(this.prerelease[1])&&(this.prerelease=[u,0]):this.prerelease=[u,0]);break;default:throw new Error(`invalid increment argument: ${e}`)}return this.format(),this.raw=this.version,this}}var mt=gt;var ht=(e,u,t)=>new mt(e,t).compare(new mt(u,t));var yt=(e,u,t)=>ht(e,u,t)<0;var Bt=(e,u,t)=>ht(e,u,t)>=0,kt="2.3.2",bt=d((function(e,u){function t(){for(var e=[],u=0;u<arguments.length;u++)e[u]=arguments[u]}function n(){return"undefined"!=typeof WeakMap?new WeakMap:{add:t,delete:t,get:t,set:t,has:function(e){return!1}}}Object.defineProperty(u,"__esModule",{value:!0}),u.outdent=void 0;var r=Object.prototype.hasOwnProperty,o=function(e,u){return r.call(e,u)};function i(e,u){for(var t in u)o(u,t)&&(e[t]=u[t]);return e}var s=/^[ \t]*(?:\r\n|\r|\n)/,a=/(?:\r\n|\r|\n)[ \t]*$/,D=/^(?:[\r\n]|$)/,c=/(?:\r\n|\r|\n)([ \t]*)(?:[^ \t\r\n]|$)/,l=/^[ \t]*[\r\n][ \t\r\n]*$/;function p(e,u,t){var n=0,r=e[0].match(c);r&&(n=r[1].length);var o=new RegExp("(\\r\\n|\\r|\\n).{0,"+n+"}","g");u&&(e=e.slice(1));var i=t.newline,D=t.trimLeadingNewline,l=t.trimTrailingNewline,p="string"==typeof i,f=e.length;return e.map((function(e,u){return e=e.replace(o,"$1"),0===u&&D&&(e=e.replace(s,"")),u===f-1&&l&&(e=e.replace(a,"")),p&&(e=e.replace(/\r\n|\n|\r/g,(function(e){return i}))),e}))}function f(e,u){for(var t="",n=0,r=e.length;n<r;n++)t+=e[n],n<r-1&&(t+=u[n]);return t}function d(e){return o(e,"raw")&&o(e,"length")}var F=function e(u){var t=n(),r=n();return i((function n(o){for(var s=[],a=1;a<arguments.length;a++)s[a-1]=arguments[a];if(d(o)){var c=o,E=(s[0]===n||s[0]===F)&&l.test(c[0])&&D.test(c[1]),C=E?r:t,A=C.get(c);if(A||(A=p(c,E,u),C.set(c,A)),0===s.length)return A[0];var g=f(A,E?s.slice(1):s);return g}return e(i(i({},u),o||{}))}),{string:function(e){return p([e],!1,u)[0]}})}({trimLeadingNewline:!0,trimTrailingNewline:!0});u.outdent=F,u.default=F;try{e.exports=F,Object.defineProperty(F,"__esModule",{value:!0}),F.default=F,F.outdent=F}catch(e){}}));const{outdent:Pt}=bt,xt={cursorOffset:{since:"1.4.0",category:"Special",type:"int",default:-1,range:{start:-1,end:Number.POSITIVE_INFINITY,step:1},description:Pt`
      Print (to stderr) where a cursor at the given position would move to after formatting.
      This option cannot be used with --range-start and --range-end.
    `,cliCategory:"Editor"},endOfLine:{since:"1.15.0",category:"Global",type:"choice",default:[{since:"1.15.0",value:"auto"},{since:"2.0.0",value:"lf"}],description:"Which end of line characters to apply.",choices:[{value:"lf",description:"Line Feed only (\\n), common on Linux and macOS as well as inside git repos"},{value:"crlf",description:"Carriage Return + Line Feed characters (\\r\\n), common on Windows"},{value:"cr",description:"Carriage Return character only (\\r), used very rarely"},{value:"auto",description:Pt`
          Maintain existing
          (mixed values within one file are normalised by looking at what's used after the first line)
        `}]},filepath:{since:"1.4.0",category:"Special",type:"path",description:"Specify the input filepath. This will be used to do parser inference.",cliName:"stdin-filepath",cliCategory:"Other",cliDescription:"Path to the file to pretend that stdin comes from."},insertPragma:{since:"1.8.0",category:"Special",type:"boolean",default:!1,description:"Insert @format pragma into file's first docblock comment.",cliCategory:"Other"},parser:{since:"0.0.10",category:"Global",type:"choice",default:[{since:"0.0.10",value:"babylon"},{since:"1.13.0",value:void 0}],description:"Which parser to use.",exception:e=>"string"==typeof e||"function"==typeof e,choices:[{value:"flow",description:"Flow"},{value:"babel",since:"1.16.0",description:"JavaScript"},{value:"babel-flow",since:"1.16.0",description:"Flow"},{value:"babel-ts",since:"2.0.0",description:"TypeScript"},{value:"typescript",since:"1.4.0",description:"TypeScript"},{value:"espree",since:"2.2.0",description:"JavaScript"},{value:"meriyah",since:"2.2.0",description:"JavaScript"},{value:"css",since:"1.7.1",description:"CSS"},{value:"less",since:"1.7.1",description:"Less"},{value:"scss",since:"1.7.1",description:"SCSS"},{value:"json",since:"1.5.0",description:"JSON"},{value:"json5",since:"1.13.0",description:"JSON5"},{value:"json-stringify",since:"1.13.0",description:"JSON.stringify"},{value:"graphql",since:"1.5.0",description:"GraphQL"},{value:"markdown",since:"1.8.0",description:"Markdown"},{value:"mdx",since:"1.15.0",description:"MDX"},{value:"vue",since:"1.10.0",description:"Vue"},{value:"yaml",since:"1.14.0",description:"YAML"},{value:"glimmer",since:"2.3.0",description:"Ember / Handlebars"},{value:"html",since:"1.15.0",description:"HTML"},{value:"angular",since:"1.15.0",description:"Angular"},{value:"lwc",since:"1.17.0",description:"Lightning Web Components"}]},plugins:{since:"1.10.0",type:"path",array:!0,default:[{value:[]}],category:"Global",description:"Add a plugin. Multiple plugins can be passed as separate `--plugin`s.",exception:e=>"string"==typeof e||"object"==typeof e,cliName:"plugin",cliCategory:"Config"},pluginSearchDirs:{since:"1.13.0",type:"path",array:!0,default:[{value:[]}],category:"Global",description:Pt`
      Custom directory that contains prettier plugins in node_modules subdirectory.
      Overrides default behavior when plugins are searched relatively to the location of Prettier.
      Multiple values are accepted.
    `,exception:e=>"string"==typeof e||"object"==typeof e,cliName:"plugin-search-dir",cliCategory:"Config"},printWidth:{since:"0.0.0",category:"Global",type:"int",default:80,description:"The line length where Prettier will try wrap.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},rangeEnd:{since:"1.4.0",category:"Special",type:"int",default:Number.POSITIVE_INFINITY,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:Pt`
      Format code ending at a given character offset (exclusive).
      The range will extend forwards to the end of the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:"Editor"},rangeStart:{since:"1.4.0",category:"Special",type:"int",default:0,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:Pt`
      Format code starting at a given character offset.
      The range will extend backwards to the start of the first line containing the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:"Editor"},requirePragma:{since:"1.7.0",category:"Special",type:"boolean",default:!1,description:Pt`
      Require either '@prettier' or '@format' to be present in the file's first docblock comment
      in order for it to be formatted.
    `,cliCategory:"Other"},tabWidth:{type:"int",category:"Global",default:2,description:"Number of spaces per indentation level.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},useTabs:{since:"1.0.0",category:"Global",type:"boolean",default:!1,description:"Indent with tabs instead of spaces."},embeddedLanguageFormatting:{since:"2.1.0",category:"Global",type:"choice",default:[{since:"2.1.0",value:"auto"}],description:"Control how Prettier formats quoted code embedded in the file.",choices:[{value:"auto",description:"Format embedded code if Prettier can automatically identify it."},{value:"off",description:"Never automatically format embedded code."}]}};const vt=["cliName","cliCategory","cliDescription"],wt={compare:ht,lt:yt,gte:Bt},St=kt,Tt={CATEGORY_CONFIG:"Config",CATEGORY_EDITOR:"Editor",CATEGORY_FORMAT:"Format",CATEGORY_OTHER:"Other",CATEGORY_OUTPUT:"Output",CATEGORY_GLOBAL:"Global",CATEGORY_SPECIAL:"Special",options:xt}.options;var Nt={getSupportInfo:function({plugins:e=[],showUnreleased:u=!1,showDeprecated:t=!1,showInternal:n=!1}={}){const r=St.split("-",1)[0],o=e.flatMap((e=>e.languages||[])).filter(D),i=(s=Object.assign({},...e.map((({options:e})=>e)),Tt),a="name",Object.entries(s).map((([e,u])=>Object.assign({[a]:e},u)))).filter((e=>D(e)&&c(e))).sort(((e,u)=>e.name===u.name?0:e.name<u.name?-1:1)).map((function(e){if(n)return e;return l(e,vt)})).map((u=>{u=Object.assign({},u),Array.isArray(u.default)&&(u.default=1===u.default.length?u.default[0].value:u.default.filter(D).sort(((e,u)=>wt.compare(u.since,e.since)))[0].value),Array.isArray(u.choices)&&(u.choices=u.choices.filter((e=>D(e)&&c(e))),"parser"===u.name&&function(e,u,t){const n=new Set(e.choices.map((e=>e.value)));for(const r of u)if(r.parsers)for(const u of r.parsers)if(!n.has(u)){n.add(u);const o=t.find((e=>e.parsers&&e.parsers[u]));let i=r.name;o&&o.name&&(i+=` (plugin: ${o.name})`),e.choices.push({value:u,description:i})}}(u,o,e));const t=Object.fromEntries(e.filter((e=>e.defaultOptions&&void 0!==e.defaultOptions[u.name])).map((e=>[e.name,e.defaultOptions[u.name]])));return Object.assign(Object.assign({},u),{},{pluginDefaults:t})}));var s,a;return{languages:o,options:i};function D(e){return u||!("since"in e)||e.since&&wt.gte(r,e.since)}function c(e){return t||!("deprecated"in e)||e.deprecated&&wt.lt(r,e.deprecated)}}};const{getSupportInfo:It}=Nt,Lt=/[^\x20-\x7F]/;function Ot(e){return(u,t,n)=>{const r=n&&n.backwards;if(!1===t)return!1;const{length:o}=u;let i=t;for(;i>=0&&i<o;){const t=u.charAt(i);if(e instanceof RegExp){if(!e.test(t))return i}else if(!e.includes(t))return i;r?i--:i++}return(-1===i||i===o)&&i}}const Rt=Ot(/\s/),qt=Ot(" \t"),jt=Ot(",; \t"),Vt=Ot(/[^\n\r]/);function Mt(e,u){if(!1===u)return!1;if("/"===e.charAt(u)&&"*"===e.charAt(u+1))for(let t=u+2;t<e.length;++t)if("*"===e.charAt(t)&&"/"===e.charAt(t+1))return t+2;return u}function $t(e,u){return!1!==u&&("/"===e.charAt(u)&&"/"===e.charAt(u+1)?Vt(e,u):u)}function Ut(e,u,t){const n=t&&t.backwards;if(!1===u)return!1;const r=e.charAt(u);if(n){if("\r"===e.charAt(u-1)&&"\n"===r)return u-2;if("\n"===r||"\r"===r||"\u2028"===r||"\u2029"===r)return u-1}else{if("\r"===r&&"\n"===e.charAt(u+1))return u+2;if("\n"===r||"\r"===r||"\u2028"===r||"\u2029"===r)return u+1}return u}function Gt(e,u,t={}){const n=qt(e,t.backwards?u-1:u,t);return n!==Ut(e,n,t)}function _t(e,u){let t=null,n=u;for(;n!==t;)t=n,n=jt(e,n),n=Mt(e,n),n=qt(e,n);return n=$t(e,n),n=Ut(e,n),!1!==n&&Gt(e,n)}function Xt(e,u){let t=null,n=u;for(;n!==t;)t=n,n=qt(e,n),n=Mt(e,n),n=$t(e,n),n=Ut(e,n);return n}function Ht(e,u,t){return Xt(e,t(u))}function zt(e,u,t=0){let n=0;for(let r=t;r<e.length;++r)"\t"===e[r]?n=n+u-n%u:n++;return n}function Jt(e,u){const t=e.slice(1,-1),n={quote:'"',regex:/"/g},r={quote:"'",regex:/'/g},o="'"===u?r:n,i=o===r?n:r;let s=o.quote;if(t.includes(o.quote)||t.includes(i.quote)){s=(t.match(o.regex)||[]).length>(t.match(i.regex)||[]).length?i.quote:o.quote}return s}function Wt(e,u,t){const n='"'===u?"'":'"',r=e.replace(/\\(.)|(["'])/gs,((e,r,o)=>r===n?r:o===u?"\\"+o:o||(t&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/.test(r)?r:"\\"+r)));return u+r+u}function Yt(e,u){(e.comments||(e.comments=[])).push(u),u.printed=!1,u.nodeDescription=function(e){const u=e.type||e.kind||"(unknown type)";let t=String(e.name||e.id&&("object"==typeof e.id?e.id.name:e.id)||e.key&&("object"==typeof e.key?e.key.name:e.key)||e.value&&("object"==typeof e.value?"":String(e.value))||e.operator||"");t.length>20&&(t=t.slice(0,19)+"\u2026");return u+(t?" "+t:"")}(e)}var Kt={inferParserByLanguage:function(e,u){const{languages:t}=It({plugins:u.plugins}),n=t.find((({name:u})=>u.toLowerCase()===e))||t.find((({aliases:u})=>Array.isArray(u)&&u.includes(e)))||t.find((({extensions:u})=>Array.isArray(u)&&u.includes(`.${e}`)));return n&&n.parsers[0]},getStringWidth:function(e){return e?Lt.test(e)?s(e):e.length:0},getMaxContinuousCount:function(e,u){const t=e.match(new RegExp(`(${D(u)})+`,"g"));return null===t?0:t.reduce(((e,t)=>Math.max(e,t.length/u.length)),0)},getMinNotPresentContinuousCount:function(e,u){const t=e.match(new RegExp(`(${D(u)})+`,"g"));if(null===t)return 0;const n=new Map;let r=0;for(const e of t){const t=e.length/u.length;n.set(t,!0),t>r&&(r=t)}for(let e=1;e<r;e++)if(!n.get(e))return e;return r+1},getPenultimate:e=>e[e.length-2],getLast:c,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:Xt,getNextNonSpaceNonCommentCharacterIndex:Ht,getNextNonSpaceNonCommentCharacter:function(e,u,t){return e.charAt(Ht(e,u,t))},skip:Ot,skipWhitespace:Rt,skipSpaces:qt,skipToLineEnd:jt,skipEverythingButNewLine:Vt,skipInlineComment:Mt,skipTrailingComment:$t,skipNewline:Ut,isNextLineEmptyAfterIndex:_t,isNextLineEmpty:function(e,u,t){return _t(e,t(u))},isPreviousLineEmpty:function(e,u,t){let n=t(u)-1;return n=qt(e,n,{backwards:!0}),n=Ut(e,n,{backwards:!0}),n=qt(e,n,{backwards:!0}),n!==Ut(e,n,{backwards:!0})},hasNewline:Gt,hasNewlineInRange:function(e,u,t){for(let n=u;n<t;++n)if("\n"===e.charAt(n))return!0;return!1},hasSpaces:function(e,u,t={}){return qt(e,t.backwards?u-1:u,t)!==u},getAlignmentSize:zt,getIndentSize:function(e,u){const t=e.lastIndexOf("\n");return-1===t?0:zt(e.slice(t+1).match(/^[\t ]*/)[0],u)},getPreferredQuote:Jt,printString:function(e,u){return Wt(e.slice(1,-1),"json"===u.parser||"json5"===u.parser&&"preserve"===u.quoteProps&&!u.singleQuote?'"':u.__isInHtmlAttribute?"'":Jt(e,u.singleQuote?"'":'"'),!("css"===u.parser||"less"===u.parser||"scss"===u.parser||u.__embeddedInHtml))},printNumber:function(e){return e.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(\d)/,"$1$2$3").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")},makeString:Wt,addLeadingComment:function(e,u){u.leading=!0,u.trailing=!1,Yt(e,u)},addDanglingComment:function(e,u,t){u.leading=!1,u.trailing=!1,t&&(u.marker=t),Yt(e,u)},addTrailingComment:function(e,u){u.leading=!1,u.trailing=!0,Yt(e,u)},isFrontMatterNode:function(e){return e&&"front-matter"===e.type},getShebang:function(e){if(!e.startsWith("#!"))return"";const u=e.indexOf("\n");return-1===u?e:e.slice(0,u)},isNonEmptyArray:function(e){return Array.isArray(e)&&e.length>0},createGroupIdMapper:function(e){const u=new WeakMap;return function(t){return u.has(t)||u.set(t,Symbol(e)),u.get(t)}}};const{isNonEmptyArray:Zt}=Kt;function Qt(e,u){const{ignoreDecorators:t}=u||{};if(!t){const u=e.declaration&&e.declaration.decorators||e.decorators;if(Zt(u))return Qt(u[0])}return e.range?e.range[0]:e.start}function en(e){return e.range?e.range[1]:e.end}function un(e,u){return Qt(e)===Qt(u)}var tn={locStart:Qt,locEnd:en,hasSameLocStart:un,hasSameLoc:function(e,u){return un(e,u)&&function(e,u){return en(e)===en(u)}(e,u)}},nn=d((function(e){!function(){function u(e){if(null==e)return!1;switch(e.type){case"BlockStatement":case"BreakStatement":case"ContinueStatement":case"DebuggerStatement":case"DoWhileStatement":case"EmptyStatement":case"ExpressionStatement":case"ForInStatement":case"ForStatement":case"IfStatement":case"LabeledStatement":case"ReturnStatement":case"SwitchStatement":case"ThrowStatement":case"TryStatement":case"VariableDeclaration":case"WhileStatement":case"WithStatement":return!0}return!1}function t(e){switch(e.type){case"IfStatement":return null!=e.alternate?e.alternate:e.consequent;case"LabeledStatement":case"ForStatement":case"ForInStatement":case"WhileStatement":case"WithStatement":return e.body}return null}e.exports={isExpression:function(e){if(null==e)return!1;switch(e.type){case"ArrayExpression":case"AssignmentExpression":case"BinaryExpression":case"CallExpression":case"ConditionalExpression":case"FunctionExpression":case"Identifier":case"Literal":case"LogicalExpression":case"MemberExpression":case"NewExpression":case"ObjectExpression":case"SequenceExpression":case"ThisExpression":case"UnaryExpression":case"UpdateExpression":return!0}return!1},isStatement:u,isIterationStatement:function(e){if(null==e)return!1;switch(e.type){case"DoWhileStatement":case"ForInStatement":case"ForStatement":case"WhileStatement":return!0}return!1},isSourceElement:function(e){return u(e)||null!=e&&"FunctionDeclaration"===e.type},isProblematicIfStatement:function(e){var u;if("IfStatement"!==e.type)return!1;if(null==e.alternate)return!1;u=e.consequent;do{if("IfStatement"===u.type&&null==u.alternate)return!0;u=t(u)}while(u);return!1},trailingStatement:t}}()})),rn=d((function(e){!function(){var u,t,n,r,o,i;function s(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(Math.floor((e-65536)/1024)+55296)+String.fromCharCode((e-65536)%1024+56320)}for(t={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/,NonAsciiIdentifierPart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/},u={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/},n=[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279],r=new Array(128),i=0;i<128;++i)r[i]=i>=97&&i<=122||i>=65&&i<=90||36===i||95===i;for(o=new Array(128),i=0;i<128;++i)o[i]=i>=97&&i<=122||i>=65&&i<=90||i>=48&&i<=57||36===i||95===i;e.exports={isDecimalDigit:function(e){return 48<=e&&e<=57},isHexDigit:function(e){return 48<=e&&e<=57||97<=e&&e<=102||65<=e&&e<=70},isOctalDigit:function(e){return e>=48&&e<=55},isWhiteSpace:function(e){return 32===e||9===e||11===e||12===e||160===e||e>=5760&&n.indexOf(e)>=0},isLineTerminator:function(e){return 10===e||13===e||8232===e||8233===e},isIdentifierStartES5:function(e){return e<128?r[e]:t.NonAsciiIdentifierStart.test(s(e))},isIdentifierPartES5:function(e){return e<128?o[e]:t.NonAsciiIdentifierPart.test(s(e))},isIdentifierStartES6:function(e){return e<128?r[e]:u.NonAsciiIdentifierStart.test(s(e))},isIdentifierPartES6:function(e){return e<128?o[e]:u.NonAsciiIdentifierPart.test(s(e))}}}()})),on=d((function(e){!function(){var u=rn;function t(e,u){return!(!u&&"yield"===e)&&n(e,u)}function n(e,u){if(u&&function(e){switch(e){case"implements":case"interface":case"package":case"private":case"protected":case"public":case"static":case"let":return!0;default:return!1}}(e))return!0;switch(e.length){case 2:return"if"===e||"in"===e||"do"===e;case 3:return"var"===e||"for"===e||"new"===e||"try"===e;case 4:return"this"===e||"else"===e||"case"===e||"void"===e||"with"===e||"enum"===e;case 5:return"while"===e||"break"===e||"catch"===e||"throw"===e||"const"===e||"yield"===e||"class"===e||"super"===e;case 6:return"return"===e||"typeof"===e||"delete"===e||"switch"===e||"export"===e||"import"===e;case 7:return"default"===e||"finally"===e||"extends"===e;case 8:return"function"===e||"continue"===e||"debugger"===e;case 10:return"instanceof"===e;default:return!1}}function r(e,u){return"null"===e||"true"===e||"false"===e||t(e,u)}function o(e,u){return"null"===e||"true"===e||"false"===e||n(e,u)}function i(e){var t,n,r;if(0===e.length)return!1;if(r=e.charCodeAt(0),!u.isIdentifierStartES5(r))return!1;for(t=1,n=e.length;t<n;++t)if(r=e.charCodeAt(t),!u.isIdentifierPartES5(r))return!1;return!0}function s(e){var t,n,r,o,i;if(0===e.length)return!1;for(i=u.isIdentifierStartES6,t=0,n=e.length;t<n;++t){if(55296<=(r=e.charCodeAt(t))&&r<=56319){if(++t>=n)return!1;if(!(56320<=(o=e.charCodeAt(t))&&o<=57343))return!1;r=1024*(r-55296)+(o-56320)+65536}if(!i(r))return!1;i=u.isIdentifierPartES6}return!0}e.exports={isKeywordES5:t,isKeywordES6:n,isReservedWordES5:r,isReservedWordES6:o,isRestrictedWord:function(e){return"eval"===e||"arguments"===e},isIdentifierNameES5:i,isIdentifierNameES6:s,isIdentifierES5:function(e,u){return i(e)&&!r(e,u)},isIdentifierES6:function(e,u){return s(e)&&!o(e,u)}}}()}));const sn=d((function(e,u){u.ast=nn,u.code=rn,u.keyword=on})).keyword.isIdentifierNameES5,{getLast:an,hasNewline:Dn,skipWhitespace:cn,isNonEmptyArray:ln,isNextLineEmptyAfterIndex:pn}=Kt,{locStart:fn,locEnd:dn,hasSameLocStart:Fn}=tn,En=new RegExp("^(?:(?=.)\\s)*:"),Cn=new RegExp("^(?:(?=.)\\s)*::");function An(e){return"Block"===e.type||"CommentBlock"===e.type||"MultiLine"===e.type}function gn(e){return"Line"===e.type||"CommentLine"===e.type||"SingleLine"===e.type||"HashbangComment"===e.type||"HTMLOpen"===e.type||"HTMLClose"===e.type}const mn=new Set(["ExportDefaultDeclaration","ExportDefaultSpecifier","DeclareExportDeclaration","ExportNamedDeclaration","ExportAllDeclaration"]);function hn(e){return e&&mn.has(e.type)}function yn(e){return"NumericLiteral"===e.type||"Literal"===e.type&&"number"==typeof e.value}function Bn(e){return"StringLiteral"===e.type||"Literal"===e.type&&"string"==typeof e.value}function kn(e){return"FunctionExpression"===e.type||"ArrowFunctionExpression"===e.type}function bn(e){return Nn(e)&&"Identifier"===e.callee.type&&("async"===e.callee.name||"inject"===e.callee.name||"fakeAsync"===e.callee.name)}function Pn(e){return"JSXElement"===e.type||"JSXFragment"===e.type}function xn(e){return"get"===e.kind||"set"===e.kind}function vn(e){return xn(e)||Fn(e,e.value)}const wn=new Set(["BinaryExpression","LogicalExpression","NGPipeExpression"]);const Sn=new Set(["AnyTypeAnnotation","TSAnyKeyword","NullLiteralTypeAnnotation","TSNullKeyword","ThisTypeAnnotation","TSThisType","NumberTypeAnnotation","TSNumberKeyword","VoidTypeAnnotation","TSVoidKeyword","BooleanTypeAnnotation","TSBooleanKeyword","BigIntTypeAnnotation","TSBigIntKeyword","SymbolTypeAnnotation","TSSymbolKeyword","StringTypeAnnotation","TSStringKeyword","BooleanLiteralTypeAnnotation","StringLiteralTypeAnnotation","BigIntLiteralTypeAnnotation","NumberLiteralTypeAnnotation","TSLiteralType","TSTemplateLiteralType","EmptyTypeAnnotation","MixedTypeAnnotation","TSNeverKeyword","TSObjectKeyword","TSUndefinedKeyword","TSUnknownKeyword"]);const Tn=/^(skip|[fx]?(it|describe|test))$/;function Nn(e){return e&&("CallExpression"===e.type||"OptionalCallExpression"===e.type)}function In(e){return e&&("MemberExpression"===e.type||"OptionalMemberExpression"===e.type)}function Ln(e){return/^(\d+|\d+\.\d+)$/.test(e)}function On(e){return e.quasis.some((e=>e.value.raw.includes("\n")))}function Rn(e){return e.extra?e.extra.raw:e.raw}const qn={"==":!0,"!=":!0,"===":!0,"!==":!0},jn={"*":!0,"/":!0,"%":!0},Vn={">>":!0,">>>":!0,"<<":!0};const Mn={};for(const[e,u]of[["|>"],["??"],["||"],["&&"],["|"],["^"],["&"],["==","===","!=","!=="],["<",">","<=",">=","in","instanceof"],[">>","<<",">>>"],["+","-"],["*","/","%"],["**"]].entries())for(const t of u)Mn[t]=e;function $n(e){return Mn[e]}const Un=new WeakMap;function Gn(e){if(Un.has(e))return Un.get(e);const u=[];return e.this&&u.push(e.this),Array.isArray(e.parameters)?u.push(...e.parameters):Array.isArray(e.params)&&u.push(...e.params),e.rest&&u.push(e.rest),Un.set(e,u),u}const _n=new WeakMap;function Xn(e){if(_n.has(e))return _n.get(e);let u=e.arguments;return"ImportExpression"===e.type&&(u=[e.source],e.attributes&&u.push(e.attributes)),_n.set(e,u),u}function Hn(e){return"prettier-ignore"===e.value.trim()&&!e.unignore}function zn(e){return e&&(e.prettierIgnore||Yn(e,Jn.PrettierIgnore))}const Jn={Leading:2,Trailing:4,Dangling:8,Block:16,Line:32,PrettierIgnore:64,First:128,Last:256},Wn=(e,u)=>{if("function"==typeof e&&(u=e,e=0),e||u)return(t,n,r)=>!(e&Jn.Leading&&!t.leading||e&Jn.Trailing&&!t.trailing||e&Jn.Dangling&&(t.leading||t.trailing)||e&Jn.Block&&!An(t)||e&Jn.Line&&!gn(t)||e&Jn.First&&0!==n||e&Jn.Last&&n!==r.length-1||e&Jn.PrettierIgnore&&!Hn(t)||u&&!u(t))};function Yn(e,u,t){if(!e||!ln(e.comments))return!1;const n=Wn(u,t);return!n||e.comments.some(n)}function Kn(e,u,t){if(!e||!Array.isArray(e.comments))return[];const n=Wn(u,t);return n?e.comments.filter(n):e.comments}function Zn(e){return Nn(e)||"NewExpression"===e.type||"ImportExpression"===e.type}var Qn={getFunctionParameters:Gn,iterateFunctionParametersPath:function(e,u){const t=e.getValue();let n=0;const r=e=>u(e,n++);t.this&&e.call(r,"this"),Array.isArray(t.parameters)?e.each(r,"parameters"):Array.isArray(t.params)&&e.each(r,"params"),t.rest&&e.call(r,"rest")},getCallArguments:Xn,iterateCallArgumentsPath:function(e,u){const t=e.getValue();"ImportExpression"===t.type?(e.call((e=>u(e,0)),"source"),t.attributes&&e.call((e=>u(e,1)),"attributes")):e.each(u,"arguments")},hasRestParameter:function(e){if(e.rest)return!0;const u=Gn(e);return u.length>0&&"RestElement"===an(u).type},getLeftSide:function(e){return e.expressions?e.expressions[0]:e.left||e.test||e.callee||e.object||e.tag||e.argument||e.expression},getLeftSidePathName:function(e,u){if(u.expressions)return["expressions",0];if(u.left)return["left"];if(u.test)return["test"];if(u.object)return["object"];if(u.callee)return["callee"];if(u.tag)return["tag"];if(u.argument)return["argument"];if(u.expression)return["expression"];throw new Error("Unexpected node has no left side.")},getParentExportDeclaration:function(e){const u=e.getParentNode();return"declaration"===e.getName()&&hn(u)?u:null},getTypeScriptMappedTypeModifier:function(e,u){return"+"===e?"+"+u:"-"===e?"-"+u:u},hasFlowAnnotationComment:function(e){return e&&An(e[0])&&Cn.test(e[0].value)},hasFlowShorthandAnnotationComment:function(e){return e.extra&&e.extra.parenthesized&&ln(e.trailingComments)&&An(e.trailingComments[0])&&En.test(e.trailingComments[0].value)},hasLeadingOwnLineComment:function(e,u){return Pn(u)?zn(u):Yn(u,Jn.Leading,(u=>Dn(e,dn(u))))},hasNakedLeftSide:function(e){return"AssignmentExpression"===e.type||"BinaryExpression"===e.type||"LogicalExpression"===e.type||"NGPipeExpression"===e.type||"ConditionalExpression"===e.type||Nn(e)||In(e)||"SequenceExpression"===e.type||"TaggedTemplateExpression"===e.type||"BindExpression"===e.type||"UpdateExpression"===e.type&&!e.prefix||"TSAsExpression"===e.type||"TSNonNullExpression"===e.type},hasNode:function e(u,t){if(!u||"object"!=typeof u)return!1;if(Array.isArray(u))return u.some((u=>e(u,t)));const n=t(u);return"boolean"==typeof n?n:Object.values(u).some((u=>e(u,t)))},hasIgnoreComment:function(e){return zn(e.getValue())},hasNodeIgnoreComment:zn,identity:function(e){return e},isBinaryish:function(e){return wn.has(e.type)},isBlockComment:An,isCallLikeExpression:Zn,isLineComment:gn,isPrettierIgnoreComment:Hn,isCallExpression:Nn,isMemberExpression:In,isExportDeclaration:hn,isFlowAnnotationComment:function(e,u){const t=fn(u),n=cn(e,dn(u));return!1!==n&&"/*"===e.slice(t,t+2)&&"*/"===e.slice(n,n+2)},isFunctionCompositionArgs:function(e){if(e.length<=1)return!1;let u=0;for(const t of e)if(kn(t)){if(u+=1,u>1)return!0}else if(Nn(t))for(const e of t.arguments)if(kn(e))return!0;return!1},isFunctionNotation:vn,isFunctionOrArrowExpression:kn,isGetterOrSetter:xn,isJestEachTemplateLiteral:function(e,u){const t=/^[fx]?(describe|it|test)$/;return"TaggedTemplateExpression"===u.type&&u.quasi===e&&"MemberExpression"===u.tag.type&&"Identifier"===u.tag.property.type&&"each"===u.tag.property.name&&("Identifier"===u.tag.object.type&&t.test(u.tag.object.name)||"MemberExpression"===u.tag.object.type&&"Identifier"===u.tag.object.property.type&&("only"===u.tag.object.property.name||"skip"===u.tag.object.property.name)&&"Identifier"===u.tag.object.object.type&&t.test(u.tag.object.object.name))},isJsxNode:Pn,isLiteral:function(e){return"BooleanLiteral"===e.type||"DirectiveLiteral"===e.type||"Literal"===e.type||"NullLiteral"===e.type||"NumericLiteral"===e.type||"BigIntLiteral"===e.type||"DecimalLiteral"===e.type||"RegExpLiteral"===e.type||"StringLiteral"===e.type||"TemplateLiteral"===e.type||"TSTypeLiteral"===e.type||"JSXText"===e.type},isLongCurriedCallExpression:function(e){const u=e.getValue(),t=e.getParentNode();return Nn(u)&&Nn(t)&&t.callee===u&&u.arguments.length>t.arguments.length&&t.arguments.length>0},isSimpleCallArgument:function e(u,t){if(t>=2)return!1;const n=u=>e(u,t+1),r="Literal"===u.type&&"regex"in u&&u.regex.pattern||"RegExpLiteral"===u.type&&u.pattern;return!(r&&r.length>5)&&("Literal"===u.type||"BigIntLiteral"===u.type||"DecimalLiteral"===u.type||"BooleanLiteral"===u.type||"NullLiteral"===u.type||"NumericLiteral"===u.type||"RegExpLiteral"===u.type||"StringLiteral"===u.type||"Identifier"===u.type||"ThisExpression"===u.type||"Super"===u.type||"PrivateName"===u.type||"PrivateIdentifier"===u.type||"ArgumentPlaceholder"===u.type||"Import"===u.type||("TemplateLiteral"===u.type?u.quasis.every((e=>!e.value.raw.includes("\n")))&&u.expressions.every(n):"ObjectExpression"===u.type?u.properties.every((e=>!e.computed&&(e.shorthand||e.value&&n(e.value)))):"ArrayExpression"===u.type?u.elements.every((e=>null===e||n(e))):Zn(u)?("ImportExpression"===u.type||e(u.callee,t))&&Xn(u).every(n):In(u)?e(u.object,t)&&e(u.property,t):"UnaryExpression"!==u.type||"!"!==u.operator&&"-"!==u.operator?"TSNonNullExpression"===u.type&&e(u.expression,t):e(u.argument,t)))},isMemberish:function(e){return In(e)||"BindExpression"===e.type&&Boolean(e.object)},isNumericLiteral:yn,isSignedNumericLiteral:function(e){return"UnaryExpression"===e.type&&("+"===e.operator||"-"===e.operator)&&yn(e.argument)},isObjectProperty:function(e){return e&&("ObjectProperty"===e.type||"Property"===e.type&&!e.method&&"init"===e.kind)},isObjectType:function(e){return"ObjectTypeAnnotation"===e.type||"TSTypeLiteral"===e.type},isObjectTypePropertyAFunction:function(e){return!("ObjectTypeProperty"!==e.type&&"ObjectTypeInternalSlot"!==e.type||"FunctionTypeAnnotation"!==e.value.type||e.static||vn(e))},isSimpleType:function(e){return!!e&&(!("GenericTypeAnnotation"!==e.type&&"TSTypeReference"!==e.type||e.typeParameters)||!!Sn.has(e.type))},isSimpleNumber:Ln,isSimpleTemplateLiteral:function(e){let u="expressions";"TSTemplateLiteralType"===e.type&&(u="types");const t=e[u];return 0!==t.length&&t.every((e=>{if(Yn(e))return!1;if("Identifier"===e.type||"ThisExpression"===e.type)return!0;if(In(e)){let u=e;for(;In(u);){if("Identifier"!==u.property.type&&"Literal"!==u.property.type&&"StringLiteral"!==u.property.type&&"NumericLiteral"!==u.property.type)return!1;if(u=u.object,Yn(u))return!1}return"Identifier"===u.type||"ThisExpression"===u.type}return!1}))},isStringLiteral:Bn,isStringPropSafeToUnquote:function(e,u){return"json"!==u.parser&&Bn(e.key)&&Rn(e.key).slice(1,-1)===e.key.value&&(sn(e.key.value)&&!(("typescript"===u.parser||"babel-ts"===u.parser)&&"ClassProperty"===e.type)||Ln(e.key.value)&&String(Number(e.key.value))===e.key.value&&("babel"===u.parser||"espree"===u.parser||"meriyah"===u.parser||"__babel_estree"===u.parser))},isTemplateOnItsOwnLine:function(e,u){return("TemplateLiteral"===e.type&&On(e)||"TaggedTemplateExpression"===e.type&&On(e.quasi))&&!Dn(u,fn(e),{backwards:!0})},isTestCall:function e(u,t){if("CallExpression"!==u.type)return!1;if(1===u.arguments.length){if(bn(u)&&t&&e(t))return kn(u.arguments[0]);if(function(e){return"Identifier"===e.callee.type&&/^(before|after)(Each|All)$/.test(e.callee.name)&&1===e.arguments.length}(u))return bn(u.arguments[0])}else if((2===u.arguments.length||3===u.arguments.length)&&("Identifier"===u.callee.type&&Tn.test(u.callee.name)||function(e){return In(e.callee)&&"Identifier"===e.callee.object.type&&"Identifier"===e.callee.property.type&&Tn.test(e.callee.object.name)&&("only"===e.callee.property.name||"skip"===e.callee.property.name)}(u))&&(function(e){return"TemplateLiteral"===e.type}(u.arguments[0])||Bn(u.arguments[0])))return!(u.arguments[2]&&!yn(u.arguments[2]))&&((2===u.arguments.length?kn(u.arguments[1]):function(e){return"FunctionExpression"===e.type||"ArrowFunctionExpression"===e.type&&"BlockStatement"===e.body.type}(u.arguments[1])&&Gn(u.arguments[1]).length<=1)||bn(u.arguments[1]));return!1},isTheOnlyJsxElementInMarkdown:function(e,u){if("markdown"!==e.parentParser&&"mdx"!==e.parentParser)return!1;const t=u.getNode();if(!t.expression||!Pn(t.expression))return!1;const n=u.getParentNode();return"Program"===n.type&&1===n.body.length},isTSXFile:function(e){return e.filepath&&/\.tsx$/i.test(e.filepath)},isTypeAnnotationAFunction:function(e){return!("TypeAnnotation"!==e.type&&"TSTypeAnnotation"!==e.type||"FunctionTypeAnnotation"!==e.typeAnnotation.type||e.static||Fn(e,e.typeAnnotation))},isNextLineEmpty:(e,{originalText:u})=>pn(u,dn(e)),needsHardlineAfterDanglingComment:function(e){if(!Yn(e))return!1;const u=an(Kn(e,Jn.Dangling));return u&&!An(u)},rawText:Rn,shouldPrintComma:function(e,u="es5"){return"es5"===e.trailingComma&&"es5"===u||"all"===e.trailingComma&&("all"===u||"es5"===u)},isBitwiseOperator:function(e){return Boolean(Vn[e])||"|"===e||"^"===e||"&"===e},shouldFlatten:function(e,u){return $n(u)===$n(e)&&("**"!==e&&((!qn[e]||!qn[u])&&(!("%"===u&&jn[e]||"%"===e&&jn[u])&&((u===e||!jn[u]||!jn[e])&&(!Vn[e]||!Vn[u])))))},startsWithNoLookaheadToken:function e(u,t){switch((u=function(e){for(;e.left;)e=e.left;return e}(u)).type){case"FunctionExpression":case"ClassExpression":case"DoExpression":return t;case"ObjectExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return e(u.object,t);case"TaggedTemplateExpression":return"FunctionExpression"!==u.tag.type&&e(u.tag,t);case"CallExpression":case"OptionalCallExpression":return"FunctionExpression"!==u.callee.type&&e(u.callee,t);case"ConditionalExpression":return e(u.test,t);case"UpdateExpression":return!u.prefix&&e(u.argument,t);case"BindExpression":return u.object&&e(u.object,t);case"SequenceExpression":return e(u.expressions[0],t);case"TSAsExpression":case"TSNonNullExpression":return e(u.expression,t);default:return!1}},getPrecedence:$n,hasComment:Yn,getComments:Kn,CommentCheckFlags:Jn};const{getLast:er,hasNewline:ur,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:tr,getNextNonSpaceNonCommentCharacter:nr,hasNewlineInRange:rr,addLeadingComment:or,addTrailingComment:ir,addDanglingComment:sr,getNextNonSpaceNonCommentCharacterIndex:ar,isNonEmptyArray:Dr}=Kt,{isBlockComment:cr,getFunctionParameters:lr,isPrettierIgnoreComment:pr,isJsxNode:fr,hasFlowShorthandAnnotationComment:dr,hasFlowAnnotationComment:Fr,hasIgnoreComment:Er,isCallLikeExpression:Cr,getCallArguments:Ar,isCallExpression:gr,isMemberExpression:mr,isObjectProperty:hr}=Qn,{locStart:yr,locEnd:Br}=tn;function kr(e,u){const t=(e.body||e.properties).find((({type:e})=>"EmptyStatement"!==e));t?or(t,u):sr(e,u)}function br(e,u){"BlockStatement"===e.type?kr(e,u):or(e,u)}function Pr({comment:e,followingNode:u}){return!(!u||!uo(e))&&(or(u,e),!0)}function xr({comment:e,precedingNode:u,enclosingNode:t,followingNode:n,text:r}){if(!t||"IfStatement"!==t.type||!n)return!1;return")"===nr(r,e,Br)?(ir(u,e),!0):u===t.consequent&&n===t.alternate?("BlockStatement"===u.type?ir(u,e):sr(t,e),!0):"BlockStatement"===n.type?(kr(n,e),!0):"IfStatement"===n.type?(br(n.consequent,e),!0):t.consequent===n&&(or(n,e),!0)}function vr({comment:e,precedingNode:u,enclosingNode:t,followingNode:n,text:r}){if(!t||"WhileStatement"!==t.type||!n)return!1;return")"===nr(r,e,Br)?(ir(u,e),!0):"BlockStatement"===n.type?(kr(n,e),!0):t.body===n&&(or(n,e),!0)}function wr({comment:e,precedingNode:u,enclosingNode:t,followingNode:n}){return!(!t||"TryStatement"!==t.type&&"CatchClause"!==t.type||!n)&&("CatchClause"===t.type&&u?(ir(u,e),!0):"BlockStatement"===n.type?(kr(n,e),!0):"TryStatement"===n.type?(br(n.finalizer,e),!0):"CatchClause"===n.type&&(br(n.body,e),!0))}function Sr({comment:e,enclosingNode:u,followingNode:t}){return!(!mr(u)||!t||"Identifier"!==t.type)&&(or(u,e),!0)}function Tr({comment:e,precedingNode:u,enclosingNode:t,followingNode:n,text:r}){const o=u&&!rr(r,Br(u),yr(e));return!(u&&o||!t||"ConditionalExpression"!==t.type&&"TSConditionalType"!==t.type||!n)&&(or(n,e),!0)}function Nr({comment:e,precedingNode:u,enclosingNode:t}){return!(!hr(t)||!t.shorthand||t.key!==u||"AssignmentPattern"!==t.value.type)&&(ir(t.value.left,e),!0)}function Ir({comment:e,precedingNode:u,enclosingNode:t,followingNode:n}){if(t&&("ClassDeclaration"===t.type||"ClassExpression"===t.type||"DeclareClass"===t.type||"DeclareInterface"===t.type||"InterfaceDeclaration"===t.type||"TSInterfaceDeclaration"===t.type)){if(Dr(t.decorators)&&(!n||"Decorator"!==n.type))return ir(er(t.decorators),e),!0;if(t.body&&n===t.body)return kr(t.body,e),!0;if(n)for(const r of["implements","extends","mixins"])if(t[r]&&n===t[r][0])return!u||u!==t.id&&u!==t.typeParameters&&u!==t.superClass?sr(t,e,r):ir(u,e),!0}return!1}function Lr({comment:e,precedingNode:u,enclosingNode:t,text:n}){return(t&&u&&("Property"===t.type||"TSDeclareMethod"===t.type||"TSAbstractMethodDefinition"===t.type)&&"Identifier"===u.type&&t.key===u&&":"!==nr(n,u,Br)||!(!u||!t||"Decorator"!==u.type||"ClassMethod"!==t.type&&"ClassProperty"!==t.type&&"PropertyDefinition"!==t.type&&"TSAbstractClassProperty"!==t.type&&"TSAbstractMethodDefinition"!==t.type&&"TSDeclareMethod"!==t.type&&"MethodDefinition"!==t.type))&&(ir(u,e),!0)}function Or({comment:e,precedingNode:u,enclosingNode:t,text:n}){return"("===nr(n,e,Br)&&(!(!u||!t||"FunctionDeclaration"!==t.type&&"FunctionExpression"!==t.type&&"ClassMethod"!==t.type&&"MethodDefinition"!==t.type&&"ObjectMethod"!==t.type)&&(ir(u,e),!0))}function Rr({comment:e,enclosingNode:u,text:t}){if(!u||"ArrowFunctionExpression"!==u.type)return!1;const n=ar(t,e,Br);return!1!==n&&"=>"===t.slice(n,n+2)&&(sr(u,e),!0)}function qr({comment:e,enclosingNode:u,text:t}){return")"===nr(t,e,Br)&&(u&&(eo(u)&&0===lr(u).length||Cr(u)&&0===Ar(u).length)?(sr(u,e),!0):!(!u||"MethodDefinition"!==u.type&&"TSAbstractMethodDefinition"!==u.type||0!==lr(u.value).length)&&(sr(u.value,e),!0))}function jr({comment:e,precedingNode:u,enclosingNode:t,followingNode:n,text:r}){if(u&&"FunctionTypeParam"===u.type&&t&&"FunctionTypeAnnotation"===t.type&&n&&"FunctionTypeParam"!==n.type)return ir(u,e),!0;if(u&&("Identifier"===u.type||"AssignmentPattern"===u.type)&&t&&eo(t)&&")"===nr(r,e,Br))return ir(u,e),!0;if(t&&"FunctionDeclaration"===t.type&&n&&"BlockStatement"===n.type){const u=(()=>{const e=lr(t);if(e.length>0)return tr(r,Br(er(e)));const u=tr(r,Br(t.id));return!1!==u&&tr(r,u+1)})();if(yr(e)>u)return kr(n,e),!0}return!1}function Vr({comment:e,enclosingNode:u}){return!(!u||"ImportSpecifier"!==u.type)&&(or(u,e),!0)}function Mr({comment:e,enclosingNode:u}){return!(!u||"LabeledStatement"!==u.type)&&(or(u,e),!0)}function $r({comment:e,enclosingNode:u}){return!(!u||"ContinueStatement"!==u.type&&"BreakStatement"!==u.type||u.label)&&(ir(u,e),!0)}function Ur({comment:e,precedingNode:u,enclosingNode:t}){return!!(gr(t)&&u&&t.callee===u&&t.arguments.length>0)&&(or(t.arguments[0],e),!0)}function Gr({comment:e,precedingNode:u,enclosingNode:t,followingNode:n}){return!t||"UnionTypeAnnotation"!==t.type&&"TSUnionType"!==t.type?(n&&("UnionTypeAnnotation"===n.type||"TSUnionType"===n.type)&&pr(e)&&(n.types[0].prettierIgnore=!0,e.unignore=!0),!1):(pr(e)&&(n.prettierIgnore=!0,e.unignore=!0),!!u&&(ir(u,e),!0))}function _r({comment:e,enclosingNode:u}){return!!hr(u)&&(or(u,e),!0)}function Xr({comment:e,enclosingNode:u,followingNode:t,ast:n,isLastComment:r}){return n&&n.body&&0===n.body.length?(r?sr(n,e):or(n,e),!0):u&&"Program"===u.type&&0===u.body.length&&!Dr(u.directives)?(r?sr(u,e):or(u,e),!0):!(!t||"Program"!==t.type||0!==t.body.length||!u||"ModuleExpression"!==u.type)&&(sr(t,e),!0)}function Hr({comment:e,enclosingNode:u}){return!(!u||"ForInStatement"!==u.type&&"ForOfStatement"!==u.type)&&(or(u,e),!0)}function zr({comment:e,precedingNode:u,enclosingNode:t,text:n}){return!!(u&&"ImportSpecifier"===u.type&&t&&"ImportDeclaration"===t.type&&ur(n,Br(e)))&&(ir(u,e),!0)}function Jr({comment:e,enclosingNode:u}){return!(!u||"AssignmentPattern"!==u.type)&&(or(u,e),!0)}function Wr({comment:e,enclosingNode:u}){return!(!u||"TypeAlias"!==u.type)&&(or(u,e),!0)}function Yr({comment:e,enclosingNode:u,followingNode:t}){return!(!u||"VariableDeclarator"!==u.type&&"AssignmentExpression"!==u.type||!t||"ObjectExpression"!==t.type&&"ArrayExpression"!==t.type&&"TemplateLiteral"!==t.type&&"TaggedTemplateExpression"!==t.type&&!cr(e))&&(or(t,e),!0)}function Kr({comment:e,enclosingNode:u,followingNode:t,text:n}){return!(t||!u||"TSMethodSignature"!==u.type&&"TSDeclareFunction"!==u.type&&"TSAbstractMethodDefinition"!==u.type||";"!==nr(n,e,Br))&&(ir(u,e),!0)}function Zr({comment:e,enclosingNode:u,followingNode:t}){if(pr(e)&&u&&"TSMappedType"===u.type&&t&&"TSTypeParameter"===t.type&&t.constraint)return u.prettierIgnore=!0,e.unignore=!0,!0}function Qr({comment:e,precedingNode:u,enclosingNode:t,followingNode:n}){return!(!t||"TSMappedType"!==t.type)&&(n&&"TSTypeParameter"===n.type&&n.name?(or(n.name,e),!0):!(!u||"TSTypeParameter"!==u.type||!u.constraint)&&(ir(u.constraint,e),!0))}function eo(e){return"ArrowFunctionExpression"===e.type||"FunctionExpression"===e.type||"FunctionDeclaration"===e.type||"ObjectMethod"===e.type||"ClassMethod"===e.type||"TSDeclareFunction"===e.type||"TSCallSignatureDeclaration"===e.type||"TSConstructSignatureDeclaration"===e.type||"TSMethodSignature"===e.type||"TSConstructorType"===e.type||"TSFunctionType"===e.type||"TSDeclareMethod"===e.type}function uo(e){return cr(e)&&"*"===e.value[0]&&/@type\b/.test(e.value)}var to={handleOwnLineComment:function(e){return[Zr,jr,Sr,xr,vr,wr,Ir,Vr,Hr,Gr,Xr,zr,Jr,Lr,Mr].some((u=>u(e)))},handleEndOfLineComment:function(e){return[Pr,jr,Tr,Vr,xr,vr,wr,Ir,Mr,Ur,_r,Xr,Wr,Yr].some((u=>u(e)))},handleRemainingComment:function(e){return[Zr,xr,vr,Nr,qr,Lr,Xr,Rr,Or,Qr,$r,Kr].some((u=>u(e)))},isTypeCastComment:uo,getCommentChildNodes:function(e,u){if(("typescript"===u.parser||"flow"===u.parser||"espree"===u.parser||"meriyah"===u.parser||"__babel_estree"===u.parser)&&"MethodDefinition"===e.type&&e.value&&"FunctionExpression"===e.value.type&&0===lr(e.value).length&&!e.value.returnType&&!Dr(e.value.typeParameters)&&e.value.body)return[...e.decorators||[],e.key,e.value.body]},willPrintOwnComments:function(e){const u=e.getValue(),t=e.getParentNode();return(u&&(fr(u)||dr(u)||gr(t)&&(Fr(u.leadingComments)||Fr(u.trailingComments)))||t&&("JSXSpreadAttribute"===t.type||"JSXSpreadChild"===t.type||"UnionTypeAnnotation"===t.type||"TSUnionType"===t.type||("ClassDeclaration"===t.type||"ClassExpression"===t.type)&&t.superClass===u))&&(!Er(e)||"UnionTypeAnnotation"===t.type||"TSUnionType"===t.type)}};const{getLast:no,getNextNonSpaceNonCommentCharacter:ro}=Kt,{locStart:oo,locEnd:io}=tn,{isTypeCastComment:so}=to;function ao(e){return"CallExpression"===e.type?(e.type="OptionalCallExpression",e.callee=ao(e.callee)):"MemberExpression"===e.type?(e.type="OptionalMemberExpression",e.object=ao(e.object)):"TSNonNullExpression"===e.type&&(e.expression=ao(e.expression)),e}function Do(e,u){let t;if(Array.isArray(e))t=e.entries();else{if(!e||"object"!=typeof e||"string"!=typeof e.type)return e;t=Object.entries(e)}for(const[n,r]of t)e[n]=Do(r,u);return Array.isArray(e)?e:u(e)||e}function co(e){return"LogicalExpression"===e.type&&"LogicalExpression"===e.right.type&&e.operator===e.right.operator}function lo(e){return co(e)?lo({type:"LogicalExpression",operator:e.operator,left:lo({type:"LogicalExpression",operator:e.operator,left:e.left,right:e.right.left,range:[oo(e.left),io(e.right.left)]}),right:e.right.right,range:[oo(e),io(e)]}):e}var po,fo=function(u,t){if("typescript"===t.parser&&t.originalText.includes("@")){const{esTreeNodeToTSNodeMap:n,tsNodeToESTreeNodeMap:r}=t.tsParseResult;u=Do(u,(u=>{const t=n.get(u);if(!t)return;const o=t.decorators;if(!Array.isArray(o))return;const i=r.get(t);if(i!==u)return;const s=i.decorators;if(!Array.isArray(s)||s.length!==o.length||o.some((e=>{const u=r.get(e);return!u||!s.includes(u)}))){const{start:u,end:t}=i.loc;throw e("Leading decorators must be attached to a class declaration",{start:{line:u.line,column:u.column+1},end:{line:t.line,column:t.column+1}})}}))}if("typescript"!==t.parser&&"flow"!==t.parser&&"espree"!==t.parser&&"meriyah"!==t.parser){const e=new Set;u=Do(u,(u=>{u.leadingComments&&u.leadingComments.some(so)&&e.add(oo(u))})),u=Do(u,(u=>{if("ParenthesizedExpression"===u.type){const{expression:t}=u;if("TypeCastExpression"===t.type)return t.range=u.range,t;const n=oo(u);if(!e.has(n))return t.extra=Object.assign(Object.assign({},t.extra),{},{parenthesized:!0}),t}}))}return u=Do(u,(e=>{switch(e.type){case"ChainExpression":return ao(e.expression);case"LogicalExpression":if(co(e))return lo(e);break;case"VariableDeclaration":{const u=no(e.declarations);u&&u.init&&function(e,u){if(";"===t.originalText[io(u)])return;e.range=[oo(e),io(u)]}(e,u);break}case"TSParenthesizedType":return e.typeAnnotation.range=[oo(e),io(e)],e.typeAnnotation;case"TSTypeParameter":if("string"==typeof e.name){const u=oo(e);e.name={type:"Identifier",name:e.name,range:[u,u+e.name.length]}}break;case"SequenceExpression":{const u=no(e.expressions);e.range=[oo(e),Math.min(io(u),io(e))];break}case"ClassProperty":e.key&&"TSPrivateIdentifier"===e.key.type&&"?"===ro(t.originalText,e.key,io)&&(e.optional=!0)}}))};function Fo(){if(void 0===po){var e=new ArrayBuffer(2),u=new Uint8Array(e),t=new Uint16Array(e);if(u[0]=1,u[1]=2,258===t[0])po="BE";else{if(513!==t[0])throw new Error("unable to figure out endianess");po="LE"}}return po}function Eo(){return void 0!==Ou.location?Ou.location.hostname:""}function Co(){return[]}function Ao(){return 0}function go(){return Number.MAX_VALUE}function mo(){return Number.MAX_VALUE}function ho(){return[]}function yo(){return"Browser"}function Bo(){return void 0!==Ou.navigator?Ou.navigator.appVersion:""}function ko(){}function bo(){}function Po(){return"javascript"}function xo(){return"browser"}function vo(){return"/tmp"}var wo=vo,So={EOL:"\n",arch:Po,platform:xo,tmpdir:wo,tmpDir:vo,networkInterfaces:ko,getNetworkInterfaces:bo,release:Bo,type:yo,cpus:ho,totalmem:mo,freemem:go,uptime:Ao,loadavg:Co,hostname:Eo,endianness:Fo},To=Object.freeze({__proto__:null,endianness:Fo,hostname:Eo,loadavg:Co,uptime:Ao,freemem:go,totalmem:mo,cpus:ho,type:yo,release:Bo,networkInterfaces:ko,getNetworkInterfaces:bo,arch:Po,platform:xo,tmpDir:vo,tmpdir:wo,EOL:"\n",default:So});const No=e=>{if("string"!=typeof e)throw new TypeError("Expected a string");const u=e.match(/(?:\r?\n)/g)||[];if(0===u.length)return;const t=u.filter((e=>"\r\n"===e)).length;return t>u.length-t?"\r\n":"\n"};var Io=No;Io.graceful=e=>"string"==typeof e&&No(e)||"\n";var Lo=f(To),Oo=function(e){const u=e.match(_o);return u?u[0].trimLeft():""},Ro=function(e){const u=e.match(_o);return u&&u[0]?e.substring(u[0].length):e},qo=function(e){return Ko(e).pragmas},jo=Ko,Vo=function({comments:e="",pragmas:u={}}){const t=(0,$o().default)(e)||Mo().EOL,n=" *",r=Object.keys(u),o=r.map((e=>Zo(e,u[e]))).reduce(((e,u)=>e.concat(u)),[]).map((e=>" * "+e+t)).join("");if(!e){if(0===r.length)return"";if(1===r.length&&!Array.isArray(u[r[0]])){const e=u[r[0]];return`/** ${Zo(r[0],e)[0]} */`}}const i=e.split(t).map((e=>` * ${e}`)).join(t)+t;return"/**"+t+(e?i:"")+(e&&r.length?n+t:"")+o+" */"};function Mo(){const e=Lo;return Mo=function(){return e},e}function $o(){const e=(u=Io)&&u.__esModule?u:{default:u};var u;return $o=function(){return e},e}const Uo=/\*\/$/,Go=/^\/\*\*/,_o=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,Xo=/(^|\s+)\/\/([^\r\n]*)/g,Ho=/^(\r?\n)+/,zo=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,Jo=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,Wo=/(\r?\n|^) *\* ?/g,Yo=[];function Ko(e){const u=(0,$o().default)(e)||Mo().EOL;e=e.replace(Go,"").replace(Uo,"").replace(Wo,"$1");let t="";for(;t!==e;)t=e,e=e.replace(zo,`${u}$1 $2${u}`);e=e.replace(Ho,"").trimRight();const n=Object.create(null),r=e.replace(Jo,"").replace(Ho,"").trimRight();let o;for(;o=Jo.exec(e);){const e=o[2].replace(Xo,"");"string"==typeof n[o[1]]||Array.isArray(n[o[1]])?n[o[1]]=Yo.concat(n[o[1]],e):n[o[1]]=e}return{comments:r,pragmas:n}}function Zo(e,u){return Yo.concat(u).map((u=>`@${e} ${u}`.trim()))}var Qo=Object.defineProperty({extract:Oo,strip:Ro,parse:qo,parseWithComments:jo,print:Vo},"__esModule",{value:!0});var ei={guessEndOfLine:function(e){const u=e.indexOf("\r");return u>=0?"\n"===e.charAt(u+1)?"crlf":"cr":"lf"},convertEndOfLineToChars:function(e){switch(e){case"cr":return"\r";case"crlf":return"\r\n";default:return"\n"}},countEndOfLineChars:function(e,u){let t;if("\n"===u)t=/\n/g;else if("\r"===u)t=/\r/g;else{if("\r\n"!==u)throw new Error(`Unexpected "eol" ${JSON.stringify(u)}.`);t=/\r\n/g}const n=e.match(t);return n?n.length:0},normalizeEndOfLine:function(e){return e.replace(/\r\n?/g,"\n")}};const{parseWithComments:ui,strip:ti,extract:ni,print:ri}=Qo,{getShebang:oi}=Kt,{normalizeEndOfLine:ii}=ei;function si(e){const u=oi(e);u&&(e=e.slice(u.length+1));const t=ni(e),{pragmas:n,comments:r}=ui(t);return{shebang:u,text:e,pragmas:n,comments:r}}var ai={hasPragma:function(e){const u=Object.keys(si(e).pragmas);return u.includes("prettier")||u.includes("format")},insertPragma:function(e){const{shebang:u,text:t,pragmas:n,comments:r}=si(e),o=ti(t),i=ri({pragmas:Object.assign({format:""},n),comments:r.trimStart()});return(u?`${u}\n`:"")+ii(i)+(o.startsWith("\n")?"\n":"\n\n")+o}};const{hasPragma:Di}=ai,{locStart:ci,locEnd:li}=tn;var pi=function(e){return e="function"==typeof e?{parse:e}:e,Object.assign({astFormat:"estree",hasPragma:Di,locStart:ci,locEnd:li},e)};const fi={0:"Unexpected token",28:"Unexpected token: '%0'",1:"Octal escape sequences are not allowed in strict mode",2:"Octal escape sequences are not allowed in template strings",3:"Unexpected token `#`",4:"Illegal Unicode escape sequence",5:"Invalid code point %0",6:"Invalid hexadecimal escape sequence",8:"Octal literals are not allowed in strict mode",7:"Decimal integer literals with a leading zero are forbidden in strict mode",9:"Expected number in radix %0",145:"Invalid left-hand side assignment to a destructible right-hand side",10:"Non-number found after exponent indicator",11:"Invalid BigIntLiteral",12:"No identifiers allowed directly after numeric literal",13:"Escapes \\8 or \\9 are not syntactically valid escapes",14:"Unterminated string literal",15:"Unterminated template literal",16:"Multiline comment was not closed properly",17:"The identifier contained dynamic unicode escape that was not closed",18:"Illegal character '%0'",19:"Missing hexadecimal digits",20:"Invalid implicit octal",21:"Invalid line break in string literal",22:"Only unicode escapes are legal in identifier names",23:"Expected '%0'",24:"Invalid left-hand side in assignment",25:"Invalid left-hand side in async arrow",26:'Calls to super must be in the "constructor" method of a class expression or class declaration that has a superclass',27:"Member access on super must be in a method",29:"Await expression not allowed in formal parameter",30:"Yield expression not allowed in formal parameter",92:"Unexpected token: 'escaped keyword'",31:"Unary expressions as the left operand of an exponentiation expression must be disambiguated with parentheses",119:"Async functions can only be declared at the top level or inside a block",32:"Unterminated regular expression",33:"Unexpected regular expression flag",34:"Duplicate regular expression flag '%0'",35:"%0 functions must have exactly %1 argument%2",36:"Setter function argument must not be a rest parameter",37:"%0 declaration must have a name in this context",38:"Function name may not contain any reserved words or be eval or arguments in strict mode",39:"The rest operator is missing an argument",40:"A getter cannot be a generator",41:"A computed property name must be followed by a colon or paren",130:"Object literal keys that are strings or numbers must be a method or have a colon",43:"Found `* async x(){}` but this should be `async * x(){}`",42:"Getters and setters can not be generators",44:"'%0' can not be generator method",45:"No line break is allowed after '=>'",46:"The left-hand side of the arrow can only be destructed through assignment",47:"The binding declaration is not destructible",48:"Async arrow can not be followed by new expression",49:"Classes may not have a static property named 'prototype'",50:"Class constructor may not be a %0",51:"Duplicate constructor method in class",52:"Invalid increment/decrement operand",53:"Invalid use of `new` keyword on an increment/decrement expression",54:"`=>` is an invalid assignment target",55:"Rest element may not have a trailing comma",56:"Missing initializer in %0 declaration",57:"'for-%0' loop head declarations can not have an initializer",58:"Invalid left-hand side in for-%0 loop: Must have a single binding",59:"Invalid shorthand property initializer",60:"Property name __proto__ appears more than once in object literal",61:"Let is disallowed as a lexically bound name",62:"Invalid use of '%0' inside new expression",63:"Illegal 'use strict' directive in function with non-simple parameter list",64:'Identifier "let" disallowed as left-hand side expression in strict mode',65:"Illegal continue statement",66:"Illegal break statement",67:"Cannot have `let[...]` as a var name in strict mode",68:"Invalid destructuring assignment target",69:"Rest parameter may not have a default initializer",70:"The rest argument must the be last parameter",71:"Invalid rest argument",73:"In strict mode code, functions can only be declared at top level or inside a block",74:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement",75:"Without web compatibility enabled functions can not be declared at top level, inside a block, or as the body of an if statement",76:"Class declaration can't appear in single-statement context",77:"Invalid left-hand side in for-%0",78:"Invalid assignment in for-%0",79:"for await (... of ...) is only valid in async functions and async generators",80:"The first token after the template expression should be a continuation of the template",82:"`let` declaration not allowed here and `let` cannot be a regular var name in strict mode",81:"`let \n [` is a restricted production at the start of a statement",83:"Catch clause requires exactly one parameter, not more (and no trailing comma)",84:"Catch clause parameter does not support default values",85:"Missing catch or finally after try",86:"More than one default clause in switch statement",87:"Illegal newline after throw",88:"Strict mode code may not include a with statement",89:"Illegal return statement",90:"The left hand side of the for-header binding declaration is not destructible",91:"new.target only allowed within functions",92:"'Unexpected token: 'escaped keyword'",93:"'#' not followed by identifier",99:"Invalid keyword",98:"Can not use 'let' as a class name",97:"'A lexical declaration can't define a 'let' binding",96:"Can not use `let` as variable name in strict mode",94:"'%0' may not be used as an identifier in this context",95:"Await is only valid in async functions",100:"The %0 keyword can only be used with the module goal",101:"Unicode codepoint must not be greater than 0x10FFFF",102:"%0 source must be string",103:"Only a identifier can be used to indicate alias",104:"Only '*' or '{...}' can be imported after default",105:"Trailing decorator may be followed by method",106:"Decorators can't be used with a constructor",107:"'%0' may not be used as an identifier in this context",108:"HTML comments are only allowed with web compatibility (Annex B)",109:"The identifier 'let' must not be in expression position in strict mode",110:"Cannot assign to `eval` and `arguments` in strict mode",111:"The left-hand side of a for-of loop may not start with 'let'",112:"Block body arrows can not be immediately invoked without a group",113:"Block body arrows can not be immediately accessed without a group",114:"Unexpected strict mode reserved word",115:"Unexpected eval or arguments in strict mode",116:"Decorators must not be followed by a semicolon",117:"Calling delete on expression not allowed in strict mode",118:"Pattern can not have a tail",120:"Can not have a `yield` expression on the left side of a ternary",121:"An arrow function can not have a postfix update operator",122:"Invalid object literal key character after generator star",123:"Private fields can not be deleted",125:"Classes may not have a field called constructor",124:"Classes may not have a private element named constructor",126:"A class field initializer may not contain arguments",127:"Generators can only be declared at the top level or inside a block",128:"Async methods are a restricted production and cannot have a newline following it",129:"Unexpected character after object literal property name",131:"Invalid key token",132:"Label '%0' has already been declared",133:"continue statement must be nested within an iteration statement",134:"Undefined label '%0'",135:"Trailing comma is disallowed inside import(...) arguments",136:"import() requires exactly one argument",137:"Cannot use new with import(...)",138:"... is not allowed in import()",139:"Expected '=>'",140:"Duplicate binding '%0'",141:"Cannot export a duplicate name '%0'",144:"Duplicate %0 for-binding",142:"Exported binding '%0' needs to refer to a top-level declared variable",143:"Unexpected private field",147:"Numeric separators are not allowed at the end of numeric literals",146:"Only one underscore is allowed as numeric separator",148:"JSX value should be either an expression or a quoted JSX text",149:"Expected corresponding JSX closing tag for %0",150:"Adjacent JSX elements must be wrapped in an enclosing tag",151:"JSX attributes must only be assigned a non-empty 'expression'",152:"'%0' has already been declared",153:"'%0' shadowed a catch clause binding",154:"Dot property must be an identifier",155:"Encountered invalid input after spread/rest argument",156:"Catch without try",157:"Finally without try",158:"Expected corresponding closing tag for JSX fragment",159:"Coalescing and logical operators used together in the same expression must be disambiguated with parentheses",160:"Invalid tagged template on optional chain",161:"Invalid optional chain from super property",162:"Invalid optional chain from new expression",163:'Cannot use "import.meta" outside a module',164:"Leading decorators must be attached to a class declaration"};class di extends SyntaxError{constructor(e,u,t,n,...r){const o="["+u+":"+t+"]: "+fi[n].replace(/%(\d+)/g,((e,u)=>r[u]));super(`${o}`),this.index=e,this.line=u,this.column=t,this.description=o,this.loc={line:u,column:t}}}function Fi(e,u,...t){throw new di(e.index,e.line,e.column,u,...t)}function Ei(e){throw new di(e.index,e.line,e.column,e.type,e.params)}function Ci(e,u,t,n,...r){throw new di(e,u,t,n,...r)}function Ai(e,u,t,n){throw new di(e,u,t,n)}const gi=((e,u)=>{const t=new Uint32Array(104448);let n=0,r=0;for(;n<3540;){const o=e[n++];if(o<0)r-=o;else{let i=e[n++];2&o&&(i=u[i]),1&o?t.fill(i,r,r+=e[n++]):t[r++]=i}}return t})([-1,2,24,2,25,2,5,-1,0,77595648,3,44,2,3,0,14,2,57,2,58,3,0,3,0,3168796671,0,4294956992,2,1,2,0,2,59,3,0,4,0,4294966523,3,0,4,2,16,2,60,2,0,0,4294836735,0,3221225471,0,4294901942,2,61,0,*********,3,0,2,0,4294951935,3,0,2,0,2683305983,0,2684354047,2,17,2,0,0,4294961151,3,0,2,2,19,2,0,0,*********,2,0,2,131,2,6,2,56,-1,2,37,0,4294443263,2,1,3,0,3,0,4294901711,2,39,0,4089839103,0,2961209759,0,1342439375,0,4294543342,0,3547201023,0,1577204103,0,4194240,0,4294688750,2,2,0,80831,0,4261478351,0,4294549486,2,2,0,2967484831,0,196559,0,3594373100,0,3288319768,0,8469959,2,194,2,3,0,3825204735,0,123747807,0,65487,0,4294828015,0,4092591615,0,1080049119,0,458703,2,3,2,0,0,2163244511,0,4227923919,0,4236247022,2,66,0,4284449919,0,851904,2,4,2,11,0,67076095,-1,2,67,0,1073741743,0,4093591391,-1,0,50331649,0,3265266687,2,32,0,4294844415,0,4278190047,2,18,2,129,-1,3,0,2,2,21,2,0,2,9,2,0,2,14,2,15,3,0,10,2,69,2,0,2,70,2,71,2,72,2,0,2,73,2,0,2,10,0,261632,2,23,3,0,2,2,12,2,4,3,0,18,2,74,2,5,3,0,2,2,75,0,2088959,2,27,2,8,0,909311,3,0,2,0,814743551,2,41,0,67057664,3,0,2,2,40,2,0,2,28,2,0,2,29,2,7,0,268374015,2,26,2,49,2,0,2,76,0,134153215,-1,2,6,2,0,2,7,0,2684354559,0,67044351,0,3221160064,0,1,-1,3,0,2,2,42,0,1046528,3,0,3,2,8,2,0,2,51,0,4294960127,2,9,2,38,2,10,0,4294377472,2,11,3,0,7,0,4227858431,3,0,8,2,12,2,0,2,78,2,9,2,0,2,79,2,80,2,81,-1,2,124,0,1048577,2,82,2,13,-1,2,13,0,131042,2,83,2,84,2,85,2,0,2,33,-83,2,0,2,53,2,7,3,0,4,0,1046559,2,0,2,14,2,0,0,2147516671,2,20,3,86,2,2,0,-16,2,87,0,524222462,2,4,2,0,0,4269801471,2,4,2,0,2,15,2,77,2,16,3,0,2,2,47,2,0,-1,2,17,-16,3,0,206,-2,3,0,655,2,18,3,0,36,2,68,-1,2,17,2,9,3,0,8,2,89,2,121,2,0,0,3220242431,3,0,3,2,19,2,90,2,91,3,0,2,2,92,2,0,2,93,2,94,2,0,0,4351,2,0,2,8,3,0,2,0,67043391,0,3909091327,2,0,2,22,2,8,2,18,3,0,2,0,67076097,2,7,2,0,2,20,0,67059711,0,4236247039,3,0,2,0,939524103,0,8191999,2,97,2,98,2,15,2,21,3,0,3,0,67057663,3,0,349,2,99,2,100,2,6,-264,3,0,11,2,22,3,0,2,2,31,-1,0,3774349439,2,101,2,102,3,0,2,2,19,2,103,3,0,10,2,9,2,17,2,0,2,45,2,0,2,30,2,104,2,23,0,1638399,2,172,2,105,3,0,3,2,18,2,24,2,25,2,5,2,26,2,0,2,7,2,106,-1,2,107,2,108,2,109,-1,3,0,3,2,11,-2,2,0,2,27,-3,2,150,-4,2,18,2,0,2,35,0,1,2,0,2,62,2,28,2,11,2,9,2,0,2,110,-1,3,0,4,2,9,2,21,2,111,2,6,2,0,2,112,2,0,2,48,-4,3,0,9,2,20,2,29,2,30,-4,2,113,2,114,2,29,2,20,2,7,-2,2,115,2,29,2,31,-2,2,0,2,116,-2,0,4277137519,0,2269118463,-1,3,18,2,-1,2,32,2,36,2,0,3,29,2,2,34,2,19,-3,3,0,2,2,33,-1,2,0,2,34,2,0,2,34,2,0,2,46,-10,2,0,0,203775,-2,2,18,2,43,2,35,-2,2,17,2,117,2,20,3,0,2,2,36,0,2147549120,2,0,2,11,2,17,2,135,2,0,2,37,2,52,0,5242879,3,0,2,0,402644511,-1,2,120,0,1090519039,-2,2,122,2,38,2,0,0,67045375,2,39,0,4226678271,0,3766565279,0,2039759,-4,3,0,2,0,3288270847,0,3,3,0,2,0,67043519,-5,2,0,0,4282384383,0,1056964609,-1,3,0,2,0,67043345,-1,2,0,2,40,2,41,-1,2,10,2,42,-6,2,0,2,11,-3,3,0,2,0,2147484671,2,125,0,4190109695,2,50,-2,2,126,0,4244635647,0,27,2,0,2,7,2,43,2,0,2,63,-1,2,0,2,40,-8,2,54,2,44,0,67043329,2,127,2,45,0,8388351,-2,2,128,0,3028287487,2,46,2,130,0,33259519,2,41,-9,2,20,-5,2,64,-2,3,0,28,2,31,-3,3,0,3,2,47,3,0,6,2,48,-85,3,0,33,2,47,-126,3,0,18,2,36,-269,3,0,17,2,40,2,7,2,41,-2,2,17,2,49,2,0,2,20,2,50,2,132,2,23,-21,3,0,2,-4,3,0,2,0,4294936575,2,0,0,4294934783,-2,0,196635,3,0,191,2,51,3,0,38,2,29,-1,2,33,-279,3,0,8,2,7,-1,2,133,2,52,3,0,11,2,6,-72,3,0,3,2,134,0,1677656575,-166,0,4161266656,0,4071,0,15360,-4,0,28,-13,3,0,2,2,37,2,0,2,136,2,137,2,55,2,0,2,138,2,139,2,140,3,0,10,2,141,2,142,2,15,3,37,2,3,53,2,3,54,2,0,4294954999,2,0,-16,2,0,2,88,2,0,0,2105343,0,4160749584,0,65534,-42,0,4194303871,0,2011,-6,2,0,0,1073684479,0,17407,-11,2,0,2,31,-40,3,0,6,0,8323103,-1,3,0,2,2,42,-37,2,55,2,144,2,145,2,146,2,147,2,148,-105,2,24,-32,3,0,1334,2,9,-1,3,0,129,2,27,3,0,6,2,9,3,0,180,2,149,3,0,233,0,1,-96,3,0,16,2,9,-47,3,0,154,2,56,-22381,3,0,7,2,23,-6130,3,5,2,-1,0,69207040,3,44,2,3,0,14,2,57,2,58,-3,0,3168731136,0,4294956864,2,1,2,0,2,59,3,0,4,0,4294966275,3,0,4,2,16,2,60,2,0,2,33,-1,2,17,2,61,-1,2,0,2,56,0,4294885376,3,0,2,0,3145727,0,2617294944,0,4294770688,2,23,2,62,3,0,2,0,131135,2,95,0,70256639,0,71303167,0,272,2,40,2,56,-1,2,37,2,30,-1,2,96,2,63,0,4278255616,0,4294836227,0,4294549473,0,600178175,0,2952806400,0,268632067,0,4294543328,0,57540095,0,1577058304,0,1835008,0,4294688736,2,65,2,64,0,33554435,2,123,2,65,2,151,0,131075,0,3594373096,0,67094296,2,64,-1,0,4294828e3,0,603979263,2,160,0,3,0,4294828001,0,602930687,2,183,0,393219,0,4294828016,0,671088639,0,2154840064,0,4227858435,0,4236247008,2,66,2,36,-1,2,4,0,917503,2,36,-1,2,67,0,537788335,0,4026531935,-1,0,1,-1,2,32,2,68,0,7936,-3,2,0,0,2147485695,0,1010761728,0,4292984930,0,16387,2,0,2,14,2,15,3,0,10,2,69,2,0,2,70,2,71,2,72,2,0,2,73,2,0,2,11,-1,2,23,3,0,2,2,12,2,4,3,0,18,2,74,2,5,3,0,2,2,75,0,253951,3,19,2,0,122879,2,0,2,8,0,276824064,-2,3,0,2,2,40,2,0,0,4294903295,2,0,2,29,2,7,-1,2,17,2,49,2,0,2,76,2,41,-1,2,20,2,0,2,27,-2,0,128,-2,2,77,2,8,0,4064,-1,2,119,0,4227907585,2,0,2,118,2,0,2,48,2,173,2,9,2,38,2,10,-1,0,74440192,3,0,6,-2,3,0,8,2,12,2,0,2,78,2,9,2,0,2,79,2,80,2,81,-3,2,82,2,13,-3,2,83,2,84,2,85,2,0,2,33,-83,2,0,2,53,2,7,3,0,4,0,817183,2,0,2,14,2,0,0,33023,2,20,3,86,2,-17,2,87,0,524157950,2,4,2,0,2,88,2,4,2,0,2,15,2,77,2,16,3,0,2,2,47,2,0,-1,2,17,-16,3,0,206,-2,3,0,655,2,18,3,0,36,2,68,-1,2,17,2,9,3,0,8,2,89,0,3072,2,0,0,2147516415,2,9,3,0,2,2,23,2,90,2,91,3,0,2,2,92,2,0,2,93,2,94,0,4294965179,0,7,2,0,2,8,2,91,2,8,-1,0,1761345536,2,95,0,4294901823,2,36,2,18,2,96,2,34,2,166,0,2080440287,2,0,2,33,2,143,0,3296722943,2,0,0,1046675455,0,939524101,0,1837055,2,97,2,98,2,15,2,21,3,0,3,0,7,3,0,349,2,99,2,100,2,6,-264,3,0,11,2,22,3,0,2,2,31,-1,0,2700607615,2,101,2,102,3,0,2,2,19,2,103,3,0,10,2,9,2,17,2,0,2,45,2,0,2,30,2,104,-3,2,105,3,0,3,2,18,-1,3,5,2,2,26,2,0,2,7,2,106,-1,2,107,2,108,2,109,-1,3,0,3,2,11,-2,2,0,2,27,-8,2,18,2,0,2,35,-1,2,0,2,62,2,28,2,29,2,9,2,0,2,110,-1,3,0,4,2,9,2,17,2,111,2,6,2,0,2,112,2,0,2,48,-4,3,0,9,2,20,2,29,2,30,-4,2,113,2,114,2,29,2,20,2,7,-2,2,115,2,29,2,31,-2,2,0,2,116,-2,0,4277075969,2,29,-1,3,18,2,-1,2,32,2,117,2,0,3,29,2,2,34,2,19,-3,3,0,2,2,33,-1,2,0,2,34,2,0,2,34,2,0,2,48,-10,2,0,0,197631,-2,2,18,2,43,2,118,-2,2,17,2,117,2,20,2,119,2,51,-2,2,119,2,23,2,17,2,33,2,119,2,36,0,4294901904,0,4718591,2,119,2,34,0,335544350,-1,2,120,2,121,-2,2,122,2,38,2,7,-1,2,123,2,65,0,3758161920,0,3,-4,2,0,2,27,0,2147485568,0,3,2,0,2,23,0,176,-5,2,0,2,47,2,186,-1,2,0,2,23,2,197,-1,2,0,0,16779263,-2,2,11,-7,2,0,2,121,-3,3,0,2,2,124,2,125,0,2147549183,0,2,-2,2,126,2,35,0,10,0,4294965249,0,67633151,0,4026597376,2,0,0,536871935,-1,2,0,2,40,-8,2,54,2,47,0,1,2,127,2,23,-3,2,128,2,35,2,129,2,130,0,16778239,-10,2,34,-5,2,64,-2,3,0,28,2,31,-3,3,0,3,2,47,3,0,6,2,48,-85,3,0,33,2,47,-126,3,0,18,2,36,-269,3,0,17,2,40,2,7,-3,2,17,2,131,2,0,2,23,2,48,2,132,2,23,-21,3,0,2,-4,3,0,2,0,67583,-1,2,103,-2,0,11,3,0,191,2,51,3,0,38,2,29,-1,2,33,-279,3,0,8,2,7,-1,2,133,2,52,3,0,11,2,6,-72,3,0,3,2,134,2,135,-187,3,0,2,2,37,2,0,2,136,2,137,2,55,2,0,2,138,2,139,2,140,3,0,10,2,141,2,142,2,15,3,37,2,3,53,2,3,54,2,2,143,-73,2,0,0,1065361407,0,16384,-11,2,0,2,121,-40,3,0,6,2,117,-1,3,0,2,0,2063,-37,2,55,2,144,2,145,2,146,2,147,2,148,-138,3,0,1334,2,9,-1,3,0,129,2,27,3,0,6,2,9,3,0,180,2,149,3,0,233,0,1,-96,3,0,16,2,9,-47,3,0,154,2,56,-28517,2,0,0,1,-1,2,124,2,0,0,8193,-21,2,193,0,10255,0,4,-11,2,64,2,171,-1,0,71680,-1,2,161,0,4292900864,0,805306431,-5,2,150,-1,2,157,-1,0,6144,-2,2,127,-1,2,154,-1,0,2147532800,2,151,2,165,2,0,2,164,0,524032,0,4,-4,2,190,0,205128192,0,1333757536,0,2147483696,0,423953,0,747766272,0,2717763192,0,4286578751,0,278545,2,152,0,4294886464,0,33292336,0,417809,2,152,0,1327482464,0,4278190128,0,700594195,0,1006647527,0,4286497336,0,4160749631,2,153,0,469762560,0,4171219488,0,8323120,2,153,0,202375680,0,3214918176,0,4294508592,2,153,-1,0,983584,0,48,0,58720273,0,3489923072,0,10517376,0,4293066815,0,1,0,2013265920,2,177,2,0,0,2089,0,3221225552,0,201375904,2,0,-2,0,256,0,122880,0,16777216,2,150,0,4160757760,2,0,-6,2,167,-11,0,3263218176,-1,0,49664,0,2160197632,0,8388802,-1,0,12713984,-1,2,154,2,159,2,178,-2,2,162,-20,0,3758096385,-2,2,155,0,4292878336,2,90,2,169,0,4294057984,-2,2,163,2,156,2,175,-2,2,155,-1,2,182,-1,2,170,2,124,0,4026593280,0,14,0,4292919296,-1,2,158,0,939588608,-1,0,805306368,-1,2,124,0,1610612736,2,156,2,157,2,4,2,0,-2,2,158,2,159,-3,0,267386880,-1,2,160,0,7168,-1,0,65024,2,154,2,161,2,179,-7,2,168,-8,2,162,-1,0,1426112704,2,163,-1,2,164,0,271581216,0,2149777408,2,23,2,161,2,124,0,851967,2,180,-1,2,23,2,181,-4,2,158,-20,2,195,2,165,-56,0,3145728,2,185,-4,2,166,2,124,-4,0,32505856,-1,2,167,-1,0,2147385088,2,90,1,2155905152,2,-3,2,103,2,0,2,168,-2,2,169,-6,2,170,0,4026597375,0,1,-1,0,1,-1,2,171,-3,2,117,2,64,-2,2,166,-2,2,176,2,124,-878,2,159,-36,2,172,-1,2,201,-10,2,188,-5,2,174,-6,0,4294965251,2,27,-1,2,173,-1,2,174,-2,0,4227874752,-3,0,2146435072,2,159,-2,0,1006649344,2,124,-1,2,90,0,201375744,-3,0,134217720,2,90,0,4286677377,0,32896,-1,2,158,-3,2,175,-349,2,176,0,1920,2,177,3,0,264,-11,2,157,-2,2,178,2,0,0,520617856,0,2692743168,0,36,-3,0,524284,-11,2,23,-1,2,187,-1,2,184,0,3221291007,2,178,-1,2,202,0,2158720,-3,2,159,0,1,-4,2,124,0,3808625411,0,3489628288,2,200,0,1207959680,0,3221274624,2,0,-3,2,179,0,120,0,7340032,-2,2,180,2,4,2,23,2,163,3,0,4,2,159,-1,2,181,2,177,-1,0,8176,2,182,2,179,2,183,-1,0,4290773232,2,0,-4,2,163,2,189,0,15728640,2,177,-1,2,161,-1,0,4294934512,3,0,4,-9,2,90,2,170,2,184,3,0,4,0,704,0,1849688064,2,185,-1,2,124,0,4294901887,2,0,0,130547712,0,1879048192,2,199,3,0,2,-1,2,186,2,187,-1,0,17829776,0,2025848832,0,4261477888,-2,2,0,-1,0,4286580608,-1,0,29360128,2,192,0,16252928,0,3791388672,2,38,3,0,2,-2,2,196,2,0,-1,2,103,-1,0,66584576,-1,2,191,3,0,9,2,124,-1,0,4294755328,3,0,2,-1,2,161,2,178,3,0,2,2,23,2,188,2,90,-2,0,245760,0,2147418112,-1,2,150,2,203,0,4227923456,-1,2,164,2,161,2,90,-3,0,4292870145,0,262144,2,124,3,0,2,0,1073758848,2,189,-1,0,4227921920,2,190,0,68289024,0,528402016,0,4292927536,3,0,4,-2,0,268435456,2,91,-2,2,191,3,0,5,-1,2,192,2,163,2,0,-2,0,4227923936,2,62,-1,2,155,2,95,2,0,2,154,2,158,3,0,6,-1,2,177,3,0,3,-2,0,2146959360,0,9440640,0,104857600,0,4227923840,3,0,2,0,768,2,193,2,77,-2,2,161,-2,2,119,-1,2,155,3,0,8,0,512,0,8388608,2,194,2,172,2,187,0,4286578944,3,0,2,0,1152,0,1266679808,2,191,0,576,0,4261707776,2,95,3,0,9,2,155,3,0,5,2,16,-1,0,2147221504,-28,2,178,3,0,3,-3,0,4292902912,-6,2,96,3,0,85,-33,0,4294934528,3,0,126,-18,2,195,3,0,269,-17,2,155,2,124,2,198,3,0,2,2,23,0,4290822144,-2,0,67174336,0,520093700,2,17,3,0,21,-2,2,179,3,0,3,-2,0,30720,-1,0,32512,3,0,2,0,4294770656,-191,2,174,-38,2,170,2,0,2,196,3,0,279,-8,2,124,2,0,0,4294508543,0,65295,-11,2,177,3,0,72,-3,0,3758159872,0,201391616,3,0,155,-7,2,170,-1,0,384,-1,0,133693440,-3,2,196,-2,2,26,3,0,4,2,169,-2,2,90,2,155,3,0,4,-2,2,164,-1,2,150,0,335552923,2,197,-1,0,538974272,0,2214592512,0,132e3,-10,0,192,-8,0,12288,-21,0,134213632,0,4294901761,3,0,42,0,100663424,0,4294965284,3,0,6,-1,0,3221282816,2,198,3,0,11,-1,2,199,3,0,40,-6,0,4286578784,2,0,-2,0,1006694400,3,0,24,2,35,-1,2,94,3,0,2,0,1,2,163,3,0,6,2,197,0,4110942569,0,1432950139,0,2701658217,0,4026532864,0,4026532881,2,0,2,45,3,0,8,-1,2,158,-2,2,169,0,98304,0,65537,2,170,-5,0,4294950912,2,0,2,118,0,65528,2,177,0,4294770176,2,26,3,0,4,-30,2,174,0,3758153728,-3,2,169,-2,2,155,2,188,2,158,-1,2,191,-1,2,161,0,4294754304,3,0,2,-3,0,33554432,-2,2,200,-3,2,169,0,4175478784,2,201,0,4286643712,0,4286644216,2,0,-4,2,202,-1,2,165,0,4227923967,3,0,32,-1334,2,163,2,0,-129,2,94,-6,2,163,-180,2,203,-233,2,4,3,0,96,-16,2,163,3,0,47,-154,2,165,3,0,22381,-7,2,17,3,0,6128],[4294967295,4294967291,4092460543,4294828031,4294967294,134217726,268435455,2147483647,1048575,1073741823,3892314111,134217727,1061158911,536805376,4294910143,4160749567,4294901759,4294901760,536870911,262143,8388607,4294902783,4294918143,65535,67043328,2281701374,4294967232,2097151,4294903807,4194303,255,67108863,4294967039,511,524287,131071,127,4292870143,4294902271,4294549487,33554431,1023,67047423,4294901888,4286578687,4294770687,67043583,32767,15,2047999,67043343,16777215,4294902e3,4294934527,4294966783,4294967279,2047,262083,20511,4290772991,41943039,493567,4294959104,603979775,65536,602799615,805044223,4294965206,8191,1031749119,4294917631,2134769663,4286578493,4282253311,4294942719,33540095,4294905855,4294967264,2868854591,1608515583,265232348,534519807,2147614720,1060109444,4093640016,17376,2139062143,224,4169138175,4294909951,4286578688,4294967292,4294965759,2044,4292870144,4294966272,4294967280,8289918,4294934399,4294901775,4294965375,1602223615,4294967259,4294443008,268369920,4292804608,486341884,4294963199,3087007615,1073692671,4128527,4279238655,4294902015,4294966591,2445279231,3670015,3238002687,31,63,4294967288,4294705151,4095,3221208447,4294549472,2147483648,4285526655,4294966527,4294705152,4294966143,64,4294966719,16383,3774873592,458752,536807423,67043839,3758096383,3959414372,3755993023,2080374783,4294835295,4294967103,4160749565,4087,184024726,2862017156,1593309078,268434431,268434414,4294901763,536870912,2952790016,202506752,139264,402653184,4261412864,4227922944,49152,61440,3758096384,117440512,65280,3233808384,3221225472,2097152,4294965248,32768,57152,67108864,4293918720,4290772992,25165824,57344,4227915776,4278190080,4227907584,65520,4026531840,4227858432,4160749568,3758129152,4294836224,63488,1073741824,4294967040,4194304,251658240,196608,4294963200,64512,417808,4227923712,12582912,50331648,65472,4294967168,4294966784,16,4294917120,2080374784,4096,65408,524288,65532]);function mi(e){return e.column++,e.currentChar=e.source.charCodeAt(++e.index)}function hi(e,u){if(55296!=(64512&u))return 0;const t=e.source.charCodeAt(e.index+1);return 56320!=(64512&t)?0:(u=e.currentChar=65536+((1023&u)<<10)+(1023&t),0==(1&gi[0+(u>>>5)]>>>u)&&Fi(e,18,ki(u)),e.index++,e.column++,1)}function yi(e,u){e.currentChar=e.source.charCodeAt(++e.index),e.flags|=1,0==(4&u)&&(e.column=0,e.line++)}function Bi(e){e.flags|=1,e.currentChar=e.source.charCodeAt(++e.index),e.column=0,e.line++}function ki(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(e>>>10)+String.fromCharCode(1023&e)}function bi(e){return e<65?e-48:e-65+10&15}const Pi=[0,0,0,0,0,0,0,0,0,0,1032,0,0,2056,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8192,0,3,0,0,8192,0,0,0,256,0,33024,0,0,242,242,114,114,114,114,114,114,594,594,0,0,16384,0,0,0,0,67,67,67,67,67,67,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,0,1,0,0,4099,0,71,71,71,71,71,71,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,16384,0,0,0,0],xi=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0],vi=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0];function wi(e){return e<=127?xi[e]:1&gi[34816+(e>>>5)]>>>e}function Si(e){return e<=127?vi[e]:1&gi[0+(e>>>5)]>>>e||8204===e||8205===e}const Ti=["SingleLine","MultiLine","HTMLOpen","HTMLClose","HashbangComment"];function Ni(e,u,t,n,r,o,i,s){return 2048&n&&Fi(e,0),Ii(e,u,t,r,o,i,s)}function Ii(e,u,t,n,r,o,i){const{index:s}=e;for(e.tokenPos=e.index,e.linePos=e.line,e.colPos=e.column;e.index<e.end;){if(8&Pi[e.currentChar]){const t=13===e.currentChar;Bi(e),t&&e.index<e.end&&10===e.currentChar&&(e.currentChar=u.charCodeAt(++e.index));break}if((8232^e.currentChar)<=1){Bi(e);break}mi(e),e.tokenPos=e.index,e.linePos=e.line,e.colPos=e.column}if(e.onComment){const t={start:{line:o,column:i},end:{line:e.linePos,column:e.colPos}};e.onComment(Ti[255&n],u.slice(s,e.tokenPos),r,e.tokenPos,t)}return 1|t}function Li(e,u,t){const{index:n}=e;for(;e.index<e.end;)if(e.currentChar<43){let r=!1;for(;42===e.currentChar;)if(r||(t&=-5,r=!0),47===mi(e)){if(mi(e),e.onComment){const t={start:{line:e.linePos,column:e.colPos},end:{line:e.line,column:e.column}};e.onComment(Ti[1],u.slice(n,e.index-2),n-2,e.index,t)}return e.tokenPos=e.index,e.linePos=e.line,e.colPos=e.column,t}if(r)continue;8&Pi[e.currentChar]?13===e.currentChar?(t|=5,Bi(e)):(yi(e,t),t=-5&t|1):mi(e)}else(8232^e.currentChar)<=1?(t=-5&t|1,Bi(e)):(t&=-5,mi(e));Fi(e,16)}function Oi(e,u){const t=e.index;let n=0;e:for(;;){const u=e.currentChar;if(mi(e),1&n)n&=-2;else switch(u){case 47:if(n)break;break e;case 92:n|=1;break;case 91:n|=2;break;case 93:n&=1;break;case 13:case 10:case 8232:case 8233:Fi(e,32)}if(e.index>=e.source.length)return Fi(e,32)}const r=e.index-1;let o=0,i=e.currentChar;const{index:s}=e;for(;Si(i);){switch(i){case 103:2&o&&Fi(e,34,"g"),o|=2;break;case 105:1&o&&Fi(e,34,"i"),o|=1;break;case 109:4&o&&Fi(e,34,"m"),o|=4;break;case 117:16&o&&Fi(e,34,"g"),o|=16;break;case 121:8&o&&Fi(e,34,"y"),o|=8;break;case 115:12&o&&Fi(e,34,"s"),o|=12;break;default:Fi(e,33)}i=mi(e)}const a=e.source.slice(s,e.index),D=e.source.slice(t,r);return e.tokenRegExp={pattern:D,flags:a},512&u&&(e.tokenRaw=e.source.slice(e.tokenPos,e.index)),e.tokenValue=function(e,u,t){try{return new RegExp(u,t)}catch(u){Fi(e,32)}}(e,D,a),65540}function Ri(e,u,t){const{index:n}=e;let r="",o=mi(e),i=e.index;for(;0==(8&Pi[o]);){if(o===t)return r+=e.source.slice(i,e.index),mi(e),512&u&&(e.tokenRaw=e.source.slice(n,e.index)),e.tokenValue=r,134283267;if(8==(8&o)&&92===o){if(r+=e.source.slice(i,e.index),o=mi(e),o<127||8232===o||8233===o){const t=qi(e,u,o);t>=0?r+=ki(t):ji(e,t,0)}else r+=ki(o);i=e.index+1}e.index>=e.end&&Fi(e,14),o=mi(e)}Fi(e,14)}function qi(e,u,t){switch(t){case 98:return 8;case 102:return 12;case 114:return 13;case 110:return 10;case 116:return 9;case 118:return 11;case 13:if(e.index<e.end){const u=e.source.charCodeAt(e.index+1);10===u&&(e.index=e.index+1,e.currentChar=u)}case 10:case 8232:case 8233:return e.column=-1,e.line++,-1;case 48:case 49:case 50:case 51:{let n=t-48,r=e.index+1,o=e.column+1;if(r<e.end){const t=e.source.charCodeAt(r);if(0==(32&Pi[t])){if((0!==n||512&Pi[t])&&1024&u)return-2}else{if(1024&u)return-2;if(e.currentChar=t,n=n<<3|t-48,r++,o++,r<e.end){const u=e.source.charCodeAt(r);32&Pi[u]&&(e.currentChar=u,n=n<<3|u-48,r++,o++)}e.flags|=64,e.index=r-1,e.column=o-1}}return n}case 52:case 53:case 54:case 55:{if(1024&u)return-2;let n=t-48;const r=e.index+1,o=e.column+1;if(r<e.end){const u=e.source.charCodeAt(r);32&Pi[u]&&(n=n<<3|u-48,e.currentChar=u,e.index=r,e.column=o)}return e.flags|=64,n}case 120:{const u=mi(e);if(0==(64&Pi[u]))return-4;const t=bi(u),n=mi(e);if(0==(64&Pi[n]))return-4;return t<<4|bi(n)}case 117:{const u=mi(e);if(123===e.currentChar){let u=0;for(;0!=(64&Pi[mi(e)]);)if(u=u<<4|bi(e.currentChar),u>1114111)return-5;return e.currentChar<1||125!==e.currentChar?-4:u}{if(0==(64&Pi[u]))return-4;const t=e.source.charCodeAt(e.index+1);if(0==(64&Pi[t]))return-4;const n=e.source.charCodeAt(e.index+2);if(0==(64&Pi[n]))return-4;const r=e.source.charCodeAt(e.index+3);return 0==(64&Pi[r])?-4:(e.index+=3,e.column+=3,e.currentChar=e.source.charCodeAt(e.index),bi(u)<<12|bi(t)<<8|bi(n)<<4|bi(r))}}case 56:case 57:if(0==(256&u))return-3;default:return t}}function ji(e,u,t){switch(u){case-1:return;case-2:Fi(e,t?2:1);case-3:Fi(e,13);case-4:Fi(e,6);case-5:Fi(e,101)}}function Vi(e,u){const{index:t}=e;let n=67174409,r="",o=mi(e);for(;96!==o;){if(36===o&&123===e.source.charCodeAt(e.index+1)){mi(e),n=67174408;break}if(8==(8&o)&&92===o)if(o=mi(e),o>126)r+=ki(o);else{const t=qi(e,1024|u,o);if(t>=0)r+=ki(t);else{if(-1!==t&&65536&u){r=void 0,o=Mi(e,o),o<0&&(n=67174408);break}ji(e,t,1)}}else e.index<e.end&&13===o&&10===e.source.charCodeAt(e.index)&&(r+=ki(o),e.currentChar=e.source.charCodeAt(++e.index)),((83&o)<3&&10===o||(8232^o)<=1)&&(e.column=-1,e.line++),r+=ki(o);e.index>=e.end&&Fi(e,15),o=mi(e)}return mi(e),e.tokenValue=r,e.tokenRaw=e.source.slice(t+1,e.index-(67174409===n?1:2)),n}function Mi(e,u){for(;96!==u;){switch(u){case 36:{const t=e.index+1;if(t<e.end&&123===e.source.charCodeAt(t))return e.index=t,e.column++,-u;break}case 10:case 8232:case 8233:e.column=-1,e.line++}e.index>=e.end&&Fi(e,15),u=mi(e)}return u}function $i(e,u){return e.index>=e.end&&Fi(e,0),e.index--,e.column--,Vi(e,u)}function Ui(e,u,t){let n=e.currentChar,r=0,o=9,i=64&t?0:1,s=0,a=0;if(64&t)r="."+Gi(e,n),n=e.currentChar,110===n&&Fi(e,11);else{if(48===n)if(n=mi(e),120==(32|n)){for(t=136,n=mi(e);4160&Pi[n];)95!==n?(a=1,r=16*r+bi(n),s++,n=mi(e)):(a||Fi(e,146),a=0,n=mi(e));(s<1||!a)&&Fi(e,s<1?19:147)}else if(111==(32|n)){for(t=132,n=mi(e);4128&Pi[n];)95!==n?(a=1,r=8*r+(n-48),s++,n=mi(e)):(a||Fi(e,146),a=0,n=mi(e));(s<1||!a)&&Fi(e,s<1?0:147)}else if(98==(32|n)){for(t=130,n=mi(e);4224&Pi[n];)95!==n?(a=1,r=2*r+(n-48),s++,n=mi(e)):(a||Fi(e,146),a=0,n=mi(e));(s<1||!a)&&Fi(e,s<1?0:147)}else if(32&Pi[n])for(1024&u&&Fi(e,1),t=1;16&Pi[n];){if(512&Pi[n]){t=32,i=0;break}r=8*r+(n-48),n=mi(e)}else 512&Pi[n]?(1024&u&&Fi(e,1),e.flags|=64,t=32):95===n&&Fi(e,0);if(48&t){if(i){for(;o>=0&&4112&Pi[n];)95!==n?(a=0,r=10*r+(n-48),n=mi(e),--o):(n=mi(e),(95===n||32&t)&&Ai(e.index,e.line,e.index+1,146),a=1);if(a&&Ai(e.index,e.line,e.index+1,147),o>=0&&!wi(n)&&46!==n)return e.tokenValue=r,512&u&&(e.tokenRaw=e.source.slice(e.tokenPos,e.index)),134283266}r+=Gi(e,n),n=e.currentChar,46===n&&(95===mi(e)&&Fi(e,0),t=64,r+="."+Gi(e,e.currentChar),n=e.currentChar)}}const D=e.index;let c=0;if(110===n&&128&t)c=1,n=mi(e);else if(101==(32|n)){n=mi(e),256&Pi[n]&&(n=mi(e));const{index:u}=e;(16&Pi[n])<1&&Fi(e,10),r+=e.source.substring(D,u)+Gi(e,n),n=e.currentChar}return(e.index<e.end&&16&Pi[n]||wi(n))&&Fi(e,12),c?(e.tokenRaw=e.source.slice(e.tokenPos,e.index),e.tokenValue=BigInt(r),134283389):(e.tokenValue=15&t?r:32&t?parseFloat(e.source.substring(e.tokenPos,e.index)):+r,512&u&&(e.tokenRaw=e.source.slice(e.tokenPos,e.index)),134283266)}function Gi(e,u){let t=0,n=e.index,r="";for(;4112&Pi[u];)if(95!==u)t=0,u=mi(e);else{const{index:o}=e;95===(u=mi(e))&&Ai(e.index,e.line,e.index+1,146),t=1,r+=e.source.substring(n,o),n=e.index}return t&&Ai(e.index,e.line,e.index+1,147),r+e.source.substring(n,e.index)}const _i=["end of source","identifier","number","string","regular expression","false","true","null","template continuation","template tail","=>","(","{",".","...","}",")",";",",","[","]",":","?","'",'"',"</","/>","++","--","=","<<=",">>=",">>>=","**=","+=","-=","*=","/=","%=","^=","|=","&=","||=","&&=","??=","typeof","delete","void","!","~","+","-","in","instanceof","*","%","/","**","&&","||","===","!==","==","!=","<=",">=","<",">","<<",">>",">>>","&","|","^","var","let","const","break","case","catch","class","continue","debugger","default","do","else","export","extends","finally","for","function","if","import","new","return","super","switch","this","throw","try","while","with","implements","interface","package","private","protected","public","static","yield","as","async","await","constructor","get","set","from","of","enum","eval","arguments","escaped keyword","escaped future reserved keyword","reserved if strict","#","BigIntLiteral","??","?.","WhiteSpace","Illegal","LineTerminator","PrivateField","Template","@","target","meta","LineFeed","Escaped","JSXText"],Xi=Object.create(null,{this:{value:86113},function:{value:86106},if:{value:20571},return:{value:20574},var:{value:86090},else:{value:20565},for:{value:20569},new:{value:86109},in:{value:8738868},typeof:{value:16863277},while:{value:20580},case:{value:20558},break:{value:20557},try:{value:20579},catch:{value:20559},delete:{value:16863278},throw:{value:86114},switch:{value:86112},continue:{value:20561},default:{value:20563},instanceof:{value:8476725},do:{value:20564},void:{value:16863279},finally:{value:20568},async:{value:209007},await:{value:209008},class:{value:86096},const:{value:86092},constructor:{value:12401},debugger:{value:20562},export:{value:20566},extends:{value:20567},false:{value:86021},from:{value:12404},get:{value:12402},implements:{value:36966},import:{value:86108},interface:{value:36967},let:{value:241739},null:{value:86023},of:{value:274549},package:{value:36968},private:{value:36969},protected:{value:36970},public:{value:36971},set:{value:12403},static:{value:36972},super:{value:86111},true:{value:86022},with:{value:20581},yield:{value:241773},enum:{value:86134},eval:{value:537079927},as:{value:77934},arguments:{value:537079928},target:{value:143494},meta:{value:143495}});function Hi(e,u,t){for(;vi[mi(e)];);return e.tokenValue=e.source.slice(e.tokenPos,e.index),92!==e.currentChar&&e.currentChar<126?Xi[e.tokenValue]||208897:Ji(e,u,0,t)}function zi(e,u){const t=Yi(e);return Si(t)||Fi(e,4),e.tokenValue=ki(t),Ji(e,u,1,4&Pi[t])}function Ji(e,u,t,n){let r=e.index;for(;e.index<e.end;)if(92===e.currentChar){e.tokenValue+=e.source.slice(r,e.index),t=1;const u=Yi(e);Si(u)||Fi(e,4),n=n&&4&Pi[u],e.tokenValue+=ki(u),r=e.index}else{if(!Si(e.currentChar)&&!hi(e,e.currentChar))break;mi(e)}e.index<=e.end&&(e.tokenValue+=e.source.slice(r,e.index));const o=e.tokenValue.length;if(n&&o>=2&&o<=11){const n=Xi[e.tokenValue];return void 0===n?208897:t?1024&u?209008===n&&0==(4196352&u)?n:36972===n||36864==(36864&n)?122:121:1073741824&u&&0==(8192&u)&&20480==(20480&n)?n:241773===n?1073741824&u?143483:2097152&u?121:n:209007===n&&1073741824&u?143483:36864==(36864&n)||209008===n&&0==(4194304&u)?n:121:n}return 208897}function Wi(e){return wi(mi(e))||Fi(e,93),131}function Yi(e){return 117!==e.source.charCodeAt(e.index+1)&&Fi(e,4),e.currentChar=e.source.charCodeAt(e.index+=2),function(e){let u=0;const t=e.currentChar;if(123===t){const t=e.index-2;for(;64&Pi[mi(e)];)u=u<<4|bi(e.currentChar),u>1114111&&Ai(t,e.line,e.index+1,101);return 125!==e.currentChar&&Ai(t,e.line,e.index-1,6),mi(e),u}0==(64&Pi[t])&&Fi(e,6);const n=e.source.charCodeAt(e.index+1);0==(64&Pi[n])&&Fi(e,6);const r=e.source.charCodeAt(e.index+2);0==(64&Pi[r])&&Fi(e,6);const o=e.source.charCodeAt(e.index+3);0==(64&Pi[o])&&Fi(e,6);return u=bi(t)<<12|bi(n)<<8|bi(r)<<4|bi(o),e.currentChar=e.source.charCodeAt(e.index+=4),u}(e)}const Ki=[129,129,129,129,129,129,129,129,129,128,136,128,128,130,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,128,16842800,134283267,131,208897,8457015,8455751,134283267,67174411,16,8457014,25233970,18,25233971,67108877,8457016,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,21,1074790417,8456258,1077936157,8456259,22,133,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,69271571,137,20,8455497,208897,132,4096,4096,4096,4096,4096,4096,4096,208897,4096,208897,208897,4096,208897,4096,208897,4096,208897,4096,4096,4096,208897,4096,4096,208897,4096,4096,2162700,8455240,1074790415,16842801,129];function Zi(e,u){if(e.flags=1^(1|e.flags),e.startPos=e.index,e.startColumn=e.column,e.startLine=e.line,e.token=Qi(e,u,0),e.onToken&&1048576!==e.token){const u={start:{line:e.linePos,column:e.colPos},end:{line:e.line,column:e.column}};e.onToken(function(e){switch(e){case 134283266:return"NumericLiteral";case 134283267:return"StringLiteral";case 86021:case 86022:return"BooleanLiteral";case 86023:return"NullLiteral";case 65540:return"RegularExpression";case 67174408:case 67174409:case 132:return"TemplateLiteral";default:return 143360==(143360&e)?"Identifier":4096==(4096&e)?"Keyword":"Punctuator"}}(e.token),e.tokenPos,e.index,u)}}function Qi(e,u,t){const n=0===e.index,r=e.source;let o=e.index,i=e.line,s=e.column;for(;e.index<e.end;){e.tokenPos=e.index,e.colPos=e.column,e.linePos=e.line;let D=e.currentChar;if(D<=126){const a=Ki[D];switch(a){case 67174411:case 16:case 2162700:case 1074790415:case 69271571:case 20:case 21:case 1074790417:case 18:case 16842801:case 133:case 129:return mi(e),a;case 208897:return Hi(e,u,0);case 4096:return Hi(e,u,1);case 134283266:return Ui(e,u,144);case 134283267:return Ri(e,u,D);case 132:return Vi(e,u);case 137:return zi(e,u);case 131:return Wi(e);case 128:mi(e);break;case 130:t|=5,Bi(e);break;case 136:yi(e,t),t=-5&t|1;break;case 8456258:let c=mi(e);if(e.index<e.end){if(60===c)return e.index<e.end&&61===mi(e)?(mi(e),4194334):8456516;if(61===c)return mi(e),8456e3;if(33===c){const n=e.index+1;if(n+1<e.end&&45===r.charCodeAt(n)&&45==r.charCodeAt(n+1)){e.column+=3,e.currentChar=r.charCodeAt(e.index+=3),t=Ni(e,r,t,u,2,e.tokenPos,e.linePos,e.colPos),o=e.tokenPos,i=e.linePos,s=e.colPos;continue}return 8456258}if(47===c){if((16&u)<1)return 8456258;const t=e.index+1;if(t<e.end&&(c=r.charCodeAt(t),42===c||47===c))break;return mi(e),25}}return 8456258;case 1077936157:{mi(e);const u=e.currentChar;return 61===u?61===mi(e)?(mi(e),8455996):8455998:62===u?(mi(e),10):1077936157}case 16842800:return 61!==mi(e)?16842800:61!==mi(e)?8455999:(mi(e),8455997);case 8457015:return 61!==mi(e)?8457015:(mi(e),4194342);case 8457014:{if(mi(e),e.index>=e.end)return 8457014;const u=e.currentChar;return 61===u?(mi(e),4194340):42!==u?8457014:61!==mi(e)?8457273:(mi(e),4194337)}case 8455497:return 61!==mi(e)?8455497:(mi(e),4194343);case 25233970:{mi(e);const u=e.currentChar;return 43===u?(mi(e),33619995):61===u?(mi(e),4194338):25233970}case 25233971:{mi(e);const a=e.currentChar;if(45===a){if(mi(e),(1&t||n)&&62===e.currentChar){0==(256&u)&&Fi(e,108),mi(e),t=Ni(e,r,t,u,3,o,i,s),o=e.tokenPos,i=e.linePos,s=e.colPos;continue}return 33619996}return 61===a?(mi(e),4194339):25233971}case 8457016:if(mi(e),e.index<e.end){const n=e.currentChar;if(47===n){mi(e),t=Ii(e,r,t,0,e.tokenPos,e.linePos,e.colPos),o=e.tokenPos,i=e.linePos,s=e.colPos;continue}if(42===n){mi(e),t=Li(e,r,t),o=e.tokenPos,i=e.linePos,s=e.colPos;continue}if(32768&u)return Oi(e,u);if(61===n)return mi(e),4259877}return 8457016;case 67108877:const l=mi(e);if(l>=48&&l<=57)return Ui(e,u,80);if(46===l){const u=e.index+1;if(u<e.end&&46===r.charCodeAt(u))return e.column+=2,e.currentChar=r.charCodeAt(e.index+=2),14}return 67108877;case 8455240:{mi(e);const u=e.currentChar;return 124===u?(mi(e),61===e.currentChar?(mi(e),4194346):8979003):61===u?(mi(e),4194344):8455240}case 8456259:{mi(e);const u=e.currentChar;if(61===u)return mi(e),8456001;if(62!==u)return 8456259;if(mi(e),e.index<e.end){const u=e.currentChar;if(62===u)return 61===mi(e)?(mi(e),4194336):8456518;if(61===u)return mi(e),4194335}return 8456517}case 8455751:{mi(e);const u=e.currentChar;return 38===u?(mi(e),61===e.currentChar?(mi(e),4194347):8979258):61===u?(mi(e),4194345):8455751}case 22:{let u=mi(e);if(63===u)return mi(e),61===e.currentChar?(mi(e),4194348):276889982;if(46===u){const t=e.index+1;if(t<e.end&&(u=r.charCodeAt(t),!(u>=48&&u<=57)))return mi(e),67108991}return 22}}}else{if((8232^D)<=1){t=-5&t|1,Bi(e);continue}if(55296==(64512&D)||0!=(1&gi[34816+(D>>>5)]>>>D))return 56320==(64512&D)&&(D=(1023&D)<<10|1023&D|65536,0==(1&gi[0+(D>>>5)]>>>D)&&Fi(e,18,ki(D)),e.index++,e.currentChar=D),e.column++,e.tokenValue="",Ji(e,u,0,0);if(160===(a=D)||65279===a||133===a||5760===a||a>=8192&&a<=8203||8239===a||8287===a||12288===a||8201===a||65519===a){mi(e);continue}Fi(e,18,ki(D))}}var a;return 1048576}const es={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Abreve:"\u0102",Acirc:"\xc2",Acy:"\u0410",Afr:"\ud835\udd04",Agrave:"\xc0",Alpha:"\u0391",Amacr:"\u0100",And:"\u2a53",Aogon:"\u0104",Aopf:"\ud835\udd38",ApplyFunction:"\u2061",Aring:"\xc5",Ascr:"\ud835\udc9c",Assign:"\u2254",Atilde:"\xc3",Auml:"\xc4",Backslash:"\u2216",Barv:"\u2ae7",Barwed:"\u2306",Bcy:"\u0411",Because:"\u2235",Bernoullis:"\u212c",Beta:"\u0392",Bfr:"\ud835\udd05",Bopf:"\ud835\udd39",Breve:"\u02d8",Bscr:"\u212c",Bumpeq:"\u224e",CHcy:"\u0427",COPY:"\xa9",Cacute:"\u0106",Cap:"\u22d2",CapitalDifferentialD:"\u2145",Cayleys:"\u212d",Ccaron:"\u010c",Ccedil:"\xc7",Ccirc:"\u0108",Cconint:"\u2230",Cdot:"\u010a",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"\u212d",Chi:"\u03a7",CircleDot:"\u2299",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201d",CloseCurlyQuote:"\u2019",Colon:"\u2237",Colone:"\u2a74",Congruent:"\u2261",Conint:"\u222f",ContourIntegral:"\u222e",Copf:"\u2102",Coproduct:"\u2210",CounterClockwiseContourIntegral:"\u2233",Cross:"\u2a2f",Cscr:"\ud835\udc9e",Cup:"\u22d3",CupCap:"\u224d",DD:"\u2145",DDotrahd:"\u2911",DJcy:"\u0402",DScy:"\u0405",DZcy:"\u040f",Dagger:"\u2021",Darr:"\u21a1",Dashv:"\u2ae4",Dcaron:"\u010e",Dcy:"\u0414",Del:"\u2207",Delta:"\u0394",Dfr:"\ud835\udd07",DiacriticalAcute:"\xb4",DiacriticalDot:"\u02d9",DiacriticalDoubleAcute:"\u02dd",DiacriticalGrave:"`",DiacriticalTilde:"\u02dc",Diamond:"\u22c4",DifferentialD:"\u2146",Dopf:"\ud835\udd3b",Dot:"\xa8",DotDot:"\u20dc",DotEqual:"\u2250",DoubleContourIntegral:"\u222f",DoubleDot:"\xa8",DoubleDownArrow:"\u21d3",DoubleLeftArrow:"\u21d0",DoubleLeftRightArrow:"\u21d4",DoubleLeftTee:"\u2ae4",DoubleLongLeftArrow:"\u27f8",DoubleLongLeftRightArrow:"\u27fa",DoubleLongRightArrow:"\u27f9",DoubleRightArrow:"\u21d2",DoubleRightTee:"\u22a8",DoubleUpArrow:"\u21d1",DoubleUpDownArrow:"\u21d5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21f5",DownBreve:"\u0311",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295e",DownLeftVector:"\u21bd",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295f",DownRightVector:"\u21c1",DownRightVectorBar:"\u2957",DownTee:"\u22a4",DownTeeArrow:"\u21a7",Downarrow:"\u21d3",Dscr:"\ud835\udc9f",Dstrok:"\u0110",ENG:"\u014a",ETH:"\xd0",Eacute:"\xc9",Ecaron:"\u011a",Ecirc:"\xca",Ecy:"\u042d",Edot:"\u0116",Efr:"\ud835\udd08",Egrave:"\xc8",Element:"\u2208",Emacr:"\u0112",EmptySmallSquare:"\u25fb",EmptyVerySmallSquare:"\u25ab",Eogon:"\u0118",Eopf:"\ud835\udd3c",Epsilon:"\u0395",Equal:"\u2a75",EqualTilde:"\u2242",Equilibrium:"\u21cc",Escr:"\u2130",Esim:"\u2a73",Eta:"\u0397",Euml:"\xcb",Exists:"\u2203",ExponentialE:"\u2147",Fcy:"\u0424",Ffr:"\ud835\udd09",FilledSmallSquare:"\u25fc",FilledVerySmallSquare:"\u25aa",Fopf:"\ud835\udd3d",ForAll:"\u2200",Fouriertrf:"\u2131",Fscr:"\u2131",GJcy:"\u0403",GT:">",Gamma:"\u0393",Gammad:"\u03dc",Gbreve:"\u011e",Gcedil:"\u0122",Gcirc:"\u011c",Gcy:"\u0413",Gdot:"\u0120",Gfr:"\ud835\udd0a",Gg:"\u22d9",Gopf:"\ud835\udd3e",GreaterEqual:"\u2265",GreaterEqualLess:"\u22db",GreaterFullEqual:"\u2267",GreaterGreater:"\u2aa2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2a7e",GreaterTilde:"\u2273",Gscr:"\ud835\udca2",Gt:"\u226b",HARDcy:"\u042a",Hacek:"\u02c7",Hat:"^",Hcirc:"\u0124",Hfr:"\u210c",HilbertSpace:"\u210b",Hopf:"\u210d",HorizontalLine:"\u2500",Hscr:"\u210b",Hstrok:"\u0126",HumpDownHump:"\u224e",HumpEqual:"\u224f",IEcy:"\u0415",IJlig:"\u0132",IOcy:"\u0401",Iacute:"\xcd",Icirc:"\xce",Icy:"\u0418",Idot:"\u0130",Ifr:"\u2111",Igrave:"\xcc",Im:"\u2111",Imacr:"\u012a",ImaginaryI:"\u2148",Implies:"\u21d2",Int:"\u222c",Integral:"\u222b",Intersection:"\u22c2",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",Iogon:"\u012e",Iopf:"\ud835\udd40",Iota:"\u0399",Iscr:"\u2110",Itilde:"\u0128",Iukcy:"\u0406",Iuml:"\xcf",Jcirc:"\u0134",Jcy:"\u0419",Jfr:"\ud835\udd0d",Jopf:"\ud835\udd41",Jscr:"\ud835\udca5",Jsercy:"\u0408",Jukcy:"\u0404",KHcy:"\u0425",KJcy:"\u040c",Kappa:"\u039a",Kcedil:"\u0136",Kcy:"\u041a",Kfr:"\ud835\udd0e",Kopf:"\ud835\udd42",Kscr:"\ud835\udca6",LJcy:"\u0409",LT:"<",Lacute:"\u0139",Lambda:"\u039b",Lang:"\u27ea",Laplacetrf:"\u2112",Larr:"\u219e",Lcaron:"\u013d",Lcedil:"\u013b",Lcy:"\u041b",LeftAngleBracket:"\u27e8",LeftArrow:"\u2190",LeftArrowBar:"\u21e4",LeftArrowRightArrow:"\u21c6",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27e6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21c3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230a",LeftRightArrow:"\u2194",LeftRightVector:"\u294e",LeftTee:"\u22a3",LeftTeeArrow:"\u21a4",LeftTeeVector:"\u295a",LeftTriangle:"\u22b2",LeftTriangleBar:"\u29cf",LeftTriangleEqual:"\u22b4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21bf",LeftUpVectorBar:"\u2958",LeftVector:"\u21bc",LeftVectorBar:"\u2952",Leftarrow:"\u21d0",Leftrightarrow:"\u21d4",LessEqualGreater:"\u22da",LessFullEqual:"\u2266",LessGreater:"\u2276",LessLess:"\u2aa1",LessSlantEqual:"\u2a7d",LessTilde:"\u2272",Lfr:"\ud835\udd0f",Ll:"\u22d8",Lleftarrow:"\u21da",Lmidot:"\u013f",LongLeftArrow:"\u27f5",LongLeftRightArrow:"\u27f7",LongRightArrow:"\u27f6",Longleftarrow:"\u27f8",Longleftrightarrow:"\u27fa",Longrightarrow:"\u27f9",Lopf:"\ud835\udd43",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",Lscr:"\u2112",Lsh:"\u21b0",Lstrok:"\u0141",Lt:"\u226a",Map:"\u2905",Mcy:"\u041c",MediumSpace:"\u205f",Mellintrf:"\u2133",Mfr:"\ud835\udd10",MinusPlus:"\u2213",Mopf:"\ud835\udd44",Mscr:"\u2133",Mu:"\u039c",NJcy:"\u040a",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041d",NegativeMediumSpace:"\u200b",NegativeThickSpace:"\u200b",NegativeThinSpace:"\u200b",NegativeVeryThinSpace:"\u200b",NestedGreaterGreater:"\u226b",NestedLessLess:"\u226a",NewLine:"\n",Nfr:"\ud835\udd11",NoBreak:"\u2060",NonBreakingSpace:"\xa0",Nopf:"\u2115",Not:"\u2aec",NotCongruent:"\u2262",NotCupCap:"\u226d",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226f",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226b\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2a7e\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224e\u0338",NotHumpEqual:"\u224f\u0338",NotLeftTriangle:"\u22ea",NotLeftTriangleBar:"\u29cf\u0338",NotLeftTriangleEqual:"\u22ec",NotLess:"\u226e",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226a\u0338",NotLessSlantEqual:"\u2a7d\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2aa2\u0338",NotNestedLessLess:"\u2aa1\u0338",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2aaf\u0338",NotPrecedesSlantEqual:"\u22e0",NotReverseElement:"\u220c",NotRightTriangle:"\u22eb",NotRightTriangleBar:"\u29d0\u0338",NotRightTriangleEqual:"\u22ed",NotSquareSubset:"\u228f\u0338",NotSquareSubsetEqual:"\u22e2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22e3",NotSubset:"\u2282\u20d2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2ab0\u0338",NotSucceedsSlantEqual:"\u22e1",NotSucceedsTilde:"\u227f\u0338",NotSuperset:"\u2283\u20d2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",Nscr:"\ud835\udca9",Ntilde:"\xd1",Nu:"\u039d",OElig:"\u0152",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"\u041e",Odblac:"\u0150",Ofr:"\ud835\udd12",Ograve:"\xd2",Omacr:"\u014c",Omega:"\u03a9",Omicron:"\u039f",Oopf:"\ud835\udd46",OpenCurlyDoubleQuote:"\u201c",OpenCurlyQuote:"\u2018",Or:"\u2a54",Oscr:"\ud835\udcaa",Oslash:"\xd8",Otilde:"\xd5",Otimes:"\u2a37",Ouml:"\xd6",OverBar:"\u203e",OverBrace:"\u23de",OverBracket:"\u23b4",OverParenthesis:"\u23dc",PartialD:"\u2202",Pcy:"\u041f",Pfr:"\ud835\udd13",Phi:"\u03a6",Pi:"\u03a0",PlusMinus:"\xb1",Poincareplane:"\u210c",Popf:"\u2119",Pr:"\u2abb",Precedes:"\u227a",PrecedesEqual:"\u2aaf",PrecedesSlantEqual:"\u227c",PrecedesTilde:"\u227e",Prime:"\u2033",Product:"\u220f",Proportion:"\u2237",Proportional:"\u221d",Pscr:"\ud835\udcab",Psi:"\u03a8",QUOT:'"',Qfr:"\ud835\udd14",Qopf:"\u211a",Qscr:"\ud835\udcac",RBarr:"\u2910",REG:"\xae",Racute:"\u0154",Rang:"\u27eb",Rarr:"\u21a0",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",Re:"\u211c",ReverseElement:"\u220b",ReverseEquilibrium:"\u21cb",ReverseUpEquilibrium:"\u296f",Rfr:"\u211c",Rho:"\u03a1",RightAngleBracket:"\u27e9",RightArrow:"\u2192",RightArrowBar:"\u21e5",RightArrowLeftArrow:"\u21c4",RightCeiling:"\u2309",RightDoubleBracket:"\u27e7",RightDownTeeVector:"\u295d",RightDownVector:"\u21c2",RightDownVectorBar:"\u2955",RightFloor:"\u230b",RightTee:"\u22a2",RightTeeArrow:"\u21a6",RightTeeVector:"\u295b",RightTriangle:"\u22b3",RightTriangleBar:"\u29d0",RightTriangleEqual:"\u22b5",RightUpDownVector:"\u294f",RightUpTeeVector:"\u295c",RightUpVector:"\u21be",RightUpVectorBar:"\u2954",RightVector:"\u21c0",RightVectorBar:"\u2953",Rightarrow:"\u21d2",Ropf:"\u211d",RoundImplies:"\u2970",Rrightarrow:"\u21db",Rscr:"\u211b",Rsh:"\u21b1",RuleDelayed:"\u29f4",SHCHcy:"\u0429",SHcy:"\u0428",SOFTcy:"\u042c",Sacute:"\u015a",Sc:"\u2abc",Scaron:"\u0160",Scedil:"\u015e",Scirc:"\u015c",Scy:"\u0421",Sfr:"\ud835\udd16",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",Sigma:"\u03a3",SmallCircle:"\u2218",Sopf:"\ud835\udd4a",Sqrt:"\u221a",Square:"\u25a1",SquareIntersection:"\u2293",SquareSubset:"\u228f",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",Sscr:"\ud835\udcae",Star:"\u22c6",Sub:"\u22d0",Subset:"\u22d0",SubsetEqual:"\u2286",Succeeds:"\u227b",SucceedsEqual:"\u2ab0",SucceedsSlantEqual:"\u227d",SucceedsTilde:"\u227f",SuchThat:"\u220b",Sum:"\u2211",Sup:"\u22d1",Superset:"\u2283",SupersetEqual:"\u2287",Supset:"\u22d1",THORN:"\xde",TRADE:"\u2122",TSHcy:"\u040b",TScy:"\u0426",Tab:"\t",Tau:"\u03a4",Tcaron:"\u0164",Tcedil:"\u0162",Tcy:"\u0422",Tfr:"\ud835\udd17",Therefore:"\u2234",Theta:"\u0398",ThickSpace:"\u205f\u200a",ThinSpace:"\u2009",Tilde:"\u223c",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",Topf:"\ud835\udd4b",TripleDot:"\u20db",Tscr:"\ud835\udcaf",Tstrok:"\u0166",Uacute:"\xda",Uarr:"\u219f",Uarrocir:"\u2949",Ubrcy:"\u040e",Ubreve:"\u016c",Ucirc:"\xdb",Ucy:"\u0423",Udblac:"\u0170",Ufr:"\ud835\udd18",Ugrave:"\xd9",Umacr:"\u016a",UnderBar:"_",UnderBrace:"\u23df",UnderBracket:"\u23b5",UnderParenthesis:"\u23dd",Union:"\u22c3",UnionPlus:"\u228e",Uogon:"\u0172",Uopf:"\ud835\udd4c",UpArrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21c5",UpDownArrow:"\u2195",UpEquilibrium:"\u296e",UpTee:"\u22a5",UpTeeArrow:"\u21a5",Uparrow:"\u21d1",Updownarrow:"\u21d5",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03d2",Upsilon:"\u03a5",Uring:"\u016e",Uscr:"\ud835\udcb0",Utilde:"\u0168",Uuml:"\xdc",VDash:"\u22ab",Vbar:"\u2aeb",Vcy:"\u0412",Vdash:"\u22a9",Vdashl:"\u2ae6",Vee:"\u22c1",Verbar:"\u2016",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200a",Vfr:"\ud835\udd19",Vopf:"\ud835\udd4d",Vscr:"\ud835\udcb1",Vvdash:"\u22aa",Wcirc:"\u0174",Wedge:"\u22c0",Wfr:"\ud835\udd1a",Wopf:"\ud835\udd4e",Wscr:"\ud835\udcb2",Xfr:"\ud835\udd1b",Xi:"\u039e",Xopf:"\ud835\udd4f",Xscr:"\ud835\udcb3",YAcy:"\u042f",YIcy:"\u0407",YUcy:"\u042e",Yacute:"\xdd",Ycirc:"\u0176",Ycy:"\u042b",Yfr:"\ud835\udd1c",Yopf:"\ud835\udd50",Yscr:"\ud835\udcb4",Yuml:"\u0178",ZHcy:"\u0416",Zacute:"\u0179",Zcaron:"\u017d",Zcy:"\u0417",Zdot:"\u017b",ZeroWidthSpace:"\u200b",Zeta:"\u0396",Zfr:"\u2128",Zopf:"\u2124",Zscr:"\ud835\udcb5",aacute:"\xe1",abreve:"\u0103",ac:"\u223e",acE:"\u223e\u0333",acd:"\u223f",acirc:"\xe2",acute:"\xb4",acy:"\u0430",aelig:"\xe6",af:"\u2061",afr:"\ud835\udd1e",agrave:"\xe0",alefsym:"\u2135",aleph:"\u2135",alpha:"\u03b1",amacr:"\u0101",amalg:"\u2a3f",amp:"&",and:"\u2227",andand:"\u2a55",andd:"\u2a5c",andslope:"\u2a58",andv:"\u2a5a",ang:"\u2220",ange:"\u29a4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29a8",angmsdab:"\u29a9",angmsdac:"\u29aa",angmsdad:"\u29ab",angmsdae:"\u29ac",angmsdaf:"\u29ad",angmsdag:"\u29ae",angmsdah:"\u29af",angrt:"\u221f",angrtvb:"\u22be",angrtvbd:"\u299d",angsph:"\u2222",angst:"\xc5",angzarr:"\u237c",aogon:"\u0105",aopf:"\ud835\udd52",ap:"\u2248",apE:"\u2a70",apacir:"\u2a6f",ape:"\u224a",apid:"\u224b",apos:"'",approx:"\u2248",approxeq:"\u224a",aring:"\xe5",ascr:"\ud835\udcb6",ast:"*",asymp:"\u2248",asympeq:"\u224d",atilde:"\xe3",auml:"\xe4",awconint:"\u2233",awint:"\u2a11",bNot:"\u2aed",backcong:"\u224c",backepsilon:"\u03f6",backprime:"\u2035",backsim:"\u223d",backsimeq:"\u22cd",barvee:"\u22bd",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23b5",bbrktbrk:"\u23b6",bcong:"\u224c",bcy:"\u0431",bdquo:"\u201e",becaus:"\u2235",because:"\u2235",bemptyv:"\u29b0",bepsi:"\u03f6",bernou:"\u212c",beta:"\u03b2",beth:"\u2136",between:"\u226c",bfr:"\ud835\udd1f",bigcap:"\u22c2",bigcirc:"\u25ef",bigcup:"\u22c3",bigodot:"\u2a00",bigoplus:"\u2a01",bigotimes:"\u2a02",bigsqcup:"\u2a06",bigstar:"\u2605",bigtriangledown:"\u25bd",bigtriangleup:"\u25b3",biguplus:"\u2a04",bigvee:"\u22c1",bigwedge:"\u22c0",bkarow:"\u290d",blacklozenge:"\u29eb",blacksquare:"\u25aa",blacktriangle:"\u25b4",blacktriangledown:"\u25be",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20e5",bnequiv:"\u2261\u20e5",bnot:"\u2310",bopf:"\ud835\udd53",bot:"\u22a5",bottom:"\u22a5",bowtie:"\u22c8",boxDL:"\u2557",boxDR:"\u2554",boxDl:"\u2556",boxDr:"\u2553",boxH:"\u2550",boxHD:"\u2566",boxHU:"\u2569",boxHd:"\u2564",boxHu:"\u2567",boxUL:"\u255d",boxUR:"\u255a",boxUl:"\u255c",boxUr:"\u2559",boxV:"\u2551",boxVH:"\u256c",boxVL:"\u2563",boxVR:"\u2560",boxVh:"\u256b",boxVl:"\u2562",boxVr:"\u255f",boxbox:"\u29c9",boxdL:"\u2555",boxdR:"\u2552",boxdl:"\u2510",boxdr:"\u250c",boxh:"\u2500",boxhD:"\u2565",boxhU:"\u2568",boxhd:"\u252c",boxhu:"\u2534",boxminus:"\u229f",boxplus:"\u229e",boxtimes:"\u22a0",boxuL:"\u255b",boxuR:"\u2558",boxul:"\u2518",boxur:"\u2514",boxv:"\u2502",boxvH:"\u256a",boxvL:"\u2561",boxvR:"\u255e",boxvh:"\u253c",boxvl:"\u2524",boxvr:"\u251c",bprime:"\u2035",breve:"\u02d8",brvbar:"\xa6",bscr:"\ud835\udcb7",bsemi:"\u204f",bsim:"\u223d",bsime:"\u22cd",bsol:"\\",bsolb:"\u29c5",bsolhsub:"\u27c8",bull:"\u2022",bullet:"\u2022",bump:"\u224e",bumpE:"\u2aae",bumpe:"\u224f",bumpeq:"\u224f",cacute:"\u0107",cap:"\u2229",capand:"\u2a44",capbrcup:"\u2a49",capcap:"\u2a4b",capcup:"\u2a47",capdot:"\u2a40",caps:"\u2229\ufe00",caret:"\u2041",caron:"\u02c7",ccaps:"\u2a4d",ccaron:"\u010d",ccedil:"\xe7",ccirc:"\u0109",ccups:"\u2a4c",ccupssm:"\u2a50",cdot:"\u010b",cedil:"\xb8",cemptyv:"\u29b2",cent:"\xa2",centerdot:"\xb7",cfr:"\ud835\udd20",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",chi:"\u03c7",cir:"\u25cb",cirE:"\u29c3",circ:"\u02c6",circeq:"\u2257",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",circledR:"\xae",circledS:"\u24c8",circledast:"\u229b",circledcirc:"\u229a",circleddash:"\u229d",cire:"\u2257",cirfnint:"\u2a10",cirmid:"\u2aef",cirscir:"\u29c2",clubs:"\u2663",clubsuit:"\u2663",colon:":",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2a6d",conint:"\u222e",copf:"\ud835\udd54",coprod:"\u2210",copy:"\xa9",copysr:"\u2117",crarr:"\u21b5",cross:"\u2717",cscr:"\ud835\udcb8",csub:"\u2acf",csube:"\u2ad1",csup:"\u2ad0",csupe:"\u2ad2",ctdot:"\u22ef",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22de",cuesc:"\u22df",cularr:"\u21b6",cularrp:"\u293d",cup:"\u222a",cupbrcap:"\u2a48",cupcap:"\u2a46",cupcup:"\u2a4a",cupdot:"\u228d",cupor:"\u2a45",cups:"\u222a\ufe00",curarr:"\u21b7",curarrm:"\u293c",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",curlyvee:"\u22ce",curlywedge:"\u22cf",curren:"\xa4",curvearrowleft:"\u21b6",curvearrowright:"\u21b7",cuvee:"\u22ce",cuwed:"\u22cf",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232d",dArr:"\u21d3",dHar:"\u2965",dagger:"\u2020",daleth:"\u2138",darr:"\u2193",dash:"\u2010",dashv:"\u22a3",dbkarow:"\u290f",dblac:"\u02dd",dcaron:"\u010f",dcy:"\u0434",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21ca",ddotseq:"\u2a77",deg:"\xb0",delta:"\u03b4",demptyv:"\u29b1",dfisht:"\u297f",dfr:"\ud835\udd21",dharl:"\u21c3",dharr:"\u21c2",diam:"\u22c4",diamond:"\u22c4",diamondsuit:"\u2666",diams:"\u2666",die:"\xa8",digamma:"\u03dd",disin:"\u22f2",div:"\xf7",divide:"\xf7",divideontimes:"\u22c7",divonx:"\u22c7",djcy:"\u0452",dlcorn:"\u231e",dlcrop:"\u230d",dollar:"$",dopf:"\ud835\udd55",dot:"\u02d9",doteq:"\u2250",doteqdot:"\u2251",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22a1",doublebarwedge:"\u2306",downarrow:"\u2193",downdownarrows:"\u21ca",downharpoonleft:"\u21c3",downharpoonright:"\u21c2",drbkarow:"\u2910",drcorn:"\u231f",drcrop:"\u230c",dscr:"\ud835\udcb9",dscy:"\u0455",dsol:"\u29f6",dstrok:"\u0111",dtdot:"\u22f1",dtri:"\u25bf",dtrif:"\u25be",duarr:"\u21f5",duhar:"\u296f",dwangle:"\u29a6",dzcy:"\u045f",dzigrarr:"\u27ff",eDDot:"\u2a77",eDot:"\u2251",eacute:"\xe9",easter:"\u2a6e",ecaron:"\u011b",ecir:"\u2256",ecirc:"\xea",ecolon:"\u2255",ecy:"\u044d",edot:"\u0117",ee:"\u2147",efDot:"\u2252",efr:"\ud835\udd22",eg:"\u2a9a",egrave:"\xe8",egs:"\u2a96",egsdot:"\u2a98",el:"\u2a99",elinters:"\u23e7",ell:"\u2113",els:"\u2a95",elsdot:"\u2a97",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",emptyv:"\u2205",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",eng:"\u014b",ensp:"\u2002",eogon:"\u0119",eopf:"\ud835\udd56",epar:"\u22d5",eparsl:"\u29e3",eplus:"\u2a71",epsi:"\u03b5",epsilon:"\u03b5",epsiv:"\u03f5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2a96",eqslantless:"\u2a95",equals:"=",equest:"\u225f",equiv:"\u2261",equivDD:"\u2a78",eqvparsl:"\u29e5",erDot:"\u2253",erarr:"\u2971",escr:"\u212f",esdot:"\u2250",esim:"\u2242",eta:"\u03b7",eth:"\xf0",euml:"\xeb",euro:"\u20ac",excl:"!",exist:"\u2203",expectation:"\u2130",exponentiale:"\u2147",fallingdotseq:"\u2252",fcy:"\u0444",female:"\u2640",ffilig:"\ufb03",fflig:"\ufb00",ffllig:"\ufb04",ffr:"\ud835\udd23",filig:"\ufb01",fjlig:"fj",flat:"\u266d",fllig:"\ufb02",fltns:"\u25b1",fnof:"\u0192",fopf:"\ud835\udd57",forall:"\u2200",fork:"\u22d4",forkv:"\u2ad9",fpartint:"\u2a0d",frac12:"\xbd",frac13:"\u2153",frac14:"\xbc",frac15:"\u2155",frac16:"\u2159",frac18:"\u215b",frac23:"\u2154",frac25:"\u2156",frac34:"\xbe",frac35:"\u2157",frac38:"\u215c",frac45:"\u2158",frac56:"\u215a",frac58:"\u215d",frac78:"\u215e",frasl:"\u2044",frown:"\u2322",fscr:"\ud835\udcbb",gE:"\u2267",gEl:"\u2a8c",gacute:"\u01f5",gamma:"\u03b3",gammad:"\u03dd",gap:"\u2a86",gbreve:"\u011f",gcirc:"\u011d",gcy:"\u0433",gdot:"\u0121",ge:"\u2265",gel:"\u22db",geq:"\u2265",geqq:"\u2267",geqslant:"\u2a7e",ges:"\u2a7e",gescc:"\u2aa9",gesdot:"\u2a80",gesdoto:"\u2a82",gesdotol:"\u2a84",gesl:"\u22db\ufe00",gesles:"\u2a94",gfr:"\ud835\udd24",gg:"\u226b",ggg:"\u22d9",gimel:"\u2137",gjcy:"\u0453",gl:"\u2277",glE:"\u2a92",gla:"\u2aa5",glj:"\u2aa4",gnE:"\u2269",gnap:"\u2a8a",gnapprox:"\u2a8a",gne:"\u2a88",gneq:"\u2a88",gneqq:"\u2269",gnsim:"\u22e7",gopf:"\ud835\udd58",grave:"`",gscr:"\u210a",gsim:"\u2273",gsime:"\u2a8e",gsiml:"\u2a90",gt:">",gtcc:"\u2aa7",gtcir:"\u2a7a",gtdot:"\u22d7",gtlPar:"\u2995",gtquest:"\u2a7c",gtrapprox:"\u2a86",gtrarr:"\u2978",gtrdot:"\u22d7",gtreqless:"\u22db",gtreqqless:"\u2a8c",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\ufe00",gvnE:"\u2269\ufe00",hArr:"\u21d4",hairsp:"\u200a",half:"\xbd",hamilt:"\u210b",hardcy:"\u044a",harr:"\u2194",harrcir:"\u2948",harrw:"\u21ad",hbar:"\u210f",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22b9",hfr:"\ud835\udd25",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21ff",homtht:"\u223b",hookleftarrow:"\u21a9",hookrightarrow:"\u21aa",hopf:"\ud835\udd59",horbar:"\u2015",hscr:"\ud835\udcbd",hslash:"\u210f",hstrok:"\u0127",hybull:"\u2043",hyphen:"\u2010",iacute:"\xed",ic:"\u2063",icirc:"\xee",icy:"\u0438",iecy:"\u0435",iexcl:"\xa1",iff:"\u21d4",ifr:"\ud835\udd26",igrave:"\xec",ii:"\u2148",iiiint:"\u2a0c",iiint:"\u222d",iinfin:"\u29dc",iiota:"\u2129",ijlig:"\u0133",imacr:"\u012b",image:"\u2111",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22b7",imped:"\u01b5",in:"\u2208",incare:"\u2105",infin:"\u221e",infintie:"\u29dd",inodot:"\u0131",int:"\u222b",intcal:"\u22ba",integers:"\u2124",intercal:"\u22ba",intlarhk:"\u2a17",intprod:"\u2a3c",iocy:"\u0451",iogon:"\u012f",iopf:"\ud835\udd5a",iota:"\u03b9",iprod:"\u2a3c",iquest:"\xbf",iscr:"\ud835\udcbe",isin:"\u2208",isinE:"\u22f9",isindot:"\u22f5",isins:"\u22f4",isinsv:"\u22f3",isinv:"\u2208",it:"\u2062",itilde:"\u0129",iukcy:"\u0456",iuml:"\xef",jcirc:"\u0135",jcy:"\u0439",jfr:"\ud835\udd27",jmath:"\u0237",jopf:"\ud835\udd5b",jscr:"\ud835\udcbf",jsercy:"\u0458",jukcy:"\u0454",kappa:"\u03ba",kappav:"\u03f0",kcedil:"\u0137",kcy:"\u043a",kfr:"\ud835\udd28",kgreen:"\u0138",khcy:"\u0445",kjcy:"\u045c",kopf:"\ud835\udd5c",kscr:"\ud835\udcc0",lAarr:"\u21da",lArr:"\u21d0",lAtail:"\u291b",lBarr:"\u290e",lE:"\u2266",lEg:"\u2a8b",lHar:"\u2962",lacute:"\u013a",laemptyv:"\u29b4",lagran:"\u2112",lambda:"\u03bb",lang:"\u27e8",langd:"\u2991",langle:"\u27e8",lap:"\u2a85",laquo:"\xab",larr:"\u2190",larrb:"\u21e4",larrbfs:"\u291f",larrfs:"\u291d",larrhk:"\u21a9",larrlp:"\u21ab",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21a2",lat:"\u2aab",latail:"\u2919",late:"\u2aad",lates:"\u2aad\ufe00",lbarr:"\u290c",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298b",lbrksld:"\u298f",lbrkslu:"\u298d",lcaron:"\u013e",lcedil:"\u013c",lceil:"\u2308",lcub:"{",lcy:"\u043b",ldca:"\u2936",ldquo:"\u201c",ldquor:"\u201e",ldrdhar:"\u2967",ldrushar:"\u294b",ldsh:"\u21b2",le:"\u2264",leftarrow:"\u2190",leftarrowtail:"\u21a2",leftharpoondown:"\u21bd",leftharpoonup:"\u21bc",leftleftarrows:"\u21c7",leftrightarrow:"\u2194",leftrightarrows:"\u21c6",leftrightharpoons:"\u21cb",leftrightsquigarrow:"\u21ad",leftthreetimes:"\u22cb",leg:"\u22da",leq:"\u2264",leqq:"\u2266",leqslant:"\u2a7d",les:"\u2a7d",lescc:"\u2aa8",lesdot:"\u2a7f",lesdoto:"\u2a81",lesdotor:"\u2a83",lesg:"\u22da\ufe00",lesges:"\u2a93",lessapprox:"\u2a85",lessdot:"\u22d6",lesseqgtr:"\u22da",lesseqqgtr:"\u2a8b",lessgtr:"\u2276",lesssim:"\u2272",lfisht:"\u297c",lfloor:"\u230a",lfr:"\ud835\udd29",lg:"\u2276",lgE:"\u2a91",lhard:"\u21bd",lharu:"\u21bc",lharul:"\u296a",lhblk:"\u2584",ljcy:"\u0459",ll:"\u226a",llarr:"\u21c7",llcorner:"\u231e",llhard:"\u296b",lltri:"\u25fa",lmidot:"\u0140",lmoust:"\u23b0",lmoustache:"\u23b0",lnE:"\u2268",lnap:"\u2a89",lnapprox:"\u2a89",lne:"\u2a87",lneq:"\u2a87",lneqq:"\u2268",lnsim:"\u22e6",loang:"\u27ec",loarr:"\u21fd",lobrk:"\u27e6",longleftarrow:"\u27f5",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",longrightarrow:"\u27f6",looparrowleft:"\u21ab",looparrowright:"\u21ac",lopar:"\u2985",lopf:"\ud835\udd5d",loplus:"\u2a2d",lotimes:"\u2a34",lowast:"\u2217",lowbar:"_",loz:"\u25ca",lozenge:"\u25ca",lozf:"\u29eb",lpar:"(",lparlt:"\u2993",lrarr:"\u21c6",lrcorner:"\u231f",lrhar:"\u21cb",lrhard:"\u296d",lrm:"\u200e",lrtri:"\u22bf",lsaquo:"\u2039",lscr:"\ud835\udcc1",lsh:"\u21b0",lsim:"\u2272",lsime:"\u2a8d",lsimg:"\u2a8f",lsqb:"[",lsquo:"\u2018",lsquor:"\u201a",lstrok:"\u0142",lt:"<",ltcc:"\u2aa6",ltcir:"\u2a79",ltdot:"\u22d6",lthree:"\u22cb",ltimes:"\u22c9",ltlarr:"\u2976",ltquest:"\u2a7b",ltrPar:"\u2996",ltri:"\u25c3",ltrie:"\u22b4",ltrif:"\u25c2",lurdshar:"\u294a",luruhar:"\u2966",lvertneqq:"\u2268\ufe00",lvnE:"\u2268\ufe00",mDDot:"\u223a",macr:"\xaf",male:"\u2642",malt:"\u2720",maltese:"\u2720",map:"\u21a6",mapsto:"\u21a6",mapstodown:"\u21a7",mapstoleft:"\u21a4",mapstoup:"\u21a5",marker:"\u25ae",mcomma:"\u2a29",mcy:"\u043c",mdash:"\u2014",measuredangle:"\u2221",mfr:"\ud835\udd2a",mho:"\u2127",micro:"\xb5",mid:"\u2223",midast:"*",midcir:"\u2af0",middot:"\xb7",minus:"\u2212",minusb:"\u229f",minusd:"\u2238",minusdu:"\u2a2a",mlcp:"\u2adb",mldr:"\u2026",mnplus:"\u2213",models:"\u22a7",mopf:"\ud835\udd5e",mp:"\u2213",mscr:"\ud835\udcc2",mstpos:"\u223e",mu:"\u03bc",multimap:"\u22b8",mumap:"\u22b8",nGg:"\u22d9\u0338",nGt:"\u226b\u20d2",nGtv:"\u226b\u0338",nLeftarrow:"\u21cd",nLeftrightarrow:"\u21ce",nLl:"\u22d8\u0338",nLt:"\u226a\u20d2",nLtv:"\u226a\u0338",nRightarrow:"\u21cf",nVDash:"\u22af",nVdash:"\u22ae",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20d2",nap:"\u2249",napE:"\u2a70\u0338",napid:"\u224b\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266e",natural:"\u266e",naturals:"\u2115",nbsp:"\xa0",nbump:"\u224e\u0338",nbumpe:"\u224f\u0338",ncap:"\u2a43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2a6d\u0338",ncup:"\u2a42",ncy:"\u043d",ndash:"\u2013",ne:"\u2260",neArr:"\u21d7",nearhk:"\u2924",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",nfr:"\ud835\udd2b",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2a7e\u0338",nges:"\u2a7e\u0338",ngsim:"\u2275",ngt:"\u226f",ngtr:"\u226f",nhArr:"\u21ce",nharr:"\u21ae",nhpar:"\u2af2",ni:"\u220b",nis:"\u22fc",nisd:"\u22fa",niv:"\u220b",njcy:"\u045a",nlArr:"\u21cd",nlE:"\u2266\u0338",nlarr:"\u219a",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219a",nleftrightarrow:"\u21ae",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2a7d\u0338",nles:"\u2a7d\u0338",nless:"\u226e",nlsim:"\u2274",nlt:"\u226e",nltri:"\u22ea",nltrie:"\u22ec",nmid:"\u2224",nopf:"\ud835\udd5f",not:"\xac",notin:"\u2209",notinE:"\u22f9\u0338",notindot:"\u22f5\u0338",notinva:"\u2209",notinvb:"\u22f7",notinvc:"\u22f6",notni:"\u220c",notniva:"\u220c",notnivb:"\u22fe",notnivc:"\u22fd",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2afd\u20e5",npart:"\u2202\u0338",npolint:"\u2a14",npr:"\u2280",nprcue:"\u22e0",npre:"\u2aaf\u0338",nprec:"\u2280",npreceq:"\u2aaf\u0338",nrArr:"\u21cf",nrarr:"\u219b",nrarrc:"\u2933\u0338",nrarrw:"\u219d\u0338",nrightarrow:"\u219b",nrtri:"\u22eb",nrtrie:"\u22ed",nsc:"\u2281",nsccue:"\u22e1",nsce:"\u2ab0\u0338",nscr:"\ud835\udcc3",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22e2",nsqsupe:"\u22e3",nsub:"\u2284",nsubE:"\u2ac5\u0338",nsube:"\u2288",nsubset:"\u2282\u20d2",nsubseteq:"\u2288",nsubseteqq:"\u2ac5\u0338",nsucc:"\u2281",nsucceq:"\u2ab0\u0338",nsup:"\u2285",nsupE:"\u2ac6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20d2",nsupseteq:"\u2289",nsupseteqq:"\u2ac6\u0338",ntgl:"\u2279",ntilde:"\xf1",ntlg:"\u2278",ntriangleleft:"\u22ea",ntrianglelefteq:"\u22ec",ntriangleright:"\u22eb",ntrianglerighteq:"\u22ed",nu:"\u03bd",num:"#",numero:"\u2116",numsp:"\u2007",nvDash:"\u22ad",nvHarr:"\u2904",nvap:"\u224d\u20d2",nvdash:"\u22ac",nvge:"\u2265\u20d2",nvgt:">\u20d2",nvinfin:"\u29de",nvlArr:"\u2902",nvle:"\u2264\u20d2",nvlt:"<\u20d2",nvltrie:"\u22b4\u20d2",nvrArr:"\u2903",nvrtrie:"\u22b5\u20d2",nvsim:"\u223c\u20d2",nwArr:"\u21d6",nwarhk:"\u2923",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",oS:"\u24c8",oacute:"\xf3",oast:"\u229b",ocir:"\u229a",ocirc:"\xf4",ocy:"\u043e",odash:"\u229d",odblac:"\u0151",odiv:"\u2a38",odot:"\u2299",odsold:"\u29bc",oelig:"\u0153",ofcir:"\u29bf",ofr:"\ud835\udd2c",ogon:"\u02db",ograve:"\xf2",ogt:"\u29c1",ohbar:"\u29b5",ohm:"\u03a9",oint:"\u222e",olarr:"\u21ba",olcir:"\u29be",olcross:"\u29bb",oline:"\u203e",olt:"\u29c0",omacr:"\u014d",omega:"\u03c9",omicron:"\u03bf",omid:"\u29b6",ominus:"\u2296",oopf:"\ud835\udd60",opar:"\u29b7",operp:"\u29b9",oplus:"\u2295",or:"\u2228",orarr:"\u21bb",ord:"\u2a5d",order:"\u2134",orderof:"\u2134",ordf:"\xaa",ordm:"\xba",origof:"\u22b6",oror:"\u2a56",orslope:"\u2a57",orv:"\u2a5b",oscr:"\u2134",oslash:"\xf8",osol:"\u2298",otilde:"\xf5",otimes:"\u2297",otimesas:"\u2a36",ouml:"\xf6",ovbar:"\u233d",par:"\u2225",para:"\xb6",parallel:"\u2225",parsim:"\u2af3",parsl:"\u2afd",part:"\u2202",pcy:"\u043f",percnt:"%",period:".",permil:"\u2030",perp:"\u22a5",pertenk:"\u2031",pfr:"\ud835\udd2d",phi:"\u03c6",phiv:"\u03d5",phmmat:"\u2133",phone:"\u260e",pi:"\u03c0",pitchfork:"\u22d4",piv:"\u03d6",planck:"\u210f",planckh:"\u210e",plankv:"\u210f",plus:"+",plusacir:"\u2a23",plusb:"\u229e",pluscir:"\u2a22",plusdo:"\u2214",plusdu:"\u2a25",pluse:"\u2a72",plusmn:"\xb1",plussim:"\u2a26",plustwo:"\u2a27",pm:"\xb1",pointint:"\u2a15",popf:"\ud835\udd61",pound:"\xa3",pr:"\u227a",prE:"\u2ab3",prap:"\u2ab7",prcue:"\u227c",pre:"\u2aaf",prec:"\u227a",precapprox:"\u2ab7",preccurlyeq:"\u227c",preceq:"\u2aaf",precnapprox:"\u2ab9",precneqq:"\u2ab5",precnsim:"\u22e8",precsim:"\u227e",prime:"\u2032",primes:"\u2119",prnE:"\u2ab5",prnap:"\u2ab9",prnsim:"\u22e8",prod:"\u220f",profalar:"\u232e",profline:"\u2312",profsurf:"\u2313",prop:"\u221d",propto:"\u221d",prsim:"\u227e",prurel:"\u22b0",pscr:"\ud835\udcc5",psi:"\u03c8",puncsp:"\u2008",qfr:"\ud835\udd2e",qint:"\u2a0c",qopf:"\ud835\udd62",qprime:"\u2057",qscr:"\ud835\udcc6",quaternions:"\u210d",quatint:"\u2a16",quest:"?",questeq:"\u225f",quot:'"',rAarr:"\u21db",rArr:"\u21d2",rAtail:"\u291c",rBarr:"\u290f",rHar:"\u2964",race:"\u223d\u0331",racute:"\u0155",radic:"\u221a",raemptyv:"\u29b3",rang:"\u27e9",rangd:"\u2992",range:"\u29a5",rangle:"\u27e9",raquo:"\xbb",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21e5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291e",rarrhk:"\u21aa",rarrlp:"\u21ac",rarrpl:"\u2945",rarrsim:"\u2974",rarrtl:"\u21a3",rarrw:"\u219d",ratail:"\u291a",ratio:"\u2236",rationals:"\u211a",rbarr:"\u290d",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298c",rbrksld:"\u298e",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201d",rdquor:"\u201d",rdsh:"\u21b3",real:"\u211c",realine:"\u211b",realpart:"\u211c",reals:"\u211d",rect:"\u25ad",reg:"\xae",rfisht:"\u297d",rfloor:"\u230b",rfr:"\ud835\udd2f",rhard:"\u21c1",rharu:"\u21c0",rharul:"\u296c",rho:"\u03c1",rhov:"\u03f1",rightarrow:"\u2192",rightarrowtail:"\u21a3",rightharpoondown:"\u21c1",rightharpoonup:"\u21c0",rightleftarrows:"\u21c4",rightleftharpoons:"\u21cc",rightrightarrows:"\u21c9",rightsquigarrow:"\u219d",rightthreetimes:"\u22cc",ring:"\u02da",risingdotseq:"\u2253",rlarr:"\u21c4",rlhar:"\u21cc",rlm:"\u200f",rmoust:"\u23b1",rmoustache:"\u23b1",rnmid:"\u2aee",roang:"\u27ed",roarr:"\u21fe",robrk:"\u27e7",ropar:"\u2986",ropf:"\ud835\udd63",roplus:"\u2a2e",rotimes:"\u2a35",rpar:")",rpargt:"\u2994",rppolint:"\u2a12",rrarr:"\u21c9",rsaquo:"\u203a",rscr:"\ud835\udcc7",rsh:"\u21b1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22cc",rtimes:"\u22ca",rtri:"\u25b9",rtrie:"\u22b5",rtrif:"\u25b8",rtriltri:"\u29ce",ruluhar:"\u2968",rx:"\u211e",sacute:"\u015b",sbquo:"\u201a",sc:"\u227b",scE:"\u2ab4",scap:"\u2ab8",scaron:"\u0161",sccue:"\u227d",sce:"\u2ab0",scedil:"\u015f",scirc:"\u015d",scnE:"\u2ab6",scnap:"\u2aba",scnsim:"\u22e9",scpolint:"\u2a13",scsim:"\u227f",scy:"\u0441",sdot:"\u22c5",sdotb:"\u22a1",sdote:"\u2a66",seArr:"\u21d8",searhk:"\u2925",searr:"\u2198",searrow:"\u2198",sect:"\xa7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",sfr:"\ud835\udd30",sfrown:"\u2322",sharp:"\u266f",shchcy:"\u0449",shcy:"\u0448",shortmid:"\u2223",shortparallel:"\u2225",shy:"\xad",sigma:"\u03c3",sigmaf:"\u03c2",sigmav:"\u03c2",sim:"\u223c",simdot:"\u2a6a",sime:"\u2243",simeq:"\u2243",simg:"\u2a9e",simgE:"\u2aa0",siml:"\u2a9d",simlE:"\u2a9f",simne:"\u2246",simplus:"\u2a24",simrarr:"\u2972",slarr:"\u2190",smallsetminus:"\u2216",smashp:"\u2a33",smeparsl:"\u29e4",smid:"\u2223",smile:"\u2323",smt:"\u2aaa",smte:"\u2aac",smtes:"\u2aac\ufe00",softcy:"\u044c",sol:"/",solb:"\u29c4",solbar:"\u233f",sopf:"\ud835\udd64",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\ufe00",sqcup:"\u2294",sqcups:"\u2294\ufe00",sqsub:"\u228f",sqsube:"\u2291",sqsubset:"\u228f",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25a1",square:"\u25a1",squarf:"\u25aa",squf:"\u25aa",srarr:"\u2192",sscr:"\ud835\udcc8",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22c6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03f5",straightphi:"\u03d5",strns:"\xaf",sub:"\u2282",subE:"\u2ac5",subdot:"\u2abd",sube:"\u2286",subedot:"\u2ac3",submult:"\u2ac1",subnE:"\u2acb",subne:"\u228a",subplus:"\u2abf",subrarr:"\u2979",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2ac5",subsetneq:"\u228a",subsetneqq:"\u2acb",subsim:"\u2ac7",subsub:"\u2ad5",subsup:"\u2ad3",succ:"\u227b",succapprox:"\u2ab8",succcurlyeq:"\u227d",succeq:"\u2ab0",succnapprox:"\u2aba",succneqq:"\u2ab6",succnsim:"\u22e9",succsim:"\u227f",sum:"\u2211",sung:"\u266a",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"\u2283",supE:"\u2ac6",supdot:"\u2abe",supdsub:"\u2ad8",supe:"\u2287",supedot:"\u2ac4",suphsol:"\u27c9",suphsub:"\u2ad7",suplarr:"\u297b",supmult:"\u2ac2",supnE:"\u2acc",supne:"\u228b",supplus:"\u2ac0",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2ac6",supsetneq:"\u228b",supsetneqq:"\u2acc",supsim:"\u2ac8",supsub:"\u2ad4",supsup:"\u2ad6",swArr:"\u21d9",swarhk:"\u2926",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292a",szlig:"\xdf",target:"\u2316",tau:"\u03c4",tbrk:"\u23b4",tcaron:"\u0165",tcedil:"\u0163",tcy:"\u0442",tdot:"\u20db",telrec:"\u2315",tfr:"\ud835\udd31",there4:"\u2234",therefore:"\u2234",theta:"\u03b8",thetasym:"\u03d1",thetav:"\u03d1",thickapprox:"\u2248",thicksim:"\u223c",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223c",thorn:"\xfe",tilde:"\u02dc",times:"\xd7",timesb:"\u22a0",timesbar:"\u2a31",timesd:"\u2a30",tint:"\u222d",toea:"\u2928",top:"\u22a4",topbot:"\u2336",topcir:"\u2af1",topf:"\ud835\udd65",topfork:"\u2ada",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",triangle:"\u25b5",triangledown:"\u25bf",triangleleft:"\u25c3",trianglelefteq:"\u22b4",triangleq:"\u225c",triangleright:"\u25b9",trianglerighteq:"\u22b5",tridot:"\u25ec",trie:"\u225c",triminus:"\u2a3a",triplus:"\u2a39",trisb:"\u29cd",tritime:"\u2a3b",trpezium:"\u23e2",tscr:"\ud835\udcc9",tscy:"\u0446",tshcy:"\u045b",tstrok:"\u0167",twixt:"\u226c",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",uArr:"\u21d1",uHar:"\u2963",uacute:"\xfa",uarr:"\u2191",ubrcy:"\u045e",ubreve:"\u016d",ucirc:"\xfb",ucy:"\u0443",udarr:"\u21c5",udblac:"\u0171",udhar:"\u296e",ufisht:"\u297e",ufr:"\ud835\udd32",ugrave:"\xf9",uharl:"\u21bf",uharr:"\u21be",uhblk:"\u2580",ulcorn:"\u231c",ulcorner:"\u231c",ulcrop:"\u230f",ultri:"\u25f8",umacr:"\u016b",uml:"\xa8",uogon:"\u0173",uopf:"\ud835\udd66",uparrow:"\u2191",updownarrow:"\u2195",upharpoonleft:"\u21bf",upharpoonright:"\u21be",uplus:"\u228e",upsi:"\u03c5",upsih:"\u03d2",upsilon:"\u03c5",upuparrows:"\u21c8",urcorn:"\u231d",urcorner:"\u231d",urcrop:"\u230e",uring:"\u016f",urtri:"\u25f9",uscr:"\ud835\udcca",utdot:"\u22f0",utilde:"\u0169",utri:"\u25b5",utrif:"\u25b4",uuarr:"\u21c8",uuml:"\xfc",uwangle:"\u29a7",vArr:"\u21d5",vBar:"\u2ae8",vBarv:"\u2ae9",vDash:"\u22a8",vangrt:"\u299c",varepsilon:"\u03f5",varkappa:"\u03f0",varnothing:"\u2205",varphi:"\u03d5",varpi:"\u03d6",varpropto:"\u221d",varr:"\u2195",varrho:"\u03f1",varsigma:"\u03c2",varsubsetneq:"\u228a\ufe00",varsubsetneqq:"\u2acb\ufe00",varsupsetneq:"\u228b\ufe00",varsupsetneqq:"\u2acc\ufe00",vartheta:"\u03d1",vartriangleleft:"\u22b2",vartriangleright:"\u22b3",vcy:"\u0432",vdash:"\u22a2",vee:"\u2228",veebar:"\u22bb",veeeq:"\u225a",vellip:"\u22ee",verbar:"|",vert:"|",vfr:"\ud835\udd33",vltri:"\u22b2",vnsub:"\u2282\u20d2",vnsup:"\u2283\u20d2",vopf:"\ud835\udd67",vprop:"\u221d",vrtri:"\u22b3",vscr:"\ud835\udccb",vsubnE:"\u2acb\ufe00",vsubne:"\u228a\ufe00",vsupnE:"\u2acc\ufe00",vsupne:"\u228b\ufe00",vzigzag:"\u299a",wcirc:"\u0175",wedbar:"\u2a5f",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",wfr:"\ud835\udd34",wopf:"\ud835\udd68",wp:"\u2118",wr:"\u2240",wreath:"\u2240",wscr:"\ud835\udccc",xcap:"\u22c2",xcirc:"\u25ef",xcup:"\u22c3",xdtri:"\u25bd",xfr:"\ud835\udd35",xhArr:"\u27fa",xharr:"\u27f7",xi:"\u03be",xlArr:"\u27f8",xlarr:"\u27f5",xmap:"\u27fc",xnis:"\u22fb",xodot:"\u2a00",xopf:"\ud835\udd69",xoplus:"\u2a01",xotime:"\u2a02",xrArr:"\u27f9",xrarr:"\u27f6",xscr:"\ud835\udccd",xsqcup:"\u2a06",xuplus:"\u2a04",xutri:"\u25b3",xvee:"\u22c1",xwedge:"\u22c0",yacute:"\xfd",yacy:"\u044f",ycirc:"\u0177",ycy:"\u044b",yen:"\xa5",yfr:"\ud835\udd36",yicy:"\u0457",yopf:"\ud835\udd6a",yscr:"\ud835\udcce",yucy:"\u044e",yuml:"\xff",zacute:"\u017a",zcaron:"\u017e",zcy:"\u0437",zdot:"\u017c",zeetrf:"\u2128",zeta:"\u03b6",zfr:"\ud835\udd37",zhcy:"\u0436",zigrarr:"\u21dd",zopf:"\ud835\udd6b",zscr:"\ud835\udccf",zwj:"\u200d",zwnj:"\u200c"},us={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376};function ts(e){return e.replace(/&(?:[a-zA-Z]+|#[xX][\da-fA-F]+|#\d+);/g,(e=>{if("#"===e.charAt(1)){const u=e.charAt(2);return function(e){if(e>=55296&&e<=57343||e>1114111)return"\ufffd";e in us&&(e=us[e]);return String.fromCodePoint(e)}("X"===u||"x"===u?parseInt(e.slice(3),16):parseInt(e.slice(2),10))}return es[e.slice(1,-1)]||e}))}function ns(e,u){return e.startPos=e.tokenPos=e.index,e.startColumn=e.colPos=e.column,e.startLine=e.linePos=e.line,e.token=8192&Pi[e.currentChar]?function(e,u){const t=e.currentChar;let n=mi(e);const r=e.index;for(;n!==t;)e.index>=e.end&&Fi(e,14),n=mi(e);n!==t&&Fi(e,14);e.tokenValue=e.source.slice(r,e.index),mi(e),512&u&&(e.tokenRaw=e.source.slice(e.tokenPos,e.index));return 134283267}(e,u):Qi(e,u,0),e.token}function rs(e,u){if(e.startPos=e.tokenPos=e.index,e.startColumn=e.colPos=e.column,e.startLine=e.linePos=e.line,e.index>=e.end)return e.token=1048576;switch(Ki[e.source.charCodeAt(e.index)]){case 8456258:mi(e),47===e.currentChar?(mi(e),e.token=25):e.token=8456258;break;case 2162700:mi(e),e.token=2162700;break;default:{let t=0;for(;e.index<e.end;){const u=Pi[e.source.charCodeAt(e.index)];if(1024&u?(t|=5,Bi(e)):2048&u?(yi(e,t),t=-5&t|1):mi(e),16384&Pi[e.currentChar])break}const n=e.source.slice(e.tokenPos,e.index);512&u&&(e.tokenRaw=n),e.tokenValue=ts(n),e.token=138}}return e.token}function os(e){if(143360==(143360&e.token)){const{index:u}=e;let t=e.currentChar;for(;32770&Pi[t];)t=mi(e);e.tokenValue+=e.source.slice(u,e.index)}return e.token=208897,e.token}function is(e,u,t){0!=(1&e.flags)||1048576==(1048576&e.token)||t||Fi(e,28,_i[255&e.token]),Ds(e,u,1074790417)}function ss(e,u,t,n){return u-t<13&&"use strict"===n&&(1048576==(1048576&e.token)||1&e.flags)?1:0}function as(e,u,t){return e.token!==t?0:(Zi(e,u),1)}function Ds(e,u,t){return e.token===t&&(Zi(e,u),!0)}function cs(e,u,t){e.token!==t&&Fi(e,23,_i[255&t]),Zi(e,u)}function ls(e,u){switch(u.type){case"ArrayExpression":u.type="ArrayPattern";const t=u.elements;for(let u=0,n=t.length;u<n;++u){const n=t[u];n&&ls(e,n)}return;case"ObjectExpression":u.type="ObjectPattern";const n=u.properties;for(let u=0,t=n.length;u<t;++u)ls(e,n[u]);return;case"AssignmentExpression":return u.type="AssignmentPattern","="!==u.operator&&Fi(e,68),delete u.operator,void ls(e,u.left);case"Property":return void ls(e,u.value);case"SpreadElement":u.type="RestElement",ls(e,u.argument)}}function ps(e,u,t,n,r){1024&u&&(36864==(36864&n)&&Fi(e,114),r||537079808!=(537079808&n)||Fi(e,115)),20480==(20480&n)&&Fi(e,99),24&t&&241739===n&&Fi(e,97),4196352&u&&209008===n&&Fi(e,95),2098176&u&&241773===n&&Fi(e,94,"yield")}function fs(e,u,t){1024&u&&(36864==(36864&t)&&Fi(e,114),537079808==(537079808&t)&&Fi(e,115),122===t&&Fi(e,92),121===t&&Fi(e,92)),20480==(20480&t)&&Fi(e,99),4196352&u&&209008===t&&Fi(e,95),2098176&u&&241773===t&&Fi(e,94,"yield")}function ds(e,u,t){return 209008===t&&(4196352&u&&Fi(e,95),e.destructible|=128),241773===t&&2097152&u&&Fi(e,94,"yield"),20480==(20480&t)||36864==(36864&t)||122==t}function Fs(e,u,t,n){for(;u;){if(u["$"+t])return n&&Fi(e,133),1;n&&u.loop&&(n=0),u=u.$}return 0}function Es(e,u,t,n,r,o){return 2&u&&(o.start=t,o.end=e.startPos,o.range=[t,e.startPos]),4&u&&(o.loc={start:{line:n,column:r},end:{line:e.startLine,column:e.startColumn}},e.sourceFile&&(o.loc.source=e.sourceFile)),o}function Cs(e){switch(e.type){case"JSXIdentifier":return e.name;case"JSXNamespacedName":return e.namespace+":"+e.name;case"JSXMemberExpression":return Cs(e.object)+"."+Cs(e.property)}}function As(e,u,t){const n=ms({parent:void 0,type:2},1024);return ys(e,u,n,t,1,0),n}function gs(e,u,...t){const{index:n,line:r,column:o}=e;return{type:u,params:t,index:n,line:r,column:o}}function ms(e,u){return{parent:e,type:u,scopeError:void 0}}function hs(e,u,t,n,r,o){4&r?Bs(e,u,t,n,r):ys(e,u,t,n,r,o),64&o&&ks(e,n)}function ys(e,u,t,n,r,o){const i=t["#"+n];i&&0==(2&i)&&(1&r?t.scopeError=gs(e,140,n):256&u&&64&i&&2&o||Fi(e,140,n)),128&t.type&&t.parent["#"+n]&&0==(2&t.parent["#"+n])&&Fi(e,140,n),1024&t.type&&i&&0==(2&i)&&1&r&&(t.scopeError=gs(e,140,n)),64&t.type&&768&t.parent["#"+n]&&Fi(e,153,n),t["#"+n]=r}function Bs(e,u,t,n,r){let o=t;for(;o&&0==(256&o.type);){const i=o["#"+n];248&i&&(256&u&&0==(1024&u)&&(128&r&&68&i||128&i&&68&r)||Fi(e,140,n)),o===t&&1&i&&1&r&&(o.scopeError=gs(e,140,n)),768&i&&(0==(512&i)||0==(256&u)||1024&u)&&Fi(e,140,n),o["#"+n]=r,o=o.parent}}function ks(e,u){void 0!==e.exportedNames&&""!==u&&(e.exportedNames["#"+u]&&Fi(e,141,u),e.exportedNames["#"+u]=1)}function bs(e,u){void 0!==e.exportedBindings&&""!==u&&(e.exportedBindings["#"+u]=1)}function Ps(e,u){return 2098176&e?!(2048&e&&209008===u)&&(!(2097152&e&&241773===u)&&(143360==(143360&u)||12288==(12288&u))):143360==(143360&u)||12288==(12288&u)||36864==(36864&u)}function xs(e,u,t,n){537079808==(537079808&t)&&(1024&u&&Fi(e,115),n&&(e.flags|=512)),Ps(u,t)||Fi(e,0)}function vs(e,u,t){let n,r,o="";null!=u&&(u.module&&(t|=3072),u.next&&(t|=1),u.loc&&(t|=4),u.ranges&&(t|=2),u.uniqueKeyInPattern&&(t|=-2147483648),u.lexical&&(t|=64),u.webcompat&&(t|=256),u.directives&&(t|=520),u.globalReturn&&(t|=32),u.raw&&(t|=512),u.preserveParens&&(t|=128),u.impliedStrict&&(t|=1024),u.jsx&&(t|=16),u.identifierPattern&&(t|=268435456),u.specDeviation&&(t|=536870912),u.source&&(o=u.source),null!=u.onComment&&(n=Array.isArray(u.onComment)?function(e,u){return function(t,n,r,o,i){const s={type:t,value:n};2&e&&(s.start=r,s.end=o,s.range=[r,o]),4&e&&(s.loc=i),u.push(s)}}(t,u.onComment):u.onComment),null!=u.onToken&&(r=Array.isArray(u.onToken)?function(e,u){return function(t,n,r,o){const i={token:t};2&e&&(i.start=n,i.end=r,i.range=[n,r]),4&e&&(i.loc=o),u.push(i)}}(t,u.onToken):u.onToken));const i=function(e,u,t,n){return{source:e,flags:0,index:0,line:1,column:0,startPos:0,end:e.length,tokenPos:0,startColumn:0,colPos:0,linePos:1,startLine:1,sourceFile:u,tokenValue:"",token:1048576,tokenRaw:"",tokenRegExp:void 0,currentChar:e.charCodeAt(0),exportedNames:[],exportedBindings:[],assignable:1,destructible:0,onComment:t,onToken:n,leadingDecorators:[]}}(e,o,n,r);1&t&&function(e){const u=e.source;35===e.currentChar&&33===u.charCodeAt(e.index+1)&&(mi(e),mi(e),Ii(e,u,0,4,e.tokenPos,e.linePos,e.colPos))}(i);const s=64&t?{parent:void 0,type:2}:void 0;let a=[],D="script";if(2048&t){if(D="module",a=function(e,u,t){Zi(e,32768|u);const n=[];if(8&u)for(;134283267===e.token;){const{tokenPos:t,linePos:r,colPos:o,token:i}=e;n.push(Rs(e,u,Fa(e,u),i,t,r,o))}for(;1048576!==e.token;)n.push(ws(e,u,t));return n}(i,8192|t,s),s)for(const e in i.exportedBindings)"#"!==e[0]||s[e]||Fi(i,142,e.slice(1))}else a=function(e,u,t){Zi(e,1073774592|u);const n=[];for(;134283267===e.token;){const{index:t,tokenPos:r,tokenValue:o,linePos:i,colPos:s,token:a}=e,D=Fa(e,u);ss(e,t,r,o)&&(u|=1024),n.push(Rs(e,u,D,a,r,i,s))}for(;1048576!==e.token;)n.push(Ss(e,u,t,4,{}));return n}(i,8192|t,s);const c={type:"Program",sourceType:D,body:a};return 2&t&&(c.start=0,c.end=e.length,c.range=[0,e.length]),4&t&&(c.loc={start:{line:1,column:0},end:{line:i.line,column:i.column}},i.sourceFile&&(c.loc.source=o)),c}function ws(e,u,t){let n;switch(e.leadingDecorators=Ia(e,u),e.token){case 20566:n=function(e,u,t){const n=e.tokenPos,r=e.linePos,o=e.colPos;Zi(e,32768|u);const i=[];let s,a=null,D=null;if(Ds(e,32768|u,20563)){switch(e.token){case 86106:a=Ea(e,u,t,4,1,1,0,e.tokenPos,e.linePos,e.colPos);break;case 133:case 86096:a=Na(e,u,t,1,e.tokenPos,e.linePos,e.colPos);break;case 209007:const{tokenPos:n,linePos:r,colPos:o}=e;a=da(e,u,0);const{flags:i}=e;(1&i)<1&&(86106===e.token?a=Ea(e,u,t,4,1,1,1,n,r,o):67174411===e.token?(a=Ta(e,u,a,1,1,0,i,n,r,o),a=na(e,u,a,0,0,n,r,o),a=Ks(e,u,0,0,n,r,o,a)):143360&e.token&&(t&&(t=As(e,u,e.tokenValue)),a=da(e,u,0),a=xa(e,u,t,[a],1,n,r,o)));break;default:a=Js(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos),is(e,32768|u)}return t&&ks(e,"default"),Es(e,u,n,r,o,{type:"ExportDefaultDeclaration",declaration:a})}switch(e.token){case 8457014:{Zi(e,u);let i=null;return Ds(e,u,77934)&&(t&&ks(e,e.tokenValue),i=da(e,u,0)),cs(e,u,12404),134283267!==e.token&&Fi(e,102,"Export"),D=Fa(e,u),is(e,32768|u),Es(e,u,n,r,o,{type:"ExportAllDeclaration",source:D,exported:i})}case 2162700:{Zi(e,u);const n=[],r=[];for(;143360&e.token;){const{tokenPos:o,tokenValue:s,linePos:a,colPos:D}=e,c=da(e,u,0);let l;77934===e.token?(Zi(e,u),134217728==(134217728&e.token)&&Fi(e,103),t&&(n.push(e.tokenValue),r.push(s)),l=da(e,u,0)):(t&&(n.push(e.tokenValue),r.push(e.tokenValue)),l=c),i.push(Es(e,u,o,a,D,{type:"ExportSpecifier",local:c,exported:l})),1074790415!==e.token&&cs(e,u,18)}if(cs(e,u,1074790415),Ds(e,u,12404))134283267!==e.token&&Fi(e,102,"Export"),D=Fa(e,u);else if(t){let u=0,t=n.length;for(;u<t;u++)ks(e,n[u]);for(u=0,t=r.length;u<t;u++)bs(e,r[u])}is(e,32768|u);break}case 86096:a=Na(e,u,t,2,e.tokenPos,e.linePos,e.colPos);break;case 86106:a=Ea(e,u,t,4,1,2,0,e.tokenPos,e.linePos,e.colPos);break;case 241739:a=Vs(e,u,t,8,64,e.tokenPos,e.linePos,e.colPos);break;case 86092:a=Vs(e,u,t,16,64,e.tokenPos,e.linePos,e.colPos);break;case 86090:a=Ms(e,u,t,64,e.tokenPos,e.linePos,e.colPos);break;case 209007:const{tokenPos:c,linePos:l,colPos:p}=e;if(Zi(e,u),(1&e.flags)<1&&86106===e.token){a=Ea(e,u,t,4,1,2,1,c,l,p),t&&(s=a.id?a.id.name:"",ks(e,s));break}default:Fi(e,28,_i[255&e.token])}return Es(e,u,n,r,o,{type:"ExportNamedDeclaration",declaration:a,specifiers:i,source:D})}(e,u,t);break;case 86108:n=function(e,u,t){const n=e.tokenPos,r=e.linePos,o=e.colPos;Zi(e,u);let i=null;const{tokenPos:s,linePos:a,colPos:D}=e;let c=[];if(134283267===e.token)i=Fa(e,u);else{if(143360&e.token){if(c=[Es(e,u,s,a,D,{type:"ImportDefaultSpecifier",local:Gs(e,u,t)})],Ds(e,u,18))switch(e.token){case 8457014:c.push(_s(e,u,t));break;case 2162700:Xs(e,u,t,c);break;default:Fi(e,104)}}else switch(e.token){case 8457014:c=[_s(e,u,t)];break;case 2162700:Xs(e,u,t,c);break;case 67174411:return zs(e,u,n,r,o);case 67108877:return Hs(e,u,n,r,o);default:Fi(e,28,_i[255&e.token])}i=function(e,u){Ds(e,u,12404),134283267!==e.token&&Fi(e,102,"Import");return Fa(e,u)}(e,u)}return is(e,32768|u),Es(e,u,n,r,o,{type:"ImportDeclaration",specifiers:c,source:i})}(e,u,t);break;default:n=Ss(e,u,t,4,{})}return e.leadingDecorators.length&&Fi(e,164),n}function Ss(e,u,t,n,r){const o=e.tokenPos,i=e.linePos,s=e.colPos;switch(e.token){case 86106:return Ea(e,u,t,n,1,0,0,o,i,s);case 133:case 86096:return Na(e,u,t,0,o,i,s);case 86092:return Vs(e,u,t,16,0,o,i,s);case 241739:return function(e,u,t,n,r,o,i){const{token:s,tokenValue:a}=e;let D=da(e,u,0);if(2240512&e.token){const n=$s(e,u,t,8,0);return is(e,32768|u),Es(e,u,r,o,i,{type:"VariableDeclaration",kind:"let",declarations:n})}e.assignable=1,1024&u&&Fi(e,82);if(21===e.token)return Ls(e,u,t,n,{},a,D,s,0,r,o,i);if(10===e.token){let t;64&u&&(t=As(e,u,a)),e.flags=128^(128|e.flags),D=xa(e,u,t,[D],0,r,o,i)}else D=na(e,u,D,0,0,r,o,i),D=Ks(e,u,0,0,r,o,i,D);18===e.token&&(D=Ws(e,u,0,r,o,i,D));return Is(e,u,D,r,o,i)}(e,u,t,n,o,i,s);case 20566:Fi(e,100,"export");case 86108:switch(Zi(e,u),e.token){case 67174411:return zs(e,u,o,i,s);case 67108877:return Hs(e,u,o,i,s);default:Fi(e,100,"import")}case 209007:return Os(e,u,t,n,r,1,o,i,s);default:return Ts(e,u,t,n,r,1,o,i,s)}}function Ts(e,u,t,n,r,o,i,s,a){switch(e.token){case 86090:return Ms(e,u,t,0,i,s,a);case 20574:return function(e,u,t,n,r){(32&u)<1&&8192&u&&Fi(e,89);Zi(e,32768|u);const o=1&e.flags||1048576&e.token?null:Ys(e,u,0,1,e.tokenPos,e.line,e.column);return is(e,32768|u),Es(e,u,t,n,r,{type:"ReturnStatement",argument:o})}(e,u,i,s,a);case 20571:return function(e,u,t,n,r,o,i){Zi(e,u),cs(e,32768|u,67174411),e.assignable=1;const s=Ys(e,u,0,1,e.tokenPos,e.line,e.colPos);cs(e,32768|u,16);const a=qs(e,u,t,n,e.tokenPos,e.linePos,e.colPos);let D=null;20565===e.token&&(Zi(e,32768|u),D=qs(e,u,t,n,e.tokenPos,e.linePos,e.colPos));return Es(e,u,r,o,i,{type:"IfStatement",test:s,consequent:a,alternate:D})}(e,u,t,r,i,s,a);case 20569:return function(e,u,t,n,r,o,i){Zi(e,u);const s=(4194304&u)>0&&Ds(e,u,209008);cs(e,32768|u,67174411),t&&(t=ms(t,1));let a,D=null,c=null,l=0,p=null,f=86090===e.token||241739===e.token||86092===e.token;const{token:d,tokenPos:F,linePos:E,colPos:C}=e;f?241739===d?(p=da(e,u,0),2240512&e.token?(8738868===e.token?1024&u&&Fi(e,64):p=Es(e,u,F,E,C,{type:"VariableDeclaration",kind:"let",declarations:$s(e,134217728|u,t,8,32)}),e.assignable=1):1024&u?Fi(e,64):(f=!1,e.assignable=1,p=na(e,u,p,0,0,F,E,C),274549===e.token&&Fi(e,111))):(Zi(e,u),p=Es(e,u,F,E,C,86090===d?{type:"VariableDeclaration",kind:"var",declarations:$s(e,134217728|u,t,4,32)}:{type:"VariableDeclaration",kind:"const",declarations:$s(e,134217728|u,t,16,32)}),e.assignable=1):1074790417===d?s&&Fi(e,79):2097152==(2097152&d)?(p=2162700===d?ya(e,u,void 0,1,0,0,2,32,F,E,C):Aa(e,u,void 0,1,0,0,2,32,F,E,C),l=e.destructible,256&u&&64&l&&Fi(e,60),e.assignable=16&l?2:1,p=na(e,134217728|u,p,0,0,e.tokenPos,e.linePos,e.colPos)):p=ta(e,134217728|u,1,0,1,F,E,C);if(262144==(262144&e.token)){if(274549===e.token){2&e.assignable&&Fi(e,77,s?"await":"of"),ls(e,p),Zi(e,32768|u),a=Js(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos),cs(e,32768|u,16);return Es(e,u,r,o,i,{type:"ForOfStatement",left:p,right:a,body:js(e,u,t,n),await:s})}2&e.assignable&&Fi(e,77,"in"),ls(e,p),Zi(e,32768|u),s&&Fi(e,79),a=Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos),cs(e,32768|u,16);return Es(e,u,r,o,i,{type:"ForInStatement",body:js(e,u,t,n),left:p,right:a})}s&&Fi(e,79);f||(8&l&&1077936157!==e.token&&Fi(e,77,"loop"),p=Ks(e,134217728|u,0,0,F,E,C,p));18===e.token&&(p=Ws(e,u,0,e.tokenPos,e.linePos,e.colPos,p));cs(e,32768|u,1074790417),1074790417!==e.token&&(D=Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos));cs(e,32768|u,1074790417),16!==e.token&&(c=Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos));cs(e,32768|u,16);const A=js(e,u,t,n);return Es(e,u,r,o,i,{type:"ForStatement",init:p,test:D,update:c,body:A})}(e,u,t,r,i,s,a);case 20564:return function(e,u,t,n,r,o,i){Zi(e,32768|u);const s=js(e,u,t,n);cs(e,u,20580),cs(e,32768|u,67174411);const a=Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos);return cs(e,32768|u,16),Ds(e,u,1074790417),Es(e,u,r,o,i,{type:"DoWhileStatement",body:s,test:a})}(e,u,t,r,i,s,a);case 20580:return function(e,u,t,n,r,o,i){Zi(e,u),cs(e,32768|u,67174411);const s=Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos);cs(e,32768|u,16);const a=js(e,u,t,n);return Es(e,u,r,o,i,{type:"WhileStatement",test:s,body:a})}(e,u,t,r,i,s,a);case 86112:return function(e,u,t,n,r,o,i){Zi(e,u),cs(e,32768|u,67174411);const s=Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos);cs(e,u,16),cs(e,u,2162700);const a=[];let D=0;t&&(t=ms(t,8));for(;1074790415!==e.token;){const{tokenPos:r,linePos:o,colPos:i}=e;let s=null;const c=[];for(Ds(e,32768|u,20558)?s=Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos):(cs(e,32768|u,20563),D&&Fi(e,86),D=1),cs(e,32768|u,21);20558!==e.token&&1074790415!==e.token&&20563!==e.token;)c.push(Ss(e,4096|u,t,2,{$:n}));a.push(Es(e,u,r,o,i,{type:"SwitchCase",test:s,consequent:c}))}return cs(e,32768|u,1074790415),Es(e,u,r,o,i,{type:"SwitchStatement",discriminant:s,cases:a})}(e,u,t,r,i,s,a);case 1074790417:return function(e,u,t,n,r){return Zi(e,32768|u),Es(e,u,t,n,r,{type:"EmptyStatement"})}(e,u,i,s,a);case 2162700:return Ns(e,u,t?ms(t,2):t,r,i,s,a);case 86114:return function(e,u,t,n,r){Zi(e,32768|u),1&e.flags&&Fi(e,87);const o=Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos);return is(e,32768|u),Es(e,u,t,n,r,{type:"ThrowStatement",argument:o})}(e,u,i,s,a);case 20557:return function(e,u,t,n,r,o){Zi(e,32768|u);let i=null;if((1&e.flags)<1&&143360&e.token){const{tokenValue:n}=e;i=da(e,32768|u,0),Fs(e,t,n,0)||Fi(e,134,n)}else(135168&u)<1&&Fi(e,66);return is(e,32768|u),Es(e,u,n,r,o,{type:"BreakStatement",label:i})}(e,u,r,i,s,a);case 20561:return function(e,u,t,n,r,o){(131072&u)<1&&Fi(e,65);Zi(e,u);let i=null;if((1&e.flags)<1&&143360&e.token){const{tokenValue:n}=e;i=da(e,32768|u,0),Fs(e,t,n,1)||Fi(e,134,n)}return is(e,32768|u),Es(e,u,n,r,o,{type:"ContinueStatement",label:i})}(e,u,r,i,s,a);case 20579:return function(e,u,t,n,r,o,i){Zi(e,32768|u);const s=t?ms(t,32):void 0,a=Ns(e,u,s,{$:n},e.tokenPos,e.linePos,e.colPos),{tokenPos:D,linePos:c,colPos:l}=e,p=Ds(e,32768|u,20559)?function(e,u,t,n,r,o,i){let s=null,a=t;Ds(e,u,67174411)&&(t&&(t=ms(t,4)),s=Va(e,u,t,2097152==(2097152&e.token)?256:512,0,e.tokenPos,e.linePos,e.colPos),18===e.token?Fi(e,83):1077936157===e.token&&Fi(e,84),cs(e,32768|u,16),t&&(a=ms(t,64)));const D=Ns(e,u,a,{$:n},e.tokenPos,e.linePos,e.colPos);return Es(e,u,r,o,i,{type:"CatchClause",param:s,body:D})}(e,u,t,n,D,c,l):null;let f=null;if(20568===e.token){Zi(e,32768|u);f=Ns(e,u,s?ms(t,4):void 0,{$:n},e.tokenPos,e.linePos,e.colPos)}p||f||Fi(e,85);return Es(e,u,r,o,i,{type:"TryStatement",block:a,handler:p,finalizer:f})}(e,u,t,r,i,s,a);case 20581:return function(e,u,t,n,r,o,i){Zi(e,u),1024&u&&Fi(e,88);cs(e,32768|u,67174411);const s=Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos);cs(e,32768|u,16);const a=Ts(e,u,t,2,n,0,e.tokenPos,e.linePos,e.colPos);return Es(e,u,r,o,i,{type:"WithStatement",object:s,body:a})}(e,u,t,r,i,s,a);case 20562:return function(e,u,t,n,r){return Zi(e,32768|u),is(e,32768|u),Es(e,u,t,n,r,{type:"DebuggerStatement"})}(e,u,i,s,a);case 209007:return Os(e,u,t,n,r,0,i,s,a);case 20559:Fi(e,156);case 20568:Fi(e,157);case 86106:Fi(e,1024&u?73:(256&u)<1?75:74);case 86096:Fi(e,76);default:return function(e,u,t,n,r,o,i,s,a){const{tokenValue:D,token:c}=e;let l;switch(c){case 241739:l=da(e,u,0),1024&u&&Fi(e,82),69271571===e.token&&Fi(e,81);break;default:l=oa(e,u,2,0,1,0,0,1,e.tokenPos,e.linePos,e.colPos)}if(143360&c&&21===e.token)return Ls(e,u,t,n,r,D,l,c,o,i,s,a);l=na(e,u,l,0,0,i,s,a),l=Ks(e,u,0,0,i,s,a,l),18===e.token&&(l=Ws(e,u,0,i,s,a,l));return Is(e,u,l,i,s,a)}(e,u,t,n,r,o,i,s,a)}}function Ns(e,u,t,n,r,o,i){const s=[];for(cs(e,32768|u,2162700);1074790415!==e.token;)s.push(Ss(e,u,t,2,{$:n}));return cs(e,32768|u,1074790415),Es(e,u,r,o,i,{type:"BlockStatement",body:s})}function Is(e,u,t,n,r,o){return is(e,32768|u),Es(e,u,n,r,o,{type:"ExpressionStatement",expression:t})}function Ls(e,u,t,n,r,o,i,s,a,D,c,l){ps(e,u,0,s,1),function(e,u,t){let n=u;for(;n;)n["$"+t]&&Fi(e,132,t),n=n.$;u["$"+t]=1}(e,r,o),Zi(e,32768|u);return Es(e,u,D,c,l,{type:"LabeledStatement",label:i,body:a&&(1024&u)<1&&256&u&&86106===e.token?Ea(e,u,ms(t,2),n,0,0,0,e.tokenPos,e.linePos,e.colPos):Ts(e,u,t,n,r,a,e.tokenPos,e.linePos,e.colPos)})}function Os(e,u,t,n,r,o,i,s,a){const{token:D,tokenValue:c}=e;let l=da(e,u,0);if(21===e.token)return Ls(e,u,t,n,r,c,l,D,1,i,s,a);const p=1&e.flags;if(!p){if(86106===e.token)return o||Fi(e,119),Ea(e,u,t,n,1,0,1,i,s,a);if(143360==(143360&e.token))return l=Sa(e,u,1,i,s,a),18===e.token&&(l=Ws(e,u,0,i,s,a,l)),Is(e,u,l,i,s,a)}return 67174411===e.token?l=Ta(e,u,l,1,1,0,p,i,s,a):(10===e.token&&(xs(e,u,D,1),l=ba(e,u,e.tokenValue,l,0,1,0,i,s,a)),e.assignable=1),l=na(e,u,l,0,0,i,s,a),18===e.token&&(l=Ws(e,u,0,i,s,a,l)),l=Ks(e,u,0,0,i,s,a,l),e.assignable=1,Is(e,u,l,i,s,a)}function Rs(e,u,t,n,r,o,i){return 1074790417!==n&&(e.assignable=2,t=na(e,u,t,0,0,r,o,i),1074790417!==e.token&&(t=Ks(e,u,0,0,r,o,i,t),18===e.token&&(t=Ws(e,u,0,r,o,i,t))),is(e,32768|u)),8&u&&"Literal"===t.type&&"string"==typeof t.value?Es(e,u,r,o,i,{type:"ExpressionStatement",expression:t,directive:t.raw.slice(1,-1)}):Es(e,u,r,o,i,{type:"ExpressionStatement",expression:t})}function qs(e,u,t,n,r,o,i){return 1024&u||(256&u)<1||86106!==e.token?Ts(e,u,t,0,{$:n},0,e.tokenPos,e.linePos,e.colPos):Ea(e,u,ms(t,2),0,0,0,0,r,o,i)}function js(e,u,t,n){return Ts(e,134217728^(134217728|u)|131072,t,0,{loop:1,$:n},0,e.tokenPos,e.linePos,e.colPos)}function Vs(e,u,t,n,r,o,i,s){Zi(e,u);const a=$s(e,u,t,n,r);return is(e,32768|u),Es(e,u,o,i,s,{type:"VariableDeclaration",kind:8&n?"let":"const",declarations:a})}function Ms(e,u,t,n,r,o,i){Zi(e,u);const s=$s(e,u,t,4,n);return is(e,32768|u),Es(e,u,r,o,i,{type:"VariableDeclaration",kind:"var",declarations:s})}function $s(e,u,t,n,r){let o=1;const i=[Us(e,u,t,n,r)];for(;Ds(e,u,18);)o++,i.push(Us(e,u,t,n,r));return o>1&&32&r&&262144&e.token&&Fi(e,58,_i[255&e.token]),i}function Us(e,u,t,n,r){const{token:o,tokenPos:i,linePos:s,colPos:a}=e;let D=null;const c=Va(e,u,t,n,r,i,s,a);return 1077936157===e.token?(Zi(e,32768|u),D=Js(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos),(32&r||(2097152&o)<1)&&(274549===e.token||8738868===e.token&&(2097152&o||(4&n)<1||1024&u))&&Ci(i,e.line,e.index-3,57,274549===e.token?"of":"in")):(16&n||(2097152&o)>0)&&262144!=(262144&e.token)&&Fi(e,56,16&n?"const":"destructuring"),Es(e,u,i,s,a,{type:"VariableDeclarator",id:c,init:D})}function Gs(e,u,t){return Ps(u,e.token)||Fi(e,114),537079808==(537079808&e.token)&&Fi(e,115),t&&ys(e,u,t,e.tokenValue,8,0),da(e,u,0)}function _s(e,u,t){const{tokenPos:n,linePos:r,colPos:o}=e;return Zi(e,u),cs(e,u,77934),134217728==(134217728&e.token)&&Ci(n,e.line,e.index,28,_i[255&e.token]),Es(e,u,n,r,o,{type:"ImportNamespaceSpecifier",local:Gs(e,u,t)})}function Xs(e,u,t,n){for(Zi(e,u);143360&e.token;){let{token:r,tokenValue:o,tokenPos:i,linePos:s,colPos:a}=e;const D=da(e,u,0);let c;Ds(e,u,77934)?(134217728==(134217728&e.token)||18===e.token?Fi(e,103):ps(e,u,16,e.token,0),o=e.tokenValue,c=da(e,u,0)):(ps(e,u,16,r,0),c=D),t&&ys(e,u,t,o,8,0),n.push(Es(e,u,i,s,a,{type:"ImportSpecifier",local:c,imported:D})),1074790415!==e.token&&cs(e,u,18)}return cs(e,u,1074790415),n}function Hs(e,u,t,n,r){let o=ia(e,u,Es(e,u,t,n,r,{type:"Identifier",name:"import"}),t,n,r);return o=na(e,u,o,0,0,t,n,r),o=Ks(e,u,0,0,t,n,r,o),Is(e,u,o,t,n,r)}function zs(e,u,t,n,r){let o=sa(e,u,0,t,n,r);return o=na(e,u,o,0,0,t,n,r),Is(e,u,o,t,n,r)}function Js(e,u,t,n,r,o,i,s){let a=oa(e,u,2,0,t,n,r,1,o,i,s);return a=na(e,u,a,r,0,o,i,s),Ks(e,u,r,0,o,i,s,a)}function Ws(e,u,t,n,r,o,i){const s=[i];for(;Ds(e,32768|u,18);)s.push(Js(e,u,1,0,t,e.tokenPos,e.linePos,e.colPos));return Es(e,u,n,r,o,{type:"SequenceExpression",expressions:s})}function Ys(e,u,t,n,r,o,i){const s=Js(e,u,n,0,t,r,o,i);return 18===e.token?Ws(e,u,t,r,o,i,s):s}function Ks(e,u,t,n,r,o,i,s){const{token:a}=e;if(4194304==(4194304&a)){2&e.assignable&&Fi(e,24),(!n&&1077936157===a&&"ArrayExpression"===s.type||"ObjectExpression"===s.type)&&ls(e,s),Zi(e,32768|u);const D=Js(e,u,1,1,t,e.tokenPos,e.linePos,e.colPos);return e.assignable=2,Es(e,u,r,o,i,n?{type:"AssignmentPattern",left:s,right:D}:{type:"AssignmentExpression",left:s,operator:_i[255&a],right:D})}return 8454144==(8454144&a)&&(s=ea(e,u,t,r,o,i,4,a,s)),Ds(e,32768|u,22)&&(s=Qs(e,u,s,r,o,i)),s}function Zs(e,u,t,n,r,o,i,s){const{token:a}=e;Zi(e,32768|u);const D=Js(e,u,1,1,t,e.tokenPos,e.linePos,e.colPos);return s=Es(e,u,r,o,i,n?{type:"AssignmentPattern",left:s,right:D}:{type:"AssignmentExpression",left:s,operator:_i[255&a],right:D}),e.assignable=2,s}function Qs(e,u,t,n,r,o){const i=Js(e,134217728^(134217728|u),1,0,0,e.tokenPos,e.linePos,e.colPos);cs(e,32768|u,21),e.assignable=1;const s=Js(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);return e.assignable=2,Es(e,u,n,r,o,{type:"ConditionalExpression",test:t,consequent:i,alternate:s})}function ea(e,u,t,n,r,o,i,s,a){const D=8738868&-((134217728&u)>0);let c,l;for(e.assignable=2;8454144&e.token&&(c=e.token,l=3840&c,(524288&c&&268435456&s||524288&s&&268435456&c)&&Fi(e,159),!(l+((8457273===c)<<8)-((D===c)<<12)<=i));)Zi(e,32768|u),a=Es(e,u,n,r,o,{type:524288&c||268435456&c?"LogicalExpression":"BinaryExpression",left:a,right:ea(e,u,t,e.tokenPos,e.linePos,e.colPos,l,c,ta(e,u,0,t,1,e.tokenPos,e.linePos,e.colPos)),operator:_i[255&c]});return 1077936157===e.token&&Fi(e,24),a}function ua(e,u,t,n,r,o){const{tokenPos:i,linePos:s,colPos:a}=e;cs(e,32768|u,2162700);const D=[],c=u;if(1074790415!==e.token){for(;134283267===e.token;){const{index:t,tokenPos:n,tokenValue:r,token:o}=e,i=Fa(e,u);ss(e,t,n,r)&&(u|=1024,128&e.flags&&Ci(e.index,e.line,e.tokenPos,63),64&e.flags&&Ci(e.index,e.line,e.tokenPos,8)),D.push(Rs(e,u,i,o,n,e.linePos,e.colPos))}1024&u&&(r&&(537079808==(537079808&r)&&Fi(e,115),36864==(36864&r)&&Fi(e,38)),512&e.flags&&Fi(e,115),256&e.flags&&Fi(e,114)),64&u&&t&&void 0!==o&&(1024&c)<1&&(8192&u)<1&&Ei(o)}for(e.flags=832^(832|e.flags),e.destructible=256^(256|e.destructible);1074790415!==e.token;)D.push(Ss(e,u,t,4,{}));return cs(e,24&n?32768|u:u,1074790415),e.flags&=-193,1077936157===e.token&&Fi(e,24),Es(e,u,i,s,a,{type:"BlockStatement",body:D})}function ta(e,u,t,n,r,o,i,s){return na(e,u,oa(e,u,2,0,t,0,n,r,o,i,s),n,0,o,i,s)}function na(e,u,t,n,r,o,i,s){if(33619968==(33619968&e.token)&&(1&e.flags)<1)t=function(e,u,t,n,r,o){2&e.assignable&&Fi(e,52);const{token:i}=e;return Zi(e,u),e.assignable=2,Es(e,u,n,r,o,{type:"UpdateExpression",argument:t,operator:_i[255&i],prefix:!1})}(e,u,t,o,i,s);else if(67108864==(67108864&e.token)){switch(u=134225920^(134225920|u),e.token){case 67108877:Zi(e,1073741824|u),e.assignable=1;t=Es(e,u,o,i,s,{type:"MemberExpression",object:t,computed:!1,property:ra(e,u)});break;case 69271571:{let r=!1;2048==(2048&e.flags)&&(r=!0,e.flags=2048^(2048|e.flags)),Zi(e,32768|u);const{tokenPos:a,linePos:D,colPos:c}=e,l=Ys(e,u,n,1,a,D,c);cs(e,u,20),e.assignable=1,t=Es(e,u,o,i,s,{type:"MemberExpression",object:t,computed:!0,property:l}),r&&(e.flags|=2048);break}case 67174411:{if(1024==(1024&e.flags))return e.flags=1024^(1024|e.flags),t;let r=!1;2048==(2048&e.flags)&&(r=!0,e.flags=2048^(2048|e.flags));const a=fa(e,u,n);e.assignable=2,t=Es(e,u,o,i,s,{type:"CallExpression",callee:t,arguments:a}),r&&(e.flags|=2048);break}case 67108991:Zi(e,u),e.flags|=2048,e.assignable=2,t=function(e,u,t,n,r,o){let i,s=!1;69271571!==e.token&&67174411!==e.token||2048==(2048&e.flags)&&(s=!0,e.flags=2048^(2048|e.flags));if(69271571===e.token){Zi(e,32768|u);const{tokenPos:s,linePos:a,colPos:D}=e,c=Ys(e,u,0,1,s,a,D);cs(e,u,20),e.assignable=2,i=Es(e,u,n,r,o,{type:"MemberExpression",object:t,computed:!0,optional:!0,property:c})}else if(67174411===e.token){const s=fa(e,u,0);e.assignable=2,i=Es(e,u,n,r,o,{type:"CallExpression",callee:t,arguments:s,optional:!0})}else{(143360&e.token)<1&&Fi(e,154);const s=da(e,u,0);e.assignable=2,i=Es(e,u,n,r,o,{type:"MemberExpression",object:t,computed:!1,optional:!0,property:s})}s&&(e.flags|=2048);return i}(e,u,t,o,i,s);break;default:2048==(2048&e.flags)&&Fi(e,160),e.assignable=2,t=Es(e,u,o,i,s,{type:"TaggedTemplateExpression",tag:t,quasi:67174408===e.token?ca(e,65536|u):Da(e,u,e.tokenPos,e.linePos,e.colPos)})}t=na(e,u,t,0,1,o,i,s)}return 0===r&&2048==(2048&e.flags)&&(e.flags=2048^(2048|e.flags),t=Es(e,u,o,i,s,{type:"ChainExpression",expression:t})),t}function ra(e,u){return(143360&e.token)<1&&131!==e.token&&Fi(e,154),1&u&&131===e.token?qa(e,u,e.tokenPos,e.linePos,e.colPos):da(e,u,0)}function oa(e,u,t,n,r,o,i,s,a,D,c){if(143360==(143360&e.token)){switch(e.token){case 209008:return function(e,u,t,n,r,o,i){if(n&&(e.destructible|=128),4194304&u){t&&Fi(e,0),8388608&u&&Ci(e.index,e.line,e.index,29),Zi(e,32768|u);const n=ta(e,u,0,0,1,e.tokenPos,e.linePos,e.colPos);return e.assignable=2,Es(e,u,r,o,i,{type:"AwaitExpression",argument:n})}return 2048&u&&Fi(e,107,"Await"),ka(e,u,r,o,i)}(e,u,n,i,a,D,c);case 241773:return function(e,u,t,n,r,o,i){if(t&&(e.destructible|=256),2097152&u){Zi(e,32768|u),8388608&u&&Fi(e,30),n||Fi(e,24),22===e.token&&Fi(e,120);let t=null,s=!1;return(1&e.flags)<1&&(s=Ds(e,32768|u,8457014),(77824&e.token||s)&&(t=Js(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos))),e.assignable=2,Es(e,u,r,o,i,{type:"YieldExpression",argument:t,delegate:s})}return 1024&u&&Fi(e,94,"yield"),ka(e,u,r,o,i)}(e,u,i,r,a,D,c);case 209007:return function(e,u,t,n,r,o,i,s,a,D){const{token:c}=e,l=da(e,u,o),{flags:p}=e;if((1&p)<1){if(86106===e.token)return Ca(e,u,1,t,s,a,D);if(143360==(143360&e.token))return n||Fi(e,0),Sa(e,u,r,s,a,D)}return i||67174411!==e.token?10===e.token?(xs(e,u,c,1),i&&Fi(e,48),ba(e,u,e.tokenValue,l,i,r,0,s,a,D)):l:Ta(e,u,l,r,1,0,p,s,a,D)}(e,u,i,s,r,o,n,a,D,c)}const{token:l,tokenValue:p}=e,f=da(e,65536|u,o);return 10===e.token?(s||Fi(e,0),xs(e,u,l,1),ba(e,u,p,f,n,r,0,a,D,c)):(16384&u&&537079928===l&&Fi(e,126),241739===l&&(1024&u&&Fi(e,109),24&t&&Fi(e,97)),e.assignable=1024&u&&537079808==(537079808&l)?2:1,f)}if(134217728==(134217728&e.token))return Fa(e,u);switch(e.token){case 33619995:case 33619996:return function(e,u,t,n,r,o,i){t&&Fi(e,53),n||Fi(e,0);const{token:s}=e;Zi(e,32768|u);const a=ta(e,u,0,0,1,e.tokenPos,e.linePos,e.colPos);return 2&e.assignable&&Fi(e,52),e.assignable=2,Es(e,u,r,o,i,{type:"UpdateExpression",argument:a,operator:_i[255&s],prefix:!0})}(e,u,n,s,a,D,c);case 16863278:case 16842800:case 16842801:case 25233970:case 25233971:case 16863277:case 16863279:return function(e,u,t,n,r,o,i){t||Fi(e,0);const s=e.token;Zi(e,32768|u);const a=ta(e,u,0,i,1,e.tokenPos,e.linePos,e.colPos);var D;return 8457273===e.token&&Fi(e,31),1024&u&&16863278===s&&("Identifier"===a.type?Fi(e,117):(D=a).property&&"PrivateIdentifier"===D.property.type&&Fi(e,123)),e.assignable=2,Es(e,u,n,r,o,{type:"UnaryExpression",operator:_i[255&s],argument:a,prefix:!0})}(e,u,s,a,D,c,i);case 86106:return Ca(e,u,0,i,a,D,c);case 2162700:return function(e,u,t,n,r,o,i){const s=ya(e,u,void 0,t,n,0,2,0,r,o,i);256&u&&64&e.destructible&&Fi(e,60);8&e.destructible&&Fi(e,59);return s}(e,u,r?0:1,i,a,D,c);case 69271571:return function(e,u,t,n,r,o,i){const s=Aa(e,u,void 0,t,n,0,2,0,r,o,i);256&u&&64&e.destructible&&Fi(e,60);8&e.destructible&&Fi(e,59);return s}(e,u,r?0:1,i,a,D,c);case 67174411:return function(e,u,t,n,r,o,i,s){e.flags=128^(128|e.flags);const{tokenPos:a,linePos:D,colPos:c}=e;Zi(e,1073774592|u);const l=64&u?ms({parent:void 0,type:2},1024):void 0;if(Ds(e,u=134225920^(134225920|u),16))return Pa(e,u,l,[],t,0,o,i,s);let p,f=0;e.destructible&=-385;let d=[],F=0,E=0;const{tokenPos:C,linePos:A,colPos:g}=e;e.assignable=1;for(;16!==e.token;){const{token:t,tokenPos:o,linePos:i,colPos:s}=e;if(143360&t)l&&ys(e,u,l,e.tokenValue,1,0),p=oa(e,u,n,0,1,0,1,1,o,i,s),16===e.token||18===e.token?2&e.assignable?(f|=16,E=1):537079808!=(537079808&t)&&36864!=(36864&t)||(E=1):(1077936157===e.token?E=1:f|=16,p=na(e,u,p,1,0,o,i,s),16!==e.token&&18!==e.token&&(p=Ks(e,u,1,0,o,i,s,p)));else{if(2097152!=(2097152&t)){if(14===t){p=ma(e,u,l,16,n,r,0,1,0,o,i,s),16&e.destructible&&Fi(e,71),E=1,!F||16!==e.token&&18!==e.token||d.push(p),f|=8;break}if(f|=16,p=Js(e,u,1,0,1,o,i,s),!F||16!==e.token&&18!==e.token||d.push(p),18===e.token&&(F||(F=1,d=[p])),F){for(;Ds(e,32768|u,18);)d.push(Js(e,u,1,0,1,e.tokenPos,e.linePos,e.colPos));e.assignable=2,p=Es(e,u,C,A,g,{type:"SequenceExpression",expressions:d})}return cs(e,u,16),e.destructible=f,p}p=2162700===t?ya(e,1073741824|u,l,0,1,0,n,r,o,i,s):Aa(e,1073741824|u,l,0,1,0,n,r,o,i,s),f|=e.destructible,E=1,e.assignable=2,16!==e.token&&18!==e.token&&(8&f&&Fi(e,118),p=na(e,u,p,0,0,o,i,s),f|=16,16!==e.token&&18!==e.token&&(p=Ks(e,u,0,0,o,i,s,p)))}if(!F||16!==e.token&&18!==e.token||d.push(p),!Ds(e,32768|u,18))break;if(F||(F=1,d=[p]),16===e.token){f|=8;break}}F&&(e.assignable=2,p=Es(e,u,C,A,g,{type:"SequenceExpression",expressions:d}));cs(e,u,16),16&f&&8&f&&Fi(e,145);if(f|=256&e.destructible?256:0|128&e.destructible?128:0,10===e.token)return 48&f&&Fi(e,46),4196352&u&&128&f&&Fi(e,29),2098176&u&&256&f&&Fi(e,30),E&&(e.flags|=128),Pa(e,u,l,F?d:[p],t,0,o,i,s);8&f&&Fi(e,139);return e.destructible=256^(256|e.destructible)|f,128&u?Es(e,u,a,D,c,{type:"ParenthesizedExpression",expression:p}):p}(e,u,r,1,0,a,D,c);case 86021:case 86022:case 86023:return function(e,u,t,n,r){const o=_i[255&e.token],i=86023===e.token?null:"true"===o;return Zi(e,u),e.assignable=2,Es(e,u,t,n,r,512&u?{type:"Literal",value:i,raw:o}:{type:"Literal",value:i})}(e,u,a,D,c);case 86113:return function(e,u){const{tokenPos:t,linePos:n,colPos:r}=e;return Zi(e,u),e.assignable=2,Es(e,u,t,n,r,{type:"ThisExpression"})}(e,u);case 65540:return function(e,u,t,n,r){const{tokenRaw:o,tokenRegExp:i,tokenValue:s}=e;return Zi(e,u),e.assignable=2,Es(e,u,t,n,r,512&u?{type:"Literal",value:s,regex:i,raw:o}:{type:"Literal",value:s,regex:i})}(e,u,a,D,c);case 133:case 86096:return function(e,u,t,n,r,o){let i=null,s=null;const a=Ia(e,u=16777216^(16778240|u));a.length&&(n=e.tokenPos,r=e.linePos,o=e.colPos);Zi(e,u),4096&e.token&&20567!==e.token&&(ds(e,u,e.token)&&Fi(e,114),537079808==(537079808&e.token)&&Fi(e,115),i=da(e,u,0));let D=u;Ds(e,32768|u,20567)?(s=ta(e,u,0,t,0,e.tokenPos,e.linePos,e.colPos),D|=524288):D=524288^(524288|D);const c=Oa(e,D,u,void 0,2,0,t);return e.assignable=2,Es(e,u,n,r,o,1&u?{type:"ClassExpression",id:i,superClass:s,decorators:a,body:c}:{type:"ClassExpression",id:i,superClass:s,body:c})}(e,u,i,a,D,c);case 86111:return function(e,u,t,n,r){switch(Zi(e,u),e.token){case 67108991:Fi(e,161);case 67174411:(524288&u)<1&&Fi(e,26),16384&u&&Fi(e,143),e.assignable=2;break;case 69271571:case 67108877:(262144&u)<1&&Fi(e,27),16384&u&&Fi(e,143),e.assignable=1;break;default:Fi(e,28,"super")}return Es(e,u,t,n,r,{type:"Super"})}(e,u,a,D,c);case 67174409:return Da(e,u,a,D,c);case 67174408:return ca(e,u);case 86109:return function(e,u,t,n,r,o){const i=da(e,32768|u,0),{tokenPos:s,linePos:a,colPos:D}=e;if(Ds(e,u,67108877)){if(67108864&u&&143494===e.token)return e.assignable=2,function(e,u,t,n,r,o){const i=da(e,u,0);return Es(e,u,n,r,o,{type:"MetaProperty",meta:t,property:i})}(e,u,i,n,r,o);Fi(e,91)}e.assignable=2,16842752==(16842752&e.token)&&Fi(e,62,_i[255&e.token]);const c=oa(e,u,2,1,0,0,t,1,s,a,D);u=134217728^(134217728|u),67108991===e.token&&Fi(e,162);const l=wa(e,u,c,t,s,a,D);return e.assignable=2,Es(e,u,n,r,o,{type:"NewExpression",callee:l,arguments:67174411===e.token?fa(e,u,t):[]})}(e,u,i,a,D,c);case 134283389:return aa(e,u,a,D,c);case 131:return qa(e,u,a,D,c);case 86108:return function(e,u,t,n,r,o,i){let s=da(e,u,0);if(67108877===e.token)return ia(e,u,s,r,o,i);t&&Fi(e,137);return s=sa(e,u,n,r,o,i),e.assignable=2,na(e,u,s,n,0,r,o,i)}(e,u,n,i,a,D,c);case 8456258:if(16&u)return $a(e,u,1,a,D,c);default:if(Ps(u,e.token))return ka(e,u,a,D,c);Fi(e,28,_i[255&e.token])}}function ia(e,u,t,n,r,o){return 0==(2048&u)&&Fi(e,163),Zi(e,u),143495!==e.token&&"meta"!==e.tokenValue&&Fi(e,28,_i[255&e.token]),e.assignable=2,Es(e,u,n,r,o,{type:"MetaProperty",meta:t,property:da(e,u,0)})}function sa(e,u,t,n,r,o){cs(e,32768|u,67174411),14===e.token&&Fi(e,138);const i=Js(e,u,1,0,t,e.tokenPos,e.linePos,e.colPos);return cs(e,u,16),Es(e,u,n,r,o,{type:"ImportExpression",source:i})}function aa(e,u,t,n,r){const{tokenRaw:o,tokenValue:i}=e;return Zi(e,u),e.assignable=2,Es(e,u,t,n,r,512&u?{type:"Literal",value:i,bigint:o.slice(0,-1),raw:o}:{type:"Literal",value:i,bigint:o.slice(0,-1)})}function Da(e,u,t,n,r){e.assignable=2;const{tokenValue:o,tokenRaw:i,tokenPos:s,linePos:a,colPos:D}=e;cs(e,u,67174409);return Es(e,u,t,n,r,{type:"TemplateLiteral",expressions:[],quasis:[la(e,u,o,i,s,a,D,!0)]})}function ca(e,u){u=134217728^(134217728|u);const{tokenValue:t,tokenRaw:n,tokenPos:r,linePos:o,colPos:i}=e;cs(e,32768|u,67174408);const s=[la(e,u,t,n,r,o,i,!1)],a=[Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos)];for(1074790415!==e.token&&Fi(e,80);67174409!==(e.token=$i(e,u));){const{tokenValue:t,tokenRaw:n,tokenPos:r,linePos:o,colPos:i}=e;cs(e,32768|u,67174408),s.push(la(e,u,t,n,r,o,i,!1)),a.push(Ys(e,u,0,1,e.tokenPos,e.linePos,e.colPos)),1074790415!==e.token&&Fi(e,80)}{const{tokenValue:t,tokenRaw:n,tokenPos:r,linePos:o,colPos:i}=e;cs(e,u,67174409),s.push(la(e,u,t,n,r,o,i,!0))}return Es(e,u,r,o,i,{type:"TemplateLiteral",expressions:a,quasis:s})}function la(e,u,t,n,r,o,i,s){const a=Es(e,u,r,o,i,{type:"TemplateElement",value:{cooked:t,raw:n},tail:s}),D=s?1:2;return 2&u&&(a.start+=1,a.range[0]+=1,a.end-=D,a.range[1]-=D),4&u&&(a.loc.start.column+=1,a.loc.end.column-=D),a}function pa(e,u,t,n,r){cs(e,32768|(u=134217728^(134217728|u)),14);const o=Js(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);return e.assignable=1,Es(e,u,t,n,r,{type:"SpreadElement",argument:o})}function fa(e,u,t){Zi(e,32768|u);const n=[];if(16===e.token)return Zi(e,u),n;for(;16!==e.token&&(14===e.token?n.push(pa(e,u,e.tokenPos,e.linePos,e.colPos)):n.push(Js(e,u,1,0,t,e.tokenPos,e.linePos,e.colPos)),18===e.token)&&(Zi(e,32768|u),16!==e.token););return cs(e,u,16),n}function da(e,u,t){const{tokenValue:n,tokenPos:r,linePos:o,colPos:i}=e;return Zi(e,u),Es(e,u,r,o,i,268435456&u?{type:"Identifier",name:n,pattern:1===t}:{type:"Identifier",name:n})}function Fa(e,u){const{tokenValue:t,tokenRaw:n,tokenPos:r,linePos:o,colPos:i}=e;return 134283389===e.token?aa(e,u,r,o,i):(Zi(e,u),e.assignable=2,Es(e,u,r,o,i,512&u?{type:"Literal",value:t,raw:n}:{type:"Literal",value:t}))}function Ea(e,u,t,n,r,o,i,s,a,D){Zi(e,32768|u);const c=r?as(e,u,8457014):0;let l,p=null,f=t?{parent:void 0,type:2}:void 0;if(67174411===e.token)(1&o)<1&&Fi(e,37,"Function");else{const r=4&n&&((8192&u)<1||(2048&u)<1)?4:64;fs(e,u|(3072&u)<<11,e.token),t&&(4&r?Bs(e,u,t,e.tokenValue,r):ys(e,u,t,e.tokenValue,r,n),f=ms(f,256),o&&2&o&&ks(e,e.tokenValue)),l=e.token,143360&e.token?p=da(e,u,0):Fi(e,28,_i[255&e.token])}u=32243712^(32243712|u)|67108864|2*i+c<<21|(c?0:1073741824),t&&(f=ms(f,512));return Es(e,u,s,a,D,{type:"FunctionDeclaration",id:p,params:va(e,8388608|u,f,0,1),body:ua(e,143360^(143360|u),t?ms(f,128):f,8,l,t?f.scopeError:void 0),async:1===i,generator:1===c})}function Ca(e,u,t,n,r,o,i){Zi(e,32768|u);const s=as(e,u,8457014),a=2*t+s<<21;let D,c=null,l=64&u?{parent:void 0,type:2}:void 0;(176128&e.token)>0&&(fs(e,32243712^(32243712|u)|a,e.token),l&&(l=ms(l,256)),D=e.token,c=da(e,u,0)),u=32243712^(32243712|u)|67108864|a|(s?0:1073741824),l&&(l=ms(l,512));const p=va(e,8388608|u,l,n,1),f=ua(e,-134377473&u,l?ms(l,128):l,0,D,void 0);return e.assignable=2,Es(e,u,r,o,i,{type:"FunctionExpression",id:c,params:p,body:f,async:1===t,generator:1===s})}function Aa(e,u,t,n,r,o,i,s,a,D,c){Zi(e,32768|u);const l=[];let p=0;for(u=134217728^(134217728|u);20!==e.token;)if(Ds(e,32768|u,18))l.push(null);else{let n;const{token:a,tokenPos:D,linePos:c,colPos:f,tokenValue:d}=e;if(143360&a)if(n=oa(e,u,i,0,1,0,r,1,D,c,f),1077936157===e.token){2&e.assignable&&Fi(e,24),Zi(e,32768|u),t&&hs(e,u,t,d,i,s);const a=Js(e,u,1,1,r,e.tokenPos,e.linePos,e.colPos);n=Es(e,u,D,c,f,o?{type:"AssignmentPattern",left:n,right:a}:{type:"AssignmentExpression",operator:"=",left:n,right:a}),p|=256&e.destructible?256:0|128&e.destructible?128:0}else 18===e.token||20===e.token?(2&e.assignable?p|=16:t&&hs(e,u,t,d,i,s),p|=256&e.destructible?256:0|128&e.destructible?128:0):(p|=1&i?32:(2&i)<1?16:0,n=na(e,u,n,r,0,D,c,f),18!==e.token&&20!==e.token?(1077936157!==e.token&&(p|=16),n=Ks(e,u,r,o,D,c,f,n)):1077936157!==e.token&&(p|=2&e.assignable?16:32));else 2097152&a?(n=2162700===e.token?ya(e,u,t,0,r,o,i,s,D,c,f):Aa(e,u,t,0,r,o,i,s,D,c,f),p|=e.destructible,e.assignable=16&e.destructible?2:1,18===e.token||20===e.token?2&e.assignable&&(p|=16):8&e.destructible?Fi(e,68):(n=na(e,u,n,r,0,D,c,f),p=2&e.assignable?16:0,18!==e.token&&20!==e.token?n=Ks(e,u,r,o,D,c,f,n):1077936157!==e.token&&(p|=2&e.assignable?16:32))):14===a?(n=ma(e,u,t,20,i,s,0,r,o,D,c,f),p|=e.destructible,18!==e.token&&20!==e.token&&Fi(e,28,_i[255&e.token])):(n=ta(e,u,1,0,1,D,c,f),18!==e.token&&20!==e.token?(n=Ks(e,u,r,o,D,c,f,n),(3&i)<1&&67174411===a&&(p|=16)):2&e.assignable?p|=16:67174411===a&&(p|=1&e.assignable&&3&i?32:16));if(l.push(n),!Ds(e,32768|u,18))break;if(20===e.token)break}cs(e,u,20);const f=Es(e,u,a,D,c,{type:o?"ArrayPattern":"ArrayExpression",elements:l});return!n&&4194304&e.token?ga(e,u,p,r,o,a,D,c,f):(e.destructible=p,f)}function ga(e,u,t,n,r,o,i,s,a){1077936157!==e.token&&Fi(e,24),Zi(e,32768|u),16&t&&Fi(e,24),r||ls(e,a);const{tokenPos:D,linePos:c,colPos:l}=e,p=Js(e,u,1,1,n,D,c,l);return e.destructible=72^(72|t)|(128&e.destructible?128:0)|(256&e.destructible?256:0),Es(e,u,o,i,s,r?{type:"AssignmentPattern",left:a,right:p}:{type:"AssignmentExpression",left:a,operator:"=",right:p})}function ma(e,u,t,n,r,o,i,s,a,D,c,l){Zi(e,32768|u);let p=null,f=0,{token:d,tokenValue:F,tokenPos:E,linePos:C,colPos:A}=e;if(143360&d)e.assignable=1,p=oa(e,u,r,0,1,0,s,1,E,C,A),d=e.token,p=na(e,u,p,s,0,E,C,A),18!==e.token&&e.token!==n&&(2&e.assignable&&1077936157===e.token&&Fi(e,68),f|=16,p=Ks(e,u,s,a,E,C,A,p)),2&e.assignable?f|=16:d===n||18===d?t&&hs(e,u,t,F,r,o):f|=32,f|=128&e.destructible?128:0;else if(d===n)Fi(e,39);else{if(!(2097152&d)){f|=32,p=ta(e,u,1,s,1,e.tokenPos,e.linePos,e.colPos);const{token:t,tokenPos:r,linePos:o,colPos:i}=e;return 1077936157===t&&t!==n&&18!==t?(2&e.assignable&&Fi(e,24),p=Ks(e,u,s,a,r,o,i,p),f|=16):(18===t?f|=16:t!==n&&(p=Ks(e,u,s,a,r,o,i,p)),f|=1&e.assignable?32:16),e.destructible=f,e.token!==n&&18!==e.token&&Fi(e,155),Es(e,u,D,c,l,{type:a?"RestElement":"SpreadElement",argument:p})}p=2162700===e.token?ya(e,u,t,1,s,a,r,o,E,C,A):Aa(e,u,t,1,s,a,r,o,E,C,A),d=e.token,1077936157!==d&&d!==n&&18!==d?(8&e.destructible&&Fi(e,68),p=na(e,u,p,s,0,E,C,A),f|=2&e.assignable?16:0,4194304==(4194304&e.token)?(1077936157!==e.token&&(f|=16),p=Ks(e,u,s,a,E,C,A,p)):(8454144==(8454144&e.token)&&(p=ea(e,u,1,E,C,A,4,d,p)),Ds(e,32768|u,22)&&(p=Qs(e,u,p,E,C,A)),f|=2&e.assignable?16:32)):f|=1074790415===n&&1077936157!==d?16:e.destructible}if(e.token!==n)if(1&r&&(f|=i?16:32),Ds(e,32768|u,1077936157)){16&f&&Fi(e,24),ls(e,p);const t=Js(e,u,1,1,s,e.tokenPos,e.linePos,e.colPos);p=Es(e,u,E,C,A,a?{type:"AssignmentPattern",left:p,right:t}:{type:"AssignmentExpression",left:p,operator:"=",right:t}),f=16}else f|=16;return e.destructible=f,Es(e,u,D,c,l,{type:a?"RestElement":"SpreadElement",argument:p})}function ha(e,u,t,n,r,o,i){const s=(64&t)<1?31981568:14680064;let a=64&(u=(u|s)^s|(88&t)<<18|100925440)?ms({parent:void 0,type:2},512):void 0;const D=function(e,u,t,n,r,o){cs(e,u,67174411);const i=[];if(e.flags=128^(128|e.flags),16===e.token)return 512&n&&Fi(e,35,"Setter","one",""),Zi(e,u),i;256&n&&Fi(e,35,"Getter","no","s");512&n&&14===e.token&&Fi(e,36);u=134217728^(134217728|u);let s=0,a=0;for(;18!==e.token;){let D=null;const{tokenPos:c,linePos:l,colPos:p}=e;if(143360&e.token?((1024&u)<1&&(36864==(36864&e.token)&&(e.flags|=256),537079808==(537079808&e.token)&&(e.flags|=512)),D=Ma(e,u,t,1|n,0,c,l,p)):(2162700===e.token?D=ya(e,u,t,1,o,1,r,0,c,l,p):69271571===e.token?D=Aa(e,u,t,1,o,1,r,0,c,l,p):14===e.token&&(D=ma(e,u,t,16,r,0,0,o,1,c,l,p)),a=1,48&e.destructible&&Fi(e,47)),1077936157===e.token){Zi(e,32768|u),a=1;D=Es(e,u,c,l,p,{type:"AssignmentPattern",left:D,right:Js(e,u,1,1,0,e.tokenPos,e.linePos,e.colPos)})}if(s++,i.push(D),!Ds(e,u,18))break;if(16===e.token)break}512&n&&1!==s&&Fi(e,35,"Setter","one","");t&&void 0!==t.scopeError&&Ei(t.scopeError);a&&(e.flags|=128);return cs(e,u,16),i}(e,8388608|u,a,t,1,n);a&&(a=ms(a,128));return Es(e,u,r,o,i,{type:"FunctionExpression",params:D,body:ua(e,-134230017&u,a,0,void 0,void 0),async:(16&t)>0,generator:(8&t)>0,id:null})}function ya(e,u,t,n,r,o,i,s,a,D,c){Zi(e,u);const l=[];let p=0,f=0;for(u=134217728^(134217728|u);1074790415!==e.token;){const{token:n,tokenValue:a,linePos:D,colPos:c,tokenPos:d}=e;if(14===n)l.push(ma(e,u,t,1074790415,i,s,0,r,o,d,D,c));else{let F,E=0,C=null;const A=e.token;if(143360&e.token||121===e.token)if(C=da(e,u,0),18===e.token||1074790415===e.token||1077936157===e.token)if(E|=4,1024&u&&537079808==(537079808&n)?p|=16:ps(e,u,i,n,0),t&&hs(e,u,t,a,i,s),Ds(e,32768|u,1077936157)){p|=8;const t=Js(e,u,1,1,r,e.tokenPos,e.linePos,e.colPos);p|=256&e.destructible?256:0|128&e.destructible?128:0,F=Es(e,u,d,D,c,{type:"AssignmentPattern",left:-2147483648&u?Object.assign({},C):C,right:t})}else p|=(209008===n?128:0)|(121===n?16:0),F=-2147483648&u?Object.assign({},C):C;else if(Ds(e,32768|u,21)){const{tokenPos:D,linePos:c,colPos:l}=e;if("__proto__"===a&&f++,143360&e.token){const n=e.token,a=e.tokenValue;p|=121===A?16:0,F=oa(e,u,i,0,1,0,r,1,D,c,l);const{token:f}=e;F=na(e,u,F,r,0,D,c,l),18===e.token||1074790415===e.token?1077936157===f||1074790415===f||18===f?(p|=128&e.destructible?128:0,2&e.assignable?p|=16:t&&143360==(143360&n)&&hs(e,u,t,a,i,s)):p|=1&e.assignable?32:16:4194304==(4194304&e.token)?(2&e.assignable?p|=16:1077936157!==f?p|=32:t&&hs(e,u,t,a,i,s),F=Ks(e,u,r,o,D,c,l,F)):(p|=16,8454144==(8454144&e.token)&&(F=ea(e,u,1,D,c,l,4,f,F)),Ds(e,32768|u,22)&&(F=Qs(e,u,F,D,c,l)))}else 2097152==(2097152&e.token)?(F=69271571===e.token?Aa(e,u,t,0,r,o,i,s,D,c,l):ya(e,u,t,0,r,o,i,s,D,c,l),p=e.destructible,e.assignable=16&p?2:1,18===e.token||1074790415===e.token?2&e.assignable&&(p|=16):8&e.destructible?Fi(e,68):(F=na(e,u,F,r,0,D,c,l),p=2&e.assignable?16:0,4194304==(4194304&e.token)?F=Zs(e,u,r,o,D,c,l,F):(8454144==(8454144&e.token)&&(F=ea(e,u,1,D,c,l,4,n,F)),Ds(e,32768|u,22)&&(F=Qs(e,u,F,D,c,l)),p|=2&e.assignable?16:32))):(F=ta(e,u,1,r,1,D,c,l),p|=1&e.assignable?32:16,18===e.token||1074790415===e.token?2&e.assignable&&(p|=16):(F=na(e,u,F,r,0,D,c,l),p=2&e.assignable?16:0,18!==e.token&&1074790415!==n&&(1077936157!==e.token&&(p|=16),F=Ks(e,u,r,o,D,c,l,F))))}else 69271571===e.token?(p|=16,209007===n&&(E|=16),E|=2|(12402===n?256:12403===n?512:1),C=Ba(e,u,r),p|=e.assignable,F=ha(e,u,E,r,e.tokenPos,e.linePos,e.colPos)):143360&e.token?(p|=16,121===n&&Fi(e,92),209007===n&&(1&e.flags&&Fi(e,128),E|=16),C=da(e,u,0),E|=12402===n?256:12403===n?512:1,F=ha(e,u,E,r,e.tokenPos,e.linePos,e.colPos)):67174411===e.token?(p|=16,E|=1,F=ha(e,u,E,r,e.tokenPos,e.linePos,e.colPos)):8457014===e.token?(p|=16,12402===n||12403===n?Fi(e,40):143483===n&&Fi(e,92),Zi(e,u),E|=9|(209007===n?16:0),143360&e.token?C=da(e,u,0):134217728==(134217728&e.token)?C=Fa(e,u):69271571===e.token?(E|=2,C=Ba(e,u,r),p|=e.assignable):Fi(e,28,_i[255&e.token]),F=ha(e,u,E,r,e.tokenPos,e.linePos,e.colPos)):134217728==(134217728&e.token)?(209007===n&&(E|=16),E|=12402===n?256:12403===n?512:1,p|=16,C=Fa(e,u),F=ha(e,u,E,r,e.tokenPos,e.linePos,e.colPos)):Fi(e,129);else if(134217728==(134217728&e.token))if(C=Fa(e,u),21===e.token){cs(e,32768|u,21);const{tokenPos:D,linePos:c,colPos:l}=e;if("__proto__"===a&&f++,143360&e.token){F=oa(e,u,i,0,1,0,r,1,D,c,l);const{token:n,tokenValue:a}=e;F=na(e,u,F,r,0,D,c,l),18===e.token||1074790415===e.token?1077936157===n||1074790415===n||18===n?2&e.assignable?p|=16:t&&hs(e,u,t,a,i,s):p|=1&e.assignable?32:16:1077936157===e.token?(2&e.assignable&&(p|=16),F=Ks(e,u,r,o,D,c,l,F)):(p|=16,F=Ks(e,u,r,o,D,c,l,F))}else 2097152==(2097152&e.token)?(F=69271571===e.token?Aa(e,u,t,0,r,o,i,s,D,c,l):ya(e,u,t,0,r,o,i,s,D,c,l),p=e.destructible,e.assignable=16&p?2:1,18===e.token||1074790415===e.token?2&e.assignable&&(p|=16):8!=(8&e.destructible)&&(F=na(e,u,F,r,0,D,c,l),p=2&e.assignable?16:0,4194304==(4194304&e.token)?F=Zs(e,u,r,o,D,c,l,F):(8454144==(8454144&e.token)&&(F=ea(e,u,1,D,c,l,4,n,F)),Ds(e,32768|u,22)&&(F=Qs(e,u,F,D,c,l)),p|=2&e.assignable?16:32))):(F=ta(e,u,1,0,1,D,c,l),p|=1&e.assignable?32:16,18===e.token||1074790415===e.token?2&e.assignable&&(p|=16):(F=na(e,u,F,r,0,D,c,l),p=1&e.assignable?0:16,18!==e.token&&1074790415!==e.token&&(1077936157!==e.token&&(p|=16),F=Ks(e,u,r,o,D,c,l,F))))}else 67174411===e.token?(E|=1,F=ha(e,u,E,r,e.tokenPos,e.linePos,e.colPos),p=16|e.assignable):Fi(e,130);else if(69271571===e.token)if(C=Ba(e,u,r),p|=256&e.destructible?256:0,E|=2,21===e.token){Zi(e,32768|u);const{tokenPos:a,linePos:D,colPos:c,tokenValue:l,token:f}=e;if(143360&e.token){F=oa(e,u,i,0,1,0,r,1,a,D,c);const{token:n}=e;F=na(e,u,F,r,0,a,D,c),4194304==(4194304&e.token)?(p|=2&e.assignable?16:1077936157===n?0:32,F=Zs(e,u,r,o,a,D,c,F)):18===e.token||1074790415===e.token?1077936157===n||1074790415===n||18===n?2&e.assignable?p|=16:t&&143360==(143360&f)&&hs(e,u,t,l,i,s):p|=1&e.assignable?32:16:(p|=16,F=Ks(e,u,r,o,a,D,c,F))}else 2097152==(2097152&e.token)?(F=69271571===e.token?Aa(e,u,t,0,r,o,i,s,a,D,c):ya(e,u,t,0,r,o,i,s,a,D,c),p=e.destructible,e.assignable=16&p?2:1,18===e.token||1074790415===e.token?2&e.assignable&&(p|=16):8&p?Fi(e,59):(F=na(e,u,F,r,0,a,D,c),p=2&e.assignable?16|p:0,4194304==(4194304&e.token)?(1077936157!==e.token&&(p|=16),F=Zs(e,u,r,o,a,D,c,F)):(8454144==(8454144&e.token)&&(F=ea(e,u,1,a,D,c,4,n,F)),Ds(e,32768|u,22)&&(F=Qs(e,u,F,a,D,c)),p|=2&e.assignable?16:32))):(F=ta(e,u,1,0,1,a,D,c),p|=1&e.assignable?32:16,18===e.token||1074790415===e.token?2&e.assignable&&(p|=16):(F=na(e,u,F,r,0,a,D,c),p=1&e.assignable?0:16,18!==e.token&&1074790415!==e.token&&(1077936157!==e.token&&(p|=16),F=Ks(e,u,r,o,a,D,c,F))))}else 67174411===e.token?(E|=1,F=ha(e,u,E,r,e.tokenPos,D,c),p=16):Fi(e,41);else if(8457014===n)if(cs(e,32768|u,8457014),E|=8,143360&e.token){const{token:t,line:n,index:o}=e;C=da(e,u,0),E|=1,67174411===e.token?(p|=16,F=ha(e,u,E,r,e.tokenPos,e.linePos,e.colPos)):Ci(o,n,o,209007===t?43:12402===t||12403===e.token?42:44,_i[255&t])}else 134217728==(134217728&e.token)?(p|=16,C=Fa(e,u),E|=1,F=ha(e,u,E,r,d,D,c)):69271571===e.token?(p|=16,E|=3,C=Ba(e,u,r),F=ha(e,u,E,r,e.tokenPos,e.linePos,e.colPos)):Fi(e,122);else Fi(e,28,_i[255&n]);p|=128&e.destructible?128:0,e.destructible=p,l.push(Es(e,u,d,D,c,{type:"Property",key:C,value:F,kind:768&E?512&E?"set":"get":"init",computed:(2&E)>0,method:(1&E)>0,shorthand:(4&E)>0}))}if(p|=e.destructible,18!==e.token)break;Zi(e,u)}cs(e,u,1074790415),f>1&&(p|=64);const d=Es(e,u,a,D,c,{type:o?"ObjectPattern":"ObjectExpression",properties:l});return!n&&4194304&e.token?ga(e,u,p,r,o,a,D,c,d):(e.destructible=p,d)}function Ba(e,u,t){Zi(e,32768|u);const n=Js(e,134217728^(134217728|u),1,0,t,e.tokenPos,e.linePos,e.colPos);return cs(e,u,20),n}function ka(e,u,t,n,r){const{tokenValue:o}=e,i=da(e,u,0);if(e.assignable=1,10===e.token){let s;return 64&u&&(s=As(e,u,o)),e.flags=128^(128|e.flags),xa(e,u,s,[i],0,t,n,r)}return i}function ba(e,u,t,n,r,o,i,s,a,D){o||Fi(e,54),r&&Fi(e,48),e.flags&=-129;return xa(e,u,64&u?As(e,u,t):void 0,[n],i,s,a,D)}function Pa(e,u,t,n,r,o,i,s,a){r||Fi(e,54);for(let u=0;u<n.length;++u)ls(e,n[u]);return xa(e,u,t,n,o,i,s,a)}function xa(e,u,t,n,r,o,i,s){1&e.flags&&Fi(e,45),cs(e,32768|u,10),u=15728640^(15728640|u)|r<<22;const a=2162700!==e.token;let D;if(t&&void 0!==t.scopeError&&Ei(t.scopeError),a)D=Js(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);else{switch(t&&(t=ms(t,128)),D=ua(e,134246400^(134246400|u),t,16,void 0,void 0),e.token){case 69271571:(1&e.flags)<1&&Fi(e,112);break;case 67108877:case 67174409:case 22:Fi(e,113);case 67174411:(1&e.flags)<1&&Fi(e,112),e.flags|=1024}8454144==(8454144&e.token)&&(1&e.flags)<1&&Fi(e,28,_i[255&e.token]),33619968==(33619968&e.token)&&Fi(e,121)}return e.assignable=2,Es(e,u,o,i,s,{type:"ArrowFunctionExpression",params:n,body:D,async:1===r,expression:a})}function va(e,u,t,n,r){cs(e,u,67174411),e.flags=128^(128|e.flags);const o=[];if(Ds(e,u,16))return o;u=134217728^(134217728|u);let i=0;for(;18!==e.token;){let s;const{tokenPos:a,linePos:D,colPos:c}=e;if(143360&e.token?((1024&u)<1&&(36864==(36864&e.token)&&(e.flags|=256),537079808==(537079808&e.token)&&(e.flags|=512)),s=Ma(e,u,t,1|r,0,a,D,c)):(2162700===e.token?s=ya(e,u,t,1,n,1,r,0,a,D,c):69271571===e.token?s=Aa(e,u,t,1,n,1,r,0,a,D,c):14===e.token?s=ma(e,u,t,16,r,0,0,n,1,a,D,c):Fi(e,28,_i[255&e.token]),i=1,48&e.destructible&&Fi(e,47)),1077936157===e.token){Zi(e,32768|u),i=1;s=Es(e,u,a,D,c,{type:"AssignmentPattern",left:s,right:Js(e,u,1,1,n,e.tokenPos,e.linePos,e.colPos)})}if(o.push(s),!Ds(e,u,18))break;if(16===e.token)break}return i&&(e.flags|=128),t&&(i||1024&u)&&void 0!==t.scopeError&&Ei(t.scopeError),cs(e,u,16),o}function wa(e,u,t,n,r,o,i){const{token:s}=e;if(67108864&s){if(67108877===s){Zi(e,1073741824|u),e.assignable=1;return wa(e,u,Es(e,u,r,o,i,{type:"MemberExpression",object:t,computed:!1,property:ra(e,u)}),0,r,o,i)}if(69271571===s){Zi(e,32768|u);const{tokenPos:s,linePos:a,colPos:D}=e,c=Ys(e,u,n,1,s,a,D);return cs(e,u,20),e.assignable=1,wa(e,u,Es(e,u,r,o,i,{type:"MemberExpression",object:t,computed:!0,property:c}),0,r,o,i)}if(67174408===s||67174409===s)return e.assignable=2,wa(e,u,Es(e,u,r,o,i,{type:"TaggedTemplateExpression",tag:t,quasi:67174408===e.token?ca(e,65536|u):Da(e,u,e.tokenPos,e.linePos,e.colPos)}),0,r,o,i)}return t}function Sa(e,u,t,n,r,o){return 209008===e.token&&Fi(e,29),2098176&u&&241773===e.token&&Fi(e,30),537079808==(537079808&e.token)&&(e.flags|=512),ba(e,u,e.tokenValue,da(e,u,0),0,t,1,n,r,o)}function Ta(e,u,t,n,r,o,i,s,a,D){Zi(e,32768|u);const c=64&u?ms({parent:void 0,type:2},1024):void 0;if(Ds(e,u=134217728^(134217728|u),16))return 10===e.token?(1&i&&Fi(e,45),Pa(e,u,c,[],n,1,s,a,D)):Es(e,u,s,a,D,{type:"CallExpression",callee:t,arguments:[]});let l=0,p=null,f=0;e.destructible=384^(384|e.destructible);const d=[];for(;16!==e.token;){const{token:n,tokenPos:i,linePos:F,colPos:E}=e;if(143360&n)c&&ys(e,u,c,e.tokenValue,r,0),p=oa(e,u,r,0,1,0,1,1,i,F,E),16===e.token||18===e.token?2&e.assignable?(l|=16,f=1):537079808==(537079808&n)?e.flags|=512:36864==(36864&n)&&(e.flags|=256):(1077936157===e.token?f=1:l|=16,p=na(e,u,p,1,0,i,F,E),16!==e.token&&18!==e.token&&(p=Ks(e,u,1,0,i,F,E,p)));else if(2097152&n)p=2162700===n?ya(e,u,c,0,1,0,r,o,i,F,E):Aa(e,u,c,0,1,0,r,o,i,F,E),l|=e.destructible,f=1,16!==e.token&&18!==e.token&&(8&l&&Fi(e,118),p=na(e,u,p,0,0,i,F,E),l|=16,8454144==(8454144&e.token)&&(p=ea(e,u,1,s,a,D,4,n,p)),Ds(e,32768|u,22)&&(p=Qs(e,u,p,s,a,D)));else{if(14!==n){for(p=Js(e,u,1,0,0,i,F,E),l=e.assignable,d.push(p);Ds(e,32768|u,18);)d.push(Js(e,u,1,0,0,i,F,E));return l|=e.assignable,cs(e,u,16),e.destructible=16|l,e.assignable=2,Es(e,u,s,a,D,{type:"CallExpression",callee:t,arguments:d})}p=ma(e,u,c,16,r,o,1,1,0,i,F,E),l|=(16===e.token?0:16)|e.destructible,f=1}if(d.push(p),!Ds(e,32768|u,18))break}return cs(e,u,16),l|=256&e.destructible?256:0|128&e.destructible?128:0,10===e.token?(48&l&&Fi(e,25),(1&e.flags||1&i)&&Fi(e,45),128&l&&Fi(e,29),2098176&u&&256&l&&Fi(e,30),f&&(e.flags|=128),Pa(e,u,c,d,n,1,s,a,D)):(8&l&&Fi(e,59),e.assignable=2,Es(e,u,s,a,D,{type:"CallExpression",callee:t,arguments:d}))}function Na(e,u,t,n,r,o,i){let s=Ia(e,u=16777216^(16778240|u));s.length&&(r=e.tokenPos,o=e.linePos,i=e.colPos),e.leadingDecorators.length&&(e.leadingDecorators.push(...s),s=e.leadingDecorators,e.leadingDecorators=[]),Zi(e,u);let a=null,D=null;const{tokenValue:c}=e;4096&e.token&&20567!==e.token?(ds(e,u,e.token)&&Fi(e,114),537079808==(537079808&e.token)&&Fi(e,115),t&&(ys(e,u,t,c,32,0),n&&2&n&&ks(e,c)),a=da(e,u,0)):(1&n)<1&&Fi(e,37,"Class");let l=u;Ds(e,32768|u,20567)?(D=ta(e,u,0,0,0,e.tokenPos,e.linePos,e.colPos),l|=524288):l=524288^(524288|l);const p=Oa(e,l,u,t,2,8,0);return Es(e,u,r,o,i,1&u?{type:"ClassDeclaration",id:a,superClass:D,decorators:s,body:p}:{type:"ClassDeclaration",id:a,superClass:D,body:p})}function Ia(e,u){const t=[];if(1&u)for(;133===e.token;)t.push(La(e,u,e.tokenPos,e.linePos,e.colPos));return t}function La(e,u,t,n,r){Zi(e,32768|u);let o=oa(e,u,2,0,1,0,0,1,t,n,r);return o=na(e,u,o,0,0,t,n,r),Es(e,u,t,n,r,{type:"Decorator",expression:o})}function Oa(e,u,t,n,r,o,i){const{tokenPos:s,linePos:a,colPos:D}=e;cs(e,32768|u,2162700),u=134217728^(134217728|u),e.flags=32^(32|e.flags);const c=[];let l;for(;1074790415!==e.token;){let o=0;l=Ia(e,u),o=l.length,o>0&&"constructor"===e.tokenValue&&Fi(e,106),1074790415===e.token&&Fi(e,105),Ds(e,u,1074790417)?o>0&&Fi(e,116):c.push(Ra(e,u,n,t,r,l,0,i,e.tokenPos,e.linePos,e.colPos))}return cs(e,8&o?32768|u:u,1074790415),Es(e,u,s,a,D,{type:"ClassBody",body:c})}function Ra(e,u,t,n,r,o,i,s,a,D,c){let l=i?32:0,p=null;const{token:f,tokenPos:d,linePos:F,colPos:E}=e;if(176128&f)switch(p=da(e,u,0),f){case 36972:if(!i&&67174411!==e.token)return Ra(e,u,t,n,r,o,1,s,a,D,c);break;case 209007:if(67174411!==e.token&&(1&e.flags)<1){if(1&u&&1073741824==(1073741824&e.token))return ja(e,u,p,l,o,d,F,E);l|=16|(as(e,u,8457014)?8:0)}break;case 12402:if(67174411!==e.token){if(1&u&&1073741824==(1073741824&e.token))return ja(e,u,p,l,o,d,F,E);l|=256}break;case 12403:if(67174411!==e.token){if(1&u&&1073741824==(1073741824&e.token))return ja(e,u,p,l,o,d,F,E);l|=512}}else 69271571===f?(l|=2,p=Ba(e,n,s)):134217728==(134217728&f)?p=Fa(e,u):8457014===f?(l|=8,Zi(e,u)):1&u&&131===e.token?(l|=4096,p=qa(e,u,d,F,E),u|=16384):1&u&&1073741824==(1073741824&e.token)?(l|=128,u|=16384):122===f?(p=da(e,u,0),67174411!==e.token&&Fi(e,28,_i[255&e.token])):Fi(e,28,_i[255&e.token]);if(792&l&&(143360&e.token?p=da(e,u,0):134217728==(134217728&e.token)?p=Fa(e,u):69271571===e.token?(l|=2,p=Ba(e,u,0)):122===e.token?p=da(e,u,0):1&u&&131===e.token?(l|=4096,p=qa(e,u,d,F,E)):Fi(e,131)),(2&l)<1&&("constructor"===e.tokenValue?(1073741824==(1073741824&e.token)?Fi(e,125):(32&l)<1&&67174411===e.token&&(920&l?Fi(e,50,"accessor"):(524288&u)<1&&(32&e.flags?Fi(e,51):e.flags|=32)),l|=64):(4096&l)<1&&824&l&&"prototype"===e.tokenValue&&Fi(e,49)),1&u&&67174411!==e.token)return ja(e,u,p,l,o,d,F,E);const C=ha(e,u,l,s,e.tokenPos,e.linePos,e.colPos);return Es(e,u,a,D,c,1&u?{type:"MethodDefinition",kind:(32&l)<1&&64&l?"constructor":256&l?"get":512&l?"set":"method",static:(32&l)>0,computed:(2&l)>0,key:p,decorators:o,value:C}:{type:"MethodDefinition",kind:(32&l)<1&&64&l?"constructor":256&l?"get":512&l?"set":"method",static:(32&l)>0,computed:(2&l)>0,key:p,value:C})}function qa(e,u,t,n,r){Zi(e,u);const{tokenValue:o}=e;return"constructor"===o&&Fi(e,124),Zi(e,u),Es(e,u,t,n,r,{type:"PrivateIdentifier",name:o})}function ja(e,u,t,n,r,o,i,s){let a=null;if(8&n&&Fi(e,0),1077936157===e.token){Zi(e,32768|u);const{tokenPos:t,linePos:n,colPos:r}=e;537079928===e.token&&Fi(e,115),a=oa(e,16384|u,2,0,1,0,0,1,t,n,r),1073741824!=(1073741824&e.token)&&(a=na(e,16384|u,a,0,0,t,n,r),a=Ks(e,16384|u,0,0,t,n,r,a),18===e.token&&(a=Ws(e,u,0,o,i,s,a)))}return Es(e,u,o,i,s,{type:"PropertyDefinition",key:t,value:a,static:(32&n)>0,computed:(2&n)>0,decorators:r})}function Va(e,u,t,n,r,o,i,s){if(143360&e.token)return Ma(e,u,t,n,r,o,i,s);2097152!=(2097152&e.token)&&Fi(e,28,_i[255&e.token]);const a=69271571===e.token?Aa(e,u,t,1,0,1,n,r,o,i,s):ya(e,u,t,1,0,1,n,r,o,i,s);return 16&e.destructible&&Fi(e,47),32&e.destructible&&Fi(e,47),a}function Ma(e,u,t,n,r,o,i,s){const{tokenValue:a,token:D}=e;return 1024&u&&(537079808==(537079808&D)?Fi(e,115):36864==(36864&D)&&Fi(e,114)),20480==(20480&D)&&Fi(e,99),2099200&u&&241773===D&&Fi(e,30),241739===D&&24&n&&Fi(e,97),4196352&u&&209008===D&&Fi(e,95),Zi(e,u),t&&hs(e,u,t,a,n,r),Es(e,u,o,i,s,{type:"Identifier",name:a})}function $a(e,u,t,n,r,o){if(Zi(e,u),8456259===e.token)return Es(e,u,n,r,o,{type:"JSXFragment",openingFragment:Ua(e,u,n,r,o),children:_a(e,u),closingFragment:Ga(e,u,t,e.tokenPos,e.linePos,e.colPos)});let i=null,s=[];const a=function(e,u,t,n,r,o){143360!=(143360&e.token)&&4096!=(4096&e.token)&&Fi(e,0);const i=Ha(e,u,e.tokenPos,e.linePos,e.colPos),s=function(e,u){const t=[];for(;8457016!==e.token&&8456259!==e.token&&1048576!==e.token;)t.push(Ja(e,u,e.tokenPos,e.linePos,e.colPos));return t}(e,u),a=8457016===e.token;8456259===e.token?rs(e,u):(cs(e,u,8457016),t?cs(e,u,8456259):rs(e,u));return Es(e,u,n,r,o,{type:"JSXOpeningElement",name:i,attributes:s,selfClosing:a})}(e,u,t,n,r,o);if(!a.selfClosing){s=_a(e,u),i=function(e,u,t,n,r,o){cs(e,u,25);const i=Ha(e,u,e.tokenPos,e.linePos,e.colPos);t?cs(e,u,8456259):e.token=rs(e,u);return Es(e,u,n,r,o,{type:"JSXClosingElement",name:i})}(e,u,t,e.tokenPos,e.linePos,e.colPos);const n=Cs(i.name);Cs(a.name)!==n&&Fi(e,149,n)}return Es(e,u,n,r,o,{type:"JSXElement",children:s,openingElement:a,closingElement:i})}function Ua(e,u,t,n,r){return rs(e,u),Es(e,u,t,n,r,{type:"JSXOpeningFragment"})}function Ga(e,u,t,n,r,o){return cs(e,u,25),cs(e,u,8456259),Es(e,u,n,r,o,{type:"JSXClosingFragment"})}function _a(e,u){const t=[];for(;25!==e.token;)e.index=e.tokenPos=e.startPos,e.column=e.colPos=e.startColumn,e.line=e.linePos=e.startLine,rs(e,u),t.push(Xa(e,u,e.tokenPos,e.linePos,e.colPos));return t}function Xa(e,u,t,n,r){return 138===e.token?function(e,u,t,n,r){rs(e,u);const o={type:"JSXText",value:e.tokenValue};512&u&&(o.raw=e.tokenRaw);return Es(e,u,t,n,r,o)}(e,u,t,n,r):2162700===e.token?Ya(e,u,0,0,t,n,r):8456258===e.token?$a(e,u,0,t,n,r):void Fi(e,0)}function Ha(e,u,t,n,r){os(e);let o=Ka(e,u,t,n,r);if(21===e.token)return Wa(e,u,o,t,n,r);for(;Ds(e,u,67108877);)os(e),o=za(e,u,o,t,n,r);return o}function za(e,u,t,n,r,o){return Es(e,u,n,r,o,{type:"JSXMemberExpression",object:t,property:Ka(e,u,e.tokenPos,e.linePos,e.colPos)})}function Ja(e,u,t,n,r){if(2162700===e.token)return function(e,u,t,n,r){Zi(e,u),cs(e,u,14);const o=Js(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);return cs(e,u,1074790415),Es(e,u,t,n,r,{type:"JSXSpreadAttribute",argument:o})}(e,u,t,n,r);os(e);let o=null,i=Ka(e,u,t,n,r);if(21===e.token&&(i=Wa(e,u,i,t,n,r)),1077936157===e.token){const t=ns(e,u),{tokenPos:n,linePos:r,colPos:i}=e;switch(t){case 134283267:o=Fa(e,u);break;case 8456258:o=$a(e,u,1,n,r,i);break;case 2162700:o=Ya(e,u,1,1,n,r,i);break;default:Fi(e,148)}}return Es(e,u,t,n,r,{type:"JSXAttribute",value:o,name:i})}function Wa(e,u,t,n,r,o){cs(e,u,21);return Es(e,u,n,r,o,{type:"JSXNamespacedName",namespace:t,name:Ka(e,u,e.tokenPos,e.linePos,e.colPos)})}function Ya(e,u,t,n,r,o,i){Zi(e,u);const{tokenPos:s,linePos:a,colPos:D}=e;if(14===e.token)return function(e,u,t,n,r){cs(e,u,14);const o=Js(e,u,1,0,0,e.tokenPos,e.linePos,e.colPos);return cs(e,u,1074790415),Es(e,u,t,n,r,{type:"JSXSpreadChild",expression:o})}(e,u,s,a,D);let c=null;return 1074790415===e.token?(n&&Fi(e,151),c=function(e,u,t,n,r){return e.startPos=e.tokenPos,e.startLine=e.linePos,e.startColumn=e.colPos,Es(e,u,t,n,r,{type:"JSXEmptyExpression"})}(e,u,e.startPos,e.startLine,e.startColumn)):c=Js(e,u,1,0,0,s,a,D),t?cs(e,u,1074790415):rs(e,u),Es(e,u,r,o,i,{type:"JSXExpressionContainer",expression:c})}function Ka(e,u,t,n,r){const{tokenValue:o}=e;return Zi(e,u),Es(e,u,t,n,r,{type:"JSXIdentifier",name:o})}var Za=Object.freeze({__proto__:null}),Qa=function(e,u){return vs(e,u,0)},eD=function(e,u){return vs(e,u,3072)},uD=function(e,u){return vs(e,u,0)},tD=Object.defineProperty({ESTree:Za,parse:Qa,parseModule:eD,parseScript:uD,version:"4.1.5"},"__esModule",{value:!0});const nD={module:!0,next:!0,ranges:!0,webcompat:!0,loc:!0,raw:!0,directives:!0,globalReturn:!0,impliedStrict:!1,preserveParens:!1,lexical:!1,identifierPattern:!1,jsx:!0,specDeviation:!0,uniqueKeyInPattern:!1};function rD(e,u){const{parse:t}=tD,n=[],r=[],o=t(e,Object.assign(Object.assign({},nD),{},{module:u,onComment:n,onToken:r}));return o.comments=n,o.tokens=r,o}var oD={parsers:{meriyah:pi((function(t,n,r){const{result:o,error:i}=u((()=>rD(t,!0)),(()=>rD(t,!1)));if(!o)throw function(u){const{message:t,line:n,column:r}=u;return"number"!=typeof n?u:e(t,{start:{line:n,column:r}})}(i);return fo(o,Object.assign(Object.assign({},r),{},{originalText:t}))}))}};export default oD;
