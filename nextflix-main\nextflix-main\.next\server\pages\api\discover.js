(function() {
var exports = {};
exports.id = "pages/api/discover";
exports.ids = ["pages/api/discover"];
exports.modules = {

/***/ "./config/genres.ts":
/*!**************************!*\
  !*** ./config/genres.ts ***!
  \**************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "genres": function() { return /* binding */ genres; }
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ "./types/index.ts");

const genres = {
  [_types__WEBPACK_IMPORTED_MODULE_0__.MediaType.MOVIE]: [{
    id: 28,
    name: 'Action'
  }, {
    id: 12,
    name: 'Adventure'
  }, {
    id: 16,
    name: 'Animation'
  }, {
    id: 35,
    name: 'Comedy'
  }, {
    id: 80,
    name: 'Crime'
  }, {
    id: 99,
    name: 'Documentary'
  }, {
    id: 18,
    name: 'Drama'
  }, {
    id: 10751,
    name: 'Family'
  }, {
    id: 14,
    name: 'Fantasy'
  }, {
    id: 36,
    name: 'History'
  }, {
    id: 27,
    name: 'Horror'
  }, {
    id: 10402,
    name: 'Music'
  }, {
    id: 9648,
    name: 'Mystery'
  }, {
    id: 10749,
    name: 'Romance'
  }, {
    id: 878,
    name: 'Science Fiction'
  }, {
    id: 10770,
    name: 'TV Movie'
  }, {
    id: 53,
    name: 'Thriller'
  }, {
    id: 10752,
    name: 'War'
  }, {
    id: 37,
    name: 'Western'
  }],
  [_types__WEBPACK_IMPORTED_MODULE_0__.MediaType.TV]: [{
    id: 10759,
    name: 'Adventure'
  }, {
    id: 16,
    name: 'Animation'
  }, {
    id: 35,
    name: 'Comedy'
  }, {
    id: 80,
    name: 'Crime'
  }, {
    id: 99,
    name: 'Documentary'
  }, {
    id: 18,
    name: 'Drama'
  }, {
    id: 10751,
    name: 'Family'
  }, {
    id: 10762,
    name: 'Kids'
  }, {
    id: 9648,
    name: 'Mystery'
  }, {
    id: 10763,
    name: 'News'
  }, {
    id: 10764,
    name: 'Reality'
  }, {
    id: 10765,
    name: 'Sci-Fi & Fantasy'
  }, {
    id: 10766,
    name: 'Soap'
  }, {
    id: 10767,
    name: 'Talk'
  }, {
    id: 10768,
    name: 'War & Politics'
  }, {
    id: 37,
    name: 'Western'
  }]
};

/***/ }),

/***/ "./pages/api/discover.ts":
/*!*******************************!*\
  !*** ./pages/api/discover.ts ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ handler; }
/* harmony export */ });
/* harmony import */ var _utils_apiResolvers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/apiResolvers */ "./utils/apiResolvers.ts");
/* harmony import */ var _utils_axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/axios */ "./utils/axios.ts");


const apiKey = process.env.TMDB_KEY;
async function handler(request, response) {
  const axios = (0,_utils_axios__WEBPACK_IMPORTED_MODULE_1__.default)();
  const {
    type,
    genre
  } = request.query;

  try {
    const result = await axios.get(`/discover/${type}`, {
      params: {
        api_key: apiKey,
        with_genres: genre,
        watch_region: 'US',
        with_networks: '213'
      }
    });
    const data = (0,_utils_apiResolvers__WEBPACK_IMPORTED_MODULE_0__.parse)(result.data.results, type);
    response.status(200).json({
      type: 'Success',
      data
    });
  } catch (error) {
    console.log(error.data);
    response.status(500).json({
      type: 'Error',
      data: error.data
    });
  }
}

/***/ }),

/***/ "./types/index.ts":
/*!************************!*\
  !*** ./types/index.ts ***!
  \************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "MediaType": function() { return /* binding */ MediaType; }
/* harmony export */ });
let MediaType;

(function (MediaType) {
  MediaType["MOVIE"] = "movie";
  MediaType["TV"] = "tv";
})(MediaType || (MediaType = {}));

/***/ }),

/***/ "./utils/apiResolvers.ts":
/*!*******************************!*\
  !*** ./utils/apiResolvers.ts ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "parse": function() { return /* binding */ parse; }
/* harmony export */ });
/* harmony import */ var _config_genres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/genres */ "./config/genres.ts");

function parse(array, type) {
  const parsedResponse = [];
  array.forEach(element => {
    const resolved = {
      id: element.id,
      title: element.name || element.title,
      rating: element.vote_average,
      overview: element.overview,
      poster: getImageUrl(element.poster_path, 'poster'),
      banner: getImageUrl(element.backdrop_path, 'original'),
      genre: getGenre(element.genre_ids, type)
    };
    parsedResponse.push(resolved);
  });
  return parsedResponse;
}

function getImageUrl(path, type) {
  const dimension = type === 'poster' ? 'w500' : 'original';
  return `https://image.tmdb.org/t/p/${dimension}${path}`;
}

function getGenre(genreIds, type) {
  const result = _config_genres__WEBPACK_IMPORTED_MODULE_0__.genres[type].filter(item => genreIds.includes(item.id));

  if (result.length > 3) {
    return result.slice(0, 3);
  }

  return result;
}

/***/ }),

/***/ "./utils/axios.ts":
/*!************************!*\
  !*** ./utils/axios.ts ***!
  \************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ getInstance; }
/* harmony export */ });
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ "axios");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(axios__WEBPACK_IMPORTED_MODULE_0__);

function getInstance() {
  return axios__WEBPACK_IMPORTED_MODULE_0___default().create({
    baseURL: 'https://api.themoviedb.org/3'
  });
}

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ (function(module) {

"use strict";
module.exports = require("axios");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
var __webpack_exports__ = (__webpack_exec__("./pages/api/discover.ts"));
module.exports = __webpack_exports__;

})();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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