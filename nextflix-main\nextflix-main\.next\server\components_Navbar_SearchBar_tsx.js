exports.id = "components_Navbar_SearchBar_tsx";
exports.ids = ["components_Navbar_SearchBar_tsx"];
exports.modules = {

/***/ "./components/Navbar/SearchBar.tsx":
/*!*****************************************!*\
  !*** ./components/Navbar/SearchBar.tsx ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ SearchBar; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ "framer-motion");
/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(framer_motion__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useExternalClick */ "./hooks/useExternalClick.ts");
/* harmony import */ var _utils_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/icons */ "./utils/icons.ts");
/* harmony import */ var _hooks_useDimensions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../hooks/useDimensions */ "./hooks/useDimensions.ts");
/* harmony import */ var _styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../styles/Navbar.module.scss */ "./styles/Navbar.module.scss");
/* harmony import */ var _styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_6__);

var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\Navbar\\SearchBar.tsx";

/* eslint-disable @next/next/no-img-element */






function SearchBar() {
  const searchRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
  const {
    0: isSearch,
    1: setIsSearch
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const {
    0: searchInput,
    1: setSearchInput
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
  const {
    isMobile
  } = (0,_hooks_useDimensions__WEBPACK_IMPORTED_MODULE_5__.default)();

  const onSearchActive = () => {
    setIsSearch(true);
  };

  (0,_hooks_useExternalClick__WEBPACK_IMPORTED_MODULE_3__.default)(searchRef, () => {
    setIsSearch(false);
  });

  const onSearchQuery = ({
    target
  }) => {
    setSearchInput(target.value);
  };

  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("div", {
    ref: searchRef,
    className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_6___default().searchPanel),
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {
      className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_6___default().searchBar),
      initial: "hidden",
      animate: isSearch ? 'visible' : 'hidden',
      transition: {
        duration: 0.45
      },
      variants: {
        visible: {
          opacity: 1,
          width: isMobile ? 120 : 250
        },
        hidden: {
          opacity: 0,
          width: 0
        }
      },
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_icons__WEBPACK_IMPORTED_MODULE_4__.Search, {
        className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_6___default().icon)
      }, void 0, false, {
        fileName: _jsxFileName,
        lineNumber: 46,
        columnNumber: 9
      }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("input", {
        type: "text",
        className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_6___default().searchBar__input),
        value: searchInput,
        onChange: onSearchQuery,
        placeholder: "Titles, people, genres"
      }, void 0, false, {
        fileName: _jsxFileName,
        lineNumber: 47,
        columnNumber: 9
      }, this)]
    }, void 0, true, {
      fileName: _jsxFileName,
      lineNumber: 31,
      columnNumber: 7
    }, this), !isSearch && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_utils_icons__WEBPACK_IMPORTED_MODULE_4__.Search, {
      className: (_styles_Navbar_module_scss__WEBPACK_IMPORTED_MODULE_6___default().icon),
      onMouseOver: onSearchActive
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 55,
      columnNumber: 21
    }, this)]
  }, void 0, true, {
    fileName: _jsxFileName,
    lineNumber: 30,
    columnNumber: 5
  }, this);
}

/***/ }),

/***/ "./hooks/useExternalClick.ts":
/*!***********************************!*\
  !*** ./hooks/useExternalClick.ts ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useExternalClick; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

function useExternalClick(ref, callback) {
  const onClick = event => {
    var _ref$current;

    if (!(ref !== null && ref !== void 0 && (_ref$current = ref.current) !== null && _ref$current !== void 0 && _ref$current.contains(event.target))) {
      callback();
    }
  };

  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    document.addEventListener('click', onClick);
    return () => document.removeEventListener('click', onClick);
  });
}

/***/ })

};
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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