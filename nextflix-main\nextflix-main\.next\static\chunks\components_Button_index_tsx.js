(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_Button_index_tsx"],{

/***/ "./components/Button/index.tsx":
/*!*************************************!*\
  !*** ./components/Button/index.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Button; }
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/react/jsx-dev-runtime.js");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../styles/Button.module.scss */ "./styles/Button.module.scss");
/* harmony import */ var _styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1__);
/* module decorator */ module = __webpack_require__.hmd(module);

var _jsxFileName = "C:\\D disk\\shadab sir cv\\nextflix-main\\nextflix-main\\components\\Button\\index.tsx";

function Button(props) {
  var filled = props.filled,
      label = props.label,
      Icon = props.Icon,
      rounded = props.rounded,
      onClick = props.onClick;
  var backgroundColor = filled ? 'white' : '#6d6d6db3';
  var fontColor = filled ? 'black' : 'white';
  /* 
  if not rounded === normal long style
  if filled ( and rounded) === round style
  if rounded and not filled === outline style
  */

  var style = !rounded ? (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().button) : filled ? (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().roundButton) : (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().outlineRounded);
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("button", {
    className: style,
    style: {
      backgroundColor: "".concat(backgroundColor),
      color: "".concat(fontColor)
    },
    onClick: onClick,
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {
      className: (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().icon)
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 26,
      columnNumber: 7
    }, this), !rounded && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("span", {
      className: (_styles_Button_module_scss__WEBPACK_IMPORTED_MODULE_1___default().label),
      children: label
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 27,
      columnNumber: 20
    }, this)]
  }, void 0, true, {
    fileName: _jsxFileName,
    lineNumber: 25,
    columnNumber: 5
  }, this);
}
_c = Button;

var _c;

$RefreshReg$(_c, "Button");

;
    var _a, _b;
    // Legacy CSS implementations will `eval` browser code in a Node.js context
    // to extract CSS. For backwards compatibility, we need to check we're in a
    // browser context before continuing.
    if (typeof self !== 'undefined' &&
        // AMP / No-JS mode does not inject these helpers:
        '$RefreshHelpers$' in self) {
        var currentExports = module.__proto__.exports;
        var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
        // This cannot happen in MainTemplate because the exports mismatch between
        // templating and execution.
        self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
        // A module can be accepted automatically based on its exports, e.g. when
        // it is a Refresh Boundary.
        if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
            // Save the previous exports on update so we can compare the boundary
            // signatures.
            module.hot.dispose(function (data) {
                data.prevExports = currentExports;
            });
            // Unconditionally accept an update to this module, we'll check if it's
            // still a Refresh Boundary later.
            module.hot.accept();
            // This field is set when the previous version of this module was a
            // Refresh Boundary, letting us know we need to check for invalidation or
            // enqueue an update.
            if (prevExports !== null) {
                // A boundary can become ineligible if its exports are incompatible
                // with the previous exports.
                //
                // For example, if you add/remove/change exports, we'll want to
                // re-execute the importing modules, and force those components to
                // re-render. Similarly, if you convert a class component to a
                // function, we want to invalidate the boundary.
                if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                    module.hot.invalidate();
                }
                else {
                    self.$RefreshHelpers$.scheduleUpdate();
                }
            }
        }
        else {
            // Since we just executed the code for the module, it's possible that the
            // new exports made it ineligible for being a boundary.
            // We only care about the case when we were _previously_ a boundary,
            // because we already accepted this update (accidental side effect).
            var isNoLongerABoundary = prevExports !== null;
            if (isNoLongerABoundary) {
                module.hot.invalidate();
            }
        }
    }


/***/ }),

/***/ "./styles/Button.module.scss":
/*!***********************************!*\
  !*** ./styles/Button.module.scss ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var api = __webpack_require__(/*! !../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ "./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js");
            var content = __webpack_require__(/*! !!../node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[1]!../node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[2]!../node_modules/next/dist/compiled/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[3].use[3]!../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[4]!./Button.module.scss */ "./node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[1]!./node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[2]!./node_modules/next/dist/compiled/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[3].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[4]!./styles/Button.module.scss");

            content = content.__esModule ? content.default : content;

            if (typeof content === 'string') {
              content = [[module.id, content, '']];
            }

var options = {};

options.insert = function(element){// These elements should always exist. If they do not,
// this code should fail.
var anchorElement=document.querySelector('#__next_css__DO_NOT_USE__');var parentNode=anchorElement.parentNode;// Normally <head>
// Each style tag should be placed right before our
// anchor. By inserting before and not after, we do not
// need to track the last inserted element.
parentNode.insertBefore(element,anchorElement);};
options.singleton = false;

var update = api(content, options);


if (true) {
  if (!content.locals || module.hot.invalidate) {
    var isEqualLocals = function isEqualLocals(a,b,isNamedExport){if(!a&&b||a&&!b){return false;}let p;for(p in a){if(isNamedExport&&p==='default'){// eslint-disable-next-line no-continue
continue;}if(a[p]!==b[p]){return false;}}for(p in b){if(isNamedExport&&p==='default'){// eslint-disable-next-line no-continue
continue;}if(!a[p]){return false;}}return true;};
    var oldLocals = content.locals;

    module.hot.accept(
      /*! !!../node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[1]!../node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[2]!../node_modules/next/dist/compiled/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[3].use[3]!../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[4]!./Button.module.scss */ "./node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[1]!./node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[2]!./node_modules/next/dist/compiled/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[3].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[4]!./styles/Button.module.scss",
      function () {
        content = __webpack_require__(/*! !!../node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[1]!../node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[2]!../node_modules/next/dist/compiled/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[3].use[3]!../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[4]!./Button.module.scss */ "./node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[1]!./node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[2]!./node_modules/next/dist/compiled/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[3].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[4]!./styles/Button.module.scss");

              content = content.__esModule ? content.default : content;

              if (typeof content === 'string') {
                content = [[module.id, content, '']];
              }

              if (!isEqualLocals(oldLocals, content.locals)) {
                module.hot.invalidate();

                return;
              }

              oldLocals = content.locals;

              update(content);
      }
    )
  }

  module.hot.dispose(function() {
    update();
  });
}

module.exports = content.locals || {};

/***/ }),

/***/ "./node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[1]!./node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[2]!./node_modules/next/dist/compiled/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[3].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[4]!./styles/Button.module.scss":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[1]!./node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[2]!./node_modules/next/dist/compiled/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[3].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[3].use[4]!./styles/Button.module.scss ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../node_modules/next/dist/compiled/css-loader/api.js */ "./node_modules/next/dist/compiled/css-loader/api.js");
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);
// Module
___CSS_LOADER_EXPORT___.push([module.id, ".Button_button__1D2JL {\n  z-index: 10;\n  color: #000;\n  text-shadow: none;\n  width: -moz-fit-content;\n  width: fit-content;\n  padding: 0.7rem 1.8rem;\n  border-radius: 0.3rem;\n  cursor: pointer;\n  display: -moz-box;\n  display: flex;\n  -moz-box-orient: horizontal;\n  -moz-box-direction: normal;\n       flex-direction: row;\n  -moz-box-pack: center;\n       justify-content: center;\n  -moz-box-align: center;\n       align-items: center;\n  margin: 0.4rem;\n  outline: none;\n  border: none;\n}\n@media only screen and (max-width: 640px) and (orientation: portrait) {\n  .Button_button__1D2JL {\n    background-color: tomato;\n    padding: 0.5rem 1.2rem;\n  }\n}\n.Button_button__1D2JL:hover {\n  opacity: 0.8;\n}\n.Button_button__1D2JL .Button_label__180cU {\n  font-weight: bold;\n  margin-left: 0.8rem;\n  font-size: -moz-calc( 0.9rem + 0.2 * math.div((100vw - 768px), 256) );\n  font-size: calc( 0.9rem + 0.2 * math.div((100vw - 768px), 256) );\n}\n@media screen and (max-width: 768px) {\n  .Button_button__1D2JL .Button_label__180cU {\n    font-size: 0.9rem;\n  }\n}\n@media screen and (min-width: 1024px) {\n  .Button_button__1D2JL .Button_label__180cU {\n    font-size: 1.1rem;\n  }\n}\n@media only screen and (max-width: 640px) and (orientation: portrait) {\n  .Button_button__1D2JL .Button_label__180cU {\n    margin-left: 0.6rem;\n  }\n}\n.Button_button__1D2JL .Button_icon__2M4k7 {\n  font-size: -moz-calc( 1.1rem + 0.3 * math.div((100vw - 768px), 256) );\n  font-size: calc( 1.1rem + 0.3 * math.div((100vw - 768px), 256) );\n}\n@media screen and (max-width: 768px) {\n  .Button_button__1D2JL .Button_icon__2M4k7 {\n    font-size: 1.1rem;\n  }\n}\n@media screen and (min-width: 1024px) {\n  .Button_button__1D2JL .Button_icon__2M4k7 {\n    font-size: 1.4rem;\n  }\n}\n\n.Button_roundButton__mjd6k {\n  z-index: 10;\n  color: #000;\n  text-shadow: none;\n  padding: 0.4rem;\n  border-radius: 100%;\n  cursor: pointer;\n  display: -moz-box;\n  display: flex;\n  -moz-box-orient: horizontal;\n  -moz-box-direction: normal;\n       flex-direction: row;\n  height: -moz-min-content;\n  height: min-content;\n  -moz-box-pack: center;\n       justify-content: center;\n  -moz-box-align: center;\n       align-items: center;\n  margin: 0.2rem;\n  outline: none;\n  border: none;\n  width: -moz-min-content;\n  width: min-content;\n}\n.Button_roundButton__mjd6k:hover {\n  opacity: 0.8;\n}\n.Button_roundButton__mjd6k .Button_icon__2M4k7 {\n  font-size: 1rem;\n}\n\n.Button_outlineRounded__adTWY {\n  z-index: 10;\n  background-color: #2a2a2a99 !important;\n  padding: 0.3rem;\n  border-radius: 100%;\n  cursor: pointer;\n  width: -moz-min-content;\n  width: min-content;\n  height: -moz-min-content;\n  height: min-content;\n  display: -moz-box;\n  display: flex;\n  -moz-box-orient: horizontal;\n  -moz-box-direction: normal;\n       flex-direction: row;\n  -moz-box-pack: center;\n       justify-content: center;\n  -moz-box-align: center;\n       align-items: center;\n  margin: 0.2rem;\n  outline: none;\n  border: #ffffff80 solid 2.2px;\n  font-size: 1rem;\n}\n.Button_outlineRounded__adTWY:hover {\n  border-color: #fff;\n}", "",{"version":3,"sources":["webpack://Button.module.scss","webpack://_variables.scss","webpack://_mixins.scss"],"names":[],"mappings":"AAGA;EACE,WAAA;EACA,WCIM;EDHN,iBAAA;EACA,uBAAA;EAAA,kBAAA;EACA,sBAAA;EACA,qBAAA;EACA,eAAA;EACA,iBAAA;EAAA,aAAA;EACA,2BAAA;EAAA,0BAAA;OAAA,mBAAA;EACA,qBAAA;OAAA,uBAAA;EACA,sBAAA;OAAA,mBAAA;EACA,cAAA;EACA,aAAA;EACA,YAAA;AAFF;AEyBE;EFrCF;IAiBI,wBAAA;IACA,sBAAA;EADF;AACF;AAGE;EACE,YAAA;AADJ;AAIE;EACE,iBAAA;EACA,mBAAA;EEqBF,qEAAA;EAAA,gEAAA;AFtBF;AE8BE;EF/BA;IEgCE,iBF5BoB;EACtB;AACF;AE6BE;EFnCA;IEoCE,iBFhC4B;EAM9B;AACF;AECE;EFZA;IAMI,mBAAA;EASJ;AACF;AANE;EEaA,qEAAA;EAAA,gEAAA;AFJF;AEYE;EFrBA;IEsBE,iBFrBoB;EAYtB;AACF;AEWE;EFzBA;IE0BE,iBFzB4B;EAiB9B;AACF;;AAdA;EACE,WAAA;EACA,WCpCM;EDqCN,iBAAA;EACA,eAAA;EACA,mBAAA;EACA,eAAA;EACA,iBAAA;EAAA,aAAA;EACA,2BAAA;EAAA,0BAAA;OAAA,mBAAA;EACA,wBAAA;EAAA,mBAAA;EACA,qBAAA;OAAA,uBAAA;EACA,sBAAA;OAAA,mBAAA;EACA,cAAA;EACA,aAAA;EACA,YAAA;EACA,uBAAA;EAAA,kBAAA;AAiBF;AAfE;EACE,YAAA;AAiBJ;AAdE;EACE,eAAA;AAgBJ;;AAZA;EACE,WAAA;EACA,sCAAA;EACA,eAAA;EACA,mBAAA;EACA,eAAA;EACA,uBAAA;EAAA,kBAAA;EACA,wBAAA;EAAA,mBAAA;EACA,iBAAA;EAAA,aAAA;EACA,2BAAA;EAAA,0BAAA;OAAA,mBAAA;EACA,qBAAA;OAAA,uBAAA;EACA,sBAAA;OAAA,mBAAA;EACA,cAAA;EACA,aAAA;EACA,6BAAA;EACA,eAAA;AAeF;AAbE;EACE,kBChFI;AD+FR","sourcesContent":["@import './variables.scss';\n@import './_mixins.scss';\n\n.button {\n  z-index: 10;\n  color: $black;\n  text-shadow: none;\n  width: fit-content;\n  padding: 0.7rem 1.8rem;\n  border-radius: 0.3rem;\n  cursor: pointer;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  margin: 0.4rem;\n  outline: none;\n  border: none;\n\n  @include for-mobile-only {\n    background-color: tomato;\n    padding: 0.5rem 1.2rem;\n  }\n\n  &:hover {\n    opacity: 0.8;\n  }\n\n  .label {\n    font-weight: bold;\n    margin-left: 0.8rem;\n\n    @include fluid-type(0.9rem, 1.1rem);\n    @include for-mobile-only {\n      margin-left: 0.6rem;\n    }\n  }\n\n  .icon {\n    @include fluid-type(1.1rem, 1.4rem);\n  }\n}\n\n.roundButton {\n  z-index: 10;\n  color: $black;\n  text-shadow: none;\n  padding: 0.4rem;\n  border-radius: 100%;\n  cursor: pointer;\n  display: flex;\n  flex-direction: row;\n  height: min-content;\n  justify-content: center;\n  align-items: center;\n  margin: 0.2rem;\n  outline: none;\n  border: none;\n  width: min-content;\n\n  &:hover {\n    opacity: 0.8;\n  }\n\n  .icon {\n    font-size: 1rem;\n  }\n}\n\n.outlineRounded {\n  z-index: 10;\n  background-color: $buttonRound-color !important;\n  padding: 0.3rem;\n  border-radius: 100%;\n  cursor: pointer;\n  width: min-content;\n  height: min-content;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  margin: 0.2rem;\n  outline: none;\n  border: $button-border solid 2.2px;\n  font-size: 1rem;\n\n  &:hover {\n    border-color: $white;\n  }\n}\n","$primary-button: #e50914;\n$primary-buttonHover: #ad070f;\n$secondary: #141414;\n\n$navBar-transparent: #141414b3;\n$navBar-gradient: #00000000;\n$background-overlay: #000000bf;\n$white: #fff;\n$white-hover: #e5e5e5;\n$black: #000;\n$card-color: #252525;\n$buttonRound-color: #2a2a2a99;\n$button-border: #ffffff80;\n$greenText: #53d853;","@use \"sass:math\";\n\n//breakpoints\n$xs: 480px;\n$sm: 640px;\n$md: 768px;\n$lg: 1024px;\n$xl: 1280px;\n$xxl: 1536px;\n\n//Font-size\n$base-font-size: 16px;\n$default-min-font-size: 8px;\n$default-max-font-size: 32px;\n\n@mixin for-xxl-devices {\n  @media (max-width: $xxl) {\n    @content;\n  }\n}\n\n@mixin for-xl-devices {\n  @media (max-width: $xl) {\n    @content;\n  }\n}\n\n@mixin for-large-devices {\n  @media (max-width: $lg) {\n    @content;\n  }\n}\n\n@mixin for-tablet-screens {\n  @media (max-width: $md) {\n    @content;\n  }\n}\n\n@mixin for-mobile-only {\n  @media only screen and (max-width: $sm) and (orientation: portrait) {\n    @content;\n  }\n}\n\n@mixin fluid-type(\n  $min-font-size: $default-min-font-size,\n  $max-font-size: $default-max-font-size,\n  $lower-range: $md,\n  $upper-range: $lg\n) {\n  font-size: calc(\n    #{$min-font-size} + #{(\n        math.div($max-font-size, ($max-font-size * 0 + 1)) - math.div($min-font-size, ($min-font-size * 0 + 1))\n      )} * math.div((100vw - #{$lower-range}), #{(\n            math.div($upper-range, ($upper-range * 0 + 1)) - math.div($lower-range, ($lower-range * 0 + 1))\n          )})\n  );\n\n  @media screen and (max-width: $lower-range) {\n    font-size: $min-font-size;\n  }\n\n  @media screen and (min-width: $upper-range) {\n    font-size: $max-font-size;\n  }\n} "],"sourceRoot":""}]);
// Exports
___CSS_LOADER_EXPORT___.locals = {
	"button": "Button_button__1D2JL",
	"label": "Button_label__180cU",
	"icon": "Button_icon__2M4k7",
	"roundButton": "Button_roundButton__mjd6k",
	"outlineRounded": "Button_outlineRounded__adTWY"
};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

}]);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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