(function() {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/pages/_error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/pages/_error.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "./node_modules/next/node_modules/@babel/runtime/helpers/interopRequireDefault.js");

exports.__esModule = true;
exports.default = void 0;

var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));

var _head = _interopRequireDefault(__webpack_require__(/*! ../next-server/lib/head */ "../next-server/lib/head"));

const statusCodes = {
  400: 'Bad Request',
  404: 'This page could not be found',
  405: 'Method Not Allowed',
  500: 'Internal Server Error'
};

function _getInitialProps({
  res,
  err
}) {
  const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;
  return {
    statusCode
  };
}
/**
* `Error` component used for handling errors.
*/


class Error extends _react.default.Component {
  render() {
    const {
      statusCode
    } = this.props;
    const title = this.props.title || statusCodes[statusCode] || 'An unexpected error has occurred';
    return /*#__PURE__*/_react.default.createElement("div", {
      style: styles.error
    }, /*#__PURE__*/_react.default.createElement(_head.default, null, /*#__PURE__*/_react.default.createElement("title", null, statusCode ? `${statusCode}: ${title}` : 'Application error: a client-side exception has occurred')), /*#__PURE__*/_react.default.createElement("div", null, /*#__PURE__*/_react.default.createElement("style", {
      dangerouslySetInnerHTML: {
        __html: 'body { margin: 0 }'
      }
    }), statusCode ? /*#__PURE__*/_react.default.createElement("h1", {
      style: styles.h1
    }, statusCode) : null, /*#__PURE__*/_react.default.createElement("div", {
      style: styles.desc
    }, /*#__PURE__*/_react.default.createElement("h2", {
      style: styles.h2
    }, this.props.title || statusCode ? title : /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, "Application error: a client-side exception has occurred (", /*#__PURE__*/_react.default.createElement("a", {
      href: "https://nextjs.org/docs/messages/client-side-exception-occurred"
    }, "developer guidance"), ")"), "."))));
  }

}

exports.default = Error;
Error.displayName = 'ErrorPage';
Error.getInitialProps = _getInitialProps;
Error.origGetInitialProps = _getInitialProps;
const styles = {
  error: {
    color: '#000',
    background: '#fff',
    fontFamily: '-apple-system, BlinkMacSystemFont, Roboto, "Segoe UI", "Fira Sans", Avenir, "Helvetica Neue", "Lucida Grande", sans-serif',
    height: '100vh',
    textAlign: 'center',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center'
  },
  desc: {
    display: 'inline-block',
    textAlign: 'left',
    lineHeight: '49px',
    height: '49px',
    verticalAlign: 'middle'
  },
  h1: {
    display: 'inline-block',
    borderRight: '1px solid rgba(0, 0, 0,.3)',
    margin: 0,
    marginRight: '20px',
    padding: '10px 23px 10px 0',
    fontSize: '24px',
    fontWeight: 500,
    verticalAlign: 'top'
  },
  h2: {
    fontSize: '14px',
    fontWeight: 'normal',
    lineHeight: 'inherit',
    margin: 0,
    padding: 0
  }
};

/***/ }),

/***/ "./node_modules/next/node_modules/@babel/runtime/helpers/interopRequireDefault.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/node_modules/@babel/runtime/helpers/interopRequireDefault.js ***!
  \****************************************************************************************/
/***/ (function(module) {

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {
    "default": obj
  };
}

module.exports = _interopRequireDefault;

/***/ }),

/***/ "../next-server/lib/head":
/*!****************************************************!*\
  !*** external "next/dist/next-server/lib/head.js" ***!
  \****************************************************/
/***/ (function(module) {

"use strict";
module.exports = require("next/dist/next-server/lib/head.js");;

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ (function(module) {

"use strict";
module.exports = require("react");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
var __webpack_exports__ = (__webpack_exec__("./node_modules/next/dist/pages/_error.js"));
module.exports = __webpack_exports__;

})();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXRmbGl4LWNsb25lLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9wYWdlcy9fZXJyb3IuanMiLCJ3ZWJwYWNrOi8vbmV0ZmxpeC1jbG9uZS8uL25vZGVfbW9kdWxlcy9uZXh0L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIsIndlYnBhY2s6Ly9uZXRmbGl4LWNsb25lL2V4dGVybmFsIFwibmV4dC9kaXN0L25leHQtc2VydmVyL2xpYi9oZWFkLmpzXCIiLCJ3ZWJwYWNrOi8vbmV0ZmxpeC1jbG9uZS9leHRlcm5hbCBcInJlYWN0XCIiXSwibmFtZXMiOlsiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJleHBvcnRzIiwiX3JlYWN0IiwiX2hlYWQiLCJzdGF0dXNDb2RlcyIsIl9nZXRJbml0aWFsUHJvcHMiLCJyZXMiLCJlcnIiLCJzdGF0dXNDb2RlIiwiRXJyb3IiLCJkZWZhdWx0IiwiQ29tcG9uZW50IiwicmVuZGVyIiwicHJvcHMiLCJ0aXRsZSIsImNyZWF0ZUVsZW1lbnQiLCJzdHlsZSIsInN0eWxlcyIsImVycm9yIiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiLCJoMSIsImRlc2MiLCJoMiIsIkZyYWdtZW50IiwiaHJlZiIsImRpc3BsYXlOYW1lIiwiZ2V0SW5pdGlhbFByb3BzIiwib3JpZ0dldEluaXRpYWxQcm9wcyIsImNvbG9yIiwiYmFja2dyb3VuZCIsImZvbnRGYW1pbHkiLCJoZWlnaHQiLCJ0ZXh0QWxpZ24iLCJkaXNwbGF5IiwiZmxleERpcmVjdGlvbiIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImxpbmVIZWlnaHQiLCJ2ZXJ0aWNhbEFsaWduIiwiYm9yZGVyUmlnaHQiLCJtYXJnaW4iLCJtYXJnaW5SaWdodCIsInBhZGRpbmciLCJmb250U2l6ZSIsImZvbnRXZWlnaHQiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBYTs7QUFBQSxJQUFJQSxzQkFBc0IsR0FBQ0MsbUJBQU8sQ0FBQyxzSUFBRCxDQUFsQzs7QUFBbUZDLGtCQUFBLEdBQW1CLElBQW5CO0FBQXdCQSxlQUFBLEdBQWdCLEtBQUssQ0FBckI7O0FBQXVCLElBQUlDLE1BQU0sR0FBQ0gsc0JBQXNCLENBQUNDLG1CQUFPLENBQUMsb0JBQUQsQ0FBUixDQUFqQzs7QUFBb0QsSUFBSUcsS0FBSyxHQUFDSixzQkFBc0IsQ0FBQ0MsbUJBQU8sQ0FBQyx3REFBRCxDQUFSLENBQWhDOztBQUFxRSxNQUFNSSxXQUFXLEdBQUM7QUFBQyxPQUFJLGFBQUw7QUFBbUIsT0FBSSw4QkFBdkI7QUFBc0QsT0FBSSxvQkFBMUQ7QUFBK0UsT0FBSTtBQUFuRixDQUFsQjs7QUFBOEgsU0FBU0MsZ0JBQVQsQ0FBMEI7QUFBQ0MsS0FBRDtBQUFLQztBQUFMLENBQTFCLEVBQW9DO0FBQUMsUUFBTUMsVUFBVSxHQUFDRixHQUFHLElBQUVBLEdBQUcsQ0FBQ0UsVUFBVCxHQUFvQkYsR0FBRyxDQUFDRSxVQUF4QixHQUFtQ0QsR0FBRyxHQUFDQSxHQUFHLENBQUNDLFVBQUwsR0FBZ0IsR0FBdkU7QUFBMkUsU0FBTTtBQUFDQTtBQUFELEdBQU47QUFBb0I7QUFBQTtBQUMxZ0I7QUFDQTs7O0FBQUcsTUFBTUMsS0FBTixTQUFvQlAsTUFBTSxDQUFDUSxPQUFQLENBQWVDLFNBQW5DLENBQTRDO0FBQUNDLFFBQU0sR0FBRTtBQUFDLFVBQUs7QUFBQ0o7QUFBRCxRQUFhLEtBQUtLLEtBQXZCO0FBQTZCLFVBQU1DLEtBQUssR0FBQyxLQUFLRCxLQUFMLENBQVdDLEtBQVgsSUFBa0JWLFdBQVcsQ0FBQ0ksVUFBRCxDQUE3QixJQUEyQyxrQ0FBdkQ7QUFBMEYsV0FBTSxhQUFhTixNQUFNLENBQUNRLE9BQVAsQ0FBZUssYUFBZixDQUE2QixLQUE3QixFQUFtQztBQUFDQyxXQUFLLEVBQUNDLE1BQU0sQ0FBQ0M7QUFBZCxLQUFuQyxFQUF3RCxhQUFhaEIsTUFBTSxDQUFDUSxPQUFQLENBQWVLLGFBQWYsQ0FBNkJaLEtBQUssQ0FBQ08sT0FBbkMsRUFBMkMsSUFBM0MsRUFBZ0QsYUFBYVIsTUFBTSxDQUFDUSxPQUFQLENBQWVLLGFBQWYsQ0FBNkIsT0FBN0IsRUFBcUMsSUFBckMsRUFBMENQLFVBQVUsR0FBRSxHQUFFQSxVQUFXLEtBQUlNLEtBQU0sRUFBekIsR0FBMkIseURBQS9FLENBQTdELENBQXJFLEVBQTZRLGFBQWFaLE1BQU0sQ0FBQ1EsT0FBUCxDQUFlSyxhQUFmLENBQTZCLEtBQTdCLEVBQW1DLElBQW5DLEVBQXdDLGFBQWFiLE1BQU0sQ0FBQ1EsT0FBUCxDQUFlSyxhQUFmLENBQTZCLE9BQTdCLEVBQXFDO0FBQUNJLDZCQUF1QixFQUFDO0FBQUNDLGNBQU0sRUFBQztBQUFSO0FBQXpCLEtBQXJDLENBQXJELEVBQW1KWixVQUFVLEdBQUMsYUFBYU4sTUFBTSxDQUFDUSxPQUFQLENBQWVLLGFBQWYsQ0FBNkIsSUFBN0IsRUFBa0M7QUFBQ0MsV0FBSyxFQUFDQyxNQUFNLENBQUNJO0FBQWQsS0FBbEMsRUFBb0RiLFVBQXBELENBQWQsR0FBOEUsSUFBM08sRUFBZ1AsYUFBYU4sTUFBTSxDQUFDUSxPQUFQLENBQWVLLGFBQWYsQ0FBNkIsS0FBN0IsRUFBbUM7QUFBQ0MsV0FBSyxFQUFDQyxNQUFNLENBQUNLO0FBQWQsS0FBbkMsRUFBdUQsYUFBYXBCLE1BQU0sQ0FBQ1EsT0FBUCxDQUFlSyxhQUFmLENBQTZCLElBQTdCLEVBQWtDO0FBQUNDLFdBQUssRUFBQ0MsTUFBTSxDQUFDTTtBQUFkLEtBQWxDLEVBQW9ELEtBQUtWLEtBQUwsQ0FBV0MsS0FBWCxJQUFrQk4sVUFBbEIsR0FBNkJNLEtBQTdCLEdBQW1DLGFBQWFaLE1BQU0sQ0FBQ1EsT0FBUCxDQUFlSyxhQUFmLENBQTZCYixNQUFNLENBQUNRLE9BQVAsQ0FBZWMsUUFBNUMsRUFBcUQsSUFBckQsRUFBMEQsMkRBQTFELEVBQXNILGFBQWF0QixNQUFNLENBQUNRLE9BQVAsQ0FBZUssYUFBZixDQUE2QixHQUE3QixFQUFpQztBQUFDVSxVQUFJLEVBQUM7QUFBTixLQUFqQyxFQUEwRyxvQkFBMUcsQ0FBbkksRUFBbVEsR0FBblEsQ0FBcEcsRUFBNFcsR0FBNVcsQ0FBcEUsQ0FBN1AsQ0FBMVIsQ0FBbkI7QUFBbStCOztBQUFwbUM7O0FBQXFtQ3hCLGVBQUEsR0FBZ0JRLEtBQWhCO0FBQXNCQSxLQUFLLENBQUNpQixXQUFOLEdBQWtCLFdBQWxCO0FBQThCakIsS0FBSyxDQUFDa0IsZUFBTixHQUFzQnRCLGdCQUF0QjtBQUF1Q0ksS0FBSyxDQUFDbUIsbUJBQU4sR0FBMEJ2QixnQkFBMUI7QUFBMkMsTUFBTVksTUFBTSxHQUFDO0FBQUNDLE9BQUssRUFBQztBQUFDVyxTQUFLLEVBQUMsTUFBUDtBQUFjQyxjQUFVLEVBQUMsTUFBekI7QUFBZ0NDLGNBQVUsRUFBQywySEFBM0M7QUFBdUtDLFVBQU0sRUFBQyxPQUE5SztBQUFzTEMsYUFBUyxFQUFDLFFBQWhNO0FBQXlNQyxXQUFPLEVBQUMsTUFBak47QUFBd05DLGlCQUFhLEVBQUMsUUFBdE87QUFBK09DLGNBQVUsRUFBQyxRQUExUDtBQUFtUUMsa0JBQWMsRUFBQztBQUFsUixHQUFQO0FBQW1TZixNQUFJLEVBQUM7QUFBQ1ksV0FBTyxFQUFDLGNBQVQ7QUFBd0JELGFBQVMsRUFBQyxNQUFsQztBQUF5Q0ssY0FBVSxFQUFDLE1BQXBEO0FBQTJETixVQUFNLEVBQUMsTUFBbEU7QUFBeUVPLGlCQUFhLEVBQUM7QUFBdkYsR0FBeFM7QUFBeVlsQixJQUFFLEVBQUM7QUFBQ2EsV0FBTyxFQUFDLGNBQVQ7QUFBd0JNLGVBQVcsRUFBQyw0QkFBcEM7QUFBaUVDLFVBQU0sRUFBQyxDQUF4RTtBQUEwRUMsZUFBVyxFQUFDLE1BQXRGO0FBQTZGQyxXQUFPLEVBQUMsa0JBQXJHO0FBQXdIQyxZQUFRLEVBQUMsTUFBakk7QUFBd0lDLGNBQVUsRUFBQyxHQUFuSjtBQUF1Sk4saUJBQWEsRUFBQztBQUFySyxHQUE1WTtBQUF3akJoQixJQUFFLEVBQUM7QUFBQ3FCLFlBQVEsRUFBQyxNQUFWO0FBQWlCQyxjQUFVLEVBQUMsUUFBNUI7QUFBcUNQLGNBQVUsRUFBQyxTQUFoRDtBQUEwREcsVUFBTSxFQUFDLENBQWpFO0FBQW1FRSxXQUFPLEVBQUM7QUFBM0U7QUFBM2pCLENBQWIsQzs7Ozs7Ozs7OztBQ0YxeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx3Qzs7Ozs7Ozs7Ozs7QUNOQSwrRDs7Ozs7Ozs7Ozs7QUNBQSxtQyIsImZpbGUiOiJwYWdlcy9fZXJyb3IuanMiLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjt2YXIgX2ludGVyb3BSZXF1aXJlRGVmYXVsdD1yZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHRcIik7ZXhwb3J0cy5fX2VzTW9kdWxlPXRydWU7ZXhwb3J0cy5kZWZhdWx0PXZvaWQgMDt2YXIgX3JlYWN0PV9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcInJlYWN0XCIpKTt2YXIgX2hlYWQ9X2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vbmV4dC1zZXJ2ZXIvbGliL2hlYWRcIikpO2NvbnN0IHN0YXR1c0NvZGVzPXs0MDA6J0JhZCBSZXF1ZXN0Jyw0MDQ6J1RoaXMgcGFnZSBjb3VsZCBub3QgYmUgZm91bmQnLDQwNTonTWV0aG9kIE5vdCBBbGxvd2VkJyw1MDA6J0ludGVybmFsIFNlcnZlciBFcnJvcid9O2Z1bmN0aW9uIF9nZXRJbml0aWFsUHJvcHMoe3JlcyxlcnJ9KXtjb25zdCBzdGF0dXNDb2RlPXJlcyYmcmVzLnN0YXR1c0NvZGU/cmVzLnN0YXR1c0NvZGU6ZXJyP2Vyci5zdGF0dXNDb2RlOjQwNDtyZXR1cm57c3RhdHVzQ29kZX07fS8qKlxuICogYEVycm9yYCBjb21wb25lbnQgdXNlZCBmb3IgaGFuZGxpbmcgZXJyb3JzLlxuICovY2xhc3MgRXJyb3IgZXh0ZW5kcyBfcmVhY3QuZGVmYXVsdC5Db21wb25lbnR7cmVuZGVyKCl7Y29uc3R7c3RhdHVzQ29kZX09dGhpcy5wcm9wcztjb25zdCB0aXRsZT10aGlzLnByb3BzLnRpdGxlfHxzdGF0dXNDb2Rlc1tzdGF0dXNDb2RlXXx8J0FuIHVuZXhwZWN0ZWQgZXJyb3IgaGFzIG9jY3VycmVkJztyZXR1cm4vKiNfX1BVUkVfXyovX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImRpdlwiLHtzdHlsZTpzdHlsZXMuZXJyb3J9LC8qI19fUFVSRV9fKi9fcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KF9oZWFkLmRlZmF1bHQsbnVsbCwvKiNfX1BVUkVfXyovX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsbnVsbCxzdGF0dXNDb2RlP2Ake3N0YXR1c0NvZGV9OiAke3RpdGxlfWA6J0FwcGxpY2F0aW9uIGVycm9yOiBhIGNsaWVudC1zaWRlIGV4Y2VwdGlvbiBoYXMgb2NjdXJyZWQnKSksLyojX19QVVJFX18qL19yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIixudWxsLC8qI19fUFVSRV9fKi9fcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic3R5bGVcIix7ZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6e19faHRtbDonYm9keSB7IG1hcmdpbjogMCB9J319KSxzdGF0dXNDb2RlPy8qI19fUFVSRV9fKi9fcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwiaDFcIix7c3R5bGU6c3R5bGVzLmgxfSxzdGF0dXNDb2RlKTpudWxsLC8qI19fUFVSRV9fKi9fcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIse3N0eWxlOnN0eWxlcy5kZXNjfSwvKiNfX1BVUkVfXyovX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImgyXCIse3N0eWxlOnN0eWxlcy5oMn0sdGhpcy5wcm9wcy50aXRsZXx8c3RhdHVzQ29kZT90aXRsZTovKiNfX1BVUkVfXyovX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcmVhY3QuZGVmYXVsdC5GcmFnbWVudCxudWxsLFwiQXBwbGljYXRpb24gZXJyb3I6IGEgY2xpZW50LXNpZGUgZXhjZXB0aW9uIGhhcyBvY2N1cnJlZCAoXCIsLyojX19QVVJFX18qL19yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJhXCIse2hyZWY6XCJodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9jbGllbnQtc2lkZS1leGNlcHRpb24tb2NjdXJyZWRcIn0sXCJkZXZlbG9wZXIgZ3VpZGFuY2VcIiksXCIpXCIpLFwiLlwiKSkpKTt9fWV4cG9ydHMuZGVmYXVsdD1FcnJvcjtFcnJvci5kaXNwbGF5TmFtZT0nRXJyb3JQYWdlJztFcnJvci5nZXRJbml0aWFsUHJvcHM9X2dldEluaXRpYWxQcm9wcztFcnJvci5vcmlnR2V0SW5pdGlhbFByb3BzPV9nZXRJbml0aWFsUHJvcHM7Y29uc3Qgc3R5bGVzPXtlcnJvcjp7Y29sb3I6JyMwMDAnLGJhY2tncm91bmQ6JyNmZmYnLGZvbnRGYW1pbHk6Jy1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgUm9ib3RvLCBcIlNlZ29lIFVJXCIsIFwiRmlyYSBTYW5zXCIsIEF2ZW5pciwgXCJIZWx2ZXRpY2EgTmV1ZVwiLCBcIkx1Y2lkYSBHcmFuZGVcIiwgc2Fucy1zZXJpZicsaGVpZ2h0OicxMDB2aCcsdGV4dEFsaWduOidjZW50ZXInLGRpc3BsYXk6J2ZsZXgnLGZsZXhEaXJlY3Rpb246J2NvbHVtbicsYWxpZ25JdGVtczonY2VudGVyJyxqdXN0aWZ5Q29udGVudDonY2VudGVyJ30sZGVzYzp7ZGlzcGxheTonaW5saW5lLWJsb2NrJyx0ZXh0QWxpZ246J2xlZnQnLGxpbmVIZWlnaHQ6JzQ5cHgnLGhlaWdodDonNDlweCcsdmVydGljYWxBbGlnbjonbWlkZGxlJ30saDE6e2Rpc3BsYXk6J2lubGluZS1ibG9jaycsYm9yZGVyUmlnaHQ6JzFweCBzb2xpZCByZ2JhKDAsIDAsIDAsLjMpJyxtYXJnaW46MCxtYXJnaW5SaWdodDonMjBweCcscGFkZGluZzonMTBweCAyM3B4IDEwcHggMCcsZm9udFNpemU6JzI0cHgnLGZvbnRXZWlnaHQ6NTAwLHZlcnRpY2FsQWxpZ246J3RvcCd9LGgyOntmb250U2l6ZTonMTRweCcsZm9udFdlaWdodDonbm9ybWFsJyxsaW5lSGVpZ2h0Oidpbmhlcml0JyxtYXJnaW46MCxwYWRkaW5nOjB9fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPV9lcnJvci5qcy5tYXAiLCJmdW5jdGlvbiBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KG9iaikge1xuICByZXR1cm4gb2JqICYmIG9iai5fX2VzTW9kdWxlID8gb2JqIDoge1xuICAgIFwiZGVmYXVsdFwiOiBvYmpcbiAgfTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0OyIsIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZShcIm5leHQvZGlzdC9uZXh0LXNlcnZlci9saWIvaGVhZC5qc1wiKTs7IiwibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKFwicmVhY3RcIik7OyJdLCJzb3VyY2VSb290IjoiIn0=